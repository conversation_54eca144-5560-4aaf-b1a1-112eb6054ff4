<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:26 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Package org.activiti.engine (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package org.activiti.engine (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package org.activiti.engine" class="title">Uses of Package<br>org.activiti.engine</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/activiti/engine/package-summary.html">org.activiti.engine</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine">org.activiti.engine</a></td>
<td class="colLast">
<div class="block">Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine"><code>ProcessEngineConfiguration</code></a>
    (typically based on a configuration file), from which a <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> can be obtained.<br/><br/>
    Through the services obtained from such a <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>: </b> Manages <a href="../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s <br/>

    <b><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>: </b> For starting and searching <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s <br/>
    
    <b><a href="../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>: </b> Exposes operations to manage human (standalone) <a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s, 
    such as claiming, completing and assigning tasks<br/>

    <b><a href="../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>: </b> Used for managing <a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s, 
    <a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s and the relations between them<br/>
        
    <b><a href="../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b><a href="../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.activiti.engine.delegate">org.activiti.engine.delegate</a></td>
<td class="colLast">
<div class="block">Interfaces used to include Java code in a process as the behavior of an activity 
    or as a listener to process events with <a href="../../../org/activiti/engine/delegate/JavaDelegate.html" title="interface in org.activiti.engine.delegate"><code>JavaDelegate</code></a>s.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine.delegate.event">org.activiti.engine.delegate.event</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.activiti.engine.delegate.event.impl">org.activiti.engine.delegate.event.impl</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine.dynamic">org.activiti.engine.dynamic</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.activiti.engine.test">org.activiti.engine.test</a></td>
<td class="colLast">
<div class="block">Helper classes for testing processes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine.test.mock">org.activiti.engine.test.mock</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> used by <a href="../../../org/activiti/engine/package-summary.html">org.activiti.engine</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ActivitiException.html#org.activiti.engine">ActivitiException</a>
<div class="block">Runtime exception that is the superclass of all Activiti exceptions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ActivitiObjectNotFoundException.html#org.activiti.engine">ActivitiObjectNotFoundException</a>
<div class="block">An exception indicating that the object that is required or actioned on
 does not exist.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/DynamicBpmnService.html#org.activiti.engine">DynamicBpmnService</a>
<div class="block">Service providing access to the repository of process definitions and deployments.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/EngineServices.html#org.activiti.engine">EngineServices</a>
<div class="block">Interface implemented by all classes that expose the Activiti services.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/FormService.html#org.activiti.engine">FormService</a>
<div class="block">Access to form data and rendered forms for starting new process instances and completing tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/HistoryService.html#org.activiti.engine">HistoryService</a>
<div class="block">Service exposing information about ongoing and past process instances.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/IdentityService.html#org.activiti.engine">IdentityService</a>
<div class="block">Service to manage <a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s and <a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ManagementService.html#org.activiti.engine">ManagementService</a>
<div class="block">Service for admin and maintenance operations on the process engine.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ProcessEngine.html#org.activiti.engine">ProcessEngine</a>
<div class="block">Provides access to all the services that expose the BPM and workflow operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ProcessEngineConfiguration.html#org.activiti.engine">ProcessEngineConfiguration</a>
<div class="block">Configuration information from which a process engine can be build.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ProcessEngineInfo.html#org.activiti.engine">ProcessEngineInfo</a>
<div class="block">Represents information about the initialization of the process engine.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ProcessEngineLifecycleListener.html#org.activiti.engine">ProcessEngineLifecycleListener</a>
<div class="block">Interface describing a listener that get's notified when certain event occurs,
 related to the process-engine lifecycle it is attached to.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/RepositoryService.html#org.activiti.engine">RepositoryService</a>
<div class="block">Service providing access to the repository of process definitions and deployments.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/RuntimeService.html#org.activiti.engine">RuntimeService</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/TaskService.html#org.activiti.engine">TaskService</a>
<div class="block">Service which provides access to <a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a> and form related operations.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.delegate">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> used by <a href="../../../org/activiti/engine/delegate/package-summary.html">org.activiti.engine.delegate</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ActivitiException.html#org.activiti.engine.delegate">ActivitiException</a>
<div class="block">Runtime exception that is the superclass of all Activiti exceptions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/EngineServices.html#org.activiti.engine.delegate">EngineServices</a>
<div class="block">Interface implemented by all classes that expose the Activiti services.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.delegate.event">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> used by <a href="../../../org/activiti/engine/delegate/event/package-summary.html">org.activiti.engine.delegate.event</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/EngineServices.html#org.activiti.engine.delegate.event">EngineServices</a>
<div class="block">Interface implemented by all classes that expose the Activiti services.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.delegate.event.impl">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> used by <a href="../../../org/activiti/engine/delegate/event/impl/package-summary.html">org.activiti.engine.delegate.event.impl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/EngineServices.html#org.activiti.engine.delegate.event.impl">EngineServices</a>
<div class="block">Interface implemented by all classes that expose the Activiti services.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.dynamic">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> used by <a href="../../../org/activiti/engine/dynamic/package-summary.html">org.activiti.engine.dynamic</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/DynamicBpmnConstants.html#org.activiti.engine.dynamic">DynamicBpmnConstants</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.test">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> used by <a href="../../../org/activiti/engine/test/package-summary.html">org.activiti.engine.test</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/FormService.html#org.activiti.engine.test">FormService</a>
<div class="block">Access to form data and rendered forms for starting new process instances and completing tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/HistoryService.html#org.activiti.engine.test">HistoryService</a>
<div class="block">Service exposing information about ongoing and past process instances.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/IdentityService.html#org.activiti.engine.test">IdentityService</a>
<div class="block">Service to manage <a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s and <a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ManagementService.html#org.activiti.engine.test">ManagementService</a>
<div class="block">Service for admin and maintenance operations on the process engine.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ProcessEngine.html#org.activiti.engine.test">ProcessEngine</a>
<div class="block">Provides access to all the services that expose the BPM and workflow operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ProcessEngineConfiguration.html#org.activiti.engine.test">ProcessEngineConfiguration</a>
<div class="block">Configuration information from which a process engine can be build.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/RepositoryService.html#org.activiti.engine.test">RepositoryService</a>
<div class="block">Service providing access to the repository of process definitions and deployments.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/RuntimeService.html#org.activiti.engine.test">RuntimeService</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/TaskService.html#org.activiti.engine.test">TaskService</a>
<div class="block">Service which provides access to <a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a> and form related operations.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.test.mock">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> used by <a href="../../../org/activiti/engine/test/mock/package-summary.html">org.activiti.engine.test.mock</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/activiti/engine/class-use/ProcessEngine.html#org.activiti.engine.test.mock">ProcessEngine</a>
<div class="block">Provides access to all the services that expose the BPM and workflow operations.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
