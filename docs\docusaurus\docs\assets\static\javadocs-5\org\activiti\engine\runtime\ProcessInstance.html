<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProcessInstance (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProcessInstance (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessInstance.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/NativeProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/ProcessInstance.html" target="_top">Frames</a></li>
<li><a href="ProcessInstance.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.runtime</div>
<h2 title="Interface ProcessInstance" class="title">Interface ProcessInstance</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ProcessInstance</span>
extends <a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a></pre>
<div class="block">Represents one execution of a  <a href="../../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><code>ProcessDefinition</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez, Daniel Meyer, Tijs Rademakers</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getBusinessKey--">getBusinessKey</a></span>()</code>
<div class="block">The business key of this process instance.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getDeploymentId--">getDeploymentId</a></span>()</code>
<div class="block">The deployment id of the process definition of the process instance.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getDescription--">getDescription</a></span>()</code>
<div class="block">Returns the description of this process instance.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getLocalizedDescription--">getLocalizedDescription</a></span>()</code>
<div class="block">Returns the localized description of this process instance.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getLocalizedName--">getLocalizedName</a></span>()</code>
<div class="block">Returns the localized name of this process instance.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this process instance.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getProcessDefinitionId--">getProcessDefinitionId</a></span>()</code>
<div class="block">The id of the process definition of the process instance.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getProcessDefinitionKey--">getProcessDefinitionKey</a></span>()</code>
<div class="block">The key of the process definition of the process instance.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getProcessDefinitionName--">getProcessDefinitionName</a></span>()</code>
<div class="block">The name of the process definition of the process instance.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getProcessDefinitionVersion--">getProcessDefinitionVersion</a></span>()</code>
<div class="block">The version of the process definition of the process instance.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getProcessVariables--">getProcessVariables</a></span>()</code>
<div class="block">Returns the process variables if requested in the process instance query</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#getTenantId--">getTenantId</a></span>()</code>
<div class="block">The tenant identifier of this process instance</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html#isSuspended--">isSuspended</a></span>()</code>
<div class="block">returns true if the process instance is suspended</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.runtime.Execution">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.runtime.<a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a></h3>
<code><a href="../../../../org/activiti/engine/runtime/Execution.html#getActivityId--">getActivityId</a>, <a href="../../../../org/activiti/engine/runtime/Execution.html#getId--">getId</a>, <a href="../../../../org/activiti/engine/runtime/Execution.html#getParentId--">getParentId</a>, <a href="../../../../org/activiti/engine/runtime/Execution.html#getProcessInstanceId--">getProcessInstanceId</a>, <a href="../../../../org/activiti/engine/runtime/Execution.html#getSuperExecutionId--">getSuperExecutionId</a>, <a href="../../../../org/activiti/engine/runtime/Execution.html#isEnded--">isEnded</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getProcessDefinitionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDefinitionId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessDefinitionId()</pre>
<div class="block">The id of the process definition of the process instance.</div>
</li>
</ul>
<a name="getProcessDefinitionName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDefinitionName</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessDefinitionName()</pre>
<div class="block">The name of the process definition of the process instance.</div>
</li>
</ul>
<a name="getProcessDefinitionKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDefinitionKey</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessDefinitionKey()</pre>
<div class="block">The key of the process definition of the process instance.</div>
</li>
</ul>
<a name="getProcessDefinitionVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDefinitionVersion</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getProcessDefinitionVersion()</pre>
<div class="block">The version of the process definition of the process instance.</div>
</li>
</ul>
<a name="getDeploymentId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeploymentId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDeploymentId()</pre>
<div class="block">The deployment id of the process definition of the process instance.</div>
</li>
</ul>
<a name="getBusinessKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusinessKey</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBusinessKey()</pre>
<div class="block">The business key of this process instance.</div>
</li>
</ul>
<a name="isSuspended--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSuspended</h4>
<pre>boolean&nbsp;isSuspended()</pre>
<div class="block">returns true if the process instance is suspended</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/runtime/Execution.html#isSuspended--">isSuspended</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a></code></dd>
</dl>
</li>
</ul>
<a name="getProcessVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessVariables</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getProcessVariables()</pre>
<div class="block">Returns the process variables if requested in the process instance query</div>
</li>
</ul>
<a name="getTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTenantId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTenantId()</pre>
<div class="block">The tenant identifier of this process instance</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/runtime/Execution.html#getTenantId--">getTenantId</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a></code></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Returns the name of this process instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/runtime/Execution.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a></code></dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDescription()</pre>
<div class="block">Returns the description of this process instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/runtime/Execution.html#getDescription--">getDescription</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a></code></dd>
</dl>
</li>
</ul>
<a name="getLocalizedName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalizedName</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getLocalizedName()</pre>
<div class="block">Returns the localized name of this process instance.</div>
</li>
</ul>
<a name="getLocalizedDescription--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getLocalizedDescription</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getLocalizedDescription()</pre>
<div class="block">Returns the localized description of this process instance.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessInstance.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/NativeProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/ProcessInstance.html" target="_top">Frames</a></li>
<li><a href="ProcessInstance.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
