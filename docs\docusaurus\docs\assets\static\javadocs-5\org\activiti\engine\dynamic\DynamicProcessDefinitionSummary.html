<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DynamicProcessDefinitionSummary (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DynamicProcessDefinitionSummary (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DynamicProcessDefinitionSummary.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/dynamic/DefaultPropertiesParser.html" title="class in org.activiti.engine.dynamic"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/dynamic/PropertiesParser.html" title="interface in org.activiti.engine.dynamic"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html" target="_top">Frames</a></li>
<li><a href="DynamicProcessDefinitionSummary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.dynamic</div>
<h2 title="Class DynamicProcessDefinitionSummary" class="title">Class DynamicProcessDefinitionSummary</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.dynamic.DynamicProcessDefinitionSummary</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine">DynamicBpmnConstants</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DynamicProcessDefinitionSummary</span>
extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine">DynamicBpmnConstants</a></pre>
<div class="block">Pojo class who can be used to check information between <a href="../../../../org/activiti/engine/DynamicBpmnService.html#getProcessDefinitionInfo-java.lang.String-"><code>DynamicBpmnService.getProcessDefinitionInfo(String)</code></a>
 and <code>BpmnModel</code>. Without exposing the internal behavoir of activiti's logic.

 Created by Pardo David on 5/12/2016.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.activiti.engine.DynamicBpmnConstants">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.activiti.engine.<a href="../../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine">DynamicBpmnConstants</a></h3>
<code><a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#BPMN_NODE">BPMN_NODE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_DESCRIPTION">LOCALIZATION_DESCRIPTION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_LANGUAGE">LOCALIZATION_LANGUAGE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_NAME">LOCALIZATION_NAME</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_NODE">LOCALIZATION_NODE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SCRIPT_TASK_SCRIPT">SCRIPT_TASK_SCRIPT</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SEQUENCE_FLOW_CONDITION">SEQUENCE_FLOW_CONDITION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_CLASS_NAME">SERVICE_TASK_CLASS_NAME</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_DELEGATE_EXPRESSION">SERVICE_TASK_DELEGATE_EXPRESSION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_EXPRESSION">SERVICE_TASK_EXPRESSION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#TASK_SKIP_EXPRESSION">TASK_SKIP_EXPRESSION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_ASSIGNEE">USER_TASK_ASSIGNEE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_GROUPS">USER_TASK_CANDIDATE_GROUPS</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS">USER_TASK_CANDIDATE_USERS</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CATEGORY">USER_TASK_CATEGORY</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_DESCRIPTION">USER_TASK_DESCRIPTION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_DUEDATE">USER_TASK_DUEDATE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_FORM_KEY">USER_TASK_FORM_KEY</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_NAME">USER_TASK_NAME</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_OWNER">USER_TASK_OWNER</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_PRIORITY">USER_TASK_PRIORITY</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html#DynamicProcessDefinitionSummary-org.activiti.bpmn.model.BpmnModel-com.fasterxml.jackson.databind.node.ObjectNode-com.fasterxml.jackson.databind.ObjectMapper-">DynamicProcessDefinitionSummary</a></span>(org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel,
                               com.fasterxml.jackson.databind.node.ObjectNode&nbsp;processInfo,
                               com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html#getBpmnProperties-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">getBpmnProperties</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;elementId,
                 com.fasterxml.jackson.databind.node.ObjectNode&nbsp;processInfoNode)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html#getElement-java.lang.String-">getElement</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;elementId)</code>
<div class="block">Returns the summary in the following structure:</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html#getSummary--">getSummary</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DynamicProcessDefinitionSummary-org.activiti.bpmn.model.BpmnModel-com.fasterxml.jackson.databind.node.ObjectNode-com.fasterxml.jackson.databind.ObjectMapper-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DynamicProcessDefinitionSummary</h4>
<pre>public&nbsp;DynamicProcessDefinitionSummary(org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel,
                                       com.fasterxml.jackson.databind.node.ObjectNode&nbsp;processInfo,
                                       com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getElement-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElement</h4>
<pre>public&nbsp;com.fasterxml.jackson.databind.node.ObjectNode&nbsp;getElement(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;elementId)
                                                          throws <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/IllegalStateException.html?is-external=true" title="class or interface in java.lang">IllegalStateException</a></pre>
<div class="block">Returns the summary in the following structure:
 <pre>
 {
     "elementId": (the elements id)
     "elementType": (the elements type)
     "elementSummary": {
         "<a href="../../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine"><code>DynamicBpmnConstants</code></a> linked to the elementType": {
             bpmnmodel : (array of strings | string | not provided if empty / blank / null)
             dynamic: (array of strings or string or not provided if blank or empty)
         }
     }
 }
 </pre>

 <p>
     If no value is found for a given <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine"><code>DynamicBpmnConstants</code></a> in the <code>BpmnModel</code> or
     ProcessDefinitionInfo. we don't store an key in the resulting <code>ObjectNode</code>. Null values should be avoided
     in JSON. Depending on the <code>ObjectMapper</code> configuration keys with a null value could even be removed when writting to json.
 </p>

 <p color="red">
     Currently supported flow elements are:
     <li>
         <ul>UserTask</ul>
         <ul>ScriptTask</ul>
     </li>
     No summary will field will be created for other elements. ElementId, and elementType will be available.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementId</code> - the id of the <code>FlowElement</code>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an <code>ObjectNode</code> with the provided structure.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/IllegalStateException.html?is-external=true" title="class or interface in java.lang">IllegalStateException</a></code> - if no <code>FlowElement</code> is found for the provided id.</dd>
</dl>
</li>
</ul>
<a name="getSummary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSummary</h4>
<pre>public&nbsp;com.fasterxml.jackson.databind.node.ObjectNode&nbsp;getSummary()</pre>
</li>
</ul>
<a name="getBpmnProperties-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBpmnProperties</h4>
<pre>protected&nbsp;com.fasterxml.jackson.databind.node.ObjectNode&nbsp;getBpmnProperties(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;elementId,
                                                                           com.fasterxml.jackson.databind.node.ObjectNode&nbsp;processInfoNode)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DynamicProcessDefinitionSummary.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/dynamic/DefaultPropertiesParser.html" title="class in org.activiti.engine.dynamic"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/dynamic/PropertiesParser.html" title="interface in org.activiti.engine.dynamic"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html" target="_top">Frames</a></li>
<li><a href="DynamicProcessDefinitionSummary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
