<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Serialized Form (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Serialized Form (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">Frames</a></li>
<li><a href="serialized-form.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<div class="serializedFormContainer">
<ul class="blockList">
<li class="blockList">
<h2 title="Package">Package&nbsp;org.activiti.engine</h2>
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine.ActivitiClassLoadingException">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/ActivitiClassLoadingException.html" title="class in org.activiti.engine">org.activiti.engine.ActivitiClassLoadingException</a> extends <a href="org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>className</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> className</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="org.activiti.engine.ActivitiException">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">org.activiti.engine.ActivitiException</a> extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/RuntimeException.html?is-external=true" title="class or interface in java.lang">RuntimeException</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="org.activiti.engine.ActivitiIllegalArgumentException">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">org.activiti.engine.ActivitiIllegalArgumentException</a> extends <a href="org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="org.activiti.engine.ActivitiObjectNotFoundException">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">org.activiti.engine.ActivitiObjectNotFoundException</a> extends <a href="org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>objectClass</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">T</a>&gt; objectClass</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="org.activiti.engine.ActivitiOptimisticLockingException">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/ActivitiOptimisticLockingException.html" title="class in org.activiti.engine">org.activiti.engine.ActivitiOptimisticLockingException</a> extends <a href="org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="org.activiti.engine.ActivitiTaskAlreadyClaimedException">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/ActivitiTaskAlreadyClaimedException.html" title="class in org.activiti.engine">org.activiti.engine.ActivitiTaskAlreadyClaimedException</a> extends <a href="org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>taskId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> taskId</pre>
<div class="block">the id of the task that is already claimed</div>
</li>
<li class="blockListLast">
<h4>taskAssignee</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> taskAssignee</pre>
<div class="block">the assignee of the task that is already claimed</div>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="org.activiti.engine.ActivitiWrongDbException">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/ActivitiWrongDbException.html" title="class in org.activiti.engine">org.activiti.engine.ActivitiWrongDbException</a> extends <a href="org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>libraryVersion</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> libraryVersion</pre>
</li>
<li class="blockListLast">
<h4>dbVersion</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> dbVersion</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="org.activiti.engine.JobNotFoundException">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/JobNotFoundException.html" title="class in org.activiti.engine">org.activiti.engine.JobNotFoundException</a> extends <a href="org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>jobId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> jobId</pre>
<div class="block">the id of the job</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="Package">Package&nbsp;org.activiti.engine.delegate</h2>
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine.delegate.BpmnError">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/delegate/BpmnError.html" title="class in org.activiti.engine.delegate">org.activiti.engine.delegate.BpmnError</a> extends <a href="org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>errorCode</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> errorCode</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="Package">Package&nbsp;org.activiti.engine.form</h2>
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine.form.AbstractFormType">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/form/AbstractFormType.html" title="class in org.activiti.engine.form">org.activiti.engine.form.AbstractFormType</a> extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="Package">Package&nbsp;org.activiti.engine.identity</h2>
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine.identity.Picture">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/identity/Picture.html" title="class in org.activiti.engine.identity">org.activiti.engine.identity.Picture</a> extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>2384375526314443322L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>bytes</h4>
<pre>byte[] bytes</pre>
</li>
<li class="blockListLast">
<h4>mimeType</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mimeType</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="Package">Package&nbsp;org.activiti.engine.query</h2>
</li>
<li class="blockList">
<h2 title="Package">Package&nbsp;org.activiti.engine.repository</h2>
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine.repository.DiagramEdge">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/repository/DiagramEdge.html" title="class in org.activiti.engine.repository">org.activiti.engine.repository.DiagramEdge</a> extends <a href="org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">DiagramElement</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>waypoints</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">E</a>&gt; waypoints</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="org.activiti.engine.repository.DiagramEdgeWaypoint">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/repository/DiagramEdgeWaypoint.html" title="class in org.activiti.engine.repository">org.activiti.engine.repository.DiagramEdgeWaypoint</a> extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>x</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> x</pre>
</li>
<li class="blockListLast">
<h4>y</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> y</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="org.activiti.engine.repository.DiagramElement">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">org.activiti.engine.repository.DiagramElement</a> extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>id</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> id</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="org.activiti.engine.repository.DiagramLayout">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/repository/DiagramLayout.html" title="class in org.activiti.engine.repository">org.activiti.engine.repository.DiagramLayout</a> extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>elements</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">K</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">V</a>&gt; elements</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="org.activiti.engine.repository.DiagramNode">
<!--   -->
</a>
<h3>Class <a href="org/activiti/engine/repository/DiagramNode.html" title="class in org.activiti.engine.repository">org.activiti.engine.repository.DiagramNode</a> extends <a href="org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">DiagramElement</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>x</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> x</pre>
</li>
<li class="blockList">
<h4>y</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> y</pre>
</li>
<li class="blockList">
<h4>width</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> width</pre>
</li>
<li class="blockListLast">
<h4>height</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> height</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="Package">Package&nbsp;org.activiti.engine.task</h2>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">Frames</a></li>
<li><a href="serialized-form.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
