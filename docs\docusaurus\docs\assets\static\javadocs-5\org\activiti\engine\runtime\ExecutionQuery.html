<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ExecutionQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ExecutionQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":38,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ExecutionQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/runtime/Job.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/ExecutionQuery.html" target="_top">Frames</a></li>
<li><a href="ExecutionQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.runtime</div>
<h2 title="Interface ExecutionQuery" class="title">Interface ExecutionQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>,<a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ExecutionQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>,<a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a>&gt;</pre>
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime"><code>Execution</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez, Frederik Heremans</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#activityId-java.lang.String-">activityId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId)</code>
<div class="block">Only select executions which contain an activity with the given id.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#executionId-java.lang.String-">executionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Only select executions with the given id.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#executionTenantId-java.lang.String-">executionTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select process instances that have the given tenant id.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#executionTenantIdLike-java.lang.String-">executionTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select process instances with a tenant id like the given one.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#executionWithoutTenantId--">executionWithoutTenantId</a></span>()</code>
<div class="block">Only select process instances that do not have a tenant id.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#locale-java.lang.String-">locale</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale)</code>
<div class="block">Localize execution name and description to specified locale.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#messageEventSubscriptionName-java.lang.String-">messageEventSubscriptionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</code>
<div class="block">Only select executions which have a message event subscription 
 for the given messageName.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#orderByProcessDefinitionId--">orderByProcessDefinitionId</a></span>()</code>
<div class="block">Order by process definition id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#orderByProcessDefinitionKey--">orderByProcessDefinitionKey</a></span>()</code>
<div class="block">Order by process definition key (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>
<div class="block">Order by id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#parentId-java.lang.String-">parentId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentId)</code>
<div class="block">Only select executions which are a direct child-execution of the execution with the given id.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processDefinitionCategory-java.lang.String-">processDefinitionCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategory)</code>
<div class="block">Only select executions which have the given process definition category.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Only select executions which have the given process definition id.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Only select executions which have the given process definition key.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processDefinitionKeys-java.util.Set-">processDefinitionKeys</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionKeys)</code>
<div class="block">Only select executions which have process definitions with the given keys.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processDefinitionName-java.lang.String-">processDefinitionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</code>
<div class="block">Only select executions which have the given process definition name.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processDefinitionVersion-java.lang.Integer-">processDefinitionVersion</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select executions which have the given process definition version.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processInstanceBusinessKey-java.lang.String-">processInstanceBusinessKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey)</code>
<div class="block">Only executions with the given business key.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processInstanceBusinessKey-java.lang.String-boolean-">processInstanceBusinessKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey,
                          boolean&nbsp;includeChildExecutions)</code>
<div class="block">Only executions with the given business key.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Only select executions which have the given process instance id.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processVariableValueEquals-java.lang.Object-">processVariableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Only select executions which are part of a process that have at least one variable
 with the given value.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processVariableValueEquals-java.lang.String-java.lang.Object-">processVariableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Only select executions which are part of a process that have a variable
 with the given name set to the given value.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">processVariableValueEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select executions which are part of a process that have a local string variable with 
 the given value, case insensitive.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processVariableValueLike-java.lang.String-java.lang.String-">processVariableValueLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select executions which are part of a process that have at least one variable like the given value.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">processVariableValueLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select executions which are part of a process that have at least one variable like the given value (case insensitive).</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processVariableValueNotEquals-java.lang.String-java.lang.Object-">processVariableValueNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Only select executions which are part of a process that have a variable  with the given name, but
 with a different value than the passed value.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">processVariableValueNotEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select executions which are part of a process that have a local string variable which is not 
 the given value, case insensitive.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#signalEventSubscription-java.lang.String-">signalEventSubscription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#signalEventSubscriptionName-java.lang.String-">signalEventSubscriptionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName)</code>
<div class="block">Only select executions which have a signal event subscription 
 for the given signal name.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueEquals-java.lang.Object-">variableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select executions which have at least one local variable with the given value.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueEquals-java.lang.String-java.lang.Object-">variableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local variable with the given value.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">variableValueEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local string variable with the given value, 
 case insensitive.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueGreaterThan-java.lang.String-java.lang.Object-">variableValueGreaterThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local variable value greater than the passed value.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">variableValueGreaterThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local variable value greater than or equal to
 the passed value.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueLessThan-java.lang.String-java.lang.Object-">variableValueLessThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local variable value less than the passed value.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueLessThanOrEqual-java.lang.String-java.lang.Object-">variableValueLessThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local variable value less than or equal to the passed value.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueLike-java.lang.String-java.lang.String-">variableValueLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local variable value like the given value.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueLikeIgnoreCase-java.lang.String-java.lang.String-">variableValueLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local variable value like the given value (case insensitive).</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueNotEquals-java.lang.String-java.lang.Object-">variableValueNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local variable with the given name, but
 with a different value than the passed value.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#variableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">variableValueNotEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select executions which have a local string variable which is not the given value, 
 case insensitive.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#withLocalizationFallback--">withLocalizationFallback</a></span>()</code>
<div class="block">Instruct localization to fallback to more general locales including the default locale of the JVM if the specified locale is not found.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="processDefinitionKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processDefinitionKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</pre>
<div class="block">Only select executions which have the given process definition key.</div>
</li>
</ul>
<a name="processDefinitionKeys-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKeys</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processDefinitionKeys(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionKeys)</pre>
<div class="block">Only select executions which have process definitions with the given keys.</div>
</li>
</ul>
<a name="processDefinitionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processDefinitionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Only select executions which have the given process definition id.</div>
</li>
</ul>
<a name="processDefinitionCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionCategory</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processDefinitionCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategory)</pre>
<div class="block">Only select executions which have the given process definition category.</div>
</li>
</ul>
<a name="processDefinitionName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionName</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processDefinitionName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</pre>
<div class="block">Only select executions which have the given process definition name.</div>
</li>
</ul>
<a name="processDefinitionVersion-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionVersion</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processDefinitionVersion(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</pre>
<div class="block">Only select executions which have the given process definition version.
 Particulary useful when used in combination with <a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processDefinitionKey-java.lang.String-"><code>processDefinitionKey(String)</code></a></div>
</li>
</ul>
<a name="processInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Only select executions which have the given process instance id.</div>
</li>
</ul>
<a name="processInstanceBusinessKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceBusinessKey</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processInstanceBusinessKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey)</pre>
<div class="block">Only executions with the given business key.
 
 Note that only process instances have a business key and as such, child executions
 will NOT be returned. If you want to return child executions of the process instance with
 the given business key too, use the <a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processInstanceBusinessKey-java.lang.String-boolean-"><code>processInstanceBusinessKey(String, boolean)</code></a> method
 with a boolean value of <i>true</i> instead.</div>
</li>
</ul>
<a name="processInstanceBusinessKey-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceBusinessKey</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processInstanceBusinessKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey,
                                          boolean&nbsp;includeChildExecutions)</pre>
<div class="block">Only executions with the given business key. 
 Similar to <a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#processInstanceBusinessKey-java.lang.String-"><code>processInstanceBusinessKey(String)</code></a>, but allows to choose
 whether child executions are returned or not.</div>
</li>
</ul>
<a name="executionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;executionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Only select executions with the given id.</div>
</li>
</ul>
<a name="activityId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;activityId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId)</pre>
<div class="block">Only select executions which contain an activity with the given id.</div>
</li>
</ul>
<a name="parentId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parentId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;parentId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentId)</pre>
<div class="block">Only select executions which are a direct child-execution of the execution with the given id.</div>
</li>
</ul>
<a name="executionTenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionTenantId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;executionTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Only select process instances that have the given tenant id.</div>
</li>
</ul>
<a name="executionTenantIdLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionTenantIdLike</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;executionTenantIdLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</pre>
<div class="block">Only select process instances with a tenant id like the given one.</div>
</li>
</ul>
<a name="executionWithoutTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionWithoutTenantId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;executionWithoutTenantId()</pre>
<div class="block">Only select process instances that do not have a tenant id.</div>
</li>
</ul>
<a name="variableValueEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local variable with the given value. The type
 of variable is determined based on the value, using types configured in 
 <code>ProcessEngineConfiguration#getVariableTypes()</code>. 
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - name of the variable, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueEqualsIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueEqualsIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local string variable with the given value, 
 case insensitive.
 <p>
 This method only works if your database has encoding/collation that supports case-sensitive
 queries. For example, use "collate UTF-8" on MySQL and for MSSQL, select one of the case-sensitive Collations 
 available (<a href="http://msdn.microsoft.com/en-us/library/ms144250(v=sql.105).aspx">MSDN Server Collation Reference</a>).
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - name of the variable, cannot be null.</dd>
<dd><code>value</code> - value of the variable, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueEquals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select executions which have at least one local variable with the given value. The type
 of variable is determined based on the value, using types configured in 
 <code>ProcessEngineConfiguration#getVariableTypes()</code>. 
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
</li>
</ul>
<a name="variableValueNotEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueNotEquals</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueNotEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local variable with the given name, but
 with a different value than the passed value.
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - name of the variable, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueNotEqualsIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueNotEqualsIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local string variable which is not the given value, 
 case insensitive.
 <p>
 This method only works if your database has encoding/collation that supports case-sensitive
 queries. For example, use "collate UTF-8" on MySQL and for MSSQL, select one of the case-sensitive Collations 
 available (<a href="http://msdn.microsoft.com/en-us/library/ms144250(v=sql.105).aspx">MSDN Server Collation Reference</a>).
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - name of the variable, cannot be null.</dd>
<dd><code>value</code> - value of the variable, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueGreaterThan-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueGreaterThan</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueGreaterThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local variable value greater than the passed value.
 Booleans, Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - variable name, cannot be null.</dd>
<dd><code>value</code> - variable value, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueGreaterThanOrEqual</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueGreaterThanOrEqual(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local variable value greater than or equal to
 the passed value. Booleans, Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which 
 are not primitive type wrappers) are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - variable name, cannot be null.</dd>
<dd><code>value</code> - variable value, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueLessThan-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLessThan</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueLessThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local variable value less than the passed value.
 Booleans, Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - variable name, cannot be null.</dd>
<dd><code>value</code> - variable value, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueLessThanOrEqual-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLessThanOrEqual</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueLessThanOrEqual(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local variable value less than or equal to the passed value.
 Booleans, Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - variable name, cannot be null.</dd>
<dd><code>value</code> - variable value, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueLike-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLike</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local variable value like the given value.
 This be used on string variables only.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - variable name, cannot be null.</dd>
<dd><code>value</code> - variable value, cannot be null. The string can include the
 wildcard character '%' to express like-strategy: 
 starts with (string%), ends with (%string) or contains (%string%).</dd>
</dl>
</li>
</ul>
<a name="variableValueLikeIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;variableValueLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select executions which have a local variable value like the given value (case insensitive).
 This be used on string variables only.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - variable name, cannot be null.</dd>
<dd><code>value</code> - variable value, cannot be null. The string can include the
 wildcard character '%' to express like-strategy: 
 starts with (string%), ends with (%string) or contains (%string%).</dd>
</dl>
</li>
</ul>
<a name="processVariableValueEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processVariableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Only select executions which are part of a process that have a variable
 with the given name set to the given value.
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
</li>
</ul>
<a name="processVariableValueEquals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processVariableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Only select executions which are part of a process that have at least one variable
 with the given value.
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
</li>
</ul>
<a name="processVariableValueNotEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueNotEquals</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processVariableValueNotEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Only select executions which are part of a process that have a variable  with the given name, but
 with a different value than the passed value.
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
</li>
</ul>
<a name="processVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueEqualsIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processVariableValueEqualsIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select executions which are part of a process that have a local string variable with 
 the given value, case insensitive.
 <p>
 This method only works if your database has encoding/collation that supports case-sensitive
 queries. For example, use "collate UTF-8" on MySQL and for MSSQL, select one of the case-sensitive Collations 
 available (<a href="http://msdn.microsoft.com/en-us/library/ms144250(v=sql.105).aspx">MSDN Server Collation Reference</a>).
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - name of the variable, cannot be null.</dd>
<dd><code>value</code> - value of the variable, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="processVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueNotEqualsIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processVariableValueNotEqualsIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select executions which are part of a process that have a local string variable which is not 
 the given value, case insensitive.
 <p>
 This method only works if your database has encoding/collation that supports case-sensitive
 queries. For example, use "collate UTF-8" on MySQL and for MSSQL, select one of the case-sensitive Collations 
 available (<a href="http://msdn.microsoft.com/en-us/library/ms144250(v=sql.105).aspx">MSDN Server Collation Reference</a>).
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - name of the variable, cannot be null.</dd>
<dd><code>value</code> - value of the variable, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="processVariableValueLike-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueLike</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processVariableValueLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select executions which are part of a process that have at least one variable like the given value.
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are not supported.</div>
</li>
</ul>
<a name="processVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;processVariableValueLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select executions which are part of a process that have at least one variable like the given value (case insensitive).
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are not supported.</div>
</li>
</ul>
<a name="signalEventSubscription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventSubscription</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
<a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;signalEventSubscription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html#signalEventSubscriptionName-java.lang.String-"><code>signalEventSubscriptionName(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="signalEventSubscriptionName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventSubscriptionName</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;signalEventSubscriptionName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName)</pre>
<div class="block">Only select executions which have a signal event subscription 
 for the given signal name.
 
 (The signalName is specified using the 'name' attribute of the signal element 
 in the BPMN 2.0 XML.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>signalName</code> - the name of the signal the execution has subscribed to</dd>
</dl>
</li>
</ul>
<a name="messageEventSubscriptionName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>messageEventSubscriptionName</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;messageEventSubscriptionName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</pre>
<div class="block">Only select executions which have a message event subscription 
 for the given messageName. 
 
 (The messageName is specified using the 'name' attribute of the message element 
 in the BPMN 2.0 XML.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>messageName</code> - the name of the message the execution has subscribed to</dd>
</dl>
</li>
</ul>
<a name="locale-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>locale</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;locale(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale)</pre>
<div class="block">Localize execution name and description to specified locale.</div>
</li>
</ul>
<a name="withLocalizationFallback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withLocalizationFallback</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;withLocalizationFallback()</pre>
<div class="block">Instruct localization to fallback to more general locales including the default locale of the JVM if the specified locale is not found.</div>
</li>
</ul>
<a name="orderByProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;orderByProcessInstanceId()</pre>
<div class="block">Order by id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessDefinitionKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;orderByProcessDefinitionKey()</pre>
<div class="block">Order by process definition key (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessDefinitionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;orderByProcessDefinitionId()</pre>
<div class="block">Order by process definition id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTenantId--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByTenantId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;orderByTenantId()</pre>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ExecutionQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/runtime/Job.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/ExecutionQuery.html" target="_top">Frames</a></li>
<li><a href="ExecutionQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
