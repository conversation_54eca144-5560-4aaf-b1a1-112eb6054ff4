<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DelegateTask (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DelegateTask (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DelegateTask.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html" title="class in org.activiti.engine.delegate"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/delegate/ExecutionListener.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/delegate/DelegateTask.html" target="_top">Frames</a></li>
<li><a href="DelegateTask.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate</div>
<h2 title="Interface DelegateTask" class="title">Interface DelegateTask</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/delegate/VariableScope.html" title="interface in org.activiti.engine.delegate">VariableScope</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">DelegateTask</span>
extends <a href="../../../../org/activiti/engine/delegate/VariableScope.html" title="interface in org.activiti.engine.delegate">VariableScope</a></pre>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#addCandidateGroup-java.lang.String-">addCandidateGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Adds the given group as candidate group to this task</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#addCandidateGroups-java.util.Collection-">addCandidateGroups</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateGroups)</code>
<div class="block">Adds multiple groups as candidate group to this task.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#addCandidateUser-java.lang.String-">addCandidateUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Adds the given user as a candidate user to this task.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#addCandidateUsers-java.util.Collection-">addCandidateUsers</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateUsers)</code>
<div class="block">Adds multiple users as candidate user to this task.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#addGroupIdentityLink-java.lang.String-java.lang.String-">addGroupIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Involves a group with group task.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#addUserIdentityLink-java.lang.String-java.lang.String-">addUserIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Involves a user with a task.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#deleteCandidateGroup-java.lang.String-">deleteCandidateGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Convenience shorthand for <code>#deleteGroupIdentityLink(String, String, String)</code>; with type <a href="../../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#deleteCandidateUser-java.lang.String-">deleteCandidateUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Convenience shorthand for <a href="../../../../org/activiti/engine/delegate/DelegateTask.html#deleteUserIdentityLink-java.lang.String-java.lang.String-"><code>deleteUserIdentityLink(String, String)</code></a>; with type <a href="../../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#deleteGroupIdentityLink-java.lang.String-java.lang.String-">deleteGroupIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Removes the association between a group and a task for the given identityLinkType.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#deleteUserIdentityLink-java.lang.String-java.lang.String-">deleteUserIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Removes the association between a user and a task for the given identityLinkType.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getAssignee--">getAssignee</a></span>()</code>
<div class="block">The <code>userId</code> of the person to which this task is delegated.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task">IdentityLink</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getCandidates--">getCandidates</a></span>()</code>
<div class="block">Retrieves the candidate users and groups associated with the task.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getCategory--">getCategory</a></span>()</code>
<div class="block">The category of the task.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getCreateTime--">getCreateTime</a></span>()</code>
<div class="block">The date/time when this task was created</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getDelegationState--">getDelegationState</a></span>()</code>
<div class="block">The current <a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a> for this task.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getDescription--">getDescription</a></span>()</code>
<div class="block">Free text description of the task.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getDueDate--">getDueDate</a></span>()</code>
<div class="block">Due date of the task.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getEventName--">getEventName</a></span>()</code>
<div class="block">Returns the event name which triggered the task listener to fire for this task.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getExecution--">getExecution</a></span>()</code>
<div class="block">Returns the execution currently at the task.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getExecutionId--">getExecutionId</a></span>()</code>
<div class="block">Reference to the path of execution or null if it is not related to a process instance.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getFormKey--">getFormKey</a></span>()</code>
<div class="block">The form key for the user task</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getId--">getId</a></span>()</code>
<div class="block">DB id of the task.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getName--">getName</a></span>()</code>
<div class="block">Name or title of the task.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getOwner--">getOwner</a></span>()</code>
<div class="block">The <code>userId</code> of the person responsible for this task.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getPriority--">getPriority</a></span>()</code>
<div class="block">indication of how important/urgent this task is with a number between 
 0 and 100 where higher values mean a higher priority and lower values mean 
 lower priority: [0..19] lowest, [20..39] low, [40..59] normal, [60..79] high 
 [80..100] highest</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getProcessDefinitionId--">getProcessDefinitionId</a></span>()</code>
<div class="block">Reference to the process definition or null if it is not related to a process.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getProcessInstanceId--">getProcessInstanceId</a></span>()</code>
<div class="block">Reference to the process instance or null if it is not related to a process instance.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getTaskDefinitionKey--">getTaskDefinitionKey</a></span>()</code>
<div class="block">The id of the activity in the process defining this task or null if this is not related to a process</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#getTenantId--">getTenantId</a></span>()</code>
<div class="block">The tenant identifier of this task</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#isSuspended--">isSuspended</a></span>()</code>
<div class="block">Indicated whether this task is suspended or not.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#setAssignee-java.lang.String-">setAssignee</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee)</code>
<div class="block">The <code>userId</code> of the person to which this task is delegated.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#setCategory-java.lang.String-">setCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</code>
<div class="block">Change the category of the task.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#setDescription-java.lang.String-">setDescription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</code>
<div class="block">Change the description of the task</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#setDueDate-java.util.Date-">setDueDate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>
<div class="block">Change due date of the task.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#setFormKey-java.lang.String-">setFormKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;formKey)</code>
<div class="block">Change the form key of the task</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#setName-java.lang.String-">setName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Change the name of the task.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#setOwner-java.lang.String-">setOwner</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner)</code>
<div class="block">The <code>userId</code> of the person responsible for this task.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateTask.html#setPriority-int-">setPriority</a></span>(int&nbsp;priority)</code>
<div class="block">indication of how important/urgent this task is with a number between 
 0 and 100 where higher values mean a higher priority and lower values mean 
 lower priority: [0..19] lowest, [20..39] low, [40..59] normal, [60..79] high 
 [80..100] highest</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.delegate.VariableScope">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.delegate.<a href="../../../../org/activiti/engine/delegate/VariableScope.html" title="interface in org.activiti.engine.delegate">VariableScope</a></h3>
<code><a href="../../../../org/activiti/engine/delegate/VariableScope.html#createVariableLocal-java.lang.String-java.lang.Object-">createVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariable-java.lang.String-">getTransientVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariableLocal-java.lang.String-">getTransientVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariables--">getTransientVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariablesLocal--">getTransientVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-">getVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-boolean-">getVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-java.lang.Class-">getVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstance-java.lang.String-">getVariableInstance</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstance-java.lang.String-boolean-">getVariableInstance</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstanceLocal-java.lang.String-">getVariableInstanceLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstanceLocal-java.lang.String-boolean-">getVariableInstanceLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances--">getVariableInstances</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-">getVariableInstances</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-boolean-">getVariableInstances</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstancesLocal--">getVariableInstancesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstancesLocal-java.util.Collection-">getVariableInstancesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstancesLocal-java.util.Collection-boolean-">getVariableInstancesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-">getVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-boolean-">getVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-java.lang.Class-">getVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableNames--">getVariableNames</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableNamesLocal--">getVariableNamesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables--">getVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-">getVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-boolean-">getVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariablesLocal--">getVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariablesLocal-java.util.Collection-">getVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariablesLocal-java.util.Collection-boolean-">getVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariable-java.lang.String-">hasVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariableLocal-java.lang.String-">hasVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariables--">hasVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariablesLocal--">hasVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariable-java.lang.String-">removeTransientVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariableLocal-java.lang.String-">removeTransientVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariables--">removeTransientVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariablesLocal--">removeTransientVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariable-java.lang.String-">removeVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariableLocal-java.lang.String-">removeVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariables--">removeVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariables-java.util.Collection-">removeVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariablesLocal--">removeVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariablesLocal-java.util.Collection-">removeVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-">setTransientVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariableLocal-java.lang.String-java.lang.Object-">setTransientVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariables-java.util.Map-">setTransientVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariablesLocal-java.util.Map-">setTransientVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-">setVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-boolean-">setVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-">setVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-boolean-">setVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-">setVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariablesLocal-java.util.Map-">setVariablesLocal</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getId()</pre>
<div class="block">DB id of the task.</div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Name or title of the task.</div>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>void&nbsp;setName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Change the name of the task.</div>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDescription()</pre>
<div class="block">Free text description of the task.</div>
</li>
</ul>
<a name="setDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescription</h4>
<pre>void&nbsp;setDescription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</pre>
<div class="block">Change the description of the task</div>
</li>
</ul>
<a name="getPriority--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriority</h4>
<pre>int&nbsp;getPriority()</pre>
<div class="block">indication of how important/urgent this task is with a number between 
 0 and 100 where higher values mean a higher priority and lower values mean 
 lower priority: [0..19] lowest, [20..39] low, [40..59] normal, [60..79] high 
 [80..100] highest</div>
</li>
</ul>
<a name="setPriority-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriority</h4>
<pre>void&nbsp;setPriority(int&nbsp;priority)</pre>
<div class="block">indication of how important/urgent this task is with a number between 
 0 and 100 where higher values mean a higher priority and lower values mean 
 lower priority: [0..19] lowest, [20..39] low, [40..59] normal, [60..79] high 
 [80..100] highest</div>
</li>
</ul>
<a name="getProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessInstanceId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessInstanceId()</pre>
<div class="block">Reference to the process instance or null if it is not related to a process instance.</div>
</li>
</ul>
<a name="getExecutionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExecutionId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getExecutionId()</pre>
<div class="block">Reference to the path of execution or null if it is not related to a process instance.</div>
</li>
</ul>
<a name="getProcessDefinitionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDefinitionId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessDefinitionId()</pre>
<div class="block">Reference to the process definition or null if it is not related to a process.</div>
</li>
</ul>
<a name="getCreateTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreateTime</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;getCreateTime()</pre>
<div class="block">The date/time when this task was created</div>
</li>
</ul>
<a name="getTaskDefinitionKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskDefinitionKey</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTaskDefinitionKey()</pre>
<div class="block">The id of the activity in the process defining this task or null if this is not related to a process</div>
</li>
</ul>
<a name="isSuspended--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSuspended</h4>
<pre>boolean&nbsp;isSuspended()</pre>
<div class="block">Indicated whether this task is suspended or not.</div>
</li>
</ul>
<a name="getTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTenantId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTenantId()</pre>
<div class="block">The tenant identifier of this task</div>
</li>
</ul>
<a name="getFormKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFormKey</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFormKey()</pre>
<div class="block">The form key for the user task</div>
</li>
</ul>
<a name="setFormKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFormKey</h4>
<pre>void&nbsp;setFormKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;formKey)</pre>
<div class="block">Change the form key of the task</div>
</li>
</ul>
<a name="getExecution--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExecution</h4>
<pre><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;getExecution()</pre>
<div class="block">Returns the execution currently at the task.</div>
</li>
</ul>
<a name="getEventName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEventName</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEventName()</pre>
<div class="block">Returns the event name which triggered the task listener to fire for this task.</div>
</li>
</ul>
<a name="getDelegationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDelegationState</h4>
<pre><a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a>&nbsp;getDelegationState()</pre>
<div class="block">The current <a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a> for this task.</div>
</li>
</ul>
<a name="addCandidateUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCandidateUser</h4>
<pre>void&nbsp;addCandidateUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Adds the given user as a candidate user to this task.</div>
</li>
</ul>
<a name="addCandidateUsers-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCandidateUsers</h4>
<pre>void&nbsp;addCandidateUsers(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateUsers)</pre>
<div class="block">Adds multiple users as candidate user to this task.</div>
</li>
</ul>
<a name="addCandidateGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCandidateGroup</h4>
<pre>void&nbsp;addCandidateGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Adds the given group as candidate group to this task</div>
</li>
</ul>
<a name="addCandidateGroups-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCandidateGroups</h4>
<pre>void&nbsp;addCandidateGroups(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateGroups)</pre>
<div class="block">Adds multiple groups as candidate group to this task.</div>
</li>
</ul>
<a name="getOwner--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOwner</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getOwner()</pre>
<div class="block">The <code>userId</code> of the person responsible for this task.</div>
</li>
</ul>
<a name="setOwner-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOwner</h4>
<pre>void&nbsp;setOwner(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner)</pre>
<div class="block">The <code>userId</code> of the person responsible for this task.</div>
</li>
</ul>
<a name="getAssignee--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAssignee</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getAssignee()</pre>
<div class="block">The <code>userId</code> of the person to which this task is delegated.</div>
</li>
</ul>
<a name="setAssignee-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAssignee</h4>
<pre>void&nbsp;setAssignee(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee)</pre>
<div class="block">The <code>userId</code> of the person to which this task is delegated.</div>
</li>
</ul>
<a name="getDueDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDueDate</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;getDueDate()</pre>
<div class="block">Due date of the task.</div>
</li>
</ul>
<a name="setDueDate-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDueDate</h4>
<pre>void&nbsp;setDueDate(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</pre>
<div class="block">Change due date of the task.</div>
</li>
</ul>
<a name="getCategory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategory</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCategory()</pre>
<div class="block">The category of the task. This is an optional field and allows to 'tag' tasks as belonging to a certain category.</div>
</li>
</ul>
<a name="setCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCategory</h4>
<pre>void&nbsp;setCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</pre>
<div class="block">Change the category of the task. This is an optional field and allows to 'tag' tasks as belonging to a certain category.</div>
</li>
</ul>
<a name="addUserIdentityLink-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addUserIdentityLink</h4>
<pre>void&nbsp;addUserIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Involves a user with a task. The type of identity link is defined by the given identityLinkType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>userId</code> - id of the user involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identityLink, cannot be null (@see <a href="../../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="addGroupIdentityLink-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGroupIdentityLink</h4>
<pre>void&nbsp;addGroupIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Involves a group with group task. The type of identityLink is defined by the given identityLink.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>groupId</code> - id of the group to involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identity, cannot be null (@see <a href="../../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteCandidateUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteCandidateUser</h4>
<pre>void&nbsp;deleteCandidateUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Convenience shorthand for <a href="../../../../org/activiti/engine/delegate/DelegateTask.html#deleteUserIdentityLink-java.lang.String-java.lang.String-"><code>deleteUserIdentityLink(String, String)</code></a>; with type <a href="../../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>userId</code> - id of the user to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteCandidateGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteCandidateGroup</h4>
<pre>void&nbsp;deleteCandidateGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Convenience shorthand for <code>#deleteGroupIdentityLink(String, String, String)</code>; with type <a href="../../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>groupId</code> - id of the group to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteUserIdentityLink-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteUserIdentityLink</h4>
<pre>void&nbsp;deleteUserIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Removes the association between a user and a task for the given identityLinkType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>userId</code> - id of the user involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identityLink, cannot be null (@see <a href="../../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteGroupIdentityLink-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteGroupIdentityLink</h4>
<pre>void&nbsp;deleteGroupIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Removes the association between a group and a task for the given identityLinkType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>groupId</code> - id of the group to involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identity, cannot be null (@see <a href="../../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="getCandidates--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCandidates</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task">IdentityLink</a>&gt;&nbsp;getCandidates()</pre>
<div class="block">Retrieves the candidate users and groups associated with the task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>set of <a href="../../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a>s of type <a href="../../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a>.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DelegateTask.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html" title="class in org.activiti.engine.delegate"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/delegate/ExecutionListener.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/delegate/DelegateTask.html" target="_top">Frames</a></li>
<li><a href="DelegateTask.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
