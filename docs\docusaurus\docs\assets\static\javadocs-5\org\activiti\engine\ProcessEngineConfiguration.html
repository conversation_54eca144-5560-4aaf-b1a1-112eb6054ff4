<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProcessEngineConfiguration (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProcessEngineConfiguration (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessEngineConfiguration.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/ProcessEngineConfiguration.html" target="_top">Frames</a></li>
<li><a href="ProcessEngineConfiguration.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Class ProcessEngineConfiguration" class="title">Class ProcessEngineConfiguration</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.ProcessEngineConfiguration</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine">EngineServices</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">ProcessEngineConfiguration</span>
extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine">EngineServices</a></pre>
<div class="block">Configuration information from which a process engine can be build.
 
 <p>Most common is to create a process engine based on the default configuration file:
 <pre>ProcessEngine processEngine = ProcessEngineConfiguration
   .createProcessEngineConfigurationFromResourceDefault()
   .buildProcessEngine();
 </pre>
 </p>
 
 <p>To create a process engine programatic, without a configuration file, 
 the first option is <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#createStandaloneProcessEngineConfiguration--"><code>createStandaloneProcessEngineConfiguration()</code></a>
 <pre>ProcessEngine processEngine = ProcessEngineConfiguration
   .createStandaloneProcessEngineConfiguration()
   .buildProcessEngine();
 </pre>
 This creates a new process engine with all the defaults to connect to 
 a remote h2 database (jdbc:h2:tcp://localhost/activiti) in standalone 
 mode.  Standalone mode means that Activiti will manage the transactions 
 on the JDBC connections that it creates.  One transaction per 
 service method.
 For a description of how to write the configuration files, see the 
 userguide.
 </p>
 
 <p>The second option is great for testing: <code>#createStandalonInMemeProcessEngineConfiguration()</code>
 <pre>ProcessEngine processEngine = ProcessEngineConfiguration
   .createStandaloneInMemProcessEngineConfiguration()
   .buildProcessEngine();
 </pre>
 This creates a new process engine with all the defaults to connect to 
 an memory h2 database (jdbc:h2:tcp://localhost/activiti) in standalone 
 mode.  The DB schema strategy default is in this case <code>create-drop</code>.  
 Standalone mode means that Activiti will manage the transactions 
 on the JDBC connections that it creates.  One transaction per 
 service method.
 </p>
 
 <p>On all forms of creating a process engine, you can first customize the configuration 
 before calling the <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#buildProcessEngine--"><code>buildProcessEngine()</code></a> method by calling any of the 
 setters like this:
 <pre>ProcessEngine processEngine = ProcessEngineConfiguration
   .createProcessEngineConfigurationFromResourceDefault()
   .setMailServerHost("gmail.com")
   .setJdbcUsername("mickey")
   .setJdbcPassword("mouse")
   .buildProcessEngine();
 </pre>
 </p></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../org/activiti/engine/ProcessEngines.html" title="class in org.activiti.engine"><code>ProcessEngines</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#activityFontName">activityFontName</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#annotationFontName">annotationFontName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected org.activiti.engine.impl.asyncexecutor.AsyncExecutor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#asyncExecutor">asyncExecutor</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#asyncExecutorActivate">asyncExecutorActivate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#asyncExecutorEnabled">asyncExecutorEnabled</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#asyncFailedJobWaitTime">asyncFailedJobWaitTime</a></span></code>
<div class="block">define the default wait time for a failed async job in seconds</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/ClassLoader.html?is-external=true" title="class or interface in java.lang">ClassLoader</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#classLoader">classLoader</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/activiti/engine/runtime/Clock.html" title="interface in org.activiti.engine.runtime">Clock</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#clock">clock</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#databaseCatalog">databaseCatalog</a></span></code>
<div class="block">database catalog to use</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#databaseSchema">databaseSchema</a></span></code>
<div class="block">In some situations you want to set the schema to use for table checks / generation if the database metadata
 doesn't return that correctly, see https://activiti.atlassian.net/browse/ACT-1220,
 https://activiti.atlassian.net/browse/ACT-1062</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#databaseSchemaUpdate">databaseSchemaUpdate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#databaseTablePrefix">databaseTablePrefix</a></span></code>
<div class="block">Allows configuring a database table prefix which is used for all runtime operations of the process engine.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#databaseType">databaseType</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#databaseWildcardEscapeCharacter">databaseWildcardEscapeCharacter</a></span></code>
<div class="block">Escape character for doing wildcard searches.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/javax/sql/DataSource.html?is-external=true" title="class or interface in javax.sql">DataSource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#dataSource">dataSource</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#dataSourceJndiName">dataSourceJndiName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#DB_SCHEMA_UPDATE_CREATE_DROP">DB_SCHEMA_UPDATE_CREATE_DROP</a></span></code>
<div class="block">Creates the schema when the process engine is being created and 
 drops the schema when the process engine is being closed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#DB_SCHEMA_UPDATE_FALSE">DB_SCHEMA_UPDATE_FALSE</a></span></code>
<div class="block">Checks the version of the DB schema against the library when 
 the process engine is being created and throws an exception
 if the versions don't match.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#DB_SCHEMA_UPDATE_TRUE">DB_SCHEMA_UPDATE_TRUE</a></span></code>
<div class="block">Upon building of the process engine, a check is performed and 
 an update of the schema is performed if it is necessary.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#defaultCamelContext">defaultCamelContext</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#defaultFailedJobWaitTime">defaultFailedJobWaitTime</a></span></code>
<div class="block">define the default wait time for a failed job in seconds</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#enableProcessDefinitionInfoCache">enableProcessDefinitionInfoCache</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#history">history</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected org.activiti.engine.impl.history.HistoryLevel</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#historyLevel">historyLevel</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#idBlockSize">idBlockSize</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isCreateDiagramOnDeploy">isCreateDiagramOnDeploy</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isDbHistoryUsed">isDbHistoryUsed</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isDbIdentityUsed">isDbIdentityUsed</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcDefaultTransactionIsolationLevel">jdbcDefaultTransactionIsolationLevel</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcDriver">jdbcDriver</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcMaxActiveConnections">jdbcMaxActiveConnections</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcMaxCheckoutTime">jdbcMaxCheckoutTime</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcMaxIdleConnections">jdbcMaxIdleConnections</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcMaxWaitTime">jdbcMaxWaitTime</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcPassword">jdbcPassword</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcPingConnectionNotUsedFor">jdbcPingConnectionNotUsedFor</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcPingEnabled">jdbcPingEnabled</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcPingQuery">jdbcPingQuery</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcUrl">jdbcUrl</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jdbcUsername">jdbcUsername</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected org.activiti.engine.impl.jobexecutor.JobExecutor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jobExecutor">jobExecutor</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jobExecutorActivate">jobExecutorActivate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jpaCloseEntityManager">jpaCloseEntityManager</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jpaEntityManagerFactory">jpaEntityManagerFactory</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jpaHandleTransaction">jpaHandleTransaction</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#jpaPersistenceUnitName">jpaPersistenceUnitName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#labelFontName">labelFontName</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#lockTimeAsyncJobWaitTime">lockTimeAsyncJobWaitTime</a></span></code>
<div class="block">Define the default lock time for an async job in seconds.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#mailServerDefaultFrom">mailServerDefaultFrom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#mailServerHost">mailServerHost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#mailServerPassword">mailServerPassword</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#mailServerPort">mailServerPort</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/cfg/MailServerInfo.html" title="class in org.activiti.engine.cfg">MailServerInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#mailServers">mailServers</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#mailServerUsername">mailServerUsername</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#mailSessionJndi">mailSessionJndi</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#mailSessionsJndi">mailSessionsJndi</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#NO_TENANT_ID">NO_TENANT_ID</a></span></code>
<div class="block">The tenant id indicating 'no tenant'</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected org.activiti.image.ProcessDiagramGenerator</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#processDiagramGenerator">processDiagramGenerator</a></span></code>
<div class="block">process diagram generator.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine">ProcessEngineLifecycleListener</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#processEngineLifecycleListener">processEngineLifecycleListener</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#processEngineName">processEngineName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#tablePrefixIsSchema">tablePrefixIsSchema</a></span></code>
<div class="block">Set to true in case the defined databaseTablePrefix is a schema-name, instead of an actual table name
 prefix.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#transactionsExternallyManaged">transactionsExternallyManaged</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#useClassForNameClassLoading">useClassForNameClassLoading</a></span></code>
<div class="block">Either use Class.forName or ClassLoader.loadClass for class loading.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#useSSL">useSSL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#useTLS">useTLS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#xmlEncoding">xmlEncoding</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier</th>
<th class="colLast" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#ProcessEngineConfiguration--">ProcessEngineConfiguration</a></span>()</code>
<div class="block">use one of the static createXxxx methods instead</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>abstract <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#buildProcessEngine--">buildProcessEngine</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromInputStream-java.io.InputStream-">createProcessEngineConfigurationFromInputStream</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromInputStream-java.io.InputStream-java.lang.String-">createProcessEngineConfigurationFromInputStream</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream,
                                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;beanName)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromResource-java.lang.String-">createProcessEngineConfigurationFromResource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resource)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromResource-java.lang.String-java.lang.String-">createProcessEngineConfigurationFromResource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resource,
                                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;beanName)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromResourceDefault--">createProcessEngineConfigurationFromResourceDefault</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#createStandaloneInMemProcessEngineConfiguration--">createStandaloneInMemProcessEngineConfiguration</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#createStandaloneProcessEngineConfiguration--">createStandaloneProcessEngineConfiguration</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getActivityFontName--">getActivityFontName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getAnnotationFontName--">getAnnotationFontName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.asyncexecutor.AsyncExecutor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getAsyncExecutor--">getAsyncExecutor</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getAsyncFailedJobWaitTime--">getAsyncFailedJobWaitTime</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/ClassLoader.html?is-external=true" title="class or interface in java.lang">ClassLoader</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getClassLoader--">getClassLoader</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/Clock.html" title="interface in org.activiti.engine.runtime">Clock</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getClock--">getClock</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDatabaseCatalog--">getDatabaseCatalog</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDatabaseSchema--">getDatabaseSchema</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDatabaseSchemaUpdate--">getDatabaseSchemaUpdate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDatabaseTablePrefix--">getDatabaseTablePrefix</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDatabaseType--">getDatabaseType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDatabaseWildcardEscapeCharacter--">getDatabaseWildcardEscapeCharacter</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/javax/sql/DataSource.html?is-external=true" title="class or interface in javax.sql">DataSource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDataSource--">getDataSource</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDataSourceJndiName--">getDataSourceJndiName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDefaultCamelContext--">getDefaultCamelContext</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getDefaultFailedJobWaitTime--">getDefaultFailedJobWaitTime</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getHistory--">getHistory</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.history.HistoryLevel</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getHistoryLevel--">getHistoryLevel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getIdBlockSize--">getIdBlockSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcDefaultTransactionIsolationLevel--">getJdbcDefaultTransactionIsolationLevel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcDriver--">getJdbcDriver</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcMaxActiveConnections--">getJdbcMaxActiveConnections</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcMaxCheckoutTime--">getJdbcMaxCheckoutTime</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcMaxIdleConnections--">getJdbcMaxIdleConnections</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcMaxWaitTime--">getJdbcMaxWaitTime</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcPassword--">getJdbcPassword</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcPingConnectionNotUsedFor--">getJdbcPingConnectionNotUsedFor</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcPingQuery--">getJdbcPingQuery</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcUrl--">getJdbcUrl</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJdbcUsername--">getJdbcUsername</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.jobexecutor.JobExecutor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJobExecutor--">getJobExecutor</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJpaEntityManagerFactory--">getJpaEntityManagerFactory</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getJpaPersistenceUnitName--">getJpaPersistenceUnitName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getLabelFontName--">getLabelFontName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getLockTimeAsyncJobWaitTime--">getLockTimeAsyncJobWaitTime</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/cfg/MailServerInfo.html" title="class in org.activiti.engine.cfg">MailServerInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailServer-java.lang.String-">getMailServer</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailServerDefaultFrom--">getMailServerDefaultFrom</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailServerHost--">getMailServerHost</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailServerPassword--">getMailServerPassword</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailServerPort--">getMailServerPort</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/cfg/MailServerInfo.html" title="class in org.activiti.engine.cfg">MailServerInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailServers--">getMailServers</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailServerUsername--">getMailServerUsername</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailServerUseSSL--">getMailServerUseSSL</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailServerUseTLS--">getMailServerUseTLS</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailSessionJndi--">getMailSessionJndi</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailSessionJndi-java.lang.String-">getMailSessionJndi</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getMailSessionsJndi--">getMailSessionsJndi</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>org.activiti.image.ProcessDiagramGenerator</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getProcessDiagramGenerator--">getProcessDiagramGenerator</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine">ProcessEngineLifecycleListener</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getProcessEngineLifecycleListener--">getProcessEngineLifecycleListener</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getProcessEngineName--">getProcessEngineName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#getXmlEncoding--">getXmlEncoding</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isAsyncExecutorActivate--">isAsyncExecutorActivate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isAsyncExecutorEnabled--">isAsyncExecutorEnabled</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isCreateDiagramOnDeploy--">isCreateDiagramOnDeploy</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isDbHistoryUsed--">isDbHistoryUsed</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isDbIdentityUsed--">isDbIdentityUsed</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isEnableProcessDefinitionInfoCache--">isEnableProcessDefinitionInfoCache</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isJdbcPingEnabled--">isJdbcPingEnabled</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isJobExecutorActivate--">isJobExecutorActivate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isJpaCloseEntityManager--">isJpaCloseEntityManager</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isJpaHandleTransaction--">isJpaHandleTransaction</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isTablePrefixIsSchema--">isTablePrefixIsSchema</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isTransactionsExternallyManaged--">isTransactionsExternallyManaged</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#isUseClassForNameClassLoading--">isUseClassForNameClassLoading</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setActivityFontName-java.lang.String-">setActivityFontName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityFontName)</code>&nbsp;</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setAnnotationFontName-java.lang.String-">setAnnotationFontName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;annotationFontName)</code>&nbsp;</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setAsyncExecutor-org.activiti.engine.impl.asyncexecutor.AsyncExecutor-">setAsyncExecutor</a></span>(org.activiti.engine.impl.asyncexecutor.AsyncExecutor&nbsp;asyncExecutor)</code>&nbsp;</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setAsyncExecutorActivate-boolean-">setAsyncExecutorActivate</a></span>(boolean&nbsp;asyncExecutorActivate)</code>&nbsp;</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setAsyncExecutorEnabled-boolean-">setAsyncExecutorEnabled</a></span>(boolean&nbsp;asyncExecutorEnabled)</code>&nbsp;</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setAsyncFailedJobWaitTime-int-">setAsyncFailedJobWaitTime</a></span>(int&nbsp;asyncFailedJobWaitTime)</code>&nbsp;</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setClassLoader-java.lang.ClassLoader-">setClassLoader</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/ClassLoader.html?is-external=true" title="class or interface in java.lang">ClassLoader</a>&nbsp;classLoader)</code>&nbsp;</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setClock-org.activiti.engine.runtime.Clock-">setClock</a></span>(<a href="../../../org/activiti/engine/runtime/Clock.html" title="interface in org.activiti.engine.runtime">Clock</a>&nbsp;clock)</code>&nbsp;</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setCreateDiagramOnDeploy-boolean-">setCreateDiagramOnDeploy</a></span>(boolean&nbsp;createDiagramOnDeploy)</code>&nbsp;</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseCatalog-java.lang.String-">setDatabaseCatalog</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseCatalog)</code>&nbsp;</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseSchema-java.lang.String-">setDatabaseSchema</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseSchema)</code>&nbsp;</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseSchemaUpdate-java.lang.String-">setDatabaseSchemaUpdate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseSchemaUpdate)</code>&nbsp;</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseTablePrefix-java.lang.String-">setDatabaseTablePrefix</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseTablePrefix)</code>&nbsp;</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseType-java.lang.String-">setDatabaseType</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseType)</code>&nbsp;</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseWildcardEscapeCharacter-java.lang.String-">setDatabaseWildcardEscapeCharacter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseWildcardEscapeCharacter)</code>&nbsp;</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDataSource-javax.sql.DataSource-">setDataSource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/javax/sql/DataSource.html?is-external=true" title="class or interface in javax.sql">DataSource</a>&nbsp;dataSource)</code>&nbsp;</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDataSourceJndiName-java.lang.String-">setDataSourceJndiName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataSourceJndiName)</code>&nbsp;</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDbHistoryUsed-boolean-">setDbHistoryUsed</a></span>(boolean&nbsp;isDbHistoryUsed)</code>&nbsp;</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDbIdentityUsed-boolean-">setDbIdentityUsed</a></span>(boolean&nbsp;isDbIdentityUsed)</code>&nbsp;</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDefaultCamelContext-java.lang.String-">setDefaultCamelContext</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;defaultCamelContext)</code>&nbsp;</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setDefaultFailedJobWaitTime-int-">setDefaultFailedJobWaitTime</a></span>(int&nbsp;defaultFailedJobWaitTime)</code>&nbsp;</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setEnableProcessDefinitionInfoCache-boolean-">setEnableProcessDefinitionInfoCache</a></span>(boolean&nbsp;enableProcessDefinitionInfoCache)</code>&nbsp;</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setHistory-java.lang.String-">setHistory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;history)</code>&nbsp;</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setHistoryLevel-org.activiti.engine.impl.history.HistoryLevel-">setHistoryLevel</a></span>(org.activiti.engine.impl.history.HistoryLevel&nbsp;historyLevel)</code>&nbsp;</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setIdBlockSize-int-">setIdBlockSize</a></span>(int&nbsp;idBlockSize)</code>&nbsp;</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcDefaultTransactionIsolationLevel-int-">setJdbcDefaultTransactionIsolationLevel</a></span>(int&nbsp;jdbcDefaultTransactionIsolationLevel)</code>&nbsp;</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcDriver-java.lang.String-">setJdbcDriver</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcDriver)</code>&nbsp;</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcMaxActiveConnections-int-">setJdbcMaxActiveConnections</a></span>(int&nbsp;jdbcMaxActiveConnections)</code>&nbsp;</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcMaxCheckoutTime-int-">setJdbcMaxCheckoutTime</a></span>(int&nbsp;jdbcMaxCheckoutTime)</code>&nbsp;</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcMaxIdleConnections-int-">setJdbcMaxIdleConnections</a></span>(int&nbsp;jdbcMaxIdleConnections)</code>&nbsp;</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcMaxWaitTime-int-">setJdbcMaxWaitTime</a></span>(int&nbsp;jdbcMaxWaitTime)</code>&nbsp;</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcPassword-java.lang.String-">setJdbcPassword</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcPassword)</code>&nbsp;</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcPingConnectionNotUsedFor-int-">setJdbcPingConnectionNotUsedFor</a></span>(int&nbsp;jdbcPingNotUsedFor)</code>&nbsp;</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcPingEnabled-boolean-">setJdbcPingEnabled</a></span>(boolean&nbsp;jdbcPingEnabled)</code>&nbsp;</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcPingQuery-java.lang.String-">setJdbcPingQuery</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcPingQuery)</code>&nbsp;</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcUrl-java.lang.String-">setJdbcUrl</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcUrl)</code>&nbsp;</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcUsername-java.lang.String-">setJdbcUsername</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcUsername)</code>&nbsp;</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJobExecutor-org.activiti.engine.impl.jobexecutor.JobExecutor-">setJobExecutor</a></span>(org.activiti.engine.impl.jobexecutor.JobExecutor&nbsp;jobExecutor)</code>&nbsp;</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJobExecutorActivate-boolean-">setJobExecutorActivate</a></span>(boolean&nbsp;jobExecutorActivate)</code>&nbsp;</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJpaCloseEntityManager-boolean-">setJpaCloseEntityManager</a></span>(boolean&nbsp;jpaCloseEntityManager)</code>&nbsp;</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJpaEntityManagerFactory-java.lang.Object-">setJpaEntityManagerFactory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;jpaEntityManagerFactory)</code>&nbsp;</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJpaHandleTransaction-boolean-">setJpaHandleTransaction</a></span>(boolean&nbsp;jpaHandleTransaction)</code>&nbsp;</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setJpaPersistenceUnitName-java.lang.String-">setJpaPersistenceUnitName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jpaPersistenceUnitName)</code>&nbsp;</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setLabelFontName-java.lang.String-">setLabelFontName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;labelFontName)</code>&nbsp;</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setLockTimeAsyncJobWaitTime-int-">setLockTimeAsyncJobWaitTime</a></span>(int&nbsp;lockTimeAsyncJobWaitTime)</code>&nbsp;</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerDefaultFrom-java.lang.String-">setMailServerDefaultFrom</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerDefaultFrom)</code>&nbsp;</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerHost-java.lang.String-">setMailServerHost</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerHost)</code>&nbsp;</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerPassword-java.lang.String-">setMailServerPassword</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerPassword)</code>&nbsp;</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerPort-int-">setMailServerPort</a></span>(int&nbsp;mailServerPort)</code>&nbsp;</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServers-java.util.Map-">setMailServers</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/cfg/MailServerInfo.html" title="class in org.activiti.engine.cfg">MailServerInfo</a>&gt;&nbsp;mailServers)</code>&nbsp;</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerUsername-java.lang.String-">setMailServerUsername</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerUsername)</code>&nbsp;</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerUseSSL-boolean-">setMailServerUseSSL</a></span>(boolean&nbsp;useSSL)</code>&nbsp;</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerUseTLS-boolean-">setMailServerUseTLS</a></span>(boolean&nbsp;useTLS)</code>&nbsp;</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailSessionJndi-java.lang.String-">setMailSessionJndi</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailSessionJndi)</code>&nbsp;</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailSessionsJndi-java.util.Map-">setMailSessionsJndi</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;mailSessionsJndi)</code>&nbsp;</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setProcessDiagramGenerator-org.activiti.image.ProcessDiagramGenerator-">setProcessDiagramGenerator</a></span>(org.activiti.image.ProcessDiagramGenerator&nbsp;processDiagramGenerator)</code>&nbsp;</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setProcessEngineLifecycleListener-org.activiti.engine.ProcessEngineLifecycleListener-">setProcessEngineLifecycleListener</a></span>(<a href="../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine">ProcessEngineLifecycleListener</a>&nbsp;processEngineLifecycleListener)</code>&nbsp;</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setProcessEngineName-java.lang.String-">setProcessEngineName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processEngineName)</code>&nbsp;</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setTablePrefixIsSchema-boolean-">setTablePrefixIsSchema</a></span>(boolean&nbsp;tablePrefixIsSchema)</code>&nbsp;</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setTransactionsExternallyManaged-boolean-">setTransactionsExternallyManaged</a></span>(boolean&nbsp;transactionsExternallyManaged)</code>&nbsp;</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setUseClassForNameClassLoading-boolean-">setUseClassForNameClassLoading</a></span>(boolean&nbsp;useClassForNameClassLoading)</code>&nbsp;</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#setXmlEncoding-java.lang.String-">setXmlEncoding</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlEncoding)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.EngineServices">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.<a href="../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine">EngineServices</a></h3>
<code><a href="../../../org/activiti/engine/EngineServices.html#getDynamicBpmnService--">getDynamicBpmnService</a>, <a href="../../../org/activiti/engine/EngineServices.html#getFormService--">getFormService</a>, <a href="../../../org/activiti/engine/EngineServices.html#getHistoryService--">getHistoryService</a>, <a href="../../../org/activiti/engine/EngineServices.html#getIdentityService--">getIdentityService</a>, <a href="../../../org/activiti/engine/EngineServices.html#getManagementService--">getManagementService</a>, <a href="../../../org/activiti/engine/EngineServices.html#getProcessEngineConfiguration--">getProcessEngineConfiguration</a>, <a href="../../../org/activiti/engine/EngineServices.html#getRepositoryService--">getRepositoryService</a>, <a href="../../../org/activiti/engine/EngineServices.html#getRuntimeService--">getRuntimeService</a>, <a href="../../../org/activiti/engine/EngineServices.html#getTaskService--">getTaskService</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DB_SCHEMA_UPDATE_FALSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DB_SCHEMA_UPDATE_FALSE</h4>
<pre>public static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> DB_SCHEMA_UPDATE_FALSE</pre>
<div class="block">Checks the version of the DB schema against the library when 
 the process engine is being created and throws an exception
 if the versions don't match.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.ProcessEngineConfiguration.DB_SCHEMA_UPDATE_FALSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DB_SCHEMA_UPDATE_CREATE_DROP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DB_SCHEMA_UPDATE_CREATE_DROP</h4>
<pre>public static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> DB_SCHEMA_UPDATE_CREATE_DROP</pre>
<div class="block">Creates the schema when the process engine is being created and 
 drops the schema when the process engine is being closed.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.ProcessEngineConfiguration.DB_SCHEMA_UPDATE_CREATE_DROP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DB_SCHEMA_UPDATE_TRUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DB_SCHEMA_UPDATE_TRUE</h4>
<pre>public static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> DB_SCHEMA_UPDATE_TRUE</pre>
<div class="block">Upon building of the process engine, a check is performed and 
 an update of the schema is performed if it is necessary.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NO_TENANT_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NO_TENANT_ID</h4>
<pre>public static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> NO_TENANT_ID</pre>
<div class="block">The tenant id indicating 'no tenant'</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.ProcessEngineConfiguration.NO_TENANT_ID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="processEngineName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processEngineName</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> processEngineName</pre>
</li>
</ul>
<a name="idBlockSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>idBlockSize</h4>
<pre>protected&nbsp;int idBlockSize</pre>
</li>
</ul>
<a name="history">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>history</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> history</pre>
</li>
</ul>
<a name="jobExecutorActivate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jobExecutorActivate</h4>
<pre>protected&nbsp;boolean jobExecutorActivate</pre>
</li>
</ul>
<a name="asyncExecutorEnabled">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asyncExecutorEnabled</h4>
<pre>protected&nbsp;boolean asyncExecutorEnabled</pre>
</li>
</ul>
<a name="asyncExecutorActivate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asyncExecutorActivate</h4>
<pre>protected&nbsp;boolean asyncExecutorActivate</pre>
</li>
</ul>
<a name="mailServerHost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mailServerHost</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mailServerHost</pre>
</li>
</ul>
<a name="mailServerUsername">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mailServerUsername</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mailServerUsername</pre>
</li>
</ul>
<a name="mailServerPassword">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mailServerPassword</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mailServerPassword</pre>
</li>
</ul>
<a name="mailServerPort">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mailServerPort</h4>
<pre>protected&nbsp;int mailServerPort</pre>
</li>
</ul>
<a name="useSSL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>useSSL</h4>
<pre>protected&nbsp;boolean useSSL</pre>
</li>
</ul>
<a name="useTLS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>useTLS</h4>
<pre>protected&nbsp;boolean useTLS</pre>
</li>
</ul>
<a name="mailServerDefaultFrom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mailServerDefaultFrom</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mailServerDefaultFrom</pre>
</li>
</ul>
<a name="mailSessionJndi">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mailSessionJndi</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mailSessionJndi</pre>
</li>
</ul>
<a name="mailServers">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mailServers</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/cfg/MailServerInfo.html" title="class in org.activiti.engine.cfg">MailServerInfo</a>&gt; mailServers</pre>
</li>
</ul>
<a name="mailSessionsJndi">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mailSessionsJndi</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt; mailSessionsJndi</pre>
</li>
</ul>
<a name="databaseType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>databaseType</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> databaseType</pre>
</li>
</ul>
<a name="databaseSchemaUpdate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>databaseSchemaUpdate</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> databaseSchemaUpdate</pre>
</li>
</ul>
<a name="jdbcDriver">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcDriver</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> jdbcDriver</pre>
</li>
</ul>
<a name="jdbcUrl">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcUrl</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> jdbcUrl</pre>
</li>
</ul>
<a name="jdbcUsername">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcUsername</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> jdbcUsername</pre>
</li>
</ul>
<a name="jdbcPassword">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcPassword</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> jdbcPassword</pre>
</li>
</ul>
<a name="dataSourceJndiName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dataSourceJndiName</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> dataSourceJndiName</pre>
</li>
</ul>
<a name="isDbIdentityUsed">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDbIdentityUsed</h4>
<pre>protected&nbsp;boolean isDbIdentityUsed</pre>
</li>
</ul>
<a name="isDbHistoryUsed">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDbHistoryUsed</h4>
<pre>protected&nbsp;boolean isDbHistoryUsed</pre>
</li>
</ul>
<a name="historyLevel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>historyLevel</h4>
<pre>protected&nbsp;org.activiti.engine.impl.history.HistoryLevel historyLevel</pre>
</li>
</ul>
<a name="jdbcMaxActiveConnections">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcMaxActiveConnections</h4>
<pre>protected&nbsp;int jdbcMaxActiveConnections</pre>
</li>
</ul>
<a name="jdbcMaxIdleConnections">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcMaxIdleConnections</h4>
<pre>protected&nbsp;int jdbcMaxIdleConnections</pre>
</li>
</ul>
<a name="jdbcMaxCheckoutTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcMaxCheckoutTime</h4>
<pre>protected&nbsp;int jdbcMaxCheckoutTime</pre>
</li>
</ul>
<a name="jdbcMaxWaitTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcMaxWaitTime</h4>
<pre>protected&nbsp;int jdbcMaxWaitTime</pre>
</li>
</ul>
<a name="jdbcPingEnabled">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcPingEnabled</h4>
<pre>protected&nbsp;boolean jdbcPingEnabled</pre>
</li>
</ul>
<a name="jdbcPingQuery">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcPingQuery</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> jdbcPingQuery</pre>
</li>
</ul>
<a name="jdbcPingConnectionNotUsedFor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcPingConnectionNotUsedFor</h4>
<pre>protected&nbsp;int jdbcPingConnectionNotUsedFor</pre>
</li>
</ul>
<a name="jdbcDefaultTransactionIsolationLevel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jdbcDefaultTransactionIsolationLevel</h4>
<pre>protected&nbsp;int jdbcDefaultTransactionIsolationLevel</pre>
</li>
</ul>
<a name="dataSource">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dataSource</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/javax/sql/DataSource.html?is-external=true" title="class or interface in javax.sql">DataSource</a> dataSource</pre>
</li>
</ul>
<a name="transactionsExternallyManaged">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>transactionsExternallyManaged</h4>
<pre>protected&nbsp;boolean transactionsExternallyManaged</pre>
</li>
</ul>
<a name="jpaPersistenceUnitName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jpaPersistenceUnitName</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> jpaPersistenceUnitName</pre>
</li>
</ul>
<a name="jpaEntityManagerFactory">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jpaEntityManagerFactory</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> jpaEntityManagerFactory</pre>
</li>
</ul>
<a name="jpaHandleTransaction">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jpaHandleTransaction</h4>
<pre>protected&nbsp;boolean jpaHandleTransaction</pre>
</li>
</ul>
<a name="jpaCloseEntityManager">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jpaCloseEntityManager</h4>
<pre>protected&nbsp;boolean jpaCloseEntityManager</pre>
</li>
</ul>
<a name="clock">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clock</h4>
<pre>protected&nbsp;<a href="../../../org/activiti/engine/runtime/Clock.html" title="interface in org.activiti.engine.runtime">Clock</a> clock</pre>
</li>
</ul>
<a name="jobExecutor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jobExecutor</h4>
<pre>protected&nbsp;org.activiti.engine.impl.jobexecutor.JobExecutor jobExecutor</pre>
</li>
</ul>
<a name="asyncExecutor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asyncExecutor</h4>
<pre>protected&nbsp;org.activiti.engine.impl.asyncexecutor.AsyncExecutor asyncExecutor</pre>
</li>
</ul>
<a name="lockTimeAsyncJobWaitTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lockTimeAsyncJobWaitTime</h4>
<pre>protected&nbsp;int lockTimeAsyncJobWaitTime</pre>
<div class="block">Define the default lock time for an async job in seconds.
 The lock time is used when creating an async job and when it expires the async executor
 assumes that the job has failed. It will be retried again.</div>
</li>
</ul>
<a name="defaultFailedJobWaitTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultFailedJobWaitTime</h4>
<pre>protected&nbsp;int defaultFailedJobWaitTime</pre>
<div class="block">define the default wait time for a failed job in seconds</div>
</li>
</ul>
<a name="asyncFailedJobWaitTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asyncFailedJobWaitTime</h4>
<pre>protected&nbsp;int asyncFailedJobWaitTime</pre>
<div class="block">define the default wait time for a failed async job in seconds</div>
</li>
</ul>
<a name="processDiagramGenerator">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDiagramGenerator</h4>
<pre>protected&nbsp;org.activiti.image.ProcessDiagramGenerator processDiagramGenerator</pre>
<div class="block">process diagram generator. Default value is DefaulProcessDiagramGenerator</div>
</li>
</ul>
<a name="databaseTablePrefix">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>databaseTablePrefix</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> databaseTablePrefix</pre>
<div class="block">Allows configuring a database table prefix which is used for all runtime operations of the process engine.
 For example, if you specify a prefix named 'PRE1.', activiti will query for executions in a table named
 'PRE1.ACT_RU_EXECUTION_'. 
 
 <p />
 <strong>NOTE: the prefix is not respected by automatic database schema management. If you use 
 <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#DB_SCHEMA_UPDATE_CREATE_DROP"><code>DB_SCHEMA_UPDATE_CREATE_DROP</code></a> 
 or <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html#DB_SCHEMA_UPDATE_TRUE"><code>DB_SCHEMA_UPDATE_TRUE</code></a>, activiti will create the database tables 
 using the default names, regardless of the prefix configured here.</strong></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.9</dd>
</dl>
</li>
</ul>
<a name="databaseWildcardEscapeCharacter">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>databaseWildcardEscapeCharacter</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> databaseWildcardEscapeCharacter</pre>
<div class="block">Escape character for doing wildcard searches.
 
 This will be added at then end of queries that include for example a LIKE clause.
 For example: SELECT * FROM table WHERE column LIKE '%\%%' ESCAPE '\';</div>
</li>
</ul>
<a name="databaseCatalog">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>databaseCatalog</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> databaseCatalog</pre>
<div class="block">database catalog to use</div>
</li>
</ul>
<a name="databaseSchema">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>databaseSchema</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> databaseSchema</pre>
<div class="block">In some situations you want to set the schema to use for table checks / generation if the database metadata
 doesn't return that correctly, see https://activiti.atlassian.net/browse/ACT-1220,
 https://activiti.atlassian.net/browse/ACT-1062</div>
</li>
</ul>
<a name="tablePrefixIsSchema">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tablePrefixIsSchema</h4>
<pre>protected&nbsp;boolean tablePrefixIsSchema</pre>
<div class="block">Set to true in case the defined databaseTablePrefix is a schema-name, instead of an actual table name
 prefix. This is relevant for checking if Activiti-tables exist, the databaseTablePrefix will not be used here
 - since the schema is taken into account already, adding a prefix for the table-check will result in wrong table-names.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.15</dd>
</dl>
</li>
</ul>
<a name="isCreateDiagramOnDeploy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCreateDiagramOnDeploy</h4>
<pre>protected&nbsp;boolean isCreateDiagramOnDeploy</pre>
</li>
</ul>
<a name="xmlEncoding">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>xmlEncoding</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> xmlEncoding</pre>
</li>
</ul>
<a name="defaultCamelContext">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultCamelContext</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> defaultCamelContext</pre>
</li>
</ul>
<a name="activityFontName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityFontName</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> activityFontName</pre>
</li>
</ul>
<a name="labelFontName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>labelFontName</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> labelFontName</pre>
</li>
</ul>
<a name="annotationFontName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>annotationFontName</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> annotationFontName</pre>
</li>
</ul>
<a name="classLoader">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>classLoader</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/ClassLoader.html?is-external=true" title="class or interface in java.lang">ClassLoader</a> classLoader</pre>
</li>
</ul>
<a name="useClassForNameClassLoading">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>useClassForNameClassLoading</h4>
<pre>protected&nbsp;boolean useClassForNameClassLoading</pre>
<div class="block">Either use Class.forName or ClassLoader.loadClass for class loading.
 See http://forums.activiti.org/content/reflectutilloadclass-and-custom-classloader</div>
</li>
</ul>
<a name="processEngineLifecycleListener">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processEngineLifecycleListener</h4>
<pre>protected&nbsp;<a href="../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine">ProcessEngineLifecycleListener</a> processEngineLifecycleListener</pre>
</li>
</ul>
<a name="enableProcessDefinitionInfoCache">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>enableProcessDefinitionInfoCache</h4>
<pre>protected&nbsp;boolean enableProcessDefinitionInfoCache</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ProcessEngineConfiguration--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ProcessEngineConfiguration</h4>
<pre>protected&nbsp;ProcessEngineConfiguration()</pre>
<div class="block">use one of the static createXxxx methods instead</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="buildProcessEngine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildProcessEngine</h4>
<pre>public abstract&nbsp;<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;buildProcessEngine()</pre>
</li>
</ul>
<a name="createProcessEngineConfigurationFromResourceDefault--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProcessEngineConfigurationFromResourceDefault</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;createProcessEngineConfigurationFromResourceDefault()</pre>
</li>
</ul>
<a name="createProcessEngineConfigurationFromResource-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProcessEngineConfigurationFromResource</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;createProcessEngineConfigurationFromResource(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resource)</pre>
</li>
</ul>
<a name="createProcessEngineConfigurationFromResource-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProcessEngineConfigurationFromResource</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;createProcessEngineConfigurationFromResource(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resource,
                                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;beanName)</pre>
</li>
</ul>
<a name="createProcessEngineConfigurationFromInputStream-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProcessEngineConfigurationFromInputStream</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;createProcessEngineConfigurationFromInputStream(<a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</pre>
</li>
</ul>
<a name="createProcessEngineConfigurationFromInputStream-java.io.InputStream-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProcessEngineConfigurationFromInputStream</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;createProcessEngineConfigurationFromInputStream(<a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream,
                                                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;beanName)</pre>
</li>
</ul>
<a name="createStandaloneProcessEngineConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createStandaloneProcessEngineConfiguration</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;createStandaloneProcessEngineConfiguration()</pre>
</li>
</ul>
<a name="createStandaloneInMemProcessEngineConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createStandaloneInMemProcessEngineConfiguration</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;createStandaloneInMemProcessEngineConfiguration()</pre>
</li>
</ul>
<a name="getProcessEngineName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessEngineName</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessEngineName()</pre>
</li>
</ul>
<a name="setProcessEngineName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProcessEngineName</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setProcessEngineName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processEngineName)</pre>
</li>
</ul>
<a name="getIdBlockSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIdBlockSize</h4>
<pre>public&nbsp;int&nbsp;getIdBlockSize()</pre>
</li>
</ul>
<a name="setIdBlockSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIdBlockSize</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setIdBlockSize(int&nbsp;idBlockSize)</pre>
</li>
</ul>
<a name="getHistory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHistory</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHistory()</pre>
</li>
</ul>
<a name="setHistory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHistory</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setHistory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;history)</pre>
</li>
</ul>
<a name="getMailServerHost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailServerHost</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMailServerHost()</pre>
</li>
</ul>
<a name="setMailServerHost-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailServerHost</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailServerHost(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerHost)</pre>
</li>
</ul>
<a name="getMailServerUsername--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailServerUsername</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMailServerUsername()</pre>
</li>
</ul>
<a name="setMailServerUsername-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailServerUsername</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailServerUsername(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerUsername)</pre>
</li>
</ul>
<a name="getMailServerPassword--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailServerPassword</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMailServerPassword()</pre>
</li>
</ul>
<a name="setMailServerPassword-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailServerPassword</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailServerPassword(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerPassword)</pre>
</li>
</ul>
<a name="getMailSessionJndi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailSessionJndi</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMailSessionJndi()</pre>
</li>
</ul>
<a name="setMailSessionJndi-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailSessionJndi</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailSessionJndi(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailSessionJndi)</pre>
</li>
</ul>
<a name="getMailServerPort--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailServerPort</h4>
<pre>public&nbsp;int&nbsp;getMailServerPort()</pre>
</li>
</ul>
<a name="setMailServerPort-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailServerPort</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailServerPort(int&nbsp;mailServerPort)</pre>
</li>
</ul>
<a name="getMailServerUseSSL--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailServerUseSSL</h4>
<pre>public&nbsp;boolean&nbsp;getMailServerUseSSL()</pre>
</li>
</ul>
<a name="setMailServerUseSSL-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailServerUseSSL</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailServerUseSSL(boolean&nbsp;useSSL)</pre>
</li>
</ul>
<a name="getMailServerUseTLS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailServerUseTLS</h4>
<pre>public&nbsp;boolean&nbsp;getMailServerUseTLS()</pre>
</li>
</ul>
<a name="setMailServerUseTLS-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailServerUseTLS</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailServerUseTLS(boolean&nbsp;useTLS)</pre>
</li>
</ul>
<a name="getMailServerDefaultFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailServerDefaultFrom</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMailServerDefaultFrom()</pre>
</li>
</ul>
<a name="setMailServerDefaultFrom-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailServerDefaultFrom</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailServerDefaultFrom(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerDefaultFrom)</pre>
</li>
</ul>
<a name="getMailServer-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailServer</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/cfg/MailServerInfo.html" title="class in org.activiti.engine.cfg">MailServerInfo</a>&nbsp;getMailServer(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
</li>
</ul>
<a name="getMailServers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailServers</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/cfg/MailServerInfo.html" title="class in org.activiti.engine.cfg">MailServerInfo</a>&gt;&nbsp;getMailServers()</pre>
</li>
</ul>
<a name="setMailServers-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailServers</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailServers(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/cfg/MailServerInfo.html" title="class in org.activiti.engine.cfg">MailServerInfo</a>&gt;&nbsp;mailServers)</pre>
</li>
</ul>
<a name="getMailSessionJndi-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailSessionJndi</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMailSessionJndi(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
</li>
</ul>
<a name="getMailSessionsJndi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMailSessionsJndi</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getMailSessionsJndi()</pre>
</li>
</ul>
<a name="setMailSessionsJndi-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMailSessionsJndi</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setMailSessionsJndi(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;mailSessionsJndi)</pre>
</li>
</ul>
<a name="getDatabaseType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDatabaseType</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDatabaseType()</pre>
</li>
</ul>
<a name="setDatabaseType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDatabaseType</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDatabaseType(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseType)</pre>
</li>
</ul>
<a name="getDatabaseSchemaUpdate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDatabaseSchemaUpdate</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDatabaseSchemaUpdate()</pre>
</li>
</ul>
<a name="setDatabaseSchemaUpdate-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDatabaseSchemaUpdate</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDatabaseSchemaUpdate(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseSchemaUpdate)</pre>
</li>
</ul>
<a name="getDataSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataSource</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/javax/sql/DataSource.html?is-external=true" title="class or interface in javax.sql">DataSource</a>&nbsp;getDataSource()</pre>
</li>
</ul>
<a name="setDataSource-javax.sql.DataSource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDataSource</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDataSource(<a href="http://docs.oracle.com/javase/6/docs/api/javax/sql/DataSource.html?is-external=true" title="class or interface in javax.sql">DataSource</a>&nbsp;dataSource)</pre>
</li>
</ul>
<a name="getJdbcDriver--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcDriver</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getJdbcDriver()</pre>
</li>
</ul>
<a name="setJdbcDriver-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcDriver</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcDriver(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcDriver)</pre>
</li>
</ul>
<a name="getJdbcUrl--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcUrl</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getJdbcUrl()</pre>
</li>
</ul>
<a name="setJdbcUrl-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcUrl</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcUrl(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcUrl)</pre>
</li>
</ul>
<a name="getJdbcUsername--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcUsername</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getJdbcUsername()</pre>
</li>
</ul>
<a name="setJdbcUsername-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcUsername</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcUsername(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcUsername)</pre>
</li>
</ul>
<a name="getJdbcPassword--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcPassword</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getJdbcPassword()</pre>
</li>
</ul>
<a name="setJdbcPassword-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcPassword</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcPassword(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcPassword)</pre>
</li>
</ul>
<a name="isTransactionsExternallyManaged--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTransactionsExternallyManaged</h4>
<pre>public&nbsp;boolean&nbsp;isTransactionsExternallyManaged()</pre>
</li>
</ul>
<a name="setTransactionsExternallyManaged-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransactionsExternallyManaged</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setTransactionsExternallyManaged(boolean&nbsp;transactionsExternallyManaged)</pre>
</li>
</ul>
<a name="getHistoryLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHistoryLevel</h4>
<pre>public&nbsp;org.activiti.engine.impl.history.HistoryLevel&nbsp;getHistoryLevel()</pre>
</li>
</ul>
<a name="setHistoryLevel-org.activiti.engine.impl.history.HistoryLevel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHistoryLevel</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setHistoryLevel(org.activiti.engine.impl.history.HistoryLevel&nbsp;historyLevel)</pre>
</li>
</ul>
<a name="isDbIdentityUsed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDbIdentityUsed</h4>
<pre>public&nbsp;boolean&nbsp;isDbIdentityUsed()</pre>
</li>
</ul>
<a name="setDbIdentityUsed-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDbIdentityUsed</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDbIdentityUsed(boolean&nbsp;isDbIdentityUsed)</pre>
</li>
</ul>
<a name="isDbHistoryUsed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDbHistoryUsed</h4>
<pre>public&nbsp;boolean&nbsp;isDbHistoryUsed()</pre>
</li>
</ul>
<a name="setDbHistoryUsed-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDbHistoryUsed</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDbHistoryUsed(boolean&nbsp;isDbHistoryUsed)</pre>
</li>
</ul>
<a name="getJdbcMaxActiveConnections--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcMaxActiveConnections</h4>
<pre>public&nbsp;int&nbsp;getJdbcMaxActiveConnections()</pre>
</li>
</ul>
<a name="setJdbcMaxActiveConnections-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcMaxActiveConnections</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcMaxActiveConnections(int&nbsp;jdbcMaxActiveConnections)</pre>
</li>
</ul>
<a name="getJdbcMaxIdleConnections--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcMaxIdleConnections</h4>
<pre>public&nbsp;int&nbsp;getJdbcMaxIdleConnections()</pre>
</li>
</ul>
<a name="setJdbcMaxIdleConnections-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcMaxIdleConnections</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcMaxIdleConnections(int&nbsp;jdbcMaxIdleConnections)</pre>
</li>
</ul>
<a name="getJdbcMaxCheckoutTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcMaxCheckoutTime</h4>
<pre>public&nbsp;int&nbsp;getJdbcMaxCheckoutTime()</pre>
</li>
</ul>
<a name="setJdbcMaxCheckoutTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcMaxCheckoutTime</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcMaxCheckoutTime(int&nbsp;jdbcMaxCheckoutTime)</pre>
</li>
</ul>
<a name="getJdbcMaxWaitTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcMaxWaitTime</h4>
<pre>public&nbsp;int&nbsp;getJdbcMaxWaitTime()</pre>
</li>
</ul>
<a name="setJdbcMaxWaitTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcMaxWaitTime</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcMaxWaitTime(int&nbsp;jdbcMaxWaitTime)</pre>
</li>
</ul>
<a name="isJdbcPingEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isJdbcPingEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isJdbcPingEnabled()</pre>
</li>
</ul>
<a name="setJdbcPingEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcPingEnabled</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcPingEnabled(boolean&nbsp;jdbcPingEnabled)</pre>
</li>
</ul>
<a name="getJdbcPingQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcPingQuery</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getJdbcPingQuery()</pre>
</li>
</ul>
<a name="setJdbcPingQuery-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcPingQuery</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcPingQuery(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcPingQuery)</pre>
</li>
</ul>
<a name="getJdbcPingConnectionNotUsedFor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcPingConnectionNotUsedFor</h4>
<pre>public&nbsp;int&nbsp;getJdbcPingConnectionNotUsedFor()</pre>
</li>
</ul>
<a name="setJdbcPingConnectionNotUsedFor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcPingConnectionNotUsedFor</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcPingConnectionNotUsedFor(int&nbsp;jdbcPingNotUsedFor)</pre>
</li>
</ul>
<a name="getJdbcDefaultTransactionIsolationLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdbcDefaultTransactionIsolationLevel</h4>
<pre>public&nbsp;int&nbsp;getJdbcDefaultTransactionIsolationLevel()</pre>
</li>
</ul>
<a name="setJdbcDefaultTransactionIsolationLevel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdbcDefaultTransactionIsolationLevel</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJdbcDefaultTransactionIsolationLevel(int&nbsp;jdbcDefaultTransactionIsolationLevel)</pre>
</li>
</ul>
<a name="isJobExecutorActivate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isJobExecutorActivate</h4>
<pre>public&nbsp;boolean&nbsp;isJobExecutorActivate()</pre>
</li>
</ul>
<a name="setJobExecutorActivate-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJobExecutorActivate</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJobExecutorActivate(boolean&nbsp;jobExecutorActivate)</pre>
</li>
</ul>
<a name="isAsyncExecutorEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAsyncExecutorEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isAsyncExecutorEnabled()</pre>
</li>
</ul>
<a name="setAsyncExecutorEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAsyncExecutorEnabled</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setAsyncExecutorEnabled(boolean&nbsp;asyncExecutorEnabled)</pre>
</li>
</ul>
<a name="isAsyncExecutorActivate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAsyncExecutorActivate</h4>
<pre>public&nbsp;boolean&nbsp;isAsyncExecutorActivate()</pre>
</li>
</ul>
<a name="setAsyncExecutorActivate-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAsyncExecutorActivate</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setAsyncExecutorActivate(boolean&nbsp;asyncExecutorActivate)</pre>
</li>
</ul>
<a name="getClassLoader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassLoader</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/ClassLoader.html?is-external=true" title="class or interface in java.lang">ClassLoader</a>&nbsp;getClassLoader()</pre>
</li>
</ul>
<a name="setClassLoader-java.lang.ClassLoader-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClassLoader</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setClassLoader(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/ClassLoader.html?is-external=true" title="class or interface in java.lang">ClassLoader</a>&nbsp;classLoader)</pre>
</li>
</ul>
<a name="isUseClassForNameClassLoading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUseClassForNameClassLoading</h4>
<pre>public&nbsp;boolean&nbsp;isUseClassForNameClassLoading()</pre>
</li>
</ul>
<a name="setUseClassForNameClassLoading-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseClassForNameClassLoading</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setUseClassForNameClassLoading(boolean&nbsp;useClassForNameClassLoading)</pre>
</li>
</ul>
<a name="getJpaEntityManagerFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJpaEntityManagerFactory</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getJpaEntityManagerFactory()</pre>
</li>
</ul>
<a name="setJpaEntityManagerFactory-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJpaEntityManagerFactory</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJpaEntityManagerFactory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;jpaEntityManagerFactory)</pre>
</li>
</ul>
<a name="isJpaHandleTransaction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isJpaHandleTransaction</h4>
<pre>public&nbsp;boolean&nbsp;isJpaHandleTransaction()</pre>
</li>
</ul>
<a name="setJpaHandleTransaction-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJpaHandleTransaction</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJpaHandleTransaction(boolean&nbsp;jpaHandleTransaction)</pre>
</li>
</ul>
<a name="isJpaCloseEntityManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isJpaCloseEntityManager</h4>
<pre>public&nbsp;boolean&nbsp;isJpaCloseEntityManager()</pre>
</li>
</ul>
<a name="setJpaCloseEntityManager-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJpaCloseEntityManager</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJpaCloseEntityManager(boolean&nbsp;jpaCloseEntityManager)</pre>
</li>
</ul>
<a name="getJpaPersistenceUnitName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJpaPersistenceUnitName</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getJpaPersistenceUnitName()</pre>
</li>
</ul>
<a name="setJpaPersistenceUnitName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJpaPersistenceUnitName</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJpaPersistenceUnitName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jpaPersistenceUnitName)</pre>
</li>
</ul>
<a name="getDataSourceJndiName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataSourceJndiName</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDataSourceJndiName()</pre>
</li>
</ul>
<a name="setDataSourceJndiName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDataSourceJndiName</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDataSourceJndiName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataSourceJndiName)</pre>
</li>
</ul>
<a name="getDefaultCamelContext--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultCamelContext</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDefaultCamelContext()</pre>
</li>
</ul>
<a name="setDefaultCamelContext-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultCamelContext</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDefaultCamelContext(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;defaultCamelContext)</pre>
</li>
</ul>
<a name="isCreateDiagramOnDeploy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCreateDiagramOnDeploy</h4>
<pre>public&nbsp;boolean&nbsp;isCreateDiagramOnDeploy()</pre>
</li>
</ul>
<a name="setCreateDiagramOnDeploy-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreateDiagramOnDeploy</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setCreateDiagramOnDeploy(boolean&nbsp;createDiagramOnDeploy)</pre>
</li>
</ul>
<a name="getActivityFontName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityFontName</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActivityFontName()</pre>
</li>
</ul>
<a name="setActivityFontName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityFontName</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setActivityFontName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityFontName)</pre>
</li>
</ul>
<a name="setProcessEngineLifecycleListener-org.activiti.engine.ProcessEngineLifecycleListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProcessEngineLifecycleListener</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setProcessEngineLifecycleListener(<a href="../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine">ProcessEngineLifecycleListener</a>&nbsp;processEngineLifecycleListener)</pre>
</li>
</ul>
<a name="getProcessEngineLifecycleListener--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessEngineLifecycleListener</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine">ProcessEngineLifecycleListener</a>&nbsp;getProcessEngineLifecycleListener()</pre>
</li>
</ul>
<a name="getLabelFontName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLabelFontName</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getLabelFontName()</pre>
</li>
</ul>
<a name="setLabelFontName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLabelFontName</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setLabelFontName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;labelFontName)</pre>
</li>
</ul>
<a name="getAnnotationFontName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnotationFontName</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getAnnotationFontName()</pre>
</li>
</ul>
<a name="setAnnotationFontName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnnotationFontName</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setAnnotationFontName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;annotationFontName)</pre>
</li>
</ul>
<a name="getDatabaseTablePrefix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDatabaseTablePrefix</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDatabaseTablePrefix()</pre>
</li>
</ul>
<a name="setDatabaseTablePrefix-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDatabaseTablePrefix</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDatabaseTablePrefix(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseTablePrefix)</pre>
</li>
</ul>
<a name="setTablePrefixIsSchema-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTablePrefixIsSchema</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setTablePrefixIsSchema(boolean&nbsp;tablePrefixIsSchema)</pre>
</li>
</ul>
<a name="isTablePrefixIsSchema--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTablePrefixIsSchema</h4>
<pre>public&nbsp;boolean&nbsp;isTablePrefixIsSchema()</pre>
</li>
</ul>
<a name="getDatabaseWildcardEscapeCharacter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDatabaseWildcardEscapeCharacter</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDatabaseWildcardEscapeCharacter()</pre>
</li>
</ul>
<a name="setDatabaseWildcardEscapeCharacter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDatabaseWildcardEscapeCharacter</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDatabaseWildcardEscapeCharacter(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseWildcardEscapeCharacter)</pre>
</li>
</ul>
<a name="getDatabaseCatalog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDatabaseCatalog</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDatabaseCatalog()</pre>
</li>
</ul>
<a name="setDatabaseCatalog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDatabaseCatalog</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDatabaseCatalog(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseCatalog)</pre>
</li>
</ul>
<a name="getDatabaseSchema--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDatabaseSchema</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDatabaseSchema()</pre>
</li>
</ul>
<a name="setDatabaseSchema-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDatabaseSchema</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDatabaseSchema(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseSchema)</pre>
</li>
</ul>
<a name="getXmlEncoding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXmlEncoding</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getXmlEncoding()</pre>
</li>
</ul>
<a name="setXmlEncoding-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setXmlEncoding</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setXmlEncoding(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlEncoding)</pre>
</li>
</ul>
<a name="getClock--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClock</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/runtime/Clock.html" title="interface in org.activiti.engine.runtime">Clock</a>&nbsp;getClock()</pre>
</li>
</ul>
<a name="setClock-org.activiti.engine.runtime.Clock-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClock</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setClock(<a href="../../../org/activiti/engine/runtime/Clock.html" title="interface in org.activiti.engine.runtime">Clock</a>&nbsp;clock)</pre>
</li>
</ul>
<a name="getProcessDiagramGenerator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDiagramGenerator</h4>
<pre>public&nbsp;org.activiti.image.ProcessDiagramGenerator&nbsp;getProcessDiagramGenerator()</pre>
</li>
</ul>
<a name="setProcessDiagramGenerator-org.activiti.image.ProcessDiagramGenerator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProcessDiagramGenerator</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setProcessDiagramGenerator(org.activiti.image.ProcessDiagramGenerator&nbsp;processDiagramGenerator)</pre>
</li>
</ul>
<a name="getJobExecutor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJobExecutor</h4>
<pre>public&nbsp;org.activiti.engine.impl.jobexecutor.JobExecutor&nbsp;getJobExecutor()</pre>
</li>
</ul>
<a name="setJobExecutor-org.activiti.engine.impl.jobexecutor.JobExecutor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJobExecutor</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setJobExecutor(org.activiti.engine.impl.jobexecutor.JobExecutor&nbsp;jobExecutor)</pre>
</li>
</ul>
<a name="getAsyncExecutor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsyncExecutor</h4>
<pre>public&nbsp;org.activiti.engine.impl.asyncexecutor.AsyncExecutor&nbsp;getAsyncExecutor()</pre>
</li>
</ul>
<a name="setAsyncExecutor-org.activiti.engine.impl.asyncexecutor.AsyncExecutor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAsyncExecutor</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setAsyncExecutor(org.activiti.engine.impl.asyncexecutor.AsyncExecutor&nbsp;asyncExecutor)</pre>
</li>
</ul>
<a name="getLockTimeAsyncJobWaitTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLockTimeAsyncJobWaitTime</h4>
<pre>public&nbsp;int&nbsp;getLockTimeAsyncJobWaitTime()</pre>
</li>
</ul>
<a name="setLockTimeAsyncJobWaitTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLockTimeAsyncJobWaitTime</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setLockTimeAsyncJobWaitTime(int&nbsp;lockTimeAsyncJobWaitTime)</pre>
</li>
</ul>
<a name="getDefaultFailedJobWaitTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultFailedJobWaitTime</h4>
<pre>public&nbsp;int&nbsp;getDefaultFailedJobWaitTime()</pre>
</li>
</ul>
<a name="setDefaultFailedJobWaitTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultFailedJobWaitTime</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setDefaultFailedJobWaitTime(int&nbsp;defaultFailedJobWaitTime)</pre>
</li>
</ul>
<a name="getAsyncFailedJobWaitTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsyncFailedJobWaitTime</h4>
<pre>public&nbsp;int&nbsp;getAsyncFailedJobWaitTime()</pre>
</li>
</ul>
<a name="setAsyncFailedJobWaitTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAsyncFailedJobWaitTime</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setAsyncFailedJobWaitTime(int&nbsp;asyncFailedJobWaitTime)</pre>
</li>
</ul>
<a name="isEnableProcessDefinitionInfoCache--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableProcessDefinitionInfoCache</h4>
<pre>public&nbsp;boolean&nbsp;isEnableProcessDefinitionInfoCache()</pre>
</li>
</ul>
<a name="setEnableProcessDefinitionInfoCache-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setEnableProcessDefinitionInfoCache</h4>
<pre>public&nbsp;<a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;setEnableProcessDefinitionInfoCache(boolean&nbsp;enableProcessDefinitionInfoCache)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessEngineConfiguration.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/ProcessEngineConfiguration.html" target="_top">Frames</a></li>
<li><a href="ProcessEngineConfiguration.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
