'use strict';

/* jshint quotmark: double */
window.SwaggerTranslator.learn({
    "Warning: Deprecated":"경고：폐기예정됨",
    "Implementation Notes":"구현 노트",
    "Response Class":"응답 클래스",
    "Status":"상태",
    "Parameters":"매개변수들",
    "Parameter":"매개변수",
    "Value":"값",
    "Description":"설명",
    "Parameter Type":"매개변수 타입",
    "Data Type":"데이터 타입",
    "Response Messages":"응답 메세지",
    "HTTP Status Code":"HTTP 상태 코드",
    "Reason":"원인",
    "Response Model":"응답 모델",
    "Request URL":"요청 URL",
    "Response Body":"응답 본문",
    "Response Code":"응답 코드",
    "Response Headers":"응답 헤더",
    "Hide Response":"응답 숨기기",
    "Headers":"헤더",
    "Try it out!":"써보기！",
    "Show/Hide":"보이기/숨기기",
    "List Operations":"목록 작업",
    "Expand Operations":"전개 작업",
    "Raw":"원본",
    "can't parse JSON.  Raw result":"JSON을 파싱할수 없음. 원본결과:",
    "Model Schema":"모델 스키마",
    "Model":"모델",
    "apply":"적용",
    "Username":"사용자 이름",
    "Password":"암호",
    "Terms of service":"이용약관",
    "Created by":"작성자",
    "See more at":"추가정보：",
    "Contact the developer":"개발자에게 문의",
    "api version":"api버전",
    "Response Content Type":"응답Content Type",
    "fetching resource":"리소스 가져오기",
    "fetching resource list":"리소스 목록 가져오기",
    "Explore":"탐색",
    "Show Swagger Petstore Example Apis":"Swagger Petstore 예제 보기",
    "Can't read from server.  It may not have the appropriate access-control-origin settings.":"서버로부터 읽어들일수 없습니다. access-control-origin 설정이 올바르지 않을수 있습니다.",
    "Please specify the protocol for":"다음을 위한 프로토콜을 정하세요",
    "Can't read swagger JSON from":"swagger JSON 을 다음으로 부터 읽을수 없습니다",
    "Finished Loading Resource Information. Rendering Swagger UI":"리소스 정보 불러오기 완료. Swagger UI 랜더링",
    "Unable to read api":"api를 읽을 수 없습니다.",
    "from path":"다음 경로로 부터",
    "server returned":"서버 응답함."
});
