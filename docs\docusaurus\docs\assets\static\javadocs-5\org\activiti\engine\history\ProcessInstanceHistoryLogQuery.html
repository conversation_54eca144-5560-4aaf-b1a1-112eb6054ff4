<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProcessInstanceHistoryLogQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProcessInstanceHistoryLogQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessInstanceHistoryLogQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" target="_top">Frames</a></li>
<li><a href="ProcessInstanceHistoryLogQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.history</div>
<h2 title="Interface ProcessInstanceHistoryLogQuery" class="title">Interface ProcessInstanceHistoryLogQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ProcessInstanceHistoryLogQuery</span></pre>
<div class="block">Allows to fetch the <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> for a process instance.
 
 Note that every includeXXX() method below will lead to an additional query.
 
 This class is actually a convenience on top of the other specific queries such 
 as <a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstanceQuery</code></a>, <a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstanceQuery</code></a>, ...
 It will execute separate queries for each included type, order the 
 data according to the date (ascending) and wrap the results in the <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html#includeActivities--">includeActivities</a></span>()</code>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a> instances.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html#includeComments--">includeComments</a></span>()</code>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task"><code>Comment</code></a> instances.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html#includeFormProperties--">includeFormProperties</a></span>()</code>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricFormProperty.html" title="interface in org.activiti.engine.history"><code>HistoricFormProperty</code></a> instances.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html#includeTasks--">includeTasks</a></span>()</code>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstance</code></a> instances.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html#includeVariables--">includeVariables</a></span>()</code>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><code>HistoricVariableInstance</code></a> instances.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html#includeVariableUpdates--">includeVariableUpdates</a></span>()</code>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricVariableUpdate.html" title="interface in org.activiti.engine.history"><code>HistoricVariableUpdate</code></a> instances.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLog</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html#singleResult--">singleResult</a></span>()</code>
<div class="block">Executes the query.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="includeTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeTasks</h4>
<pre><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a>&nbsp;includeTasks()</pre>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstance</code></a> instances.</div>
</li>
</ul>
<a name="includeActivities--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeActivities</h4>
<pre><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a>&nbsp;includeActivities()</pre>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a> instances.</div>
</li>
</ul>
<a name="includeVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeVariables</h4>
<pre><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a>&nbsp;includeVariables()</pre>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><code>HistoricVariableInstance</code></a> instances.</div>
</li>
</ul>
<a name="includeComments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeComments</h4>
<pre><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a>&nbsp;includeComments()</pre>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task"><code>Comment</code></a> instances.</div>
</li>
</ul>
<a name="includeVariableUpdates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeVariableUpdates</h4>
<pre><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a>&nbsp;includeVariableUpdates()</pre>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricVariableUpdate.html" title="interface in org.activiti.engine.history"><code>HistoricVariableUpdate</code></a> instances.</div>
</li>
</ul>
<a name="includeFormProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeFormProperties</h4>
<pre><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a>&nbsp;includeFormProperties()</pre>
<div class="block">The <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> will contain the <a href="../../../../org/activiti/engine/history/HistoricFormProperty.html" title="interface in org.activiti.engine.history"><code>HistoricFormProperty</code></a> instances.</div>
</li>
</ul>
<a name="singleResult--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>singleResult</h4>
<pre><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLog</a>&nbsp;singleResult()</pre>
<div class="block">Executes the query.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessInstanceHistoryLogQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" target="_top">Frames</a></li>
<li><a href="ProcessInstanceHistoryLogQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
