# GRCOS OSCAL Model Implementation Guide

## Overview

This document provides comprehensive technical implementation guidance for integrating OSCAL models within the GRCOS platform. It covers data structures, validation patterns, transformation pipelines, and best practices for maintaining OSCAL compliance while enabling advanced GRC automation capabilities.

## OSCAL Model Implementation Architecture

### Core Implementation Patterns

#### OSCAL Document Base Class
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import json
import uuid
from datetime import datetime
import hashlib

class OSCALDocument(ABC):
    """
    Base class for all OSCAL document implementations
    """
    
    def __init__(self, document_type: str, document_uuid: Optional[str] = None):
        self.document_type = document_type
        self.uuid = document_uuid or str(uuid.uuid4())
        self.metadata = self._initialize_metadata()
        self.blockchain_hash = None
        self.validation_errors = []
    
    def _initialize_metadata(self) -> Dict[str, Any]:
        """Initialize OSCAL metadata structure"""
        return {
            "title": "",
            "version": "1.0.0",
            "oscal-version": "1.1.3",
            "last-modified": datetime.utcnow().isoformat() + "Z",
            "props": [],
            "links": [],
            "responsible-parties": []
        }
    
    @abstractmethod
    def to_oscal_dict(self) -> Dict[str, Any]:
        """Convert to OSCAL-compliant dictionary"""
        pass
    
    @abstractmethod
    def from_oscal_dict(self, oscal_data: Dict[str, Any]) -> 'OSCALDocument':
        """Create instance from OSCAL dictionary"""
        pass
    
    def validate_schema(self) -> bool:
        """Validate against OSCAL schema"""
        oscal_data = self.to_oscal_dict()
        return self._validate_oscal_schema(oscal_data)
    
    def calculate_hash(self) -> str:
        """Calculate SHA-256 hash of document content"""
        content = json.dumps(self.to_oscal_dict(), sort_keys=True)
        return hashlib.sha256(content.encode()).hexdigest()
    
    def register_blockchain(self, blockchain_service):
        """Register document on blockchain"""
        self.blockchain_hash = self.calculate_hash()
        return blockchain_service.register_document(
            self.uuid, 
            self.document_type, 
            self.blockchain_hash,
            self.metadata
        )
```

#### OSCAL Catalog Implementation
```python
class OSCALCatalog(OSCALDocument):
    """
    Implementation of OSCAL Catalog model
    """
    
    def __init__(self, catalog_uuid: Optional[str] = None):
        super().__init__("catalog", catalog_uuid)
        self.groups = []
        self.controls = []
        self.back_matter = None
    
    def add_control_group(self, group_id: str, title: str, controls: List[Dict]) -> None:
        """Add control group to catalog"""
        group = {
            "id": group_id,
            "title": title,
            "controls": controls
        }
        self.groups.append(group)
    
    def add_control(self, control_id: str, title: str, parts: List[Dict]) -> None:
        """Add individual control to catalog"""
        control = {
            "id": control_id,
            "title": title,
            "parts": parts,
            "props": [],
            "links": []
        }
        self.controls.append(control)
    
    def to_oscal_dict(self) -> Dict[str, Any]:
        """Convert to OSCAL catalog format"""
        return {
            "catalog": {
                "uuid": self.uuid,
                "metadata": self.metadata,
                "groups": self.groups,
                "controls": self.controls,
                "back-matter": self.back_matter
            }
        }
    
    def from_oscal_dict(self, oscal_data: Dict[str, Any]) -> 'OSCALCatalog':
        """Create catalog from OSCAL data"""
        catalog_data = oscal_data.get("catalog", {})
        
        self.uuid = catalog_data.get("uuid", self.uuid)
        self.metadata = catalog_data.get("metadata", self.metadata)
        self.groups = catalog_data.get("groups", [])
        self.controls = catalog_data.get("controls", [])
        self.back_matter = catalog_data.get("back-matter")
        
        return self
    
    def get_control_by_id(self, control_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve control by ID"""
        # Search in direct controls
        for control in self.controls:
            if control.get("id") == control_id:
                return control
        
        # Search in groups
        for group in self.groups:
            for control in group.get("controls", []):
                if control.get("id") == control_id:
                    return control
        
        return None
```

#### OSCAL System Security Plan Implementation
```python
class OSCALSystemSecurityPlan(OSCALDocument):
    """
    Implementation of OSCAL System Security Plan model
    """
    
    def __init__(self, ssp_uuid: Optional[str] = None):
        super().__init__("system-security-plan", ssp_uuid)
        self.import_profile = None
        self.system_characteristics = {}
        self.system_implementation = {}
        self.control_implementation = {}
        self.back_matter = None
    
    def set_import_profile(self, profile_href: str) -> None:
        """Set imported profile reference"""
        self.import_profile = {"href": profile_href}
    
    def add_system_component(self, component_uuid: str, component_type: str, 
                           title: str, description: str) -> None:
        """Add system component"""
        if "components" not in self.system_implementation:
            self.system_implementation["components"] = []
        
        component = {
            "uuid": component_uuid,
            "type": component_type,
            "title": title,
            "description": description,
            "props": [],
            "links": [],
            "responsible-roles": []
        }
        self.system_implementation["components"].append(component)
    
    def add_control_implementation(self, control_id: str, implementation_uuid: str,
                                 description: str, implementation_status: str) -> None:
        """Add control implementation"""
        if "implemented-requirements" not in self.control_implementation:
            self.control_implementation["implemented-requirements"] = []
        
        implementation = {
            "uuid": implementation_uuid,
            "control-id": control_id,
            "statements": [
                {
                    "statement-id": f"{control_id}_stmt",
                    "uuid": str(uuid.uuid4()),
                    "description": description,
                    "implementation-status": {"state": implementation_status}
                }
            ]
        }
        self.control_implementation["implemented-requirements"].append(implementation)
    
    def to_oscal_dict(self) -> Dict[str, Any]:
        """Convert to OSCAL SSP format"""
        return {
            "system-security-plan": {
                "uuid": self.uuid,
                "metadata": self.metadata,
                "import-profile": self.import_profile,
                "system-characteristics": self.system_characteristics,
                "system-implementation": self.system_implementation,
                "control-implementation": self.control_implementation,
                "back-matter": self.back_matter
            }
        }
    
    def from_oscal_dict(self, oscal_data: Dict[str, Any]) -> 'OSCALSystemSecurityPlan':
        """Create SSP from OSCAL data"""
        ssp_data = oscal_data.get("system-security-plan", {})
        
        self.uuid = ssp_data.get("uuid", self.uuid)
        self.metadata = ssp_data.get("metadata", self.metadata)
        self.import_profile = ssp_data.get("import-profile")
        self.system_characteristics = ssp_data.get("system-characteristics", {})
        self.system_implementation = ssp_data.get("system-implementation", {})
        self.control_implementation = ssp_data.get("control-implementation", {})
        self.back_matter = ssp_data.get("back-matter")
        
        return self
```

### OSCAL Validation Framework

#### Schema Validation Engine
```python
import jsonschema
from typing import List, Dict, Any

class OSCALValidator:
    """
    OSCAL document validation engine
    """
    
    def __init__(self, schema_directory: str):
        self.schema_directory = schema_directory
        self.schemas = self._load_schemas()
    
    def _load_schemas(self) -> Dict[str, Any]:
        """Load OSCAL JSON schemas"""
        schemas = {}
        schema_files = {
            "catalog": "oscal_catalog_schema.json",
            "profile": "oscal_profile_schema.json",
            "system-security-plan": "oscal_ssp_schema.json",
            "assessment-plan": "oscal_assessment_plan_schema.json",
            "assessment-results": "oscal_assessment_results_schema.json",
            "plan-of-action-and-milestones": "oscal_poam_schema.json"
        }
        
        for doc_type, schema_file in schema_files.items():
            with open(f"{self.schema_directory}/{schema_file}", 'r') as f:
                schemas[doc_type] = json.load(f)
        
        return schemas
    
    def validate_document(self, document: OSCALDocument) -> List[str]:
        """Validate OSCAL document against schema"""
        errors = []
        
        try:
            oscal_data = document.to_oscal_dict()
            schema = self.schemas.get(document.document_type)
            
            if not schema:
                errors.append(f"No schema found for document type: {document.document_type}")
                return errors
            
            jsonschema.validate(oscal_data, schema)
            
        except jsonschema.ValidationError as e:
            errors.append(f"Schema validation error: {e.message}")
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        # Additional business rule validation
        business_rule_errors = self._validate_business_rules(document)
        errors.extend(business_rule_errors)
        
        return errors
    
    def _validate_business_rules(self, document: OSCALDocument) -> List[str]:
        """Validate GRCOS-specific business rules"""
        errors = []
        
        # Common validation rules
        if not document.metadata.get("title"):
            errors.append("Document title is required")
        
        if not document.metadata.get("version"):
            errors.append("Document version is required")
        
        # Document-specific validation
        if document.document_type == "system-security-plan":
            errors.extend(self._validate_ssp_rules(document))
        elif document.document_type == "catalog":
            errors.extend(self._validate_catalog_rules(document))
        
        return errors
    
    def _validate_ssp_rules(self, ssp: OSCALSystemSecurityPlan) -> List[str]:
        """Validate SSP-specific business rules"""
        errors = []
        
        if not ssp.import_profile:
            errors.append("SSP must import a profile")
        
        if not ssp.system_characteristics:
            errors.append("System characteristics are required")
        
        if not ssp.control_implementation:
            errors.append("Control implementation is required")
        
        return errors
```

### OSCAL Transformation Pipeline

#### Document Transformation Engine
```python
class OSCALTransformer:
    """
    OSCAL document transformation and conversion engine
    """
    
    def __init__(self):
        self.transformation_rules = self._load_transformation_rules()
    
    def transform_external_to_oscal(self, source_data: Dict[str, Any], 
                                   source_format: str, target_type: str) -> OSCALDocument:
        """Transform external format to OSCAL"""
        
        if source_format == "nist_xml" and target_type == "catalog":
            return self._transform_nist_xml_to_catalog(source_data)
        elif source_format == "iso_excel" and target_type == "catalog":
            return self._transform_iso_excel_to_catalog(source_data)
        else:
            raise ValueError(f"Unsupported transformation: {source_format} to {target_type}")
    
    def _transform_nist_xml_to_catalog(self, nist_data: Dict[str, Any]) -> OSCALCatalog:
        """Transform NIST XML format to OSCAL catalog"""
        catalog = OSCALCatalog()
        
        # Extract metadata
        catalog.metadata["title"] = nist_data.get("title", "NIST Framework")
        catalog.metadata["version"] = nist_data.get("version", "1.0.0")
        
        # Transform control families to groups
        for family in nist_data.get("families", []):
            controls = []
            
            for control_data in family.get("controls", []):
                control = {
                    "id": control_data.get("id"),
                    "title": control_data.get("title"),
                    "parts": self._transform_control_parts(control_data.get("parts", []))
                }
                controls.append(control)
            
            catalog.add_control_group(
                family.get("id"),
                family.get("title"),
                controls
            )
        
        return catalog
    
    def resolve_profile(self, profile: Dict[str, Any]) -> OSCALCatalog:
        """Resolve OSCAL profile to create merged catalog"""
        resolved_catalog = OSCALCatalog()
        
        # Process imports
        for import_spec in profile.get("imports", []):
            source_catalog = self._load_catalog(import_spec.get("href"))
            
            # Apply include/exclude rules
            selected_controls = self._apply_selection_rules(
                source_catalog, 
                import_spec.get("include-controls", []),
                import_spec.get("exclude-controls", [])
            )
            
            # Apply modifications
            modified_controls = self._apply_modifications(
                selected_controls,
                profile.get("modify", {})
            )
            
            # Merge into resolved catalog
            self._merge_controls(resolved_catalog, modified_controls)
        
        return resolved_catalog
```

### OSCAL Data Access Layer

#### Repository Pattern Implementation
```python
from abc import ABC, abstractmethod
from typing import List, Optional

class OSCALRepository(ABC):
    """
    Abstract repository for OSCAL document persistence
    """
    
    @abstractmethod
    def save(self, document: OSCALDocument) -> str:
        """Save OSCAL document and return ID"""
        pass
    
    @abstractmethod
    def find_by_uuid(self, uuid: str) -> Optional[OSCALDocument]:
        """Find document by UUID"""
        pass
    
    @abstractmethod
    def find_by_type(self, document_type: str) -> List[OSCALDocument]:
        """Find documents by type"""
        pass
    
    @abstractmethod
    def update(self, document: OSCALDocument) -> bool:
        """Update existing document"""
        pass
    
    @abstractmethod
    def delete(self, uuid: str) -> bool:
        """Delete document by UUID"""
        pass

class MongoOSCALRepository(OSCALRepository):
    """
    MongoDB implementation of OSCAL repository
    """
    
    def __init__(self, database_connection):
        self.db = database_connection
        self.collection = self.db.oscal_documents
    
    def save(self, document: OSCALDocument) -> str:
        """Save OSCAL document to MongoDB"""
        doc_data = {
            "uuid": document.uuid,
            "document_type": document.document_type,
            "content": document.to_oscal_dict(),
            "metadata": document.metadata,
            "blockchain_hash": document.blockchain_hash,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        result = self.collection.insert_one(doc_data)
        return str(result.inserted_id)
    
    def find_by_uuid(self, uuid: str) -> Optional[OSCALDocument]:
        """Find document by UUID"""
        doc_data = self.collection.find_one({"uuid": uuid})
        
        if not doc_data:
            return None
        
        return self._create_document_from_data(doc_data)
    
    def _create_document_from_data(self, doc_data: Dict[str, Any]) -> OSCALDocument:
        """Create OSCAL document instance from database data"""
        document_type = doc_data["document_type"]
        
        if document_type == "catalog":
            document = OSCALCatalog()
        elif document_type == "system-security-plan":
            document = OSCALSystemSecurityPlan()
        else:
            raise ValueError(f"Unknown document type: {document_type}")
        
        document.from_oscal_dict(doc_data["content"])
        document.blockchain_hash = doc_data.get("blockchain_hash")
        
        return document
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Implementation Team
