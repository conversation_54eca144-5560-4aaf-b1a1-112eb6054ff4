<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:26 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Package org.activiti.engine.delegate.event.impl (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package org.activiti.engine.delegate.event.impl (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package org.activiti.engine.delegate.event.impl" class="title">Uses of Package<br>org.activiti.engine.delegate.event.impl</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../../org/activiti/engine/delegate/event/impl/package-summary.html">org.activiti.engine.delegate.event.impl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine.delegate.event.impl">org.activiti.engine.delegate.event.impl</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.delegate.event.impl">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../../org/activiti/engine/delegate/event/impl/package-summary.html">org.activiti.engine.delegate.event.impl</a> used by <a href="../../../../../../org/activiti/engine/delegate/event/impl/package-summary.html">org.activiti.engine.delegate.event.impl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../org/activiti/engine/delegate/event/impl/class-use/ActivitiActivityEventImpl.html#org.activiti.engine.delegate.event.impl">ActivitiActivityEventImpl</a>
<div class="block">Implementation of an <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiActivityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiActivityEvent</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../org/activiti/engine/delegate/event/impl/class-use/ActivitiEntityEventImpl.html#org.activiti.engine.delegate.event.impl">ActivitiEntityEventImpl</a>
<div class="block">Base class for all <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> implementations, related to entities.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../org/activiti/engine/delegate/event/impl/class-use/ActivitiEntityWithVariablesEventImpl.html#org.activiti.engine.delegate.event.impl">ActivitiEntityWithVariablesEventImpl</a>
<div class="block">Base class for all <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a> implementations, related to entities with variables.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../org/activiti/engine/delegate/event/impl/class-use/ActivitiEventImpl.html#org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a>
<div class="block">Base class for all <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> implementations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../org/activiti/engine/delegate/event/impl/class-use/ActivitiEventSupport.html#org.activiti.engine.delegate.event.impl">ActivitiEventSupport</a>
<div class="block">Class that allows adding and removing event listeners and dispatching events
 to the appropriate listeners.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
