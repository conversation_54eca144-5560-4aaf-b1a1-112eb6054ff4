<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Delegate<PERSON>el<PERSON> (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DelegateHelper (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DelegateHelper.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/delegate/DelegateTask.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/delegate/DelegateHelper.html" target="_top">Frames</a></li>
<li><a href="DelegateHelper.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate</div>
<h2 title="Class DelegateHelper" class="title">Class DelegateHelper</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.delegate.DelegateHelper</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DelegateHelper</span>
extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Class that provides helper operations for use in the <a href="../../../../org/activiti/engine/delegate/JavaDelegate.html" title="interface in org.activiti.engine.delegate"><code>JavaDelegate</code></a>,
 <code>ActivityBehavior</code>, <a href="../../../../org/activiti/engine/delegate/ExecutionListener.html" title="interface in org.activiti.engine.delegate"><code>ExecutionListener</code></a> and <a href="../../../../org/activiti/engine/delegate/TaskListener.html" title="interface in org.activiti.engine.delegate"><code>TaskListener</code></a>
 interfaces.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html#DelegateHelper--">DelegateHelper</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate">Expression</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html#createExpressionForField-org.activiti.bpmn.model.FieldExtension-">createExpressionForField</a></span>(org.activiti.bpmn.model.FieldExtension&nbsp;fieldExtension)</code>
<div class="block">Creates an <a href="../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate"><code>Expression</code></a> for the <code>FieldExtension</code>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static org.activiti.bpmn.model.BpmnModel</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html#getBpmnModel-org.activiti.engine.delegate.DelegateExecution-">getBpmnModel</a></span>(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>
<div class="block">Returns the <code>BpmnModel</code> matching the process definition bpmn model
 for the process definition of the passed <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.bpmn.model.ExtensionElement&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html#getExtensionElements-org.activiti.engine.delegate.DelegateExecution-">getExtensionElements</a></span>(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>
<div class="block">Returns for the activityId of the passed <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a> the
 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util"><code>Map</code></a> of <code>ExtensionElement</code> instances.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static org.activiti.bpmn.model.FieldExtension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html#getField-org.activiti.engine.delegate.DelegateExecution-java.lang.String-">getField</a></span>(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldName)</code>
<div class="block">Returns the <code>FieldExtension</code> matching the provided 'fieldName' which is
 defined for the current activity of the provided <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate">Expression</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html#getFieldExpression-org.activiti.engine.delegate.DelegateExecution-java.lang.String-">getFieldExpression</a></span>(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldName)</code>
<div class="block">Returns the <a href="../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate"><code>Expression</code></a> for the field defined for the current activity
 of the provided <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.bpmn.model.FieldExtension&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html#getFields-org.activiti.engine.delegate.DelegateExecution-">getFields</a></span>(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>
<div class="block">Returns the list of field extensions, represented as instances of <code>FieldExtension</code>,
 for the current activity of the passed <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static org.activiti.bpmn.model.FlowElement</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html#getFlowElement-org.activiti.engine.delegate.DelegateExecution-">getFlowElement</a></span>(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>
<div class="block">Returns the current <code>FlowElement</code> where the <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a> is currently at.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DelegateHelper--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DelegateHelper</h4>
<pre>public&nbsp;DelegateHelper()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBpmnModel-org.activiti.engine.delegate.DelegateExecution-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBpmnModel</h4>
<pre>public static&nbsp;org.activiti.bpmn.model.BpmnModel&nbsp;getBpmnModel(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</pre>
<div class="block">Returns the <code>BpmnModel</code> matching the process definition bpmn model
 for the process definition of the passed <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</li>
</ul>
<a name="getFlowElement-org.activiti.engine.delegate.DelegateExecution-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFlowElement</h4>
<pre>public static&nbsp;org.activiti.bpmn.model.FlowElement&nbsp;getFlowElement(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</pre>
<div class="block">Returns the current <code>FlowElement</code> where the <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a> is currently at.</div>
</li>
</ul>
<a name="getExtensionElements-org.activiti.engine.delegate.DelegateExecution-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtensionElements</h4>
<pre>public static&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.bpmn.model.ExtensionElement&gt;&gt;&nbsp;getExtensionElements(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</pre>
<div class="block">Returns for the activityId of the passed <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a> the
 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util"><code>Map</code></a> of <code>ExtensionElement</code> instances. These represent the
 extension elements defined in the BPMN 2.0 XML as part of that particular
 activity.</div>
</li>
</ul>
<a name="getFields-org.activiti.engine.delegate.DelegateExecution-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFields</h4>
<pre>public static&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.bpmn.model.FieldExtension&gt;&nbsp;getFields(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</pre>
<div class="block">Returns the list of field extensions, represented as instances of <code>FieldExtension</code>,
 for the current activity of the passed <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</li>
</ul>
<a name="getField-org.activiti.engine.delegate.DelegateExecution-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getField</h4>
<pre>public static&nbsp;org.activiti.bpmn.model.FieldExtension&nbsp;getField(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution,
                                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldName)</pre>
<div class="block">Returns the <code>FieldExtension</code> matching the provided 'fieldName' which is
 defined for the current activity of the provided <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.
 
 Returns null if no such <code>FieldExtension</code> can be found.</div>
</li>
</ul>
<a name="createExpressionForField-org.activiti.bpmn.model.FieldExtension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createExpressionForField</h4>
<pre>public static&nbsp;<a href="../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate">Expression</a>&nbsp;createExpressionForField(org.activiti.bpmn.model.FieldExtension&nbsp;fieldExtension)</pre>
<div class="block">Creates an <a href="../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate"><code>Expression</code></a> for the <code>FieldExtension</code>.</div>
</li>
</ul>
<a name="getFieldExpression-org.activiti.engine.delegate.DelegateExecution-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getFieldExpression</h4>
<pre>public static&nbsp;<a href="../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate">Expression</a>&nbsp;getFieldExpression(<a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution,
                                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldName)</pre>
<div class="block">Returns the <a href="../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate"><code>Expression</code></a> for the field defined for the current activity
 of the provided <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.
 
  Returns null if no such field was found in the process definition xml.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DelegateHelper.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/delegate/DelegateTask.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/delegate/DelegateHelper.html" target="_top">Frames</a></li>
<li><a href="DelegateHelper.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
