{"id": "Flowable", "realm": "Flowable", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "ccdf96b7-999a-4b09-b864-b2eb66af9384", "name": "access-task", "description": "For accessing the Task Application", "composite": false, "clientRole": false, "containerId": "Flowable", "attributes": {}}, {"id": "7a558a6c-9f84-4ef3-b31e-cc0a8f3bb965", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Flowable", "attributes": {}}, {"id": "c471459d-bf0b-4bae-8c86-2b973390c836", "name": "access-idm", "description": "For accessing IDM Application", "composite": false, "clientRole": false, "containerId": "Flowable", "attributes": {}}, {"id": "a825481b-27ad-4ca5-a291-331cf8240c7f", "name": "access-admin", "description": "For accessing the Admin Application", "composite": false, "clientRole": false, "containerId": "Flowable", "attributes": {}}, {"id": "9af63f32-4fe2-4d80-8c43-5d9eaac33461", "name": "access-modeler", "description": "For accessing the Modeler Application", "composite": false, "clientRole": false, "containerId": "Flowable", "attributes": {}}, {"id": "b2ee562b-3106-42c5-921e-9c93a02210da", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Flowable", "attributes": {}}], "client": {"realm-management": [{"id": "9d641827-1cba-4817-9886-8cbb2d27076a", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "7e7352c8-a25a-49f0-99e4-590fcd93716d", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "fed35834-3a7f-4012-bd6d-ffd794188d68", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "f479a5b5-8419-4c61-b487-2633d02fcecb", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-identity-providers", "query-clients", "create-client", "manage-authorization", "view-users", "view-authorization", "impersonation", "view-clients", "view-events", "query-realms", "manage-realm", "manage-clients", "manage-events", "manage-identity-providers", "query-users", "query-groups", "view-realm", "manage-users"]}}, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "e6a11061-4cae-4a02-8736-a720dbe0ea61", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "afa6c650-8adf-4812-ad8d-14c60ba34365", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "7fbab47f-3256-4ba1-b379-141fb8688d68", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "ce724d56-85bb-4659-b4cb-6b2d3cfb0941", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "7737d919-a21c-4f9b-ad64-eb7c1504d70f", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "dca2e5b0-8459-49dc-bb86-a2083e3c34f8", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "77eed418-865f-4286-8a0e-4622e1ad58cc", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "f3c55ff2-bb04-4ef9-b868-a0dabdc9af4b", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "31daa563-4372-4a6e-9d2e-7953753361c9", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "fb412587-46b7-49e3-9ea1-b38a5fe0de55", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "828a5c58-d671-4275-b999-fce820e56ea8", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "20f47997-1d9c-482e-a6d8-7fa03028563a", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "811d88ce-fc30-46cb-a4c9-eb0184820854", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "ea263a09-0a5c-4e54-84b0-************", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}, {"id": "********-eed2-4e96-b072-c7a3b1c3a9bc", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "attributes": {}}], "security-admin-console": [], "flowable-client": [], "admin-cli": [], "account-console": [], "broker": [{"id": "916a1b37-d792-4f82-a924-c5ddd6e69b46", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "2f7b61f5-f1ea-4b1d-abfa-b92f453f9e3e", "attributes": {}}], "account": [{"id": "7cd15a74-6186-4d43-95eb-830969f91454", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "e1800222-7454-41fe-a567-02c5ee46ba7a", "attributes": {}}, {"id": "ccb510a7-35c8-404b-8907-c8bc8e93bb35", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "e1800222-7454-41fe-a567-02c5ee46ba7a", "attributes": {}}, {"id": "c27d6c85-7fe0-4ea8-8ba4-b5a4127ac93f", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "e1800222-7454-41fe-a567-02c5ee46ba7a", "attributes": {}}, {"id": "13b479aa-f73c-4fbe-a8c6-46ec9bfaa30e", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "e1800222-7454-41fe-a567-02c5ee46ba7a", "attributes": {}}, {"id": "5567c297-9d1f-4588-a0d2-810cc0c63608", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "e1800222-7454-41fe-a567-02c5ee46ba7a", "attributes": {}}, {"id": "0039d668-d454-41da-bf30-9b3192624416", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "e1800222-7454-41fe-a567-02c5ee46ba7a", "attributes": {}}]}}, "groups": [{"id": "1fa3e1d0-1bec-4bd6-912c-53253716e7be", "name": "flowableUsers", "path": "/flowableUsers", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["uma_authorization", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "a061f0ec-898a-46c3-baab-64f39ee4b965", "createdTimestamp": 1597765831342, "username": "admin@flowable", "enabled": true, "totp": false, "emailVerified": true, "firstName": "Admin", "lastName": "Admin", "credentials": [{"id": "fef1f13f-f4d3-4a75-b69c-bdf7f8b3c4ad", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"mQpMnh3MDdFjT4vvPR5N55+7UMC5y7VZSirt7Cjq/3LuxQMXGiDMzDUW7Vcwtdw42zSm2wskmfSnSVl6BoYzMQ==\",\"salt\":\"dkoKT3nlv2VybjrfgH/JuA==\"}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\"}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["access-task", "uma_authorization", "access-idm", "access-admin", "access-modeler", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": ["/flowableUsers"]}, {"id": "cc129875-f57c-4890-a49e-219b833abaf6", "createdTimestamp": *************, "username": "modeler@flowable", "enabled": true, "totp": false, "emailVerified": true, "firstName": "Workflow", "lastName": "Workflow", "credentials": [{"id": "b1fb2f48-357d-42d3-a2f0-5319470cecdc", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"/osFDj3z9Sdd6O/rk7Cgf2o8iqKOicfM1debqzg3ftS4TGYeXsKFRShp1m4UcdtKjYiR7BYyTyJB3i9YiOHd9Q==\",\"salt\":\"FQXhP/Wmf4wkEO8nSmGYIA==\"}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\"}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "access-modeler", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": ["/flowableUsers"]}, {"id": "4f7a2399-35d7-4283-a3c6-71e592809217", "createdTimestamp": *************, "username": "workflow@flowable", "enabled": true, "totp": false, "emailVerified": true, "firstName": "Workflow", "lastName": "Workflow", "credentials": [{"id": "cabd2fc3-596a-4f77-af2f-f2753dacec96", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"3V+pz79HyEVWQIKV4lDadMTU2tWh7cOaAy9kOML95pBJ6vPMgBgsEIqIJ1/sDlWIjufKL7TlTqZrRdC+u26BUg==\",\"salt\":\"/dxp9LKjHURC0D6cXhHtKA==\"}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\"}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["access-task", "uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": ["/flowableUsers"]}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "e1800222-7454-41fe-a567-02c5ee46ba7a", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Flowable/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "defaultRoles": ["view-profile", "manage-account"], "redirectUris": ["/realms/Flowable/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "8aebe2c1-cfe0-4dfb-933d-3bdf670d7dc6", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Flowable/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/realms/Flowable/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "69c6f50b-3353-4f1f-9393-c86ea9f1429b", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "91f25392-c9f6-4884-9a3f-f7ba69aa2e2d", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "2f7b61f5-f1ea-4b1d-abfa-b92f453f9e3e", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "81293e16-bb51-441b-b7c7-ee9fdb3529dd", "clientId": "flowable-client", "rootUrl": "http://localhost:8080/flowable-ui", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "5a63612b-c419-40fe-8d01-2f91689bb4c0", "redirectUris": ["http://localhost:8080/flowable-ui/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "88e5e796-3217-4408-81c6-156146da6bba", "name": "User Groups Mapper", "protocol": "openid-connect", "protocolMapper": "oidc-group-membership-mapper", "consentRequired": false, "config": {"full.path": "false", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "userGroups", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "54ee97bf-9fa4-4915-9cf3-91c0279117dd", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "0895727a-d27d-499c-a26c-90f2950c5701", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/Flowable/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/admin/Flowable/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "751e570e-896e-41fc-a751-3d537a22a37d", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "66b503ac-0637-417a-b85c-6c97c038d307", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "6766bd92-235e-4641-be5d-7c1793e0e409", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "f30ac9b2-51ff-4d45-869c-0bdc566cefe8", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "da92125d-9001-43a2-b92e-e9e6acaef0d2", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "0c8f46f6-30ba-4bfe-8993-5ccb47fcda10", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "b85bf550-9469-475f-9c66-0efc69333868", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "426944e8-20f0-42fb-81b4-2e5fe2214bc4", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "6b2a256e-556d-4b06-8592-9e37d2af72e7", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "1a0738d3-2e72-462e-b5a2-97298eeab2d8", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "81925b50-1441-4590-abad-36bbfbe43856", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "90eb911d-1f22-4c5d-a32f-d5323377a06c", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "645223a8-db06-4a2b-a622-2e14ee52a575", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "f890e6cf-d467-4475-a789-70058f19a134", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "ec8b5b9d-e47e-47da-a723-b1377dfd6744", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "df04d526-e10b-4b67-b7b5-4021ddecc7ee", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "4a7f7741-4451-45fd-aeb7-f00d1fddcdc9", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "93b5a0a4-43ae-4dcd-8173-09f13e4d1ec7", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "bbfe7213-4da1-4a74-a12b-9adbe075a245", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "6f9e4c18-7420-4201-a8db-b3381579bcc2", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "f1f4a878-c09b-45e3-9a49-ee4cce333935", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "764f3b9a-4d6b-4d39-bca8-3a331a4f259b", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "d40800ac-ba34-420f-a77a-9103f609d181", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "14f71def-bd80-4051-beb7-1ae2eda65bcf", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "3628ebb5-61e6-4991-8fed-208464fdf0c1", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "7d4f6545-b23c-49e4-81b4-89003edc3b1c", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "981d13f8-7277-4f4d-9a40-7d69b4c37c2e", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "3bad20d1-a304-4f2b-a2dc-387e18c2a702", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}]}, {"id": "53fa8d3a-89d5-4f46-8f45-037fb30e63cc", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "0f1ac352-d7f1-426e-b5c1-9669ce567550", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "4cc94c6a-2147-4077-876e-61fdb5290aa8", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "17d84b6b-b0d8-4359-a87f-109ff5e49ee2", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "0d6e00a0-e680-4873-b368-5c106d3fe412", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "c432cfbe-3540-4045-be79-d3d532a49963", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "d65039e1-5da9-4e8b-a836-c728e88021fa", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "067b3133-4c08-4f54-b3c8-1ab87fc652be", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["roles", "role_list", "web-origins", "email", "profile"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "ac81329f-7b78-43ac-8f11-243cb1b46e83", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "aed3df6d-54dd-47c1-80b5-7059553a2cba", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "b54e92fc-d669-4b57-a7ff-9347bfc2eaa5", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "6c48e605-e62c-4e8e-bd57-db53a124a41a", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper"]}}, {"id": "34ceb2f1-551a-4c60-b077-60b286de82dc", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "saml-user-attribute-mapper", "oidc-address-mapper"]}}, {"id": "8f2de24b-93bf-4c97-bada-b5314cbaa771", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "ff822faf-42f4-499b-94c0-3653cd618597", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "f4ac827d-eac6-4edf-a9d1-ba2adf222eef", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}], "org.keycloak.keys.KeyProvider": [{"id": "e20c335e-4241-4e2e-bd24-741d3019531d", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["512afa65-6fec-4eb0-858f-7cb19e646f1f"], "secret": ["PDF9LYswZYqMb7hGWSukAAMXi6l4bbdpTER9AAomavZpTBG1Ih_Hp3xL5ur7IWy3jT8VBgGXU8T5ULAzUHJyfA"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "113a92d0-790a-4a9e-b476-7e924a75bf52", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEogIBAAKCAQEAnD2+scxX1zC/K/Tfj8fDbqz8q6wcfCiD0JEGJlIXxm1Dd6g8Ylr2eG1QAmcCt9Wk1WpNXqA+5T2OiDJ7IIZ1cjrdMG+WyLXaEGfDquEYIsw1PQgvnE2rnOYI2DDmekOpCr4ENSA9kIJJXSeslZjLtUyK5tJBRlhMnQtL50ihjjAV7j6RcmAltcWprMbUgMgS3tD6cThm/3aNutwakKHPKvwB34FPjhX5WpP8Tx2kG+0hFKTlU/FGrPPJ4K2DAcQDyJU+GrLsA5lSJWdtuVsETeW7/6adhM1js5XzKQzJNQ9AejTFjumkKkkNTVgroaXhHATaPpKapy70fKUDy4hXHwIDAQABAoIBAB7NyaASUqs5kCdhcwZM80oJzxMlrsQH1N01CopZmxhjCG85+xXFK1zY6HJgztdhxsrQ14o1e9EEjuppUX87I0KvOlbHWWoe+GxNmPVCxdhufHyY9ZISeihH7ix5j6dBWb2lcvzqa1jnZwF3xf4Ejv0ipywU/guZcxlvVbq2xitbHcOyYRBGiUeMTK3YltX4UH56LfJlI8dRnb0vXO0ZXhqBpbbOoMZ61RZVULBurta3DhoegAcX3OnvxROyPN9S9n0L0D547mBsihMvcegAjVV5nd3/XqgbGsUXQvp8YkumYfQTirpxuaeumMWfKa++rJ8qt6Wgd1dx6xsfYeWkY1ECgYEA6wdvw5TbKIoZ6vmE+U/ohvL1ANEgvUie01Br4FqV1vlPbx33ZMCu3wQGgv4PWrntwSjlCt2lJOlvcndZ+Lf9ORk6JufR6UdnmsTytCjyQeho22fFw+Ns4++b3YJ+pRrSy3AF5dlN03rT+GDOuh56LZ+g5e852cUDsEqTVv/tS5cCgYEAqi6gRNPg7cEvmBAD2Uces0YDeSX8KNLPdxOJrzxSUgcAolwC+ypfKqQDKTLz8z/DZ75TpzwHxyT2TcsEPQwQLoVi81UDRRSzARDRgYZf+cW9KauRRPJG502JML1VKfx2yy0kiRapgPiFiX9j1TEjkhW22rmUh22qRlWkmwrr4bkCgYBCTT7CNeP9c7gMCYfxrREzwKZNLLHHtAzaH/3OtL9AfeRivFlmGtU6JLCRk4pRVdI0g4zZm91SpGfEKtc+yuisiulUwGD/Jh2cE6NEH6j33tB11Lh9olK9moKl4oJHoWGF8nw3cy35UtUpZGXtK1vcMwP42ZvAS0vXF7KpAFm40QKBgEnxJkuhb7p91KsSUknGn+0uv3WU7oHiTqgjQhB0eiQPzNO+fMEagOzrBECTVfFbxBBAXuJrr8l0ItxcGERaCHHeHMczKc+LFdehshXAB9VjeDQxylWVmv8G7lPR/73VZtwSiixSP2W9FXBPfFX2GslvS9qvBXzuRix3K6T90s1pAoGAQvHPVHxBhUpKuzRrC1+SjPf8++l9cv5Z78qyXo4FfDW+6UVkrpDGPPZWYw2c0Q3Oo6W/tfUTX6h+gTrw6RBpyjGSR5cNCbbi/kxWAUKLzjNiezGuXLkAh55RkfXUdvlJKypj4VtY/WPhkAC/T6EfnNkW09jicX1bZtDRhBWP2EQ="], "certificate": ["MIICnzCCAYcCBgF0AkFZ5zANBgkqhkiG9w0BAQsFADATMREwDwYDVQQDDAhGbG93YWJsZTAeFw0yMDA4MTgxNTQ2MTFaFw0zMDA4MTgxNTQ3NTFaMBMxETAPBgNVBAMMCEZsb3dhYmxlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnD2+scxX1zC/K/Tfj8fDbqz8q6wcfCiD0JEGJlIXxm1Dd6g8Ylr2eG1QAmcCt9Wk1WpNXqA+5T2OiDJ7IIZ1cjrdMG+WyLXaEGfDquEYIsw1PQgvnE2rnOYI2DDmekOpCr4ENSA9kIJJXSeslZjLtUyK5tJBRlhMnQtL50ihjjAV7j6RcmAltcWprMbUgMgS3tD6cThm/3aNutwakKHPKvwB34FPjhX5WpP8Tx2kG+0hFKTlU/FGrPPJ4K2DAcQDyJU+GrLsA5lSJWdtuVsETeW7/6adhM1js5XzKQzJNQ9AejTFjumkKkkNTVgroaXhHATaPpKapy70fKUDy4hXHwIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCDf8O3FNIKWmJzKqUA/LEN0pi+o9SQdS8stxM84u3kCrYBDTAXC+QOzJLgrgDNDOfmvKktx45tO5INkPgEYsNeWB5OphYRWFjse6rCqsDOLqrbLzSltycBT5dxyv2Y/lcDTdI6oLVirnjZ3RV9dP9joBsLLZqIDJWwzxmopwhVWo8nYuYOswOiDvSj/yBQMj5idutMpZG9qysgdsc2s9GjGueaakoG7oNpORt93C9lNt7tsO2zixCXpXDS1sSOUfMlwV31sAuvUJzJsUFu3BkEQgUF0a/B/nxeEMtjwNPwQRpzj8Oj1O88+xkxbgfn3jnVPNwanbqk1+wk9QPIIe65"], "priority": ["100"]}}, {"id": "8c0d9418-7dd2-4e47-8e0a-43d78d900a83", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["e5a51460-cf1f-49a1-8e21-f8d9dcfa7213"], "secret": ["BG98AzU1X01DiJjr_eGGFw"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "b3e1e418-09ef-4e4e-a95a-fbe81262d3ad", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "82935d6b-6546-4ea5-8368-8bd488296ff0", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "f43342a6-dcd9-457a-8490-5827dd3cda07", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "4c5915fd-15db-4dcb-a4ad-1d0351ba12b4", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "9ee59c5b-2d37-42c7-8660-e905f28e9cf7", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "3056c855-b6c8-4636-bf5c-f4d02c8789fe", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Account verification options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "52df08a6-bcec-44ce-b37c-476cb7755215", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "4644bbbe-db00-4696-a4a5-2fef70aa8ded", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "6800c85e-3a1c-466f-933c-0bb77ecb0e86", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "69194cff-8676-469f-9b24-193e850a37f9", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e950a3df-c1b6-4840-af74-067abb3d2c74", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b9da8423-2912-48be-a4fc-e49f6c6d7699", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 30, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "54e5cf78-990c-4950-81d8-636284e1899f", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "cbbe3d9d-1069-47fb-8aee-0f74e3b856de", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "User creation or linking", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "4c4e432b-d1e6-451d-8261-4fa4c082d613", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "7545d1b5-763e-4d57-908e-985b00843844", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Authentication Options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "563b8bdd-2e21-4bcb-940b-767c8183dc4e", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "9d49c58d-4928-461d-bb3b-0098c22ca595", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "3fc05545-b358-43d5-9c03-ce304de067c5", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 40, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "ec41863b-2e05-4210-858a-54ba14855ee8", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "92393108-b8bd-48b2-ad9b-46629f7c895b", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "6d3997b0-d580-42ae-bb76-362ea8671b8b", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"clientOfflineSessionMaxLifespan": "0", "clientSessionIdleTimeout": "0", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0"}, "keycloakVersion": "11.0.0", "userManagedAccessAllowed": false}