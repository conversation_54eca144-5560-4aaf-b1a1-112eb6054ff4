<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ModelQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ModelQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ModelQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/repository/NativeDeploymentQuery.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/ModelQuery.html" target="_top">Frames</a></li>
<li><a href="ModelQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.repository</div>
<h2 title="Interface ModelQuery" class="title">Interface ModelQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>,<a href="../../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository">Model</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ModelQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>,<a href="../../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository">Model</a>&gt;</pre>
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository"><code>Model</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tijs Rademakers, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#deployed--">deployed</a></span>()</code>
<div class="block">Only select models that are deployed (ie deploymentId != null)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#deploymentId-java.lang.String-">deploymentId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block">Only select models that are the source for the provided deployment</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#latestVersion--">latestVersion</a></span>()</code>
<div class="block">Only select models which has the highest version.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelCategory-java.lang.String-">modelCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelCategory)</code>
<div class="block">Only select models with the given category.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelCategoryLike-java.lang.String-">modelCategoryLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelCategoryLike)</code>
<div class="block">Only select models where the category matches the given parameter.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelCategoryNotEquals-java.lang.String-">modelCategoryNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;categoryNotEquals)</code>
<div class="block">Only select models that have a different category then the given one.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelId-java.lang.String-">modelId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</code>
<div class="block">Only select model with the given id.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelKey-java.lang.String-">modelKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</code>
<div class="block">Only selects models with the given key.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelName-java.lang.String-">modelName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelName)</code>
<div class="block">Only select models with the given name.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelNameLike-java.lang.String-">modelNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelNameLike)</code>
<div class="block">Only select models where the name matches the given parameter.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelTenantId-java.lang.String-">modelTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select models that have the given tenant id.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelTenantIdLike-java.lang.String-">modelTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select models with a tenant id like the given one.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelVersion-java.lang.Integer-">modelVersion</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;modelVersion)</code>
<div class="block">Only select model with a certain version.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#modelWithoutTenantId--">modelWithoutTenantId</a></span>()</code>
<div class="block">Only select models that do not have a tenant id.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#notDeployed--">notDeployed</a></span>()</code>
<div class="block">Only select models that are not yet deployed</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#orderByCreateTime--">orderByCreateTime</a></span>()</code>
<div class="block">Order by the creation time of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#orderByLastUpdateTime--">orderByLastUpdateTime</a></span>()</code>
<div class="block">Order by the last update time of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#orderByModelCategory--">orderByModelCategory</a></span>()</code>
<div class="block">Order by the category of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#orderByModelId--">orderByModelId</a></span>()</code>
<div class="block">Order by the id of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#orderByModelKey--">orderByModelKey</a></span>()</code>
<div class="block">Order by the key of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#orderByModelName--">orderByModelName</a></span>()</code>
<div class="block">Order by the name of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#orderByModelVersion--">orderByModelVersion</a></span>()</code>
<div class="block">Order by the version of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ModelQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="modelId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</pre>
<div class="block">Only select model with the given id.</div>
</li>
</ul>
<a name="modelCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelCategory</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelCategory)</pre>
<div class="block">Only select models with the given category.</div>
</li>
</ul>
<a name="modelCategoryLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelCategoryLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelCategoryLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelCategoryLike)</pre>
<div class="block">Only select models where the category matches the given parameter.
 The syntax that should be used is the same as in SQL, eg. %activiti%</div>
</li>
</ul>
<a name="modelCategoryNotEquals-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelCategoryNotEquals</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelCategoryNotEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;categoryNotEquals)</pre>
<div class="block">Only select models that have a different category then the given one.</div>
</li>
</ul>
<a name="modelName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelName</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelName)</pre>
<div class="block">Only select models with the given name.</div>
</li>
</ul>
<a name="modelNameLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelNameLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelNameLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelNameLike)</pre>
<div class="block">Only select models where the name matches the given parameter.
 The syntax that should be used is the same as in SQL, eg. %activiti%</div>
</li>
</ul>
<a name="modelKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelKey</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</pre>
<div class="block">Only selects models with the given key.</div>
</li>
</ul>
<a name="modelVersion-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelVersion</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelVersion(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;modelVersion)</pre>
<div class="block">Only select model with a certain version.</div>
</li>
</ul>
<a name="latestVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>latestVersion</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;latestVersion()</pre>
<div class="block">Only select models which has the highest version.
 
 Note: if modelKey(key) is not used in this query, all the models with
 the highest version for each key will be returned (similar to process definitions)</div>
</li>
</ul>
<a name="deploymentId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;deploymentId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</pre>
<div class="block">Only select models that are the source for the provided deployment</div>
</li>
</ul>
<a name="deployed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deployed</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;deployed()</pre>
<div class="block">Only select models that are deployed (ie deploymentId != null)</div>
</li>
</ul>
<a name="notDeployed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>notDeployed</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;notDeployed()</pre>
<div class="block">Only select models that are not yet deployed</div>
</li>
</ul>
<a name="modelTenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelTenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Only select models that have the given tenant id.</div>
</li>
</ul>
<a name="modelTenantIdLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelTenantIdLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelTenantIdLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</pre>
<div class="block">Only select models with a tenant id like the given one.</div>
</li>
</ul>
<a name="modelWithoutTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modelWithoutTenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;modelWithoutTenantId()</pre>
<div class="block">Only select models that do not have a tenant id.</div>
</li>
</ul>
<a name="orderByModelCategory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByModelCategory</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;orderByModelCategory()</pre>
<div class="block">Order by the category of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByModelId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByModelId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;orderByModelId()</pre>
<div class="block">Order by the id of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByModelKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByModelKey</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;orderByModelKey()</pre>
<div class="block">Order by the key of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByModelVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByModelVersion</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;orderByModelVersion()</pre>
<div class="block">Order by the version of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByModelName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByModelName</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;orderByModelName()</pre>
<div class="block">Order by the name of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByCreateTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByCreateTime</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;orderByCreateTime()</pre>
<div class="block">Order by the creation time of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByLastUpdateTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByLastUpdateTime</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;orderByLastUpdateTime()</pre>
<div class="block">Order by the last update time of the models (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTenantId--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByTenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;orderByTenantId()</pre>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ModelQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/repository/NativeDeploymentQuery.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/ModelQuery.html" target="_top">Frames</a></li>
<li><a href="ModelQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
