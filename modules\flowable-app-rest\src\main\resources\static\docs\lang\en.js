'use strict';

/* jshint quotmark: double */
window.SwaggerTranslator.learn({
    "Warning: Deprecated":"Warning: Deprecated",
    "Implementation Notes":"Implementation Notes",
    "Response Class":"Response Class",
    "Status":"Status",
    "Parameters":"Parameters",
    "Parameter":"Parameter",
    "Value":"Value",
    "Description":"Description",
    "Parameter Type":"Parameter Type",
    "Data Type":"Data Type",
    "Response Messages":"Response Messages",
    "HTTP Status Code":"HTTP Status Code",
    "Reason":"Reason",
    "Response Model":"Response Model",
    "Request URL":"Request URL",
    "Response Body":"Response Body",
    "Response Code":"Response Code",
    "Response Headers":"Response Headers",
    "Hide Response":"Hide Response",
    "Headers":"Headers",
    "Try it out!":"Try it out!",
    "Show/Hide":"Show/Hide",
    "List Operations":"List Operations",
    "Expand Operations":"Expand Operations",
    "Raw":"Raw",
    "can't parse JSON.  Raw result":"can't parse JSON.  Raw result",
    "Example Value":"Example Value",
    "Model Schema":"Model Schema",
    "Model":"Model",
    "Click to set as parameter value":"Click to set as parameter value",
    "apply":"apply",
    "Username":"Username",
    "Password":"Password",
    "Terms of service":"Terms of service",
    "Created by":"Created by",
    "See more at":"See more at",
    "Contact the developer":"Contact the developer",
    "api version":"api version",
    "Response Content Type":"Response Content Type",
    "Parameter content type:":"Parameter content type:",
    "fetching resource":"fetching resource",
    "fetching resource list":"fetching resource list",
    "Explore":"Explore",
    "Show Swagger Petstore Example Apis":"Show Swagger Petstore Example Apis",
    "Can't read from server.  It may not have the appropriate access-control-origin settings.":"Can't read from server.  It may not have the appropriate access-control-origin settings.",
    "Please specify the protocol for":"Please specify the protocol for",
    "Can't read swagger JSON from":"Can't read swagger JSON from",
    "Finished Loading Resource Information. Rendering Swagger UI":"Finished Loading Resource Information. Rendering Swagger UI",
    "Unable to read api":"Unable to read api",
    "from path":"from path",
    "server returned":"server returned"
});
