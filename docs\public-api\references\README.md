# Flowable Rest API Documentation

In this folder you will retrieve all OpenAPI Specifications for Flowable Public REST API.

# References

**[OpenAPI](https://github.com/OAI/OpenAPI-Specification)** is a standard specification to describe a REST API. It's vendor neutral and backed by the [OpenAPI initiative](https://www.openapis.org/) which includes [many software companies](https://www.openapis.org/membership/members) (Google, Microsoft, IBM, Atlassian...). 

## Swagger Specification V2

You can retrieve the Swagger Specification (V2) for Flowable API by following links below.

| API Name | Syntax Validation  | 
|:---:|:---:|
| [Process API](swagger/process/flowable-swagger-process.yaml) | <img src="http://online.swagger.io/validator?url=https://raw.githubusercontent.com/flowable/flowable-engine/master/docs/public-api/references/swagger/process/flowable-swagger-process.yaml">  |  
| [Form API](swagger/form/flowable-swagger-form.yaml) |  <img src="http://online.swagger.io/validator?url=https://raw.githubusercontent.com/flowable/flowable-engine/master/docs/public-api/references/swagger/form/flowable-swagger-form.yaml"> |  
| [Decision API](swagger/decision/flowable-swagger-decision.yaml) |  <img src="http://online.swagger.io/validator?url=https://raw.githubusercontent.com/flowable/flowable-engine/master/docs/public-api/references/swagger/decision/flowable-swagger-decision.yaml"> | 
| [Content API](swagger/content/flowable-swagger-content.yaml) | <img src="http://online.swagger.io/validator?url=https://raw.githubusercontent.com/flowable/flowable-engine/master/docs/public-api/references/swagger/content/flowable-swagger-content.yaml">  | 


## OpenAPI Specification V3

You can retrieve the OpenApi Specification (V3) for Flowable API by following links below.


| API Name |
|:---:|
| [Process API](openapi/process/flowable-oas-process.yaml) |    
| [Form API](openapi/form/flowable-oas-form.yaml) | 
| [Decision API](openapi/decision/flowable-oas-decision.yaml) |  
| [Content API](openapi/content/flowable-oas-content.yaml) |   





