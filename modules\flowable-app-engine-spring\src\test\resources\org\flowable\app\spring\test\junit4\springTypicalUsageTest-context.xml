<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer" />

    <bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource">
        <property name="jdbcUrl" value="${jdbc.url:jdbc:h2:mem:flowable;DB_CLOSE_DELAY=1000}" />
        <property name="driverClassName" value="${jdbc.driver:org.h2.Driver}" />
        <property name="username" value="${jdbc.username:sa}" />
        <property name="password" value="${jdbc.password:}" />
    </bean>

    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <bean id="appEngineConfiguration" class="org.flowable.app.spring.SpringAppEngineConfiguration">
        <property name="dataSource" ref="dataSource"/>
        <property name="transactionManager" ref="transactionManager"/>
        <property name="databaseSchemaUpdate" value="true"/>
        <property name="idmEngineConfigurator" ref="idmEngineConfigurator" />
    </bean>

    <bean id="idmEngineConfigurator" class="org.flowable.idm.spring.configurator.SpringIdmEngineConfigurator" />

    <bean id="appEngine" class="org.flowable.app.spring.AppEngineFactoryBean">
        <property name="appEngineConfiguration" ref="appEngineConfiguration"/>
    </bean>

    <bean id="appRepositoryService" factory-bean="appEngine" factory-method="getAppRepositoryService"/>
    
    <tx:annotation-driven transaction-manager="transactionManager"/>

</beans>