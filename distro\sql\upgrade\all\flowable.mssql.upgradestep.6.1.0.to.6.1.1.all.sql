update ACT_GE_PROPERTY set VALUE_ = '6.1.1.0' where NAME_ = 'schema.version';

alter table ACT_RU_JOB add CREATE_TIME_ datetime2;
alter table ACT_RU_TIMER_JOB add CREATE_TIME_ datetime2;
alter table ACT_RU_SUSPENDED_JOB add CREATE_TIME_ datetime2;
alter table ACT_RU_DEADLETTER_JOB add CREATE_TIME_ datetime2;

update ACT_RU_JOB set CREATE_TIME_=CURRENT_TIMESTAMP;
update ACT_RU_TIMER_JOB set CREATE_TIME_=CURRENT_TIMESTAMP;
update ACT_RU_SUSPENDED_JOB set CREATE_TIME_=CURRENT_TIMESTAMP;
update ACT_RU_DEADLETTER_JOB set CREATE_TIME_=CURRENT_TIMESTAMP;

update ACT_ID_PROPERTY set VALUE_ = '6.1.1.0' where NAME_ = 'schema.version';

UPDATE [ACT_DMN_DATABASECHANGELOGLOCK] SET [LOCKED] = 1, [LOCKEDBY] = '*********** (***********)', [LOCKGRANTED] = '2019-03-13T21:39:23.976' WHERE [ID] = 1 AND [LOCKED] = 0

CREATE TABLE [ACT_DMN_HI_DECISION_EXECUTION] ([ID_] [varchar](255) NOT NULL, [DECISION_DEFINITION_ID_] [varchar](255), [DEPLOYMENT_ID_] [varchar](255), [START_TIME_] [datetime], [END_TIME_] [datetime], [INSTANCE_ID_] [varchar](255), [EXECUTION_ID_] [varchar](255), [ACTIVITY_ID_] [varchar](255), [FAILED_] [bit] CONSTRAINT [DF_ACT_DMN_HI_DECISION_EXECUTION_FAILED_] DEFAULT 0, [TENANT_ID_] [varchar](255), [EXECUTION_JSON_] [varchar](MAX), CONSTRAINT [PK_ACT_DMN_HI_DECISION_EXECUTION] PRIMARY KEY ([ID_]))

INSERT INTO [ACT_DMN_DATABASECHANGELOG] ([ID], [AUTHOR], [FILENAME], [DATEEXECUTED], [ORDEREXECUTED], [MD5SUM], [DESCRIPTION], [COMMENTS], [EXECTYPE], [CONTEXTS], [LABELS], [LIQUIBASE], [DEPLOYMENT_ID]) VALUES ('2', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', GETDATE(), 2, '7:15a6bda1fce898a58e04fe6ac2d89f54', 'createTable tableName=ACT_DMN_HI_DECISION_EXECUTION', '', 'EXECUTED', NULL, NULL, '3.5.3', '2509564704')

UPDATE [ACT_DMN_DATABASECHANGELOGLOCK] SET [LOCKED] = 0, [LOCKEDBY] = NULL, [LOCKGRANTED] = NULL WHERE [ID] = 1



UPDATE [ACT_FO_DATABASECHANGELOGLOCK] SET [LOCKED] = 1, [LOCKEDBY] = '*********** (***********)', [LOCKGRANTED] = '2019-03-13T21:39:31.344' WHERE [ID] = 1 AND [LOCKED] = 0

UPDATE [ACT_FO_DATABASECHANGELOGLOCK] SET [LOCKED] = 0, [LOCKEDBY] = NULL, [LOCKGRANTED] = NULL WHERE [ID] = 1



UPDATE [ACT_CO_DATABASECHANGELOGLOCK] SET [LOCKED] = 1, [LOCKEDBY] = '*********** (***********)', [LOCKGRANTED] = '2019-03-13T21:39:38.133' WHERE [ID] = 1 AND [LOCKED] = 0

UPDATE [ACT_CO_DATABASECHANGELOGLOCK] SET [LOCKED] = 0, [LOCKEDBY] = NULL, [LOCKGRANTED] = NULL WHERE [ID] = 1

