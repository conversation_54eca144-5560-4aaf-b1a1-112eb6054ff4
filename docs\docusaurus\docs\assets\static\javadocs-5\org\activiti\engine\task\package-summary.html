<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine.task (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.activiti.engine.task (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../org/activiti/engine/test/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/task/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.activiti.engine.task</h1>
<div class="docSummary">
<div class="block">Classes related to the <a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a></td>
<td class="colLast">
<div class="block">Any type of content that is be associated with
 a task or with a process instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a></td>
<td class="colLast">
<div class="block">User comments that form discussions around tasks.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/Event.html" title="interface in org.activiti.engine.task">Event</a></td>
<td class="colLast">
<div class="block">Exposes twitter-like feeds for tasks and process instances.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task">IdentityLink</a></td>
<td class="colLast">
<div class="block">An identity link is used to associate a task with a certain identity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/NativeTaskQuery.html" title="interface in org.activiti.engine.task">NativeTaskQuery</a></td>
<td class="colLast">
<div class="block">Allows querying of <a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s via native (SQL) queries</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a></td>
<td class="colLast">
<div class="block">Represents one task for a human user.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task">TaskInfoQuery</a>&lt;T extends <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task">TaskInfoQuery</a>&lt;?,?&gt;,V extends <a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a>&gt;</td>
<td class="colLast">
<div class="block">Interface containing shared methods between the <a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task"><code>TaskQuery</code></a> and the <a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstanceQuery</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></td>
<td class="colLast">
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s;</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task">IdentityLinkType</a></td>
<td class="colLast">
<div class="block">Contains constants for all types of identity links that can be used to involve a 
 user or group with a certain task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/TaskInfoQueryWrapper.html" title="class in org.activiti.engine.task">TaskInfoQueryWrapper</a></td>
<td class="colLast">
<div class="block">This is a helper class to help you work with the <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task"><code>TaskInfoQuery</code></a>, without having to care about the awful generics.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></td>
<td class="colLast">
<div class="block">Defines the different states of delegation that a task can be in.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package org.activiti.engine.task Description">Package org.activiti.engine.task Description</h2>
<div class="block">Classes related to the <a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../org/activiti/engine/test/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/task/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
