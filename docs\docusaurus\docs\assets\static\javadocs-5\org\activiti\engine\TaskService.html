<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TaskService (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TaskService (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6,"i44":6,"i45":6,"i46":6,"i47":6,"i48":6,"i49":6,"i50":6,"i51":6,"i52":6,"i53":6,"i54":6,"i55":6,"i56":6,"i57":6,"i58":6,"i59":6,"i60":6,"i61":6,"i62":6,"i63":6,"i64":6,"i65":6,"i66":6,"i67":6,"i68":6,"i69":6,"i70":6,"i71":6,"i72":6,"i73":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TaskService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/TaskService.html" target="_top">Frames</a></li>
<li><a href="TaskService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Interface TaskService" class="title">Interface TaskService</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">TaskService</span></pre>
<div class="block">Service which provides access to <a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a> and form related operations.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#addCandidateGroup-java.lang.String-java.lang.String-">addCandidateGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/TaskService.html#addGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>addGroupIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#addCandidateUser-java.lang.String-java.lang.String-">addCandidateUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/TaskService.html#addUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>addUserIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#addComment-java.lang.String-java.lang.String-java.lang.String-">addComment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</code>
<div class="block">Add a comment to a task and/or process instance.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#addComment-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">addComment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</code>
<div class="block">Add a comment to a task and/or process instance with a custom type.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#addGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-">addGroupIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Involves a group with a task.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#addUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-">addUserIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Involves a user with a task.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#claim-java.lang.String-java.lang.String-">claim</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Claim responsibility for a task: the given user is made assignee for the task.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#complete-java.lang.String-">complete</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Called when the task is successfully executed.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#complete-java.lang.String-java.util.Map-">complete</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Called when the task is successfully executed, 
 and the required task parameters are given by the end-user.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#complete-java.lang.String-java.util.Map-boolean-">complete</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
        boolean&nbsp;localScope)</code>
<div class="block">Called when the task is successfully executed, 
 and the required task paramaters are given by the end-user.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#complete-java.lang.String-java.util.Map-java.util.Map-">complete</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/TaskService.html#complete-java.lang.String-java.util.Map-"><code>complete(String, Map)</code></a>, but allows to set transient variables too.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#createAttachment-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.io.InputStream-">createAttachment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentType,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentName,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentDescription,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;content)</code>
<div class="block">Add a new attachment to a task and/or a process instance and use an input stream to provide the content</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#createAttachment-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">createAttachment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentType,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentName,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentDescription,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;url)</code>
<div class="block">Add a new attachment to a task and/or a process instance and use an url as the content</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/NativeTaskQuery.html" title="interface in org.activiti.engine.task">NativeTaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#createNativeTaskQuery--">createNativeTaskQuery</a></span>()</code>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for tasks.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#createTaskQuery--">createTaskQuery</a></span>()</code>
<div class="block">Returns a new <a href="../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task"><code>TaskQuery</code></a> that can be used to dynamically query tasks.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#delegateTask-java.lang.String-java.lang.String-">delegateTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Delegates the task to another user.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteAttachment-java.lang.String-">deleteAttachment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)</code>
<div class="block">Delete an attachment</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteCandidateGroup-java.lang.String-java.lang.String-">deleteCandidateGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/TaskService.html#deleteGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>deleteGroupIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteCandidateUser-java.lang.String-java.lang.String-">deleteCandidateUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/TaskService.html#deleteUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>deleteUserIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteComment-java.lang.String-">deleteComment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;commentId)</code>
<div class="block">Removes an individual comment with the given id.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteComments-java.lang.String-java.lang.String-">deleteComments</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Removes all comments from the provided task and/or process instance</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-">deleteGroupIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Removes the association between a group and a task for the given identityLinkType.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteTask-java.lang.String-">deleteTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Deletes the given task, not deleting historic information that is related to this task.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteTask-java.lang.String-boolean-">deleteTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
          boolean&nbsp;cascade)</code>
<div class="block">Deletes the given task.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteTask-java.lang.String-java.lang.String-">deleteTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deleteReason)</code>
<div class="block">Deletes the given task, not deleting historic information that is related to this task..</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteTasks-java.util.Collection-">deleteTasks</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds)</code>
<div class="block">Deletes all tasks of the given collection, not deleting historic information that is related 
 to these tasks.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteTasks-java.util.Collection-boolean-">deleteTasks</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds,
           boolean&nbsp;cascade)</code>
<div class="block">Deletes all tasks of the given collection.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteTasks-java.util.Collection-java.lang.String-">deleteTasks</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deleteReason)</code>
<div class="block">Deletes all tasks of the given collection, not deleting historic information that is related to these tasks.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#deleteUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-">deleteUserIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Removes the association between a user and a task for the given identityLinkType.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getAttachment-java.lang.String-">getAttachment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)</code>
<div class="block">Retrieve a particular attachment</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getAttachmentContent-java.lang.String-">getAttachmentContent</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)</code>
<div class="block">Retrieve stream content of a particular attachment</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getComment-java.lang.String-">getComment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;commentId)</code>
<div class="block">Returns an individual comment with the given id.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getCommentsByType-java.lang.String-">getCommentsByType</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">All comments of a given type.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/Event.html" title="interface in org.activiti.engine.task">Event</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getEvent-java.lang.String-">getEvent</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventId)</code>
<div class="block">Returns an individual event with the given id.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task">IdentityLink</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getIdentityLinksForTask-java.lang.String-">getIdentityLinksForTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a>s associated with the given task.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getProcessInstanceAttachments-java.lang.String-">getProcessInstanceAttachments</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">The list of attachments associated to a process instance</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getProcessInstanceComments-java.lang.String-">getProcessInstanceComments</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">The comments related to the given process instance.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getProcessInstanceComments-java.lang.String-java.lang.String-">getProcessInstanceComments</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">The comments related to the given process instance.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getSubTasks-java.lang.String-">getSubTasks</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentTaskId)</code>
<div class="block">The list of subtasks for this parent task</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getTaskAttachments-java.lang.String-">getTaskAttachments</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">The list of attachments associated to a task</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getTaskComments-java.lang.String-">getTaskComments</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">The comments related to the given task.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getTaskComments-java.lang.String-java.lang.String-">getTaskComments</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">The comments related to the given task of the given type.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Event.html" title="interface in org.activiti.engine.task">Event</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getTaskEvents-java.lang.String-">getTaskEvents</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">The all events related to the given task.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getVariable-java.lang.String-java.lang.String-">getVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">get a variables and search in the task scope and if available also the execution scopes.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getVariable-java.lang.String-java.lang.String-java.lang.Class-">getVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</code>
<div class="block">get a variables and search in the task scope and if available also the execution scopes.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getVariableInstancesLocalByTaskIds-java.util.Set-">getVariableInstancesLocalByTaskIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds)</code>
<div class="block">get all variables and search only in the task scope.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getVariableLocal-java.lang.String-java.lang.String-">getVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">checks whether or not the task has a variable defined with the given name.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getVariableLocal-java.lang.String-java.lang.String-java.lang.Class-">getVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</code>
<div class="block">checks whether or not the task has a variable defined with the given name.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getVariables-java.lang.String-">getVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">get all variables and search in the task scope and if available also the execution scopes.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getVariables-java.lang.String-java.util.Collection-">getVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">get values for all given variableNames and search only in the task scope.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getVariablesLocal-java.lang.String-">getVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">get all variables and search only in the task scope.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#getVariablesLocal-java.lang.String-java.util.Collection-">getVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">get a variable on a task</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#hasVariable-java.lang.String-java.lang.String-">hasVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">checks whether or not the task has a variable defined with the given name, in the task scope and if available also the execution scopes.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#hasVariableLocal-java.lang.String-java.lang.String-">hasVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">checks whether or not the task has a variable defined with the given name, local task scope only.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#newTask--">newTask</a></span>()</code>
<div class="block">Creates a new task that is not related to any process instance.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#newTask-java.lang.String-">newTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">create a new task with a user defined task id</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#removeVariable-java.lang.String-java.lang.String-">removeVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Removes the variable from the task.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#removeVariableLocal-java.lang.String-java.lang.String-">removeVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Removes the variable from the task (not considering parent scopes).</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#removeVariables-java.lang.String-java.util.Collection-">removeVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
               <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Removes all variables in the given collection from the task.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#removeVariablesLocal-java.lang.String-java.util.Collection-">removeVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Removes all variables in the given collection from the task (not considering parent scopes).</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#resolveTask-java.lang.String-">resolveTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Marks that the assignee is done with this task and that it can be send back to the owner.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#resolveTask-java.lang.String-java.util.Map-">resolveTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Marks that the assignee is done with this task providing the required
 variables and that it can be sent back to the owner.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#resolveTask-java.lang.String-java.util.Map-java.util.Map-">resolveTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/TaskService.html#resolveTask-java.lang.String-java.util.Map-"><code>resolveTask(String, Map)</code></a>, but allows to set transient variables too.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#saveAttachment-org.activiti.engine.task.Attachment-">saveAttachment</a></span>(<a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a>&nbsp;attachment)</code>
<div class="block">Update the name and decription of an attachment</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#saveTask-org.activiti.engine.task.Task-">saveTask</a></span>(<a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a>&nbsp;task)</code>
<div class="block">Saves the given task to the persistent data store.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#setAssignee-java.lang.String-java.lang.String-">setAssignee</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Changes the assignee of the given task to the given userId.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#setDueDate-java.lang.String-java.util.Date-">setDueDate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
          <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>
<div class="block">Changes the due date of the task</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#setOwner-java.lang.String-java.lang.String-">setOwner</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Transfers ownership of this task to another user.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#setPriority-java.lang.String-int-">setPriority</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
           int&nbsp;priority)</code>
<div class="block">Changes the priority of the task.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#setVariable-java.lang.String-java.lang.String-java.lang.Object-">setVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">set variable on a task.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#setVariableLocal-java.lang.String-java.lang.String-java.lang.Object-">setVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">set variable on a task.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#setVariables-java.lang.String-java.util.Map-">setVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">set variables on a task.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#setVariablesLocal-java.lang.String-java.util.Map-">setVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">set variables on a task.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/TaskService.html#unclaim-java.lang.String-">unclaim</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">A shortcut to <a href="../../../org/activiti/engine/TaskService.html#claim-java.lang.String-java.lang.String-"><code>claim(java.lang.String, java.lang.String)</code></a> with null user in order to unclaim the task</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="newTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newTask</h4>
<pre><a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a>&nbsp;newTask()</pre>
<div class="block">Creates a new task that is not related to any process instance.
 
 The returned task is transient and must be saved with <a href="../../../org/activiti/engine/TaskService.html#saveTask-org.activiti.engine.task.Task-"><code>saveTask(Task)</code></a> 'manually'.</div>
</li>
</ul>
<a name="newTask-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newTask</h4>
<pre><a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a>&nbsp;newTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">create a new task with a user defined task id</div>
</li>
</ul>
<a name="saveTask-org.activiti.engine.task.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveTask</h4>
<pre>void&nbsp;saveTask(<a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a>&nbsp;task)</pre>
<div class="block">Saves the given task to the persistent data store. If the task is already
 present in the persistent store, it is updated.
 After a new task has been saved, the task instance passed into this method
 is updated with the id of the newly created task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>task</code> - the task, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="deleteTask-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteTask</h4>
<pre>void&nbsp;deleteTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">Deletes the given task, not deleting historic information that is related to this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - The id of the task that will be deleted, cannot be null. If no task
 exists with the given taskId, the operation is ignored.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task with given id does not exist.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - when an error occurs while deleting the task or in case the task is part
   of a running process.</dd>
</dl>
</li>
</ul>
<a name="deleteTasks-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteTasks</h4>
<pre>void&nbsp;deleteTasks(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds)</pre>
<div class="block">Deletes all tasks of the given collection, not deleting historic information that is related 
 to these tasks.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskIds</code> - The id's of the tasks that will be deleted, cannot be null. All
 id's in the list that don't have an existing task will be ignored.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when one of the task does not exist.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - when an error occurs while deleting the tasks or in case one of the tasks
  is part of a running process.</dd>
</dl>
</li>
</ul>
<a name="deleteTask-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteTask</h4>
<pre>void&nbsp;deleteTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                boolean&nbsp;cascade)</pre>
<div class="block">Deletes the given task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - The id of the task that will be deleted, cannot be null. If no task
 exists with the given taskId, the operation is ignored.</dd>
<dd><code>cascade</code> - If cascade is true, also the historic information related to this task is deleted.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task with given id does not exist.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - when an error occurs while deleting the task or in case the task is part
   of a running process.</dd>
</dl>
</li>
</ul>
<a name="deleteTasks-java.util.Collection-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteTasks</h4>
<pre>void&nbsp;deleteTasks(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds,
                 boolean&nbsp;cascade)</pre>
<div class="block">Deletes all tasks of the given collection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskIds</code> - The id's of the tasks that will be deleted, cannot be null. All
 id's in the list that don't have an existing task will be ignored.</dd>
<dd><code>cascade</code> - If cascade is true, also the historic information related to this task is deleted.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when one of the tasks does not exist.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - when an error occurs while deleting the tasks or in case one of the tasks
  is part of a running process.</dd>
</dl>
</li>
</ul>
<a name="deleteTask-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteTask</h4>
<pre>void&nbsp;deleteTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deleteReason)</pre>
<div class="block">Deletes the given task, not deleting historic information that is related to this task..</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - The id of the task that will be deleted, cannot be null. If no task
 exists with the given taskId, the operation is ignored.</dd>
<dd><code>deleteReason</code> - reason the task is deleted. Is recorded in history, if enabled.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task with given id does not exist.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - when an error occurs while deleting the task or in case the task is part
  of a running process</dd>
</dl>
</li>
</ul>
<a name="deleteTasks-java.util.Collection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteTasks</h4>
<pre>void&nbsp;deleteTasks(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deleteReason)</pre>
<div class="block">Deletes all tasks of the given collection, not deleting historic information that is related to these tasks.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskIds</code> - The id's of the tasks that will be deleted, cannot be null. All
 id's in the list that don't have an existing task will be ignored.</dd>
<dd><code>deleteReason</code> - reason the task is deleted. Is recorded in history, if enabled.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when one of the tasks does not exist.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - when an error occurs while deleting the tasks or in case one of the tasks
  is part of a running process.</dd>
</dl>
</li>
</ul>
<a name="claim-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>claim</h4>
<pre>void&nbsp;claim(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Claim responsibility for a task: the given user is made assignee for the task.
 The difference with <a href="../../../org/activiti/engine/TaskService.html#setAssignee-java.lang.String-java.lang.String-"><code>setAssignee(String, String)</code></a> is that here 
 a check is done if the task already has a user assigned to it.
 No check is done whether the user is known by the identity component.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - task to claim, cannot be null.</dd>
<dd><code>userId</code> - user that claims the task. When userId is null the task is unclaimed,
 assigned to no one.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task doesn't exist.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiTaskAlreadyClaimedException.html" title="class in org.activiti.engine">ActivitiTaskAlreadyClaimedException</a></code> - when the task is already claimed by another user.</dd>
</dl>
</li>
</ul>
<a name="unclaim-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unclaim</h4>
<pre>void&nbsp;unclaim(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">A shortcut to <a href="../../../org/activiti/engine/TaskService.html#claim-java.lang.String-java.lang.String-"><code>claim(java.lang.String, java.lang.String)</code></a> with null user in order to unclaim the task</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - task to unclaim, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="complete-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>complete</h4>
<pre>void&nbsp;complete(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">Called when the task is successfully executed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - the id of the task to complete, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no task exists with the given id.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - when this task is <code>DelegationState#PENDING</code> delegation.</dd>
</dl>
</li>
</ul>
<a name="delegateTask-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>delegateTask</h4>
<pre>void&nbsp;delegateTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Delegates the task to another user. This means that the assignee is set 
 and the delegation state is set to <code>DelegationState#PENDING</code>.
 If no owner is set on the task, the owner is set to the current assignee
 of the task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - The id of the task that will be delegated.</dd>
<dd><code>userId</code> - The id of the user that will be set as assignee.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no task exists with the given id.</dd>
</dl>
</li>
</ul>
<a name="resolveTask-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resolveTask</h4>
<pre>void&nbsp;resolveTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">Marks that the assignee is done with this task and that it can be send back to the owner.  
 Can only be called when this task is <code>DelegationState#PENDING</code> delegation.
 After this method returns, the <a href="../../../org/activiti/engine/task/Task.html#getDelegationState--"><code>delegationState</code></a> is set to <code>DelegationState#RESOLVED</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - the id of the task to resolve, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no task exists with the given id.</dd>
</dl>
</li>
</ul>
<a name="resolveTask-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resolveTask</h4>
<pre>void&nbsp;resolveTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Marks that the assignee is done with this task providing the required
 variables and that it can be sent back to the owner. Can only be called
 when this task is <code>DelegationState#PENDING</code> delegation. After this
 method returns, the <a href="../../../org/activiti/engine/task/Task.html#getDelegationState--"><code>delegationState</code></a> is
 set to <code>DelegationState#RESOLVED</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - </dd>
<dd><code>variables</code> - </dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>ProcessEngineException</code> - When no task exists with the given id.</dd>
</dl>
</li>
</ul>
<a name="resolveTask-java.lang.String-java.util.Map-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resolveTask</h4>
<pre>void&nbsp;resolveTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/TaskService.html#resolveTask-java.lang.String-java.util.Map-"><code>resolveTask(String, Map)</code></a>, but allows to set transient variables too.</div>
</li>
</ul>
<a name="complete-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>complete</h4>
<pre>void&nbsp;complete(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Called when the task is successfully executed, 
 and the required task parameters are given by the end-user.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - the id of the task to complete, cannot be null.</dd>
<dd><code>variables</code> - task parameters. May be null or empty.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no task exists with the given id.</dd>
</dl>
</li>
</ul>
<a name="complete-java.lang.String-java.util.Map-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>complete</h4>
<pre>void&nbsp;complete(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/TaskService.html#complete-java.lang.String-java.util.Map-"><code>complete(String, Map)</code></a>, but allows to set transient variables too.</div>
</li>
</ul>
<a name="complete-java.lang.String-java.util.Map-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>complete</h4>
<pre>void&nbsp;complete(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
              boolean&nbsp;localScope)</pre>
<div class="block">Called when the task is successfully executed, 
 and the required task paramaters are given by the end-user.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - the id of the task to complete, cannot be null.</dd>
<dd><code>variables</code> - task parameters. May be null or empty.</dd>
<dd><code>localScope</code> - If true, the provided variables will be stored task-local, 
                                                                         instead of process instance wide (which is the default for <a href="../../../org/activiti/engine/TaskService.html#complete-java.lang.String-java.util.Map-"><code>complete(String, Map)</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no task exists with the given id.</dd>
</dl>
</li>
</ul>
<a name="setAssignee-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAssignee</h4>
<pre>void&nbsp;setAssignee(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Changes the assignee of the given task to the given userId.
 No check is done whether the user is known by the identity component.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>userId</code> - id of the user to use as assignee.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="setOwner-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOwner</h4>
<pre>void&nbsp;setOwner(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Transfers ownership of this task to another user.
 No check is done whether the user is known by the identity component.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>userId</code> - of the person that is receiving ownership.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="getIdentityLinksForTask-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIdentityLinksForTask</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task">IdentityLink</a>&gt;&nbsp;getIdentityLinksForTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a>s associated with the given task.
 Such an <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a> informs how a certain identity (eg. group or user)
 is associated with a certain task (eg. as candidate, assignee, etc.)</div>
</li>
</ul>
<a name="addCandidateUser-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCandidateUser</h4>
<pre>void&nbsp;addCandidateUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/TaskService.html#addUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>addUserIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>userId</code> - id of the user to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="addCandidateGroup-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCandidateGroup</h4>
<pre>void&nbsp;addCandidateGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/TaskService.html#addGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>addGroupIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>groupId</code> - id of the group to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="addUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addUserIdentityLink</h4>
<pre>void&nbsp;addUserIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Involves a user with a task. The type of identity link is defined by the
 given identityLinkType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>userId</code> - id of the user involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identityLink, cannot be null (@see <a href="../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="addGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGroupIdentityLink</h4>
<pre>void&nbsp;addGroupIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Involves a group with a task. The type of identityLink is defined by the
 given identityLink.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>groupId</code> - id of the group to involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identity, cannot be null (@see <a href="../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteCandidateUser-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteCandidateUser</h4>
<pre>void&nbsp;deleteCandidateUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/TaskService.html#deleteUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>deleteUserIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>userId</code> - id of the user to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteCandidateGroup-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteCandidateGroup</h4>
<pre>void&nbsp;deleteCandidateGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/TaskService.html#deleteGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>deleteGroupIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>groupId</code> - id of the group to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteUserIdentityLink</h4>
<pre>void&nbsp;deleteUserIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Removes the association between a user and a task for the given identityLinkType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>userId</code> - id of the user involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identityLink, cannot be null (@see <a href="../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteGroupIdentityLink</h4>
<pre>void&nbsp;deleteGroupIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Removes the association between a group and a task for the given identityLinkType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>groupId</code> - id of the group to involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identity, cannot be null (@see <a href="../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="setPriority-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriority</h4>
<pre>void&nbsp;setPriority(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                 int&nbsp;priority)</pre>
<div class="block">Changes the priority of the task.
 
 Authorization: actual owner / business admin</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>priority</code> - the new priority for the task.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="setDueDate-java.lang.String-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDueDate</h4>
<pre>void&nbsp;setDueDate(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</pre>
<div class="block">Changes the due date of the task</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskId</code> - id of the task, cannot be null.</dd>
<dd><code>dueDate</code> - the new due date for the task</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - when the task doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="createTaskQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTaskQuery</h4>
<pre><a href="../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>&nbsp;createTaskQuery()</pre>
<div class="block">Returns a new <a href="../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task"><code>TaskQuery</code></a> that can be used to dynamically query tasks.</div>
</li>
</ul>
<a name="createNativeTaskQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeTaskQuery</h4>
<pre><a href="../../../org/activiti/engine/task/NativeTaskQuery.html" title="interface in org.activiti.engine.task">NativeTaskQuery</a>&nbsp;createNativeTaskQuery()</pre>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for tasks.</div>
</li>
</ul>
<a name="setVariable-java.lang.String-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariable</h4>
<pre>void&nbsp;setVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">set variable on a task.  If the variable is not already existing, it will be created in the 
 most outer scope.  This means the process instance in case this task is related to an 
 execution.</div>
</li>
</ul>
<a name="setVariables-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariables</h4>
<pre>void&nbsp;setVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">set variables on a task.  If the variable is not already existing, it will be created in the 
 most outer scope.  This means the process instance in case this task is related to an 
 execution.</div>
</li>
</ul>
<a name="setVariableLocal-java.lang.String-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariableLocal</h4>
<pre>void&nbsp;setVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">set variable on a task.  If the variable is not already existing, it will be created in the 
 task.</div>
</li>
</ul>
<a name="setVariablesLocal-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariablesLocal</h4>
<pre>void&nbsp;setVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">set variables on a task.  If the variable is not already existing, it will be created in the 
 task.</div>
</li>
</ul>
<a name="getVariable-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariable</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">get a variables and search in the task scope and if available also the execution scopes.</div>
</li>
</ul>
<a name="getVariable-java.lang.String-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariable</h4>
<pre>&lt;T&gt;&nbsp;T&nbsp;getVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</pre>
<div class="block">get a variables and search in the task scope and if available also the execution scopes.</div>
</li>
</ul>
<a name="hasVariable-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasVariable</h4>
<pre>boolean&nbsp;hasVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">checks whether or not the task has a variable defined with the given name, in the task scope and if available also the execution scopes.</div>
</li>
</ul>
<a name="getVariableLocal-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">checks whether or not the task has a variable defined with the given name.</div>
</li>
</ul>
<a name="getVariableLocal-java.lang.String-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableLocal</h4>
<pre>&lt;T&gt;&nbsp;T&nbsp;getVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</pre>
<div class="block">checks whether or not the task has a variable defined with the given name.</div>
</li>
</ul>
<a name="hasVariableLocal-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasVariableLocal</h4>
<pre>boolean&nbsp;hasVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">checks whether or not the task has a variable defined with the given name, local task scope only.</div>
</li>
</ul>
<a name="getVariables-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariables</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">get all variables and search in the task scope and if available also the execution scopes. 
 If you have many variables and you only need a few, consider using <a href="../../../org/activiti/engine/TaskService.html#getVariables-java.lang.String-java.util.Collection-"><code>getVariables(String, Collection)</code></a> 
 for better performance.</div>
</li>
</ul>
<a name="getVariablesLocal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariablesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">get all variables and search only in the task scope.
 If you have many task local variables and you only need a few, consider using <a href="../../../org/activiti/engine/TaskService.html#getVariablesLocal-java.lang.String-java.util.Collection-"><code>getVariablesLocal(String, Collection)</code></a> 
 for better performance.</div>
</li>
</ul>
<a name="getVariables-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariables</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">get values for all given variableNames and search only in the task scope.</div>
</li>
</ul>
<a name="getVariablesLocal-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariablesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">get a variable on a task</div>
</li>
</ul>
<a name="getVariableInstancesLocalByTaskIds-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstancesLocalByTaskIds</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstancesLocalByTaskIds(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds)</pre>
<div class="block">get all variables and search only in the task scope.</div>
</li>
</ul>
<a name="removeVariable-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariable</h4>
<pre>void&nbsp;removeVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Removes the variable from the task.
 When the variable does not exist, nothing happens.</div>
</li>
</ul>
<a name="removeVariableLocal-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariableLocal</h4>
<pre>void&nbsp;removeVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Removes the variable from the task (not considering parent scopes).
 When the variable does not exist, nothing happens.</div>
</li>
</ul>
<a name="removeVariables-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariables</h4>
<pre>void&nbsp;removeVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Removes all variables in the given collection from the task.
 Non existing variable names are simply ignored.</div>
</li>
</ul>
<a name="removeVariablesLocal-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariablesLocal</h4>
<pre>void&nbsp;removeVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Removes all variables in the given collection from the task (not considering parent scopes).
 Non existing variable names are simply ignored.</div>
</li>
</ul>
<a name="addComment-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addComment</h4>
<pre><a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&nbsp;addComment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</pre>
<div class="block">Add a comment to a task and/or process instance.</div>
</li>
</ul>
<a name="addComment-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addComment</h4>
<pre><a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&nbsp;addComment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</pre>
<div class="block">Add a comment to a task and/or process instance with a custom type.</div>
</li>
</ul>
<a name="getComment-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComment</h4>
<pre><a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&nbsp;getComment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;commentId)</pre>
<div class="block">Returns an individual comment with the given id. Returns null if no comment exists with the given id.</div>
</li>
</ul>
<a name="deleteComments-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteComments</h4>
<pre>void&nbsp;deleteComments(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Removes all comments from the provided task and/or process instance</div>
</li>
</ul>
<a name="deleteComment-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteComment</h4>
<pre>void&nbsp;deleteComment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;commentId)</pre>
<div class="block">Removes an individual comment with the given id.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no comment exists with the given id.</dd>
</dl>
</li>
</ul>
<a name="getTaskComments-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskComments</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;&nbsp;getTaskComments(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">The comments related to the given task.</div>
</li>
</ul>
<a name="getTaskComments-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskComments</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;&nbsp;getTaskComments(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</pre>
<div class="block">The comments related to the given task of the given type.</div>
</li>
</ul>
<a name="getCommentsByType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCommentsByType</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;&nbsp;getCommentsByType(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</pre>
<div class="block">All comments of a given type.</div>
</li>
</ul>
<a name="getTaskEvents-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskEvents</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Event.html" title="interface in org.activiti.engine.task">Event</a>&gt;&nbsp;getTaskEvents(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">The all events related to the given task.</div>
</li>
</ul>
<a name="getEvent-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEvent</h4>
<pre><a href="../../../org/activiti/engine/task/Event.html" title="interface in org.activiti.engine.task">Event</a>&nbsp;getEvent(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventId)</pre>
<div class="block">Returns an individual event with the given id. Returns null if no event exists with the given id.</div>
</li>
</ul>
<a name="getProcessInstanceComments-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessInstanceComments</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;&nbsp;getProcessInstanceComments(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">The comments related to the given process instance.</div>
</li>
</ul>
<a name="getProcessInstanceComments-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessInstanceComments</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Comment.html" title="interface in org.activiti.engine.task">Comment</a>&gt;&nbsp;getProcessInstanceComments(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</pre>
<div class="block">The comments related to the given process instance.</div>
</li>
</ul>
<a name="createAttachment-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAttachment</h4>
<pre><a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a>&nbsp;createAttachment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentType,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentName,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentDescription,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;content)</pre>
<div class="block">Add a new attachment to a task and/or a process instance and use an input stream to provide the content</div>
</li>
</ul>
<a name="createAttachment-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAttachment</h4>
<pre><a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a>&nbsp;createAttachment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentType,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentName,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentDescription,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;url)</pre>
<div class="block">Add a new attachment to a task and/or a process instance and use an url as the content</div>
</li>
</ul>
<a name="saveAttachment-org.activiti.engine.task.Attachment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveAttachment</h4>
<pre>void&nbsp;saveAttachment(<a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a>&nbsp;attachment)</pre>
<div class="block">Update the name and decription of an attachment</div>
</li>
</ul>
<a name="getAttachment-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAttachment</h4>
<pre><a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a>&nbsp;getAttachment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)</pre>
<div class="block">Retrieve a particular attachment</div>
</li>
</ul>
<a name="getAttachmentContent-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAttachmentContent</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;getAttachmentContent(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)</pre>
<div class="block">Retrieve stream content of a particular attachment</div>
</li>
</ul>
<a name="getTaskAttachments-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskAttachments</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a>&gt;&nbsp;getTaskAttachments(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">The list of attachments associated to a task</div>
</li>
</ul>
<a name="getProcessInstanceAttachments-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessInstanceAttachments</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Attachment.html" title="interface in org.activiti.engine.task">Attachment</a>&gt;&nbsp;getProcessInstanceAttachments(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">The list of attachments associated to a process instance</div>
</li>
</ul>
<a name="deleteAttachment-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteAttachment</h4>
<pre>void&nbsp;deleteAttachment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)</pre>
<div class="block">Delete an attachment</div>
</li>
</ul>
<a name="getSubTasks-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSubTasks</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a>&gt;&nbsp;getSubTasks(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentTaskId)</pre>
<div class="block">The list of subtasks for this parent task</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TaskService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/TaskService.html" target="_top">Frames</a></li>
<li><a href="TaskService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
