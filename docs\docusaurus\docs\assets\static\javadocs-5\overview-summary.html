<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:26 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Overview (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Overview (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li class="navBarCell1Rev">Overview</li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-summary.html" target="_top">Frames</a></li>
<li><a href="overview-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Flowable - Engine 5.23.0 API</h1>
</div>
<div class="contentContainer">
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Packages table, listing packages, and an explanation">
<caption><span>Packages</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/package-summary.html">org.activiti.engine</a></td>
<td class="colLast">
<div class="block">Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a <a href="org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine"><code>ProcessEngineConfiguration</code></a>
    (typically based on a configuration file), from which a <a href="org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> can be obtained.<br/><br/>
    Through the services obtained from such a <a href="org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b><a href="org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>: </b> Manages <a href="org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s <br/>

    <b><a href="org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>: </b> For starting and searching <a href="org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s <br/>
    
    <b><a href="org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>: </b> Exposes operations to manage human (standalone) <a href="org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s, 
    such as claiming, completing and assigning tasks<br/>

    <b><a href="org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>: </b> Used for managing <a href="org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s, 
    <a href="org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s and the relations between them<br/>
        
    <b><a href="org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b><a href="org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b><a href="org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="org/activiti/engine/cfg/package-summary.html">org.activiti.engine.cfg</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/delegate/package-summary.html">org.activiti.engine.delegate</a></td>
<td class="colLast">
<div class="block">Interfaces used to include Java code in a process as the behavior of an activity 
    or as a listener to process events with <a href="org/activiti/engine/delegate/JavaDelegate.html" title="interface in org.activiti.engine.delegate"><code>JavaDelegate</code></a>s.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="org/activiti/engine/delegate/event/package-summary.html">org.activiti.engine.delegate.event</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/delegate/event/impl/package-summary.html">org.activiti.engine.delegate.event.impl</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="org/activiti/engine/dynamic/package-summary.html">org.activiti.engine.dynamic</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/event/package-summary.html">org.activiti.engine.event</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="org/activiti/engine/form/package-summary.html">org.activiti.engine.form</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/history/package-summary.html">org.activiti.engine.history</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="org/activiti/engine/identity/package-summary.html">org.activiti.engine.identity</a></td>
<td class="colLast">
<div class="block">classes related to the <a href="org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/logging/package-summary.html">org.activiti.engine.logging</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="org/activiti/engine/management/package-summary.html">org.activiti.engine.management</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/parse/package-summary.html">org.activiti.engine.parse</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="org/activiti/engine/query/package-summary.html">org.activiti.engine.query</a></td>
<td class="colLast">
<div class="block">Classes related to the querying entities in the engine.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/repository/package-summary.html">org.activiti.engine.repository</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="org/activiti/engine/runtime/package-summary.html">org.activiti.engine.runtime</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/task/package-summary.html">org.activiti.engine.task</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="org/activiti/engine/test/package-summary.html">org.activiti.engine.test</a></td>
<td class="colLast">
<div class="block">Helper classes for testing processes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="org/activiti/engine/test/mock/package-summary.html">org.activiti.engine.test.mock</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li class="navBarCell1Rev">Overview</li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-summary.html" target="_top">Frames</a></li>
<li><a href="overview-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
