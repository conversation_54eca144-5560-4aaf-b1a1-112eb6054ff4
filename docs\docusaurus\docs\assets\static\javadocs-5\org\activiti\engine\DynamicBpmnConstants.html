<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DynamicBpmnConstants (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DynamicBpmnConstants (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DynamicBpmnConstants.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/ActivitiWrongDbException.html" title="class in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/DynamicBpmnService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/DynamicBpmnConstants.html" target="_top">Frames</a></li>
<li><a href="DynamicBpmnConstants.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Interface DynamicBpmnConstants" class="title">Interface DynamicBpmnConstants</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../org/activiti/engine/dynamic/BasePropertiesParser.html" title="class in org.activiti.engine.dynamic">BasePropertiesParser</a>, <a href="../../../org/activiti/engine/dynamic/DefaultPropertiesParser.html" title="class in org.activiti.engine.dynamic">DefaultPropertiesParser</a>, <a href="../../../org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html" title="class in org.activiti.engine.dynamic">DynamicProcessDefinitionSummary</a>, <a href="../../../org/activiti/engine/dynamic/ScriptTaskPropertiesParser.html" title="class in org.activiti.engine.dynamic">ScriptTaskPropertiesParser</a>, <a href="../../../org/activiti/engine/dynamic/UserTaskPropertiesParser.html" title="class in org.activiti.engine.dynamic">UserTaskPropertiesParser</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">DynamicBpmnConstants</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#BPMN_NODE">BPMN_NODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_DESCRIPTION">LOCALIZATION_DESCRIPTION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_LANGUAGE">LOCALIZATION_LANGUAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_NAME">LOCALIZATION_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_NODE">LOCALIZATION_NODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#SCRIPT_TASK_SCRIPT">SCRIPT_TASK_SCRIPT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#SEQUENCE_FLOW_CONDITION">SEQUENCE_FLOW_CONDITION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_CLASS_NAME">SERVICE_TASK_CLASS_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_DELEGATE_EXPRESSION">SERVICE_TASK_DELEGATE_EXPRESSION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_EXPRESSION">SERVICE_TASK_EXPRESSION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#TASK_SKIP_EXPRESSION">TASK_SKIP_EXPRESSION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_ASSIGNEE">USER_TASK_ASSIGNEE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_GROUPS">USER_TASK_CANDIDATE_GROUPS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS">USER_TASK_CANDIDATE_USERS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CATEGORY">USER_TASK_CATEGORY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_DESCRIPTION">USER_TASK_DESCRIPTION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_DUEDATE">USER_TASK_DUEDATE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_FORM_KEY">USER_TASK_FORM_KEY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_NAME">USER_TASK_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_OWNER">USER_TASK_OWNER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_PRIORITY">USER_TASK_PRIORITY</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="BPMN_NODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BPMN_NODE</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> BPMN_NODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.BPMN_NODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LOCALIZATION_NODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCALIZATION_NODE</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> LOCALIZATION_NODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.LOCALIZATION_NODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TASK_SKIP_EXPRESSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_SKIP_EXPRESSION</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> TASK_SKIP_EXPRESSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.TASK_SKIP_EXPRESSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SERVICE_TASK_CLASS_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SERVICE_TASK_CLASS_NAME</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> SERVICE_TASK_CLASS_NAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.SERVICE_TASK_CLASS_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SERVICE_TASK_EXPRESSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SERVICE_TASK_EXPRESSION</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> SERVICE_TASK_EXPRESSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.SERVICE_TASK_EXPRESSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SERVICE_TASK_DELEGATE_EXPRESSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SERVICE_TASK_DELEGATE_EXPRESSION</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> SERVICE_TASK_DELEGATE_EXPRESSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.SERVICE_TASK_DELEGATE_EXPRESSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCRIPT_TASK_SCRIPT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCRIPT_TASK_SCRIPT</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> SCRIPT_TASK_SCRIPT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.SCRIPT_TASK_SCRIPT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_NAME</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_NAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_DESCRIPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_DESCRIPTION</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_DESCRIPTION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_DESCRIPTION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_DUEDATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_DUEDATE</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_DUEDATE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_DUEDATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_PRIORITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_PRIORITY</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_PRIORITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_PRIORITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_CATEGORY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_CATEGORY</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_CATEGORY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_CATEGORY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_FORM_KEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_FORM_KEY</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_FORM_KEY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_FORM_KEY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_ASSIGNEE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_ASSIGNEE</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_ASSIGNEE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_ASSIGNEE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_OWNER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_OWNER</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_OWNER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_OWNER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_CANDIDATE_USERS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_CANDIDATE_USERS</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_CANDIDATE_USERS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="USER_TASK_CANDIDATE_GROUPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_TASK_CANDIDATE_GROUPS</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> USER_TASK_CANDIDATE_GROUPS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.USER_TASK_CANDIDATE_GROUPS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SEQUENCE_FLOW_CONDITION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SEQUENCE_FLOW_CONDITION</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> SEQUENCE_FLOW_CONDITION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.SEQUENCE_FLOW_CONDITION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LOCALIZATION_LANGUAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCALIZATION_LANGUAGE</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> LOCALIZATION_LANGUAGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.LOCALIZATION_LANGUAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LOCALIZATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCALIZATION_NAME</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> LOCALIZATION_NAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.LOCALIZATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LOCALIZATION_DESCRIPTION">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LOCALIZATION_DESCRIPTION</h4>
<pre>static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> LOCALIZATION_DESCRIPTION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.DynamicBpmnConstants.LOCALIZATION_DESCRIPTION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DynamicBpmnConstants.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/ActivitiWrongDbException.html" title="class in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/DynamicBpmnService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/DynamicBpmnConstants.html" target="_top">Frames</a></li>
<li><a href="DynamicBpmnConstants.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
