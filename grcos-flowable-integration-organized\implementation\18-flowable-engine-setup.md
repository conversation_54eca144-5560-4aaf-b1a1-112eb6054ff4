# Flowable Engine Setup for GRCOS Integration

## Overview

This guide provides step-by-step instructions for setting up and configuring the Flowable BPM engine specifically for GRCOS integration. The setup includes AI agent integration, blockchain audit trails, OSCAL workflow generation, and custom service tasks for GRC operations.

## Prerequisites

### System Requirements
- **Java**: OpenJDK 17 or higher
- **Database**: PostgreSQL 15+ (primary), MongoDB 7+ (OSCAL documents)
- **Cache**: Redis 7.0+ cluster
- **Container Platform**: Kubernetes 1.28+ or Docker Compose
- **Memory**: Minimum 8GB RAM per engine instance
- **Storage**: SSD storage with 1000+ IOPS

### Dependencies
```xml
<dependencies>
    <!-- Flowable Core -->
    <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter</artifactId>
        <version>7.0.1</version>
    </dependency>
    
    <!-- GRCOS Extensions -->
    <dependency>
        <groupId>com.grcos</groupId>
        <artifactId>grcos-flowable-extensions</artifactId>
        <version>1.0.0</version>
    </dependency>
    
    <!-- AI Integration -->
    <dependency>
        <groupId>com.grcos</groupId>
        <artifactId>grcos-ai-agents</artifactId>
        <version>1.0.0</version>
    </dependency>
    
    <!-- Blockchain Integration -->
    <dependency>
        <groupId>org.hyperledger.fabric</groupId>
        <artifactId>fabric-gateway</artifactId>
        <version>2.5.0</version>
    </dependency>
    
    <!-- OSCAL Integration -->
    <dependency>
        <groupId>com.grcos</groupId>
        <artifactId>grcos-oscal-integration</artifactId>
        <version>1.0.0</version>
    </dependency>
</dependencies>
```

## Core Configuration

### Application Configuration

#### application.yml
```yaml
# GRCOS Flowable Configuration
spring:
  application:
    name: grcos-flowable-engine
  profiles:
    active: grcos,production
  
  # Database Configuration
  datasource:
    url: ******************************************************
    username: ${DB_USERNAME:flowable}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis Configuration
  redis:
    cluster:
      nodes:
        - redis-node-1:6379
        - redis-node-2:6379
        - redis-node-3:6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

# Flowable Configuration
flowable:
  # Process Engine Configuration
  process:
    definition-cache-limit: 1000
    enable-safe-xml: true
    xml-encoding: UTF-8
    
  # Database Configuration
  database-schema-update: true
  db-identity-used: true
  
  # Async Executor Configuration
  async:
    executor:
      default-async-job-acquire-wait-time: 10000
      default-timer-job-acquire-wait-time: 10000
      async-executor-activate: true
      async-executor-number-of-retries: 3
      timer-executor-number-of-retries: 3
      
  # REST API Configuration
  rest:
    app:
      authentication-mode: verify-privilege
      enable-cors: true
      cors-allowed-origins: "*"
      cors-allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"

# GRCOS Specific Configuration
grcos:
  # AI Agent Configuration
  ai:
    crew-ai:
      enabled: true
      api-key: ${CREW_AI_API_KEY}
      model: gpt-4
      temperature: 0.1
    
    workflow-agent:
      enabled: true
      optimization-interval: 300000 # 5 minutes
      performance-threshold: 10000 # 10 seconds
  
  # Blockchain Configuration
  blockchain:
    hyperledger-fabric:
      enabled: true
      network-config: /config/fabric/network-config.yaml
      wallet-path: /config/fabric/wallet
      user-name: grcos-user
      channel-name: grcos-channel
      contract-name: WorkflowAuditContract
  
  # OSCAL Configuration
  oscal:
    enabled: true
    repository-url: http://grcos-oscal-service:8080
    auto-generate-workflows: true
    validation-enabled: true
  
  # Module Integration
  modules:
    datagerry:
      enabled: true
      api-url: http://datagerry-api:4000
      api-key: ${DATAGERRY_API_KEY}
    
    opa:
      enabled: true
      server-url: http://opa-server:8181
      policy-package: grcos.policies
    
    wazuh:
      enabled: true
      api-url: http://wazuh-api:55000
      username: ${WAZUH_USERNAME}
      password: ${WAZUH_PASSWORD}

# Logging Configuration
logging:
  level:
    org.flowable: INFO
    com.grcos: DEBUG
    org.hyperledger.fabric: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /logs/grcos-flowable.log
    max-size: 100MB
    max-history: 30

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### Engine Configuration Class

#### GRCOSFlowableConfiguration.java
```java
@Configuration
@EnableFlowable
@EnableConfigurationProperties({GRCOSProperties.class})
public class GRCOSFlowableConfiguration {
    
    @Autowired
    private GRCOSProperties grcosProperties;
    
    @Bean
    @Primary
    public ProcessEngineConfiguration processEngineConfiguration(
            DataSource dataSource,
            PlatformTransactionManager transactionManager) {
        
        SpringProcessEngineConfiguration configuration = 
            new SpringProcessEngineConfiguration();
        
        // Basic Configuration
        configuration.setDataSource(dataSource);
        configuration.setTransactionManager(transactionManager);
        configuration.setDatabaseSchemaUpdate(
            ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE);
        
        // GRCOS Customizations
        configuration.setActivityFontName("Arial");
        configuration.setLabelFontName("Arial");
        configuration.setAnnotationFontName("Arial");
        
        // Custom Beans
        configuration.setBeans(createCustomBeans());
        
        // Event Listeners
        configuration.setEventListeners(createEventListeners());
        
        // Custom Service Tasks
        configuration.setCustomServiceTasks(createCustomServiceTasks());
        
        // Job Executor
        configuration.setAsyncExecutor(createAsyncExecutor());
        
        // Expression Manager
        configuration.setExpressionManager(createExpressionManager());
        
        return configuration;
    }
    
    private Map<Object, Object> createCustomBeans() {
        Map<Object, Object> beans = new HashMap<>();
        
        // GRCOS Services
        beans.put("oscalService", new OSCALServiceImpl());
        beans.put("datagerryService", new DatagerryServiceImpl());
        beans.put("opaService", new OPAServiceImpl());
        beans.put("blockchainService", new BlockchainServiceImpl());
        beans.put("workflowAgent", new WorkflowAgentImpl());
        
        return beans;
    }
    
    private List<FlowableEventListener> createEventListeners() {
        List<FlowableEventListener> listeners = new ArrayList<>();
        
        // Blockchain Audit Listener
        listeners.add(new BlockchainAuditEventListener());
        
        // AI Agent Notification Listener
        listeners.add(new AIAgentNotificationListener());
        
        // OSCAL Document Event Listener
        listeners.add(new OSCALDocumentEventListener());
        
        // Performance Monitoring Listener
        listeners.add(new PerformanceMonitoringListener());
        
        return listeners;
    }
    
    private List<ServiceTask> createCustomServiceTasks() {
        List<ServiceTask> serviceTasks = new ArrayList<>();
        
        // OSCAL Service Tasks
        serviceTasks.add(new OSCALDocumentProcessorTask());
        serviceTasks.add(new OSCALAssessmentExecutorTask());
        serviceTasks.add(new OSCALResultsAnalyzerTask());
        
        // CMDB Service Tasks
        serviceTasks.add(new CMDBAssetManagerTask());
        serviceTasks.add(new CMDBConfigurationValidatorTask());
        
        // Policy Service Tasks
        serviceTasks.add(new OPAPolicyEvaluatorTask());
        serviceTasks.add(new OPAComplianceCheckerTask());
        
        // AI Service Tasks
        serviceTasks.add(new AIAgentCoordinatorTask());
        serviceTasks.add(new AIDecisionEngineTask());
        
        return serviceTasks;
    }
    
    @Bean
    public AsyncExecutor createAsyncExecutor() {
        DefaultAsyncJobExecutor executor = new DefaultAsyncJobExecutor();
        
        // Core Configuration
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setKeepAliveTime(300000); // 5 minutes
        executor.setQueueSize(1000);
        
        // Timing Configuration
        executor.setDefaultAsyncJobAcquireWaitTimeInMillis(10000);
        executor.setDefaultTimerJobAcquireWaitTimeInMillis(10000);
        executor.setAsyncJobLockTimeInMillis(300000); // 5 minutes
        executor.setTimerLockTimeInMillis(300000); // 5 minutes
        
        // Retry Configuration
        executor.setRetryWaitTimeInMillis(5000);
        executor.setMaxAsyncJobsDuePerAcquisition(10);
        executor.setMaxTimerJobsPerAcquisition(10);
        
        return executor;
    }
    
    @Bean
    public ExpressionManager createExpressionManager() {
        SpelExpressionManager expressionManager = new SpelExpressionManager();
        
        // Add GRCOS-specific functions
        Map<String, Method> functions = new HashMap<>();
        
        try {
            // OSCAL Functions
            functions.put("oscalValidate", 
                OSCALFunctions.class.getMethod("validate", String.class));
            functions.put("oscalTransform", 
                OSCALFunctions.class.getMethod("transform", String.class, String.class));
            
            // AI Functions
            functions.put("aiOptimize", 
                AIFunctions.class.getMethod("optimize", String.class));
            functions.put("aiPredict", 
                AIFunctions.class.getMethod("predict", String.class, Map.class));
            
            // Compliance Functions
            functions.put("complianceCheck", 
                ComplianceFunctions.class.getMethod("check", String.class, String.class));
            
        } catch (NoSuchMethodException e) {
            throw new RuntimeException("Failed to register GRCOS functions", e);
        }
        
        expressionManager.setFunctions(functions);
        
        return expressionManager;
    }
}
```

## Custom Service Task Implementation

### Base Service Task Class

#### AbstractGRCOSServiceTask.java
```java
public abstract class AbstractGRCOSServiceTask implements JavaDelegate {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    @Autowired
    protected OSCALService oscalService;
    
    @Autowired
    protected BlockchainService blockchainService;
    
    @Autowired
    protected WorkflowAgent workflowAgent;
    
    @Override
    public final void execute(DelegateExecution execution) {
        String taskId = execution.getCurrentActivityId();
        String processInstanceId = execution.getProcessInstanceId();
        
        logger.info("Executing GRCOS service task: {} in process: {}", 
                   taskId, processInstanceId);
        
        try {
            // Pre-execution validation
            validateExecution(execution);
            
            // Execute task logic
            TaskExecutionResult result = executeTask(execution);
            
            // Store results
            storeResults(execution, result);
            
            // Record on blockchain
            recordBlockchainEvent(execution, result);
            
            // Notify AI agents
            notifyAIAgents(execution, result);
            
        } catch (Exception e) {
            handleExecutionError(execution, e);
            throw new BpmnError("TASK_EXECUTION_ERROR", e.getMessage());
        }
    }
    
    protected abstract void validateExecution(DelegateExecution execution) 
        throws ValidationException;
    
    protected abstract TaskExecutionResult executeTask(DelegateExecution execution) 
        throws TaskExecutionException;
    
    protected void storeResults(DelegateExecution execution, TaskExecutionResult result) {
        // Store results in process variables
        execution.setVariable("taskResult", result.toMap());
        execution.setVariable("taskStatus", result.getStatus());
        execution.setVariable("taskTimestamp", System.currentTimeMillis());
    }
    
    protected void recordBlockchainEvent(DelegateExecution execution, 
                                       TaskExecutionResult result) {
        try {
            TaskExecutionEvent event = new TaskExecutionEvent(
                execution.getCurrentActivityId(),
                execution.getProcessInstanceId(),
                result.getStatus(),
                result.getData(),
                System.currentTimeMillis()
            );
            
            blockchainService.recordTaskExecution(event);
            
        } catch (Exception e) {
            logger.warn("Failed to record task execution on blockchain", e);
            // Don't fail the task for blockchain recording issues
        }
    }
    
    protected void notifyAIAgents(DelegateExecution execution, TaskExecutionResult result) {
        try {
            TaskCompletionNotification notification = new TaskCompletionNotification(
                execution.getCurrentActivityId(),
                execution.getProcessInstanceId(),
                result
            );
            
            workflowAgent.notifyTaskCompletion(notification);
            
        } catch (Exception e) {
            logger.warn("Failed to notify AI agents", e);
            // Don't fail the task for AI notification issues
        }
    }
    
    protected void handleExecutionError(DelegateExecution execution, Exception error) {
        logger.error("Task execution failed: {}", error.getMessage(), error);
        
        // Store error information
        execution.setVariable("taskError", error.getMessage());
        execution.setVariable("taskErrorTimestamp", System.currentTimeMillis());
        
        // Record error on blockchain
        try {
            TaskErrorEvent errorEvent = new TaskErrorEvent(
                execution.getCurrentActivityId(),
                execution.getProcessInstanceId(),
                error.getMessage(),
                System.currentTimeMillis()
            );
            
            blockchainService.recordTaskError(errorEvent);
            
        } catch (Exception e) {
            logger.warn("Failed to record task error on blockchain", e);
        }
    }
}
```

## Database Setup

### PostgreSQL Schema Creation

#### schema.sql
```sql
-- GRCOS Flowable Database Schema

-- Create database
CREATE DATABASE grcos_flowable;

-- Create user
CREATE USER flowable_user WITH PASSWORD 'secure_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE grcos_flowable TO flowable_user;

-- Connect to database
\c grcos_flowable;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create custom tables for GRCOS integration
CREATE TABLE grcos_workflow_metadata (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    process_instance_id VARCHAR(64) NOT NULL,
    oscal_document_id VARCHAR(255),
    assessment_plan_id VARCHAR(255),
    workflow_type VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

CREATE TABLE grcos_task_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id VARCHAR(64) NOT NULL,
    process_instance_id VARCHAR(64) NOT NULL,
    task_type VARCHAR(100) NOT NULL,
    execution_result JSONB NOT NULL,
    blockchain_hash VARCHAR(256),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE grcos_ai_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    process_instance_id VARCHAR(64) NOT NULL,
    task_id VARCHAR(64),
    recommendation_type VARCHAR(100) NOT NULL,
    recommendation_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2),
    applied BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_grcos_workflow_metadata_process_id 
    ON grcos_workflow_metadata(process_instance_id);
CREATE INDEX idx_grcos_workflow_metadata_oscal_id 
    ON grcos_workflow_metadata(oscal_document_id);
CREATE INDEX idx_grcos_task_results_process_id 
    ON grcos_task_results(process_instance_id);
CREATE INDEX idx_grcos_task_results_task_id 
    ON grcos_task_results(task_id);
CREATE INDEX idx_grcos_ai_recommendations_process_id 
    ON grcos_ai_recommendations(process_instance_id);

-- Grant permissions to flowable user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO flowable_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO flowable_user;
```

## Docker Deployment

### Docker Compose Configuration

#### docker-compose.yml
```yaml
version: '3.8'

services:
  grcos-flowable:
    image: grcos/flowable-engine:latest
    container_name: grcos-flowable-engine
    ports:
      - "8080:8080"
      - "9090:9090" # Management port
    environment:
      - SPRING_PROFILES_ACTIVE=docker,grcos
      - DB_USERNAME=flowable_user
      - DB_PASSWORD=secure_password
      - CREW_AI_API_KEY=${CREW_AI_API_KEY}
      - DATAGERRY_API_KEY=${DATAGERRY_API_KEY}
      - WAZUH_USERNAME=${WAZUH_USERNAME}
      - WAZUH_PASSWORD=${WAZUH_PASSWORD}
    volumes:
      - ./config:/config
      - ./logs:/logs
      - ./workflows:/workflows
    depends_on:
      - postgres
      - redis
      - mongodb
    networks:
      - grcos-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  postgres:
    image: postgres:15
    container_name: grcos-postgres
    environment:
      - POSTGRES_DB=grcos_flowable
      - POSTGRES_USER=flowable_user
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./schema.sql:/docker-entrypoint-initdb.d/schema.sql
    networks:
      - grcos-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: grcos-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - grcos-network
    restart: unless-stopped

  mongodb:
    image: mongo:7
    container_name: grcos-mongodb
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=secure_password
    volumes:
      - mongodb_data:/data/db
    networks:
      - grcos-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  mongodb_data:

networks:
  grcos-network:
    driver: bridge
```

## Verification and Testing

### Health Check Endpoints

#### Test Engine Status
```bash
# Check engine health
curl http://localhost:8080/actuator/health

# Check process engine info
curl http://localhost:8080/flowable-rest/service/management/engine

# Check deployed process definitions
curl http://localhost:8080/flowable-rest/service/repository/process-definitions
```

### Sample Workflow Deployment

#### Deploy Test Workflow
```bash
# Deploy sample GRCOS workflow
curl -X POST \
  http://localhost:8080/flowable-rest/service/repository/deployments \
  -H 'Content-Type: multipart/form-data' \
  -F 'deployment=grcos-test-workflow' \
  -F 'file=@test-workflow.bpmn20.xml'
```

## Next Steps

1. **Configure AI Agents** - Set up CrewAI integration and workflow agents
2. **Implement Custom Service Tasks** - Create GRCOS-specific service tasks
3. **Set up Event Listeners** - Configure blockchain and AI event handling
4. **Deploy Sample Workflows** - Test with basic OSCAL-generated workflows
5. **Configure Monitoring** - Set up Prometheus metrics and Grafana dashboards

This setup provides a robust foundation for GRCOS workflow automation with AI intelligence and blockchain security.
