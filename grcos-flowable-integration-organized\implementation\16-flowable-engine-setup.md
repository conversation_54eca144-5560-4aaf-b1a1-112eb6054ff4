# Flowable Engine Setup for GRCOS Integration

## Overview

This guide provides comprehensive instructions for setting up and configuring the Flowable Engine within the GRCOS ecosystem. The setup includes engine configuration, database setup, clustering, security, and integration with GRCOS components.

## Prerequisites

### System Requirements

- **Java**: OpenJDK 17 or higher
- **Database**: PostgreSQL 14+ (primary), MongoDB 6+ (OSCAL data)
- **Memory**: Minimum 8GB RAM (16GB+ recommended for production)
- **Storage**: SSD storage with at least 100GB available space
- **Network**: High-speed network connectivity for distributed components

### Required Dependencies

```xml
<!-- pom.xml dependencies -->
<dependencies>
    <!-- Flowable Engine -->
    <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter</artifactId>
        <version>7.0.1</version>
    </dependency>
    
    <!-- Flowable UI Components -->
    <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-ui-modeler-app</artifactId>
        <version>7.0.1</version>
    </dependency>
    
    <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-ui-admin-app</artifactId>
        <version>7.0.1</version>
    </dependency>
    
    <!-- Database Drivers -->
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>42.7.1</version>
    </dependency>
    
    <!-- GRCOS Integration -->
    <dependency>
        <groupId>com.grcos</groupId>
        <artifactId>grcos-flowable-integration</artifactId>
        <version>1.0.0</version>
    </dependency>
    
    <!-- Monitoring and Metrics -->
    <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-prometheus</artifactId>
    </dependency>
    
    <!-- Security -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    
    <!-- Caching -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
</dependencies>
```

## Database Configuration

### PostgreSQL Setup

#### 1. Database Creation
```sql
-- Create GRCOS Flowable database
CREATE DATABASE grcos_flowable;

-- Create dedicated user
CREATE USER flowable_user WITH PASSWORD 'secure_password_here';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE grcos_flowable TO flowable_user;

-- Connect to the database
\c grcos_flowable;

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO flowable_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO flowable_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO flowable_user;
```

#### 2. Performance Optimization
```sql
-- Optimize PostgreSQL for Flowable
ALTER SYSTEM SET shared_buffers = '2GB';
ALTER SYSTEM SET effective_cache_size = '6GB';
ALTER SYSTEM SET maintenance_work_mem = '512MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Reload configuration
SELECT pg_reload_conf();
```

#### 3. Indexing Strategy
```sql
-- Create custom indexes for GRCOS-specific queries
CREATE INDEX CONCURRENTLY idx_act_ru_execution_grcos_business_key 
ON act_ru_execution (business_key_) 
WHERE business_key_ IS NOT NULL;

CREATE INDEX CONCURRENTLY idx_act_hi_procinst_grcos_start_time 
ON act_hi_procinst (start_time_) 
WHERE end_time_ IS NULL;

CREATE INDEX CONCURRENTLY idx_act_ru_task_grcos_assignee_created 
ON act_ru_task (assignee_, create_time_) 
WHERE assignee_ IS NOT NULL;

-- Partial indexes for active processes
CREATE INDEX CONCURRENTLY idx_act_ru_execution_active 
ON act_ru_execution (proc_inst_id_, act_id_) 
WHERE is_active_ = true;
```

## Flowable Engine Configuration

### Application Configuration

#### application.yml
```yaml
# GRCOS Flowable Configuration
spring:
  application:
    name: grcos-flowable-engine
  
  # Database Configuration
  datasource:
    primary:
      url: ******************************************************
      username: flowable_user
      password: ${DB_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        leak-detection-threshold: 60000
    
    # MongoDB for OSCAL data
    mongodb:
      uri: mongodb://mongodb-cluster:27017/grcos_oscal
      
  # Redis Configuration
  redis:
    host: redis-cluster
    port: 6379
    password: ${REDIS_PASSWORD}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

# Flowable Configuration
flowable:
  # Process Engine Configuration
  process:
    definition-cache-limit: 1000
    enable-safe-xml: true
    
  # Database Configuration
  database-schema-update: true
  db-identity-used: true
  
  # Async Executor Configuration
  async:
    executor:
      default-async-job-acquire-wait-time: 10000
      default-timer-job-acquire-wait-time: 10000
      async-executor-activate: true
      async-executor-core-pool-size: 8
      async-executor-max-pool-size: 16
      async-executor-queue-capacity: 1000
      
  # History Configuration
  history-level: FULL
  
  # REST API Configuration
  rest:
    app:
      authentication-mode: verify-privilege
      
  # UI Configuration
  modeler:
    app:
      deployment-api-url: http://localhost:8080/flowable-rest/service
      
# GRCOS Specific Configuration
grcos:
  flowable:
    # AI Agent Configuration
    agents:
      enabled: true
      crew-ai:
        api-key: ${CREW_AI_API_KEY}
        model: gpt-4
        temperature: 0.1
        
    # OSCAL Integration
    oscal:
      enabled: true
      validation:
        strict: true
        schema-validation: true
        
    # Blockchain Integration
    blockchain:
      enabled: true
      network-config: /config/blockchain/network-config.yaml
      wallet-path: /config/blockchain/wallet
      user-name: grcos-user
      channel-name: grcos-channel
      contract-name: workflow-audit
      
    # Monitoring Configuration
    monitoring:
      metrics:
        enabled: true
        export:
          prometheus:
            enabled: true
            step: 30s
      tracing:
        enabled: true
        jaeger:
          endpoint: http://jaeger:14268/api/traces
          
    # Security Configuration
    security:
      authentication:
        provider: oauth2
        oauth2:
          issuer-uri: ${OAUTH2_ISSUER_URI}
          client-id: ${OAUTH2_CLIENT_ID}
          client-secret: ${OAUTH2_CLIENT_SECRET}
      authorization:
        enabled: true
        default-role: GRCOS_USER
        
# Logging Configuration
logging:
  level:
    org.flowable: INFO
    com.grcos: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /logs/grcos-flowable.log
    max-size: 100MB
    max-history: 30

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,flowable
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### Engine Configuration Class

#### FlowableEngineConfiguration.java
```java
@Configuration
@EnableConfigurationProperties(GRCOSFlowableProperties.class)
public class FlowableEngineConfiguration {
    
    @Autowired
    private GRCOSFlowableProperties properties;
    
    @Bean
    @Primary
    public ProcessEngineConfiguration processEngineConfiguration(
            @Qualifier("primaryDataSource") DataSource dataSource) {
        
        SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
        
        // Database Configuration
        config.setDataSource(dataSource);
        config.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE);
        config.setDatabaseType("postgres");
        config.setDbIdentityUsed(true);
        
        // History Configuration
        config.setHistory(ProcessEngineConfiguration.HISTORY_FULL);
        config.setHistoryCleanupBatchSize(1000);
        config.setHistoryCleanupBatchThreshold(10);
        
        // Async Executor Configuration
        config.setAsyncExecutorActivate(true);
        config.setAsyncExecutorCorePoolSize(8);
        config.setAsyncExecutorMaxPoolSize(16);
        config.setAsyncExecutorQueueCapacity(1000);
        config.setAsyncExecutorDefaultAsyncJobAcquireWaitTime(Duration.ofSeconds(10));
        config.setAsyncExecutorDefaultTimerJobAcquireWaitTime(Duration.ofSeconds(10));
        
        // Performance Optimizations
        config.setProcessDefinitionCacheLimit(1000);
        config.setEnableSafeBpmnXml(true);
        config.setActivityFontName("Arial");
        config.setLabelFontName("Arial");
        config.setAnnotationFontName("Arial");
        
        // GRCOS Custom Configurations
        configureGRCOSExtensions(config);
        
        return config;
    }
    
    @Bean
    public ProcessEngine processEngine(ProcessEngineConfiguration configuration) {
        return configuration.buildProcessEngine();
    }
    
    @Bean
    public RuntimeService runtimeService(ProcessEngine processEngine) {
        return processEngine.getRuntimeService();
    }
    
    @Bean
    public TaskService taskService(ProcessEngine processEngine) {
        return processEngine.getTaskService();
    }
    
    @Bean
    public HistoryService historyService(ProcessEngine processEngine) {
        return processEngine.getHistoryService();
    }
    
    @Bean
    public RepositoryService repositoryService(ProcessEngine processEngine) {
        return processEngine.getRepositoryService();
    }
    
    @Bean
    public ManagementService managementService(ProcessEngine processEngine) {
        return processEngine.getManagementService();
    }
    
    private void configureGRCOSExtensions(SpringProcessEngineConfiguration config) {
        // Add GRCOS-specific event listeners
        List<FlowableEventListener> eventListeners = new ArrayList<>();
        eventListeners.add(new GRCOSWorkflowEventListener());
        eventListeners.add(new BlockchainAuditEventListener());
        eventListeners.add(new MetricsCollectionEventListener());
        config.setEventListeners(eventListeners);
        
        // Add custom function delegates
        Map<String, Object> beans = new HashMap<>();
        beans.put("grcosServiceTaskDelegate", new GRCOSServiceTaskDelegate());
        beans.put("oscalProcessorDelegate", new OSCALProcessorDelegate());
        beans.put("aiAgentDelegate", new AIAgentDelegate());
        config.setBeans(beans);
        
        // Configure custom identity service
        config.setCustomUserEntityManager(new GRCOSUserEntityManager());
        config.setCustomGroupEntityManager(new GRCOSGroupEntityManager());
        
        // Add custom job handlers
        Map<String, JobHandler> customJobHandlers = new HashMap<>();
        customJobHandlers.put("grcos-assessment-job", new AssessmentJobHandler());
        customJobHandlers.put("grcos-monitoring-job", new MonitoringJobHandler());
        config.setCustomJobHandlers(customJobHandlers);
    }
}
```

## Clustering Configuration

### Kubernetes Deployment

#### flowable-deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grcos-flowable-engine
  namespace: grcos
  labels:
    app: grcos-flowable-engine
    component: workflow-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: grcos-flowable-engine
  template:
    metadata:
      labels:
        app: grcos-flowable-engine
    spec:
      containers:
      - name: flowable-engine
        image: grcos/flowable-engine:1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: management
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes,production"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grcos-db-secret
              key: password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grcos-redis-secret
              key: password
        - name: OAUTH2_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: grcos-oauth2-secret
              key: client-secret
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8081
          initialDelaySeconds: 120
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 10
        volumeMounts:
        - name: config-volume
          mountPath: /config
        - name: logs-volume
          mountPath: /logs
      volumes:
      - name: config-volume
        configMap:
          name: grcos-flowable-config
      - name: logs-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: grcos-flowable-service
  namespace: grcos
spec:
  selector:
    app: grcos-flowable-engine
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: management
    port: 8081
    targetPort: 8081
  type: ClusterIP
```

### Load Balancer Configuration

#### nginx-flowable.conf
```nginx
upstream grcos_flowable_backend {
    least_conn;
    server grcos-flowable-service:8080 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 80;
    server_name flowable.grcos.local;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=flowable_api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=flowable_ui:10m rate=5r/s;
    
    # API endpoints
    location /flowable-rest/ {
        limit_req zone=flowable_api burst=20 nodelay;
        
        proxy_pass http://grcos_flowable_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # UI endpoints
    location /flowable-ui/ {
        limit_req zone=flowable_ui burst=10 nodelay;
        
        proxy_pass http://grcos_flowable_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Health check endpoint
    location /actuator/health {
        proxy_pass http://grcos_flowable_backend;
        access_log off;
    }
}
```

## Security Configuration

### OAuth2 Integration

#### SecurityConfiguration.java
```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfiguration {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/flowable-rest/**").hasRole("GRCOS_API_USER")
                .requestMatchers("/flowable-ui/**").hasRole("GRCOS_UI_USER")
                .anyRequest().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt
                    .jwtAuthenticationConverter(jwtAuthenticationConverter())
                )
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )
            .csrf(csrf -> csrf.disable())
            .cors(cors -> cors.configurationSource(corsConfigurationSource()));
        
        return http.build();
    }
    
    @Bean
    public JwtAuthenticationConverter jwtAuthenticationConverter() {
        JwtGrantedAuthoritiesConverter authoritiesConverter = new JwtGrantedAuthoritiesConverter();
        authoritiesConverter.setAuthorityPrefix("ROLE_");
        authoritiesConverter.setAuthoritiesClaimName("roles");
        
        JwtAuthenticationConverter converter = new JwtAuthenticationConverter();
        converter.setJwtGrantedAuthoritiesConverter(authoritiesConverter);
        return converter;
    }
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("https://*.grcos.local"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
```

This comprehensive setup guide provides the foundation for deploying Flowable Engine in a production GRCOS environment with proper security, clustering, and monitoring capabilities.
