<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine.runtime (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../org/activiti/engine/runtime/package-summary.html" target="classFrame">org.activiti.engine.runtime</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="Clock.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">Clock</span></a></li>
<li><a href="ClockReader.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">ClockReader</span></a></li>
<li><a href="Execution.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">Execution</span></a></li>
<li><a href="ExecutionQuery.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">ExecutionQuery</span></a></li>
<li><a href="Job.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">Job</span></a></li>
<li><a href="JobQuery.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">JobQuery</span></a></li>
<li><a href="NativeExecutionQuery.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">NativeExecutionQuery</span></a></li>
<li><a href="NativeProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">NativeProcessInstanceQuery</span></a></li>
<li><a href="ProcessInstance.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">ProcessInstance</span></a></li>
<li><a href="ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">ProcessInstanceBuilder</span></a></li>
<li><a href="ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime" target="classFrame"><span class="interfaceName">ProcessInstanceQuery</span></a></li>
</ul>
</div>
</body>
</html>
