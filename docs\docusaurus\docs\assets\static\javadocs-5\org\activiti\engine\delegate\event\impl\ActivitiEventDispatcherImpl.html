<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitiEventDispatcherImpl (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitiEventDispatcherImpl (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEventDispatcherImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html" target="_top">Frames</a></li>
<li><a href="ActivitiEventDispatcherImpl.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate.event.impl</div>
<h2 title="Class ActivitiEventDispatcherImpl" class="title">Class ActivitiEventDispatcherImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.delegate.event.impl.ActivitiEventDispatcherImpl</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventDispatcher</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ActivitiEventDispatcherImpl</span>
extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventDispatcher</a></pre>
<div class="block">Class capable of dispatching events.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Frederik Heremans</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#enabled">enabled</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventSupport.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventSupport</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#eventSupport">eventSupport</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#ActivitiEventDispatcherImpl--">ActivitiEventDispatcherImpl</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">addEventListener</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd)</code>
<div class="block">Adds an event-listener which will be notified of ALL events by the dispatcher.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-org.activiti.engine.delegate.event.ActivitiEventType...-">addEventListener</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd,
                <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>...&nbsp;types)</code>
<div class="block">Adds an event-listener which will only be notified when an event of the given types occurs.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#dispatchEvent-org.activiti.engine.delegate.event.ActivitiEvent-">dispatchEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">Dispatches the given event to any listeners that are registered.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#extractProcessDefinitionEntityFromEvent-org.activiti.engine.delegate.event.ActivitiEvent-">extractProcessDefinitionEntityFromEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">In case no process-context is active, this method attempts to extract a
 process-definition based on the event.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#isEnabled--">isEnabled</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#removeEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">removeEventListener</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToRemove)</code>
<div class="block">Removes the given listener from this dispatcher.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html#setEnabled-boolean-">setEnabled</a></span>(boolean&nbsp;enabled)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="eventSupport">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eventSupport</h4>
<pre>protected&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventSupport.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventSupport</a> eventSupport</pre>
</li>
</ul>
<a name="enabled">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>enabled</h4>
<pre>protected&nbsp;boolean enabled</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ActivitiEventDispatcherImpl--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ActivitiEventDispatcherImpl</h4>
<pre>public&nbsp;ActivitiEventDispatcherImpl()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnabled</h4>
<pre>public&nbsp;void&nbsp;setEnabled(boolean&nbsp;enabled)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#setEnabled-boolean-">setEnabled</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventDispatcher</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enabled</code> - true, if event dispatching should be enabled.</dd>
</dl>
</li>
</ul>
<a name="isEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isEnabled()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#isEnabled--">isEnabled</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventDispatcher</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true, if event dispatcher is enabled.</dd>
</dl>
</li>
</ul>
<a name="addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEventListener</h4>
<pre>public&nbsp;void&nbsp;addEventListener(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">ActivitiEventDispatcher</a></code></span></div>
<div class="block">Adds an event-listener which will be notified of ALL events by the dispatcher.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">addEventListener</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventDispatcher</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listenerToAdd</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-org.activiti.engine.delegate.event.ActivitiEventType...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEventListener</h4>
<pre>public&nbsp;void&nbsp;addEventListener(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd,
                             <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>...&nbsp;types)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-org.activiti.engine.delegate.event.ActivitiEventType...-">ActivitiEventDispatcher</a></code></span></div>
<div class="block">Adds an event-listener which will only be notified when an event of the given types occurs.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-org.activiti.engine.delegate.event.ActivitiEventType...-">addEventListener</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventDispatcher</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listenerToAdd</code> - the listener to add</dd>
<dd><code>types</code> - types of events the listener should be notified for</dd>
</dl>
</li>
</ul>
<a name="removeEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeEventListener</h4>
<pre>public&nbsp;void&nbsp;removeEventListener(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToRemove)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#removeEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">ActivitiEventDispatcher</a></code></span></div>
<div class="block">Removes the given listener from this dispatcher. The listener will no longer be notified,
 regardless of the type(s) it was registered for in the first place.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#removeEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">removeEventListener</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventDispatcher</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listenerToRemove</code> - listener to remove</dd>
</dl>
</li>
</ul>
<a name="dispatchEvent-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dispatchEvent</h4>
<pre>public&nbsp;void&nbsp;dispatchEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#dispatchEvent-org.activiti.engine.delegate.event.ActivitiEvent-">ActivitiEventDispatcher</a></code></span></div>
<div class="block">Dispatches the given event to any listeners that are registered.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#dispatchEvent-org.activiti.engine.delegate.event.ActivitiEvent-">dispatchEvent</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventDispatcher</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - event to dispatch.</dd>
</dl>
</li>
</ul>
<a name="extractProcessDefinitionEntityFromEvent-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>extractProcessDefinitionEntityFromEvent</h4>
<pre>protected&nbsp;org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity&nbsp;extractProcessDefinitionEntityFromEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block">In case no process-context is active, this method attempts to extract a
 process-definition based on the event. In case it's an event related to an
 entity, this can be deducted by inspecting the entity, without additional
 queries to the database.
 
 If not an entity-related event, the process-definition will be retrieved
 based on the processDefinitionId (if filled in). This requires an
 additional query to the database in case not already cached. However,
 queries will only occur when the definition is not yet in the cache, which
 is very unlikely to happen, unless evicted.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEventDispatcherImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html" target="_top">Frames</a></li>
<li><a href="ActivitiEventDispatcherImpl.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
