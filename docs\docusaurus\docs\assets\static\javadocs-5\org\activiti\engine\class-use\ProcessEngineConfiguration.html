<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class org.activiti.engine.ProcessEngineConfiguration (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class org.activiti.engine.ProcessEngineConfiguration (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/class-use/ProcessEngineConfiguration.html" target="_top">Frames</a></li>
<li><a href="ProcessEngineConfiguration.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class org.activiti.engine.ProcessEngineConfiguration" class="title">Uses of Class<br>org.activiti.engine.ProcessEngineConfiguration</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine">org.activiti.engine</a></td>
<td class="colLast">
<div class="block">Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine"><code>ProcessEngineConfiguration</code></a>
    (typically based on a configuration file), from which a <a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> can be obtained.<br/><br/>
    Through the services obtained from such a <a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b><a href="../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>: </b> Manages <a href="../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s <br/>

    <b><a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>: </b> For starting and searching <a href="../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s <br/>
    
    <b><a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>: </b> Exposes operations to manage human (standalone) <a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s, 
    such as claiming, completing and assigning tasks<br/>

    <b><a href="../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>: </b> Used for managing <a href="../../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s, 
    <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s and the relations between them<br/>
        
    <b><a href="../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b><a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b><a href="../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.activiti.engine.test">org.activiti.engine.test</a></td>
<td class="colLast">
<div class="block">Helper classes for testing processes.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a> in <a href="../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> that return <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromInputStream-java.io.InputStream-">createProcessEngineConfigurationFromInputStream</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromInputStream-java.io.InputStream-java.lang.String-">createProcessEngineConfigurationFromInputStream</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream,
                                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;beanName)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromResource-java.lang.String-">createProcessEngineConfigurationFromResource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resource)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromResource-java.lang.String-java.lang.String-">createProcessEngineConfigurationFromResource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resource,
                                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;beanName)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#createProcessEngineConfigurationFromResourceDefault--">createProcessEngineConfigurationFromResourceDefault</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#createStandaloneInMemProcessEngineConfiguration--">createStandaloneInMemProcessEngineConfiguration</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#createStandaloneProcessEngineConfiguration--">createStandaloneProcessEngineConfiguration</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">EngineServices.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/EngineServices.html#getProcessEngineConfiguration--">getProcessEngineConfiguration</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setActivityFontName-java.lang.String-">setActivityFontName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityFontName)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setAnnotationFontName-java.lang.String-">setAnnotationFontName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;annotationFontName)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setAsyncExecutor-org.activiti.engine.impl.asyncexecutor.AsyncExecutor-">setAsyncExecutor</a></span>(org.activiti.engine.impl.asyncexecutor.AsyncExecutor&nbsp;asyncExecutor)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setAsyncExecutorActivate-boolean-">setAsyncExecutorActivate</a></span>(boolean&nbsp;asyncExecutorActivate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setAsyncExecutorEnabled-boolean-">setAsyncExecutorEnabled</a></span>(boolean&nbsp;asyncExecutorEnabled)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setAsyncFailedJobWaitTime-int-">setAsyncFailedJobWaitTime</a></span>(int&nbsp;asyncFailedJobWaitTime)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setClassLoader-java.lang.ClassLoader-">setClassLoader</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/ClassLoader.html?is-external=true" title="class or interface in java.lang">ClassLoader</a>&nbsp;classLoader)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setClock-org.activiti.engine.runtime.Clock-">setClock</a></span>(<a href="../../../../org/activiti/engine/runtime/Clock.html" title="interface in org.activiti.engine.runtime">Clock</a>&nbsp;clock)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setCreateDiagramOnDeploy-boolean-">setCreateDiagramOnDeploy</a></span>(boolean&nbsp;createDiagramOnDeploy)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseCatalog-java.lang.String-">setDatabaseCatalog</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseCatalog)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseSchema-java.lang.String-">setDatabaseSchema</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseSchema)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseSchemaUpdate-java.lang.String-">setDatabaseSchemaUpdate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseSchemaUpdate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseTablePrefix-java.lang.String-">setDatabaseTablePrefix</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseTablePrefix)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseType-java.lang.String-">setDatabaseType</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseType)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDatabaseWildcardEscapeCharacter-java.lang.String-">setDatabaseWildcardEscapeCharacter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseWildcardEscapeCharacter)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDataSource-javax.sql.DataSource-">setDataSource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/javax/sql/DataSource.html?is-external=true" title="class or interface in javax.sql">DataSource</a>&nbsp;dataSource)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDataSourceJndiName-java.lang.String-">setDataSourceJndiName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataSourceJndiName)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDbHistoryUsed-boolean-">setDbHistoryUsed</a></span>(boolean&nbsp;isDbHistoryUsed)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDbIdentityUsed-boolean-">setDbIdentityUsed</a></span>(boolean&nbsp;isDbIdentityUsed)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDefaultCamelContext-java.lang.String-">setDefaultCamelContext</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;defaultCamelContext)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setDefaultFailedJobWaitTime-int-">setDefaultFailedJobWaitTime</a></span>(int&nbsp;defaultFailedJobWaitTime)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setEnableProcessDefinitionInfoCache-boolean-">setEnableProcessDefinitionInfoCache</a></span>(boolean&nbsp;enableProcessDefinitionInfoCache)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setHistory-java.lang.String-">setHistory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;history)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setHistoryLevel-org.activiti.engine.impl.history.HistoryLevel-">setHistoryLevel</a></span>(org.activiti.engine.impl.history.HistoryLevel&nbsp;historyLevel)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setIdBlockSize-int-">setIdBlockSize</a></span>(int&nbsp;idBlockSize)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcDefaultTransactionIsolationLevel-int-">setJdbcDefaultTransactionIsolationLevel</a></span>(int&nbsp;jdbcDefaultTransactionIsolationLevel)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcDriver-java.lang.String-">setJdbcDriver</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcDriver)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcMaxActiveConnections-int-">setJdbcMaxActiveConnections</a></span>(int&nbsp;jdbcMaxActiveConnections)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcMaxCheckoutTime-int-">setJdbcMaxCheckoutTime</a></span>(int&nbsp;jdbcMaxCheckoutTime)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcMaxIdleConnections-int-">setJdbcMaxIdleConnections</a></span>(int&nbsp;jdbcMaxIdleConnections)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcMaxWaitTime-int-">setJdbcMaxWaitTime</a></span>(int&nbsp;jdbcMaxWaitTime)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcPassword-java.lang.String-">setJdbcPassword</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcPassword)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcPingConnectionNotUsedFor-int-">setJdbcPingConnectionNotUsedFor</a></span>(int&nbsp;jdbcPingNotUsedFor)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcPingEnabled-boolean-">setJdbcPingEnabled</a></span>(boolean&nbsp;jdbcPingEnabled)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcPingQuery-java.lang.String-">setJdbcPingQuery</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcPingQuery)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcUrl-java.lang.String-">setJdbcUrl</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcUrl)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJdbcUsername-java.lang.String-">setJdbcUsername</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jdbcUsername)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJobExecutor-org.activiti.engine.impl.jobexecutor.JobExecutor-">setJobExecutor</a></span>(org.activiti.engine.impl.jobexecutor.JobExecutor&nbsp;jobExecutor)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJobExecutorActivate-boolean-">setJobExecutorActivate</a></span>(boolean&nbsp;jobExecutorActivate)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJpaCloseEntityManager-boolean-">setJpaCloseEntityManager</a></span>(boolean&nbsp;jpaCloseEntityManager)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJpaEntityManagerFactory-java.lang.Object-">setJpaEntityManagerFactory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;jpaEntityManagerFactory)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJpaHandleTransaction-boolean-">setJpaHandleTransaction</a></span>(boolean&nbsp;jpaHandleTransaction)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setJpaPersistenceUnitName-java.lang.String-">setJpaPersistenceUnitName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jpaPersistenceUnitName)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setLabelFontName-java.lang.String-">setLabelFontName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;labelFontName)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setLockTimeAsyncJobWaitTime-int-">setLockTimeAsyncJobWaitTime</a></span>(int&nbsp;lockTimeAsyncJobWaitTime)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerDefaultFrom-java.lang.String-">setMailServerDefaultFrom</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerDefaultFrom)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerHost-java.lang.String-">setMailServerHost</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerHost)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerPassword-java.lang.String-">setMailServerPassword</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerPassword)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerPort-int-">setMailServerPort</a></span>(int&nbsp;mailServerPort)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServers-java.util.Map-">setMailServers</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../../org/activiti/engine/cfg/MailServerInfo.html" title="class in org.activiti.engine.cfg">MailServerInfo</a>&gt;&nbsp;mailServers)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerUsername-java.lang.String-">setMailServerUsername</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailServerUsername)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerUseSSL-boolean-">setMailServerUseSSL</a></span>(boolean&nbsp;useSSL)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailServerUseTLS-boolean-">setMailServerUseTLS</a></span>(boolean&nbsp;useTLS)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailSessionJndi-java.lang.String-">setMailSessionJndi</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailSessionJndi)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setMailSessionsJndi-java.util.Map-">setMailSessionsJndi</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;mailSessionsJndi)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setProcessDiagramGenerator-org.activiti.image.ProcessDiagramGenerator-">setProcessDiagramGenerator</a></span>(org.activiti.image.ProcessDiagramGenerator&nbsp;processDiagramGenerator)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setProcessEngineLifecycleListener-org.activiti.engine.ProcessEngineLifecycleListener-">setProcessEngineLifecycleListener</a></span>(<a href="../../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine">ProcessEngineLifecycleListener</a>&nbsp;processEngineLifecycleListener)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setProcessEngineName-java.lang.String-">setProcessEngineName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processEngineName)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setTablePrefixIsSchema-boolean-">setTablePrefixIsSchema</a></span>(boolean&nbsp;tablePrefixIsSchema)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setTransactionsExternallyManaged-boolean-">setTransactionsExternallyManaged</a></span>(boolean&nbsp;transactionsExternallyManaged)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setUseClassForNameClassLoading-boolean-">setUseClassForNameClassLoading</a></span>(boolean&nbsp;useClassForNameClassLoading)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessEngineConfiguration.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html#setXmlEncoding-java.lang.String-">setXmlEncoding</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlEncoding)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.test">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a> in <a href="../../../../org/activiti/engine/test/package-summary.html">org.activiti.engine.test</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../org/activiti/engine/test/package-summary.html">org.activiti.engine.test</a> declared as <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ActivitiTestCase.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#processEngineConfiguration">processEngineConfiguration</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><span class="typeNameLabel">ActivitiRule.</span><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#processEngineConfiguration">processEngineConfiguration</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/class-use/ProcessEngineConfiguration.html" target="_top">Frames</a></li>
<li><a href="ProcessEngineConfiguration.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
