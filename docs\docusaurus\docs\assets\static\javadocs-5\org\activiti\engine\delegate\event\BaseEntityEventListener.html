<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BaseEntityEventListener (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BaseEntityEventListener (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BaseEntityEventListener.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/BaseEntityEventListener.html" target="_top">Frames</a></li>
<li><a href="BaseEntityEventListener.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate.event</div>
<h2 title="Class BaseEntityEventListener" class="title">Class BaseEntityEventListener</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.delegate.event.BaseEntityEventListener</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BaseEntityEventListener</span>
extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a></pre>
<div class="block">Base event listener that can be used when implementing an
 <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEventListener</code></a> to get notified when an entity is created,
 updated, deleted or if another entity-related event occurs.
 
 Override the <code>onXX(..)</code> methods to respond to entity changes
 accordingly.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Frederik Heremans</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#entityClass">entityClass</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#failOnException">failOnException</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#BaseEntityEventListener--">BaseEntityEventListener</a></span>()</code>
<div class="block">Create a new BaseEntityEventListener, notified when an event that targets
 any type of entity is received.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#BaseEntityEventListener-boolean-">BaseEntityEventListener</a></span>(boolean&nbsp;failOnException)</code>
<div class="block">Create a new BaseEntityEventListener.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#BaseEntityEventListener-boolean-java.lang.Class-">BaseEntityEventListener</a></span>(boolean&nbsp;failOnException,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;&nbsp;entityClass)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#isFailOnException--">isFailOnException</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#isValidEvent-org.activiti.engine.delegate.event.ActivitiEvent-">isValidEvent</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#onCreate-org.activiti.engine.delegate.event.ActivitiEvent-">onCreate</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">Called when an entity create event is received.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#onDelete-org.activiti.engine.delegate.event.ActivitiEvent-">onDelete</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">Called when an entity delete event is received.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#onEntityEvent-org.activiti.engine.delegate.event.ActivitiEvent-">onEntityEvent</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">Called when an event is received, which is not a create, an update or
 delete.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#onEvent-org.activiti.engine.delegate.event.ActivitiEvent-">onEvent</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">Called when an event has been fired</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#onInitialized-org.activiti.engine.delegate.event.ActivitiEvent-">onInitialized</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">Called when an entity initialized event is received.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#onUpdate-org.activiti.engine.delegate.event.ActivitiEvent-">onUpdate</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">Called when an entity update event is received.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="failOnException">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>failOnException</h4>
<pre>protected&nbsp;boolean failOnException</pre>
</li>
</ul>
<a name="entityClass">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>entityClass</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt; entityClass</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BaseEntityEventListener--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BaseEntityEventListener</h4>
<pre>public&nbsp;BaseEntityEventListener()</pre>
<div class="block">Create a new BaseEntityEventListener, notified when an event that targets
 any type of entity is received. Returning true when
 <a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#isFailOnException--"><code>isFailOnException()</code></a> is called.</div>
</li>
</ul>
<a name="BaseEntityEventListener-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BaseEntityEventListener</h4>
<pre>public&nbsp;BaseEntityEventListener(boolean&nbsp;failOnException)</pre>
<div class="block">Create a new BaseEntityEventListener.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>failOnException</code> - return value for <a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html#isFailOnException--"><code>isFailOnException()</code></a>.</dd>
</dl>
</li>
</ul>
<a name="BaseEntityEventListener-boolean-java.lang.Class-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BaseEntityEventListener</h4>
<pre>public&nbsp;BaseEntityEventListener(boolean&nbsp;failOnException,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;&nbsp;entityClass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="onEvent-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onEvent</h4>
<pre>public final&nbsp;void&nbsp;onEvent(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html#onEvent-org.activiti.engine.delegate.event.ActivitiEvent-">ActivitiEventListener</a></code></span></div>
<div class="block">Called when an event has been fired</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html#onEvent-org.activiti.engine.delegate.event.ActivitiEvent-">onEvent</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event</dd>
</dl>
</li>
</ul>
<a name="isFailOnException--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFailOnException</h4>
<pre>public&nbsp;boolean&nbsp;isFailOnException()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html#isFailOnException--">isFailOnException</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>whether or not the current operation should fail when this listeners execution
 throws an exception.</dd>
</dl>
</li>
</ul>
<a name="isValidEvent-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isValidEvent</h4>
<pre>protected&nbsp;boolean&nbsp;isValidEvent(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true, if the event is an <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a> and (if needed) the entityClass
 set in this instance, is assignable from the entity class in the event.</dd>
</dl>
</li>
</ul>
<a name="onCreate-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onCreate</h4>
<pre>protected&nbsp;void&nbsp;onCreate(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block">Called when an entity create event is received.</div>
</li>
</ul>
<a name="onInitialized-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onInitialized</h4>
<pre>protected&nbsp;void&nbsp;onInitialized(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block">Called when an entity initialized event is received.</div>
</li>
</ul>
<a name="onDelete-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDelete</h4>
<pre>protected&nbsp;void&nbsp;onDelete(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block">Called when an entity delete event is received.</div>
</li>
</ul>
<a name="onUpdate-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onUpdate</h4>
<pre>protected&nbsp;void&nbsp;onUpdate(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block">Called when an entity update event is received.</div>
</li>
</ul>
<a name="onEntityEvent-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onEntityEvent</h4>
<pre>protected&nbsp;void&nbsp;onEntityEvent(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block">Called when an event is received, which is not a create, an update or
 delete.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BaseEntityEventListener.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/BaseEntityEventListener.html" target="_top">Frames</a></li>
<li><a href="BaseEntityEventListener.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
