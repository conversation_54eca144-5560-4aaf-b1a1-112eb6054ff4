<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitiRule (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitiRule (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiRule.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html" title="class in org.activiti.engine.test"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/test/ActivitiRule.html" target="_top">Frames</a></li>
<li><a href="ActivitiRule.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.test</div>
<h2 title="Class ActivitiRule" class="title">Class ActivitiRule</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.test.ActivitiRule</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>org.junit.rules.TestRule</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ActivitiRule</span>
extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements org.junit.rules.TestRule</pre>
<div class="block">Convenience for ProcessEngine and services initialization in the form of a
 JUnit rule.
 
 <p>
 Usage:
 </p>
 
 <pre>
 public class YourTest {
 
   &#64;Rule
   public ActivitiRule activitiRule = new ActivitiRule();
   
   ...
 }
 </pre>
 
 <p>
 The ProcessEngine and the services will be made available to the test class
 through the getters of the activitiRule. The processEngine will be
 initialized by default with the activiti.cfg.xml resource on the classpath.
 To specify a different configuration file, pass the resource location in
 <a href="../../../../org/activiti/engine/test/ActivitiRule.html#ActivitiRule-java.lang.String-"><code>the appropriate constructor</code></a>. Process engines
 will be cached statically. Right before the first time the setUp is called
 for a given configuration resource, the process engine will be constructed.
 </p>
 
 <p>
 You can declare a deployment with the <a href="../../../../org/activiti/engine/test/Deployment.html" title="annotation in org.activiti.engine.test"><code>Deployment</code></a> annotation. This
 base class will make sure that this deployment gets deployed before the setUp
 and <a href="../../../../org/activiti/engine/RepositoryService.html#deleteDeployment-java.lang.String-boolean-"><code>cascade
 deleted</code></a> after the tearDown.
 </p>
 
 <p>
 The activitiRule also lets you <a href="../../../../org/activiti/engine/test/ActivitiRule.html#setCurrentTime-java.util.Date-"><code>set
 the current time used by the process engine</code></a>. This can be handy to control the
 exact time that is used by the engine in order to verify e.g. e.g. due dates
 of timers. Or start, end and duration times in the history service. In the
 tearDown, the internal clock will automatically be reset to use the current
 system time rather then the time that was set during a test method.
 </p></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#configurationResource">configurationResource</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#deploymentId">deploymentId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine">FormService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#formService">formService</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#historyService">historyService</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#identityService">identityService</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#managementService">managementService</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#mockSupport">mockSupport</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#processEngine">processEngine</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#processEngineConfiguration">processEngineConfiguration</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#repositoryService">repositoryService</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#runtimeService">runtimeService</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#taskService">taskService</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#ActivitiRule--">ActivitiRule</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#ActivitiRule-org.activiti.engine.ProcessEngine-">ActivitiRule</a></span>(<a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;processEngine)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#ActivitiRule-java.lang.String-">ActivitiRule</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;configurationResource)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>org.junit.runners.model.Statement</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#apply-org.junit.runners.model.Statement-org.junit.runner.Description-">apply</a></span>(org.junit.runners.model.Statement&nbsp;base,
     org.junit.runner.Description&nbsp;description)</code>
<div class="block">Implementation based on <code>TestWatcher</code>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#configureProcessEngine--">configureProcessEngine</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#failed-java.lang.Throwable-org.junit.runner.Description-">failed</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;e,
      org.junit.runner.Description&nbsp;description)</code>
<div class="block">Invoked when a test fails</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#finished-org.junit.runner.Description-">finished</a></span>(org.junit.runner.Description&nbsp;description)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getConfigurationResource--">getConfigurationResource</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine">FormService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getFormService--">getFormService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getHistoryService--">getHistoryService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getIdentityService--">getIdentityService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getManagementService--">getManagementService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getMockSupport--">getMockSupport</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getProcessEngine--">getProcessEngine</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getRepositoryService--">getRepositoryService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getRuntimeService--">getRuntimeService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#getTaskService--">getTaskService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#initializeMockSupport--">initializeMockSupport</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#initializeProcessEngine--">initializeProcessEngine</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#initializeServices--">initializeServices</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#mockSupport--">mockSupport</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setConfigurationResource-java.lang.String-">setConfigurationResource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;configurationResource)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setCurrentTime-java.util.Date-">setCurrentTime</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;currentTime)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setHistoricDataService-org.activiti.engine.HistoryService-">setHistoricDataService</a></span>(<a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a>&nbsp;historicDataService)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setIdentityService-org.activiti.engine.IdentityService-">setIdentityService</a></span>(<a href="../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a>&nbsp;identityService)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setManagementService-org.activiti.engine.ManagementService-">setManagementService</a></span>(<a href="../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a>&nbsp;managementService)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setProcessEngine-org.activiti.engine.ProcessEngine-">setProcessEngine</a></span>(<a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;processEngine)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setProcessEngineConfiguration-org.activiti.engine.impl.cfg.ProcessEngineConfigurationImpl-">setProcessEngineConfiguration</a></span>(org.activiti.engine.impl.cfg.ProcessEngineConfigurationImpl&nbsp;processEngineConfiguration)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setRepositoryService-org.activiti.engine.RepositoryService-">setRepositoryService</a></span>(<a href="../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a>&nbsp;repositoryService)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setRuntimeService-org.activiti.engine.RuntimeService-">setRuntimeService</a></span>(<a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a>&nbsp;runtimeService)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#setTaskService-org.activiti.engine.TaskService-">setTaskService</a></span>(<a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a>&nbsp;taskService)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#skipped-org.junit.internal.AssumptionViolatedException-org.junit.runner.Description-">skipped</a></span>(org.junit.internal.AssumptionViolatedException&nbsp;e,
       org.junit.runner.Description&nbsp;description)</code>
<div class="block">Invoked when a test is skipped due to a failed assumption.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#starting-org.junit.runner.Description-">starting</a></span>(org.junit.runner.Description&nbsp;description)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiRule.html#succeeded-org.junit.runner.Description-">succeeded</a></span>(org.junit.runner.Description&nbsp;description)</code>
<div class="block">Invoked when a test succeeds</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="configurationResource">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configurationResource</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> configurationResource</pre>
</li>
</ul>
<a name="deploymentId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentId</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> deploymentId</pre>
</li>
</ul>
<a name="processEngineConfiguration">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processEngineConfiguration</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a> processEngineConfiguration</pre>
</li>
</ul>
<a name="processEngine">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processEngine</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a> processEngine</pre>
</li>
</ul>
<a name="repositoryService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>repositoryService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a> repositoryService</pre>
</li>
</ul>
<a name="runtimeService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runtimeService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a> runtimeService</pre>
</li>
</ul>
<a name="taskService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a> taskService</pre>
</li>
</ul>
<a name="historyService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>historyService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a> historyService</pre>
</li>
</ul>
<a name="identityService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>identityService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a> identityService</pre>
</li>
</ul>
<a name="managementService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>managementService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a> managementService</pre>
</li>
</ul>
<a name="formService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine">FormService</a> formService</pre>
</li>
</ul>
<a name="mockSupport">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>mockSupport</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a> mockSupport</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ActivitiRule--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ActivitiRule</h4>
<pre>public&nbsp;ActivitiRule()</pre>
</li>
</ul>
<a name="ActivitiRule-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ActivitiRule</h4>
<pre>public&nbsp;ActivitiRule(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;configurationResource)</pre>
</li>
</ul>
<a name="ActivitiRule-org.activiti.engine.ProcessEngine-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ActivitiRule</h4>
<pre>public&nbsp;ActivitiRule(<a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;processEngine)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="apply-org.junit.runners.model.Statement-org.junit.runner.Description-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>apply</h4>
<pre>public&nbsp;org.junit.runners.model.Statement&nbsp;apply(org.junit.runners.model.Statement&nbsp;base,
                                               org.junit.runner.Description&nbsp;description)</pre>
<div class="block">Implementation based on <code>TestWatcher</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>apply</code>&nbsp;in interface&nbsp;<code>org.junit.rules.TestRule</code></dd>
</dl>
</li>
</ul>
<a name="succeeded-org.junit.runner.Description-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>succeeded</h4>
<pre>protected&nbsp;void&nbsp;succeeded(org.junit.runner.Description&nbsp;description)</pre>
<div class="block">Invoked when a test succeeds</div>
</li>
</ul>
<a name="failed-java.lang.Throwable-org.junit.runner.Description-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>failed</h4>
<pre>protected&nbsp;void&nbsp;failed(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;e,
                      org.junit.runner.Description&nbsp;description)</pre>
<div class="block">Invoked when a test fails</div>
</li>
</ul>
<a name="skipped-org.junit.internal.AssumptionViolatedException-org.junit.runner.Description-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>skipped</h4>
<pre>protected&nbsp;void&nbsp;skipped(org.junit.internal.AssumptionViolatedException&nbsp;e,
                       org.junit.runner.Description&nbsp;description)</pre>
<div class="block">Invoked when a test is skipped due to a failed assumption.</div>
</li>
</ul>
<a name="starting-org.junit.runner.Description-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>starting</h4>
<pre>protected&nbsp;void&nbsp;starting(org.junit.runner.Description&nbsp;description)</pre>
</li>
</ul>
<a name="initializeProcessEngine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initializeProcessEngine</h4>
<pre>protected&nbsp;void&nbsp;initializeProcessEngine()</pre>
</li>
</ul>
<a name="initializeServices--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initializeServices</h4>
<pre>protected&nbsp;void&nbsp;initializeServices()</pre>
</li>
</ul>
<a name="initializeMockSupport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initializeMockSupport</h4>
<pre>protected&nbsp;void&nbsp;initializeMockSupport()</pre>
</li>
</ul>
<a name="configureProcessEngine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configureProcessEngine</h4>
<pre>protected&nbsp;void&nbsp;configureProcessEngine()</pre>
</li>
</ul>
<a name="finished-org.junit.runner.Description-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finished</h4>
<pre>protected&nbsp;void&nbsp;finished(org.junit.runner.Description&nbsp;description)</pre>
</li>
</ul>
<a name="setCurrentTime-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrentTime</h4>
<pre>public&nbsp;void&nbsp;setCurrentTime(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;currentTime)</pre>
</li>
</ul>
<a name="getConfigurationResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigurationResource</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getConfigurationResource()</pre>
</li>
</ul>
<a name="setConfigurationResource-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfigurationResource</h4>
<pre>public&nbsp;void&nbsp;setConfigurationResource(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;configurationResource)</pre>
</li>
</ul>
<a name="getProcessEngine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessEngine</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;getProcessEngine()</pre>
</li>
</ul>
<a name="setProcessEngine-org.activiti.engine.ProcessEngine-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProcessEngine</h4>
<pre>public&nbsp;void&nbsp;setProcessEngine(<a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;processEngine)</pre>
</li>
</ul>
<a name="getRepositoryService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRepositoryService</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a>&nbsp;getRepositoryService()</pre>
</li>
</ul>
<a name="setRepositoryService-org.activiti.engine.RepositoryService-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRepositoryService</h4>
<pre>public&nbsp;void&nbsp;setRepositoryService(<a href="../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a>&nbsp;repositoryService)</pre>
</li>
</ul>
<a name="getRuntimeService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuntimeService</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a>&nbsp;getRuntimeService()</pre>
</li>
</ul>
<a name="setRuntimeService-org.activiti.engine.RuntimeService-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRuntimeService</h4>
<pre>public&nbsp;void&nbsp;setRuntimeService(<a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a>&nbsp;runtimeService)</pre>
</li>
</ul>
<a name="getTaskService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskService</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a>&nbsp;getTaskService()</pre>
</li>
</ul>
<a name="setTaskService-org.activiti.engine.TaskService-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTaskService</h4>
<pre>public&nbsp;void&nbsp;setTaskService(<a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a>&nbsp;taskService)</pre>
</li>
</ul>
<a name="getHistoryService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHistoryService</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a>&nbsp;getHistoryService()</pre>
</li>
</ul>
<a name="setHistoricDataService-org.activiti.engine.HistoryService-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHistoricDataService</h4>
<pre>public&nbsp;void&nbsp;setHistoricDataService(<a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a>&nbsp;historicDataService)</pre>
</li>
</ul>
<a name="getIdentityService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIdentityService</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a>&nbsp;getIdentityService()</pre>
</li>
</ul>
<a name="setIdentityService-org.activiti.engine.IdentityService-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIdentityService</h4>
<pre>public&nbsp;void&nbsp;setIdentityService(<a href="../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a>&nbsp;identityService)</pre>
</li>
</ul>
<a name="getManagementService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManagementService</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a>&nbsp;getManagementService()</pre>
</li>
</ul>
<a name="getFormService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFormService</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine">FormService</a>&nbsp;getFormService()</pre>
</li>
</ul>
<a name="setManagementService-org.activiti.engine.ManagementService-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManagementService</h4>
<pre>public&nbsp;void&nbsp;setManagementService(<a href="../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a>&nbsp;managementService)</pre>
</li>
</ul>
<a name="setProcessEngineConfiguration-org.activiti.engine.impl.cfg.ProcessEngineConfigurationImpl-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProcessEngineConfiguration</h4>
<pre>public&nbsp;void&nbsp;setProcessEngineConfiguration(org.activiti.engine.impl.cfg.ProcessEngineConfigurationImpl&nbsp;processEngineConfiguration)</pre>
</li>
</ul>
<a name="getMockSupport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMockSupport</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a>&nbsp;getMockSupport()</pre>
</li>
</ul>
<a name="mockSupport--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>mockSupport</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a>&nbsp;mockSupport()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiRule.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html" title="class in org.activiti.engine.test"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/test/ActivitiRule.html" target="_top">Frames</a></li>
<li><a href="ActivitiRule.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
