<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MockElResolver (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MockElResolver (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/MockElResolver.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../org/activiti/engine/test/mock/MockExpressionManager.html" title="class in org.activiti.engine.test.mock"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/test/mock/MockElResolver.html" target="_top">Frames</a></li>
<li><a href="MockElResolver.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.activiti.engine.impl.javax.el.ELResolver">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.test.mock</div>
<h2 title="Class MockElResolver" class="title">Class MockElResolver</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.impl.javax.el.ELResolver</li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.test.mock.MockElResolver</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">MockElResolver</span>
extends org.activiti.engine.impl.javax.el.ELResolver</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.activiti.engine.impl.javax.el.ELResolver">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.activiti.engine.impl.javax.el.ELResolver</h3>
<code>RESOLVABLE_AT_DESIGN_TIME, TYPE</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/test/mock/MockElResolver.html#MockElResolver--">MockElResolver</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/test/mock/MockElResolver.html#getCommonPropertyType-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-">getCommonPropertyType</a></span>(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base)</code>
<div class="block">Returns the most general type that this resolver accepts for the property argument, given a
 base object.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Iterator.html?is-external=true" title="class or interface in java.util">Iterator</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/beans/FeatureDescriptor.html?is-external=true" title="class or interface in java.beans">FeatureDescriptor</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/test/mock/MockElResolver.html#getFeatureDescriptors-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-">getFeatureDescriptors</a></span>(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base)</code>
<div class="block">Returns information about the set of variables or properties that can be resolved for the
 given base object.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/test/mock/MockElResolver.html#getType-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-java.lang.Object-">getType</a></span>(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base,
       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;property)</code>
<div class="block">For a given base and property, attempts to identify the most general type that is acceptable
 for an object to be passed as the value parameter in a future call to the
 <code>ELResolver.setValue(ELContext, Object, Object, Object)</code> method.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/test/mock/MockElResolver.html#getValue-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-java.lang.Object-">getValue</a></span>(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;property)</code>
<div class="block">Attempts to resolve the given property object on the given base object.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/test/mock/MockElResolver.html#isReadOnly-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-java.lang.Object-">isReadOnly</a></span>(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base,
          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;property)</code>
<div class="block">For a given base and property, attempts to determine whether a call to
 <code>ELResolver.setValue(ELContext, Object, Object, Object)</code> will always fail.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/test/mock/MockElResolver.html#setValue-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-java.lang.Object-java.lang.Object-">setValue</a></span>(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;property,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Attempts to set the value of the given property object on the given base object.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.impl.javax.el.ELResolver">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.activiti.engine.impl.javax.el.ELResolver</h3>
<code>invoke</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="MockElResolver--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MockElResolver</h4>
<pre>public&nbsp;MockElResolver()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCommonPropertyType-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCommonPropertyType</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;&nbsp;getCommonPropertyType(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></span></div>
<div class="block">Returns the most general type that this resolver accepts for the property argument, given a
 base object. One use for this method is to assist tools in auto-completion. This assists
 tools in auto-completion and also provides a way to express that the resolver accepts a
 primitive value, such as an integer index into an array. For example, the
 <code>ArrayELResolver</code> will accept any int as a property, so the return value would be
 Integer.class.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getCommonPropertyType</code>&nbsp;in class&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - The context of this evaluation.</dd>
<dd><code>base</code> - The base object to return the most general property type for, or null to enumerate
            the set of top-level variables that this resolver can evaluate.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>null if this ELResolver does not know how to handle the given base object; otherwise
         Object.class if any type of property is accepted; otherwise the most general property
         type accepted for the given base.</dd>
</dl>
</li>
</ul>
<a name="getFeatureDescriptors-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFeatureDescriptors</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Iterator.html?is-external=true" title="class or interface in java.util">Iterator</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/beans/FeatureDescriptor.html?is-external=true" title="class or interface in java.beans">FeatureDescriptor</a>&gt;&nbsp;getFeatureDescriptors(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></span></div>
<div class="block">Returns information about the set of variables or properties that can be resolved for the
 given base object. One use for this method is to assist tools in auto-completion. If the base
 parameter is null, the resolver must enumerate the list of top-level variables it can
 resolve. The Iterator returned must contain zero or more instances of
 java.beans.FeatureDescriptor, in no guaranteed order. In the case of primitive types such as
 int, the value null must be returned. This is to prevent the useless iteration through all
 possible primitive values. A return value of null indicates that this resolver does not
 handle the given base object or that the results are too complex to represent with this
 method and the <code>ELResolver.getCommonPropertyType(ELContext, Object)</code> method should be used
 instead. Each FeatureDescriptor will contain information about a single variable or property.
 In addition to the standard properties, the FeatureDescriptor must have two named attributes
 (as set by the setValue method):
 <ul>
 <li><code>ELResolver.TYPE</code> - The value of this named attribute must be an instance of java.lang.Class
 and specify the runtime type of the variable or property.</li>
 <li><code>ELResolver.RESOLVABLE_AT_DESIGN_TIME</code> - The value of this named attribute must be an
 instance of java.lang.Boolean and indicates whether it is safe to attempt to resolve this
 property at designtime. For instance, it may be unsafe to attempt a resolution at design time
 if the ELResolver needs access to a resource that is only available at runtime and no
 acceptable simulated value can be provided.</li>
 </ul>
 The caller should be aware that the Iterator returned might iterate through a very large or
 even infinitely large set of properties. Care should be taken by the caller to not get stuck
 in an infinite loop. This is a "best-effort" list. Not all ELResolvers will return completely
 accurate results, but all must be callable at both design-time and runtime (i.e. whether or
 not Beans.isDesignTime() returns true), without causing errors. The propertyResolved property
 of the ELContext is not relevant to this method. The results of all ELResolvers are
 concatenated in the case of composite resolvers.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getFeatureDescriptors</code>&nbsp;in class&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - The context of this evaluation.</dd>
<dd><code>base</code> - The base object whose set of valid properties is to be enumerated, or null to
            enumerate the set of top-level variables that this resolver can evaluate.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An Iterator containing zero or more (possibly infinitely more) FeatureDescriptor
         objects, or null if this resolver does not handle the given base object or that the
         results are too complex to represent with this method</dd>
</dl>
</li>
</ul>
<a name="getType-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;&nbsp;getType(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;property)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></span></div>
<div class="block">For a given base and property, attempts to identify the most general type that is acceptable
 for an object to be passed as the value parameter in a future call to the
 <code>ELResolver.setValue(ELContext, Object, Object, Object)</code> method. If this resolver handles the
 given (base, property) pair, the propertyResolved property of the ELContext object must be
 set to true by the resolver, before returning. If this property is not true after this method
 is called, the caller should ignore the return value. This is not always the same as
 getValue().getClass(). For example, in the case of an <code>ArrayELResolver</code>, the getType
 method will return the element type of the array, which might be a superclass of the type of
 the actual element that is currently in the specified array element.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getType</code>&nbsp;in class&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - The context of this evaluation.</dd>
<dd><code>base</code> - The base object whose property value is to be analyzed, or null to analyze a
            top-level variable.</dd>
<dd><code>property</code> - The property or variable to return the acceptable type for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>If the propertyResolved property of ELContext was set to true, then the most general
         acceptable type; otherwise undefined.</dd>
</dl>
</li>
</ul>
<a name="getValue-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getValue(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;property)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></span></div>
<div class="block">Attempts to resolve the given property object on the given base object. If this resolver
 handles the given (base, property) pair, the propertyResolved property of the ELContext
 object must be set to true by the resolver, before returning. If this property is not true
 after this method is called, the caller should ignore the return value.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getValue</code>&nbsp;in class&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - The context of this evaluation.</dd>
<dd><code>base</code> - The base object whose property value is to be returned, or null to resolve a
            top-level variable.</dd>
<dd><code>property</code> - The property or variable to be resolved.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>If the propertyResolved property of ELContext was set to true, then the result of the
         variable or property resolution; otherwise undefined.</dd>
</dl>
</li>
</ul>
<a name="isReadOnly-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isReadOnly</h4>
<pre>public&nbsp;boolean&nbsp;isReadOnly(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;property)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></span></div>
<div class="block">For a given base and property, attempts to determine whether a call to
 <code>ELResolver.setValue(ELContext, Object, Object, Object)</code> will always fail. If this resolver
 handles the given (base, property) pair, the propertyResolved property of the ELContext
 object must be set to true by the resolver, before returning. If this property is not true
 after this method is called, the caller should ignore the return value.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>isReadOnly</code>&nbsp;in class&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - The context of this evaluation.</dd>
<dd><code>base</code> - The base object whose property value is to be analyzed, or null to analyze a
            top-level variable.</dd>
<dd><code>property</code> - The property or variable to return the read-only status for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>If the propertyResolved property of ELContext was set to true, then true if the
         property is read-only or false if not; otherwise undefined.</dd>
</dl>
</li>
</ul>
<a name="setValue-org.activiti.engine.impl.javax.el.ELContext-java.lang.Object-java.lang.Object-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setValue</h4>
<pre>public&nbsp;void&nbsp;setValue(org.activiti.engine.impl.javax.el.ELContext&nbsp;context,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;base,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;property,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></span></div>
<div class="block">Attempts to set the value of the given property object on the given base object. If this
 resolver handles the given (base, property) pair, the propertyResolved property of the
 ELContext object must be set to true by the resolver, before returning. If this property is
 not true after this method is called, the caller can safely assume no value has been set.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>setValue</code>&nbsp;in class&nbsp;<code>org.activiti.engine.impl.javax.el.ELResolver</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - The context of this evaluation.</dd>
<dd><code>base</code> - The base object whose property value is to be set, or null to set a top-level
            variable.</dd>
<dd><code>property</code> - The property or variable to be set.</dd>
<dd><code>value</code> - The value to set the property or variable to.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/MockElResolver.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../org/activiti/engine/test/mock/MockExpressionManager.html" title="class in org.activiti.engine.test.mock"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/test/mock/MockElResolver.html" target="_top">Frames</a></li>
<li><a href="MockElResolver.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.activiti.engine.impl.javax.el.ELResolver">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
