# CrewAI Integration Setup for GRCOS Flowable

## Overview

This guide provides detailed instructions for integrating CrewAI's multi-agent orchestration framework with the GRCOS Flowable workflow engine. The integration enables intelligent workflow automation through coordinated AI agents that can make decisions, optimize processes, and adapt to changing conditions.

## CrewAI Architecture Integration

### Agent Framework Setup

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        GRCOS CrewAI Integration Architecture                     │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        CrewAI Agent Layer                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │ Workflow    │  │ Assessment  │  │ Compliance  │  │    Risk     │       │ │
│  │  │Orchestrator │  │Intelligence │  │ Automation  │  │  Analysis   │       │ │
│  │  │   Agent     │  │   Agent     │  │   Agent     │  │   Agent     │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                     CrewAI Coordination Layer                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │    Crew     │  │    Task     │  │ Knowledge   │  │ Decision    │       │ │
│  │  │  Manager    │  │Coordinator  │  │   Sharing   │  │ Consensus   │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                    Flowable Integration Layer                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │  Service    │  │   Event     │  │  Variable   │  │  Decision   │       │ │
│  │  │   Tasks     │  │ Listeners   │  │  Resolvers  │  │  Tables     │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                      Flowable Engine                                        │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## CrewAI Configuration

### Dependencies and Setup

#### pom.xml
```xml
<dependencies>
    <!-- CrewAI Java Integration -->
    <dependency>
        <groupId>com.crewai</groupId>
        <artifactId>crewai-java-client</artifactId>
        <version>1.0.0</version>
    </dependency>
    
    <!-- OpenAI API for LLM Integration -->
    <dependency>
        <groupId>com.theokanning.openai-gpt3-java</groupId>
        <artifactId>service</artifactId>
        <version>0.18.2</version>
    </dependency>
    
    <!-- Langchain4j for LLM Operations -->
    <dependency>
        <groupId>dev.langchain4j</groupId>
        <artifactId>langchain4j</artifactId>
        <version>0.25.0</version>
    </dependency>
    
    <dependency>
        <groupId>dev.langchain4j</groupId>
        <artifactId>langchain4j-open-ai</artifactId>
        <version>0.25.0</version>
    </dependency>
    
    <!-- Vector Database for Knowledge Storage -->
    <dependency>
        <groupId>dev.langchain4j</groupId>
        <artifactId>langchain4j-embeddings-all-minilm-l6-v2</artifactId>
        <version>0.25.0</version>
    </dependency>
    
    <!-- JSON Processing -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
    </dependency>
</dependencies>
```

### CrewAI Configuration Class

#### CrewAIConfiguration.java
```java
@Configuration
@EnableConfigurationProperties(CrewAIProperties.class)
public class CrewAIConfiguration {
    
    @Autowired
    private CrewAIProperties properties;
    
    @Bean
    public OpenAiChatModel chatModel() {
        return OpenAiChatModel.builder()
            .apiKey(properties.getOpenai().getApiKey())
            .modelName(properties.getOpenai().getModelName())
            .temperature(properties.getOpenai().getTemperature())
            .maxTokens(properties.getOpenai().getMaxTokens())
            .timeout(Duration.ofSeconds(properties.getOpenai().getTimeoutSeconds()))
            .build();
    }
    
    @Bean
    public EmbeddingModel embeddingModel() {
        return new AllMiniLmL6V2EmbeddingModel();
    }
    
    @Bean
    public EmbeddingStore<TextSegment> embeddingStore() {
        return new InMemoryEmbeddingStore<>();
    }
    
    @Bean
    public ContentRetriever contentRetriever(EmbeddingStore<TextSegment> embeddingStore,
                                           EmbeddingModel embeddingModel) {
        return EmbeddingStoreContentRetriever.builder()
            .embeddingStore(embeddingStore)
            .embeddingModel(embeddingModel)
            .maxResults(properties.getRetrieval().getMaxResults())
            .minScore(properties.getRetrieval().getMinScore())
            .build();
    }
    
    @Bean
    public CrewAIClient crewAIClient() {
        return CrewAIClient.builder()
            .apiKey(properties.getCrewai().getApiKey())
            .baseUrl(properties.getCrewai().getBaseUrl())
            .timeout(Duration.ofSeconds(properties.getCrewai().getTimeoutSeconds()))
            .build();
    }
    
    @Bean
    public AgentRegistry agentRegistry() {
        return new AgentRegistry();
    }
    
    @Bean
    public CrewManager crewManager(AgentRegistry agentRegistry, CrewAIClient client) {
        return new CrewManager(agentRegistry, client);
    }
}
```

### Properties Configuration

#### CrewAIProperties.java
```java
@ConfigurationProperties(prefix = "grcos.crewai")
@Data
public class CrewAIProperties {
    
    private OpenAI openai = new OpenAI();
    private CrewAI crewai = new CrewAI();
    private Retrieval retrieval = new Retrieval();
    private Agents agents = new Agents();
    
    @Data
    public static class OpenAI {
        private String apiKey;
        private String modelName = "gpt-4";
        private Double temperature = 0.1;
        private Integer maxTokens = 2000;
        private Integer timeoutSeconds = 60;
    }
    
    @Data
    public static class CrewAI {
        private String apiKey;
        private String baseUrl = "https://api.crewai.com";
        private Integer timeoutSeconds = 120;
        private Boolean enableLogging = true;
    }
    
    @Data
    public static class Retrieval {
        private Integer maxResults = 10;
        private Double minScore = 0.7;
        private Integer chunkSize = 1000;
        private Integer chunkOverlap = 200;
    }
    
    @Data
    public static class Agents {
        private Boolean enableWorkflowOrchestrator = true;
        private Boolean enableAssessmentIntelligence = true;
        private Boolean enableComplianceAutomation = true;
        private Boolean enableRiskAnalysis = true;
        private Boolean enablePerformanceOptimization = true;
    }
}
```

## Base Agent Implementation

### Abstract Base Agent

#### BaseGRCOSAgent.java
```java
@Component
public abstract class BaseGRCOSAgent {
    
    @Autowired
    protected OpenAiChatModel chatModel;
    
    @Autowired
    protected ContentRetriever contentRetriever;
    
    @Autowired
    protected AgentKnowledgeBase knowledgeBase;
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    public abstract String getRole();
    public abstract String getGoal();
    public abstract String getBackstory();
    
    protected ChatResponse executeTask(String taskDescription, Map<String, Object> context) {
        try {
            // Build context-aware prompt
            String prompt = buildContextualPrompt(taskDescription, context);
            
            // Retrieve relevant knowledge
            List<Content> relevantContent = contentRetriever.retrieve(Query.from(taskDescription));
            
            // Enhance prompt with retrieved knowledge
            String enhancedPrompt = enhancePromptWithKnowledge(prompt, relevantContent);
            
            // Execute chat completion
            ChatResponse response = chatModel.chat(ChatRequest.builder()
                .messages(List.of(
                    SystemMessage.from(buildSystemMessage()),
                    UserMessage.from(enhancedPrompt)
                ))
                .build());
            
            // Store interaction for learning
            storeInteraction(taskDescription, context, response);
            
            return response;
            
        } catch (Exception e) {
            logger.error("Failed to execute task: {}", taskDescription, e);
            throw new AgentExecutionException("Task execution failed", e);
        }
    }
    
    protected String buildSystemMessage() {
        return String.format(
            "You are a %s. Your goal is to %s. %s\n\n" +
            "You are working within the GRCOS (GRC Operating System) platform, " +
            "which uses OSCAL standards for compliance management and Flowable for workflow automation. " +
            "Always provide structured, actionable responses that can be integrated into automated workflows.",
            getRole(), getGoal(), getBackstory()
        );
    }
    
    protected String buildContextualPrompt(String taskDescription, Map<String, Object> context) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("Task: ").append(taskDescription).append("\n\n");
        
        if (context != null && !context.isEmpty()) {
            prompt.append("Context:\n");
            for (Map.Entry<String, Object> entry : context.entrySet()) {
                prompt.append("- ").append(entry.getKey()).append(": ")
                      .append(entry.getValue()).append("\n");
            }
            prompt.append("\n");
        }
        
        prompt.append("Please provide a detailed response with specific recommendations and actions.");
        
        return prompt.toString();
    }
    
    protected String enhancePromptWithKnowledge(String prompt, List<Content> relevantContent) {
        if (relevantContent.isEmpty()) {
            return prompt;
        }
        
        StringBuilder enhanced = new StringBuilder();
        enhanced.append("Relevant Knowledge:\n");
        
        for (Content content : relevantContent) {
            enhanced.append("- ").append(content.textSegment().text()).append("\n");
        }
        
        enhanced.append("\n").append(prompt);
        
        return enhanced.toString();
    }
    
    protected void storeInteraction(String task, Map<String, Object> context, ChatResponse response) {
        AgentInteraction interaction = AgentInteraction.builder()
            .agentId(getClass().getSimpleName())
            .task(task)
            .context(context)
            .response(response.content().text())
            .timestamp(Instant.now())
            .build();
        
        knowledgeBase.storeInteraction(interaction);
    }
}
```

### Workflow Orchestrator Agent

#### WorkflowOrchestratorAgent.java
```java
@Component
@ConditionalOnProperty(name = "grcos.crewai.agents.enable-workflow-orchestrator", havingValue = "true")
public class WorkflowOrchestratorAgent extends BaseGRCOSAgent {
    
    @Autowired
    private FlowableRuntimeService runtimeService;
    
    @Autowired
    private FlowableTaskService taskService;
    
    @Autowired
    private WorkflowAnalyticsService analyticsService;
    
    @Override
    public String getRole() {
        return "Workflow Orchestration Specialist";
    }
    
    @Override
    public String getGoal() {
        return "Optimize workflow execution and ensure efficient process automation across all GRCOS modules";
    }
    
    @Override
    public String getBackstory() {
        return "You are an expert in business process management with deep knowledge of GRCOS workflows, " +
               "OSCAL standards, and compliance automation. You understand how to optimize workflow performance, " +
               "route tasks intelligently, and predict potential issues before they occur.";
    }
    
    public WorkflowOptimizationResult optimizeWorkflow(String processInstanceId) {
        Map<String, Object> context = new HashMap<>();
        context.put("processInstanceId", processInstanceId);
        context.put("currentMetrics", analyticsService.getProcessMetrics(processInstanceId));
        context.put("historicalData", analyticsService.getHistoricalPerformance(processInstanceId));
        
        ChatResponse response = executeTask(
            "Analyze the current workflow performance and provide optimization recommendations", 
            context
        );
        
        return parseOptimizationResponse(response.content().text());
    }
    
    public TaskRoutingDecision routeTask(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        
        Map<String, Object> context = new HashMap<>();
        context.put("taskId", taskId);
        context.put("taskName", task.getName());
        context.put("taskDescription", task.getDescription());
        context.put("processVariables", runtimeService.getVariables(task.getProcessInstanceId()));
        context.put("availableUsers", getAvailableUsers());
        context.put("currentWorkloads", getCurrentWorkloads());
        
        ChatResponse response = executeTask(
            "Determine the optimal assignee for this task based on skills, workload, and availability",
            context
        );
        
        return parseRoutingResponse(response.content().text());
    }
    
    public WorkflowPrediction predictWorkflowOutcome(String processInstanceId) {
        Map<String, Object> context = new HashMap<>();
        context.put("processInstanceId", processInstanceId);
        context.put("currentState", analyticsService.getCurrentProcessState(processInstanceId));
        context.put("historicalPatterns", analyticsService.getHistoricalPatterns(processInstanceId));
        context.put("riskFactors", analyticsService.identifyRiskFactors(processInstanceId));
        
        ChatResponse response = executeTask(
            "Predict the likely outcome of this workflow and identify potential risks or delays",
            context
        );
        
        return parsePredictionResponse(response.content().text());
    }
    
    private WorkflowOptimizationResult parseOptimizationResponse(String response) {
        try {
            // Use structured parsing to extract optimization recommendations
            ObjectMapper mapper = new ObjectMapper();
            JsonNode responseNode = mapper.readTree(response);
            
            return WorkflowOptimizationResult.builder()
                .recommendations(parseRecommendations(responseNode.get("recommendations")))
                .expectedImprovement(responseNode.get("expectedImprovement").asDouble())
                .confidence(responseNode.get("confidence").asDouble())
                .reasoning(responseNode.get("reasoning").asText())
                .build();
                
        } catch (Exception e) {
            logger.warn("Failed to parse structured response, using text analysis", e);
            return parseOptimizationResponseFromText(response);
        }
    }
    
    private List<String> getAvailableUsers() {
        // Implementation to get available users from identity service
        return identityService.getActiveUsers().stream()
            .map(User::getId)
            .collect(Collectors.toList());
    }
    
    private Map<String, Double> getCurrentWorkloads() {
        // Implementation to get current workload metrics
        return workloadService.getCurrentWorkloads();
    }
}
```

## CrewAI Service Integration

### Crew Management Service

#### CrewManagementService.java
```java
@Service
public class CrewManagementService {
    
    @Autowired
    private AgentRegistry agentRegistry;
    
    @Autowired
    private CrewAIClient crewAIClient;
    
    @Autowired
    private TaskCoordinationService taskCoordinationService;
    
    public CrewExecutionResult executeCrewTask(CrewTaskRequest request) {
        try {
            // Select appropriate agents for the task
            List<BaseGRCOSAgent> selectedAgents = selectAgentsForTask(request);
            
            // Create crew configuration
            CrewConfiguration crewConfig = createCrewConfiguration(selectedAgents, request);
            
            // Execute crew task
            CrewExecution execution = crewAIClient.executeCrew(crewConfig);
            
            // Monitor execution
            CrewExecutionResult result = monitorCrewExecution(execution);
            
            // Process results
            processCrewResults(result);
            
            return result;
            
        } catch (Exception e) {
            logger.error("Failed to execute crew task: {}", request.getTaskId(), e);
            throw new CrewExecutionException("Crew task execution failed", e);
        }
    }
    
    public CollaborativeDecisionResult makeCollaborativeDecision(DecisionRequest request) {
        // Get relevant agents for decision
        List<BaseGRCOSAgent> decisionAgents = selectDecisionAgents(request);
        
        // Collect individual agent decisions
        List<AgentDecision> individualDecisions = collectIndividualDecisions(decisionAgents, request);
        
        // Facilitate agent collaboration
        AgentCollaboration collaboration = facilitateCollaboration(decisionAgents, individualDecisions);
        
        // Reach consensus
        ConsensusResult consensus = reachConsensus(collaboration);
        
        return CollaborativeDecisionResult.builder()
            .request(request)
            .individualDecisions(individualDecisions)
            .collaboration(collaboration)
            .consensus(consensus)
            .finalDecision(consensus.getDecision())
            .confidence(consensus.getConfidence())
            .build();
    }
    
    private List<BaseGRCOSAgent> selectAgentsForTask(CrewTaskRequest request) {
        List<BaseGRCOSAgent> selectedAgents = new ArrayList<>();
        
        // Select agents based on task requirements
        for (AgentCapability capability : request.getRequiredCapabilities()) {
            List<BaseGRCOSAgent> capableAgents = agentRegistry.getAgentsByCapability(capability);
            
            if (!capableAgents.isEmpty()) {
                // Select best agent for this capability
                BaseGRCOSAgent bestAgent = selectBestAgent(capableAgents, request);
                if (!selectedAgents.contains(bestAgent)) {
                    selectedAgents.add(bestAgent);
                }
            }
        }
        
        return selectedAgents;
    }
    
    private CrewConfiguration createCrewConfiguration(List<BaseGRCOSAgent> agents, 
                                                    CrewTaskRequest request) {
        return CrewConfiguration.builder()
            .agents(agents.stream()
                .map(this::convertToCrewAgent)
                .collect(Collectors.toList()))
            .tasks(createCrewTasks(request))
            .process(determineCrewProcess(request))
            .verbose(true)
            .memory(true)
            .build();
    }
    
    private CrewAgent convertToCrewAgent(BaseGRCOSAgent agent) {
        return CrewAgent.builder()
            .role(agent.getRole())
            .goal(agent.getGoal())
            .backstory(agent.getBackstory())
            .tools(getAgentTools(agent))
            .verbose(true)
            .allowDelegation(true)
            .build();
    }
    
    private List<CrewTask> createCrewTasks(CrewTaskRequest request) {
        return request.getSubTasks().stream()
            .map(subTask -> CrewTask.builder()
                .description(subTask.getDescription())
                .expectedOutput(subTask.getExpectedOutput())
                .agent(subTask.getAssignedAgent())
                .tools(subTask.getRequiredTools())
                .build())
            .collect(Collectors.toList());
    }
}
```

### Flowable Integration Service Tasks

#### CrewAIServiceTask.java
```java
@Component("crewAIServiceTask")
public class CrewAIServiceTask implements JavaDelegate {
    
    @Autowired
    private CrewManagementService crewManagementService;
    
    @Override
    public void execute(DelegateExecution execution) {
        try {
            // Extract task parameters
            String crewTaskType = (String) execution.getVariable("crewTaskType");
            Map<String, Object> taskContext = extractTaskContext(execution);
            
            // Create crew task request
            CrewTaskRequest request = CrewTaskRequest.builder()
                .taskId(execution.getCurrentActivityId())
                .taskType(crewTaskType)
                .context(taskContext)
                .processInstanceId(execution.getProcessInstanceId())
                .requiredCapabilities(determineRequiredCapabilities(crewTaskType))
                .build();
            
            // Execute crew task
            CrewExecutionResult result = crewManagementService.executeCrewTask(request);
            
            // Store results in process variables
            storeResultsInProcess(execution, result);
            
        } catch (Exception e) {
            logger.error("CrewAI service task execution failed", e);
            throw new BpmnError("CREW_AI_EXECUTION_FAILED", e.getMessage());
        }
    }
    
    private Map<String, Object> extractTaskContext(DelegateExecution execution) {
        Map<String, Object> context = new HashMap<>();
        
        // Add process variables
        context.putAll(execution.getVariables());
        
        // Add execution context
        context.put("processDefinitionKey", execution.getProcessDefinitionId());
        context.put("activityId", execution.getCurrentActivityId());
        context.put("executionId", execution.getId());
        
        // Add OSCAL context if available
        if (execution.hasVariable("oscalDocumentUuid")) {
            context.put("oscalDocument", getOSCALDocument(
                (String) execution.getVariable("oscalDocumentUuid")));
        }
        
        return context;
    }
    
    private void storeResultsInProcess(DelegateExecution execution, CrewExecutionResult result) {
        // Store main result
        execution.setVariable("crewAIResult", result.getMainResult());
        
        // Store individual agent results
        for (Map.Entry<String, Object> entry : result.getAgentResults().entrySet()) {
            execution.setVariable("agent_" + entry.getKey() + "_result", entry.getValue());
        }
        
        // Store execution metadata
        execution.setVariable("crewAIExecutionTime", result.getExecutionTime());
        execution.setVariable("crewAIConfidence", result.getConfidence());
        execution.setVariable("crewAISuccess", result.isSuccess());
    }
}
```

### Event Listeners for AI Integration

#### AIWorkflowEventListener.java
```java
@Component
public class AIWorkflowEventListener implements FlowableEventListener {
    
    @Autowired
    private CrewManagementService crewManagementService;
    
    @Autowired
    private WorkflowOrchestratorAgent orchestratorAgent;
    
    @Override
    public void onEvent(FlowableEvent event) {
        switch (event.getType()) {
            case PROCESS_STARTED:
                handleProcessStarted((FlowableProcessEvent) event);
                break;
            case TASK_CREATED:
                handleTaskCreated((FlowableEntityEvent) event);
                break;
            case PROCESS_COMPLETED:
                handleProcessCompleted((FlowableProcessEvent) event);
                break;
            case TIMER_FIRED:
                handleTimerFired((FlowableEntityEvent) event);
                break;
        }
    }
    
    private void handleProcessStarted(FlowableProcessEvent event) {
        try {
            // Analyze process for optimization opportunities
            String processInstanceId = event.getProcessInstanceId();
            
            CompletableFuture.runAsync(() -> {
                WorkflowOptimizationResult optimization = 
                    orchestratorAgent.optimizeWorkflow(processInstanceId);
                
                if (optimization.hasRecommendations()) {
                    applyOptimizations(processInstanceId, optimization);
                }
            });
            
        } catch (Exception e) {
            logger.warn("Failed to analyze process start event", e);
        }
    }
    
    private void handleTaskCreated(FlowableEntityEvent event) {
        try {
            String taskId = event.getEntity().getId();
            
            CompletableFuture.runAsync(() -> {
                TaskRoutingDecision routing = orchestratorAgent.routeTask(taskId);
                
                if (routing.hasRecommendation()) {
                    applyTaskRouting(taskId, routing);
                }
            });
            
        } catch (Exception e) {
            logger.warn("Failed to analyze task creation event", e);
        }
    }
    
    @Override
    public boolean isFailOnException() {
        return false; // Don't fail the process if AI analysis fails
    }
}
```

This CrewAI integration setup provides comprehensive multi-agent coordination capabilities within the Flowable workflow engine, enabling intelligent automation and decision-making across all GRCOS modules.
