<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitiEntityEventImpl (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitiEntityEventImpl (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEntityEventImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiActivityEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEntityExceptionEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/ActivitiEntityEventImpl.html" target="_top">Frames</a></li>
<li><a href="ActivitiEntityEventImpl.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate.event.impl</div>
<h2 title="Class ActivitiEntityEventImpl" class="title">Class ActivitiEntityEventImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">org.activiti.engine.delegate.event.impl.ActivitiEventImpl</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.delegate.event.impl.ActivitiEntityEventImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a>, <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEntityWithVariablesEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEntityWithVariablesEventImpl</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ActivitiEntityEventImpl</span>
extends <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a>
implements <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a></pre>
<div class="block">Base class for all <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> implementations, related to entities.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Frederik Heremans</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEntityEventImpl.html#entity">entity</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.activiti.engine.delegate.event.impl.ActivitiEventImpl">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.activiti.engine.delegate.event.impl.<a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a></h3>
<code><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#executionId">executionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#processDefinitionId">processDefinitionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#processInstanceId">processInstanceId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#type">type</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEntityEventImpl.html#ActivitiEntityEventImpl-java.lang.Object-org.activiti.engine.delegate.event.ActivitiEventType-">ActivitiEntityEventImpl</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                       <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEntityEventImpl.html#getEntity--">getEntity</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.delegate.event.impl.ActivitiEventImpl">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.activiti.engine.delegate.event.impl.<a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a></h3>
<code><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getEngineServices--">getEngineServices</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getExecutionId--">getExecutionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getProcessDefinitionId--">getProcessDefinitionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getProcessInstanceId--">getProcessInstanceId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getType--">getType</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#setExecutionId-java.lang.String-">setExecutionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#setProcessDefinitionId-java.lang.String-">setProcessDefinitionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#setProcessInstanceId-java.lang.String-">setProcessInstanceId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#setType-org.activiti.engine.delegate.event.ActivitiEventType-">setType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.delegate.event.ActivitiEvent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.delegate.event.<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a></h3>
<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getEngineServices--">getEngineServices</a>, <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getExecutionId--">getExecutionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getProcessDefinitionId--">getProcessDefinitionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getProcessInstanceId--">getProcessInstanceId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getType--">getType</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="entity">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>entity</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> entity</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ActivitiEntityEventImpl-java.lang.Object-org.activiti.engine.delegate.event.ActivitiEventType-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ActivitiEntityEventImpl</h4>
<pre>public&nbsp;ActivitiEntityEventImpl(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                               <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getEntity--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getEntity</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getEntity()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html#getEntity--">getEntity</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the entity that is targeted by this event.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEntityEventImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiActivityEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEntityExceptionEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/ActivitiEntityEventImpl.html" target="_top">Frames</a></li>
<li><a href="ActivitiEntityEventImpl.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
