<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RuntimeService (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RuntimeService (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6,"i44":6,"i45":6,"i46":6,"i47":6,"i48":6,"i49":6,"i50":6,"i51":6,"i52":6,"i53":6,"i54":6,"i55":6,"i56":6,"i57":6,"i58":6,"i59":6,"i60":6,"i61":6,"i62":6,"i63":6,"i64":6,"i65":6,"i66":6,"i67":6,"i68":6,"i69":6,"i70":6,"i71":6,"i72":6,"i73":6,"i74":6,"i75":6,"i76":6,"i77":6,"i78":6,"i79":6,"i80":6,"i81":6,"i82":6,"i83":6,"i84":6,"i85":6,"i86":6,"i87":6,"i88":6,"i89":6,"i90":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RuntimeService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/RuntimeService.html" target="_top">Frames</a></li>
<li><a href="RuntimeService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Interface RuntimeService" class="title">Interface RuntimeService</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">RuntimeService</span></pre>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez, Daniel Meyer</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#activateProcessInstanceById-java.lang.String-">activateProcessInstanceById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Activates the process instance with the given id.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">addEventListener</a></span>(<a href="../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd)</code>
<div class="block">Adds an event-listener which will be notified of ALL events by the
 dispatcher.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-org.activiti.engine.delegate.event.ActivitiEventType...-">addEventListener</a></span>(<a href="../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd,
                <a href="../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>...&nbsp;types)</code>
<div class="block">Adds an event-listener which will only be notified when an event occurs,
 which type is in the given types.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#addGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-">addGroupIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Involves a group with a process instance.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#addParticipantGroup-java.lang.String-java.lang.String-">addParticipantGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/RuntimeService.html#addGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>addGroupIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#addParticipantUser-java.lang.String-java.lang.String-">addParticipantUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/RuntimeService.html#addUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>addUserIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#addUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-">addUserIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Involves a user with a process instance.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#createExecutionQuery--">createExecutionQuery</a></span>()</code>
<div class="block">Creates a new <a href="../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime"><code>ExecutionQuery</code></a> instance, that can be used to query
 the executions and process instances.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/NativeExecutionQuery.html" title="interface in org.activiti.engine.runtime">NativeExecutionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#createNativeExecutionQuery--">createNativeExecutionQuery</a></span>()</code>
<div class="block">creates a new <a href="../../../org/activiti/engine/runtime/NativeExecutionQuery.html" title="interface in org.activiti.engine.runtime"><code>NativeExecutionQuery</code></a> to query <a href="../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime"><code>Execution</code></a>s by
 SQL directly</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/NativeProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">NativeProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#createNativeProcessInstanceQuery--">createNativeProcessInstanceQuery</a></span>()</code>
<div class="block">creates a new <a href="../../../org/activiti/engine/runtime/NativeProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>NativeProcessInstanceQuery</code></a> to query
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s by SQL directly</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#createProcessInstanceBuilder--">createProcessInstanceBuilder</a></span>()</code>
<div class="block">Create a <a href="../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceBuilder</code></a>, that allows to set various options for starting a process instance,
 as an alternative to the various startProcessInstanceByXX methods.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#createProcessInstanceQuery--">createProcessInstanceQuery</a></span>()</code>
<div class="block">Creates a new <a href="../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instance, that can be used to
 query process instances.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#deleteGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-">deleteGroupIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Removes the association between a group and a process instance for the given identityLinkType.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#deleteParticipantGroup-java.lang.String-java.lang.String-">deleteParticipantGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/RuntimeService.html#deleteGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>deleteGroupIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#deleteParticipantUser-java.lang.String-java.lang.String-">deleteParticipantUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/RuntimeService.html#deleteUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>deleteUserIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#deleteProcessInstance-java.lang.String-java.lang.String-">deleteProcessInstance</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deleteReason)</code>
<div class="block">Delete an existing runtime process instance.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#deleteUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-">deleteUserIdentityLink</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</code>
<div class="block">Removes the association between a user and a process instance for the given identityLinkType.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#dispatchEvent-org.activiti.engine.delegate.event.ActivitiEvent-">dispatchEvent</a></span>(<a href="../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">Dispatches the given event to any listeners that are registered.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getActiveActivityIds-java.lang.String-">getActiveActivityIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Finds the activity ids for all executions that are waiting in activities.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task">IdentityLink</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getIdentityLinksForProcessInstance-java.lang.String-">getIdentityLinksForProcessInstance</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;instanceId)</code>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a>s associated with the given process
 instance.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Event.html" title="interface in org.activiti.engine.task">Event</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getProcessInstanceEvents-java.lang.String-">getProcessInstanceEvents</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">The all events related to the given Process Instance.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariable-java.lang.String-java.lang.String-">getVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">The variable value.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariable-java.lang.String-java.lang.String-java.lang.Class-">getVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</code>
<div class="block">The variable value.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.persistence.entity.VariableInstance</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstance-java.lang.String-java.lang.String-">getVariableInstance</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">The variable.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.persistence.entity.VariableInstance</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstance-java.lang.String-java.lang.String-java.lang.String-boolean-">getVariableInstance</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                   boolean&nbsp;withLocalizationFallback)</code>
<div class="block">The variable.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.persistence.entity.VariableInstance</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstanceLocal-java.lang.String-java.lang.String-">getVariableInstanceLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">The variable for an execution.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.persistence.entity.VariableInstance</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstanceLocal-java.lang.String-java.lang.String-java.lang.String-boolean-">getVariableInstanceLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                        boolean&nbsp;withLocalizationFallback)</code>
<div class="block">The variable for an execution.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstances-java.lang.String-">getVariableInstances</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">All variables visible from the given execution scope (including parent scopes).</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstances-java.lang.String-java.util.Collection-">getVariableInstances</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">The variable values for all given variableNames, takes all variables into account which are visible from the given execution scope (including parent scopes).</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstances-java.lang.String-java.util.Collection-java.lang.String-boolean-">getVariableInstances</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                    boolean&nbsp;withLocalizationFallback)</code>
<div class="block">The variable values for all given variableNames, takes all variables into account which are visible from the given execution scope (including parent scopes).</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstances-java.lang.String-java.lang.String-boolean-">getVariableInstances</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                    boolean&nbsp;withLocalizationFallback)</code>
<div class="block">All variables visible from the given execution scope (including parent scopes).</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstancesByExecutionIds-java.util.Set-">getVariableInstancesByExecutionIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;executionIds)</code>
<div class="block">All variables visible from the given execution scope (including parent
 scopes).</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstancesLocal-java.lang.String-">getVariableInstancesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">All variable values that are defined in the execution scope, without taking outer scopes into account.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstancesLocal-java.lang.String-java.util.Collection-">getVariableInstancesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">The variable values for the given variableNames only taking the given execution scope into account, not looking in outer scopes.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstancesLocal-java.lang.String-java.util.Collection-java.lang.String-boolean-">getVariableInstancesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                         boolean&nbsp;withLocalizationFallback)</code>
<div class="block">The variable values for the given variableNames only taking the given execution scope into account, not looking in outer scopes.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstancesLocal-java.lang.String-java.lang.String-boolean-">getVariableInstancesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                         boolean&nbsp;withLocalizationFallback)</code>
<div class="block">All variable values that are defined in the execution scope, without taking outer scopes into account.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableLocal-java.lang.String-java.lang.String-">getVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">The variable value for an execution.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariableLocal-java.lang.String-java.lang.String-java.lang.Class-">getVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</code>
<div class="block">The variable value for an execution.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariables-java.lang.String-">getVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">All variables visible from the given execution scope (including parent
 scopes).</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariables-java.lang.String-java.util.Collection-">getVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">The variable values for all given variableNames, takes all variables into
 account which are visible from the given execution scope (including parent
 scopes).</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariablesLocal-java.lang.String-">getVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">All variable values that are defined in the execution scope, without taking
 outer scopes into account.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#getVariablesLocal-java.lang.String-java.util.Collection-">getVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">The variable values for the given variableNames only taking the given
 execution scope into account, not looking in outer scopes.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#hasVariable-java.lang.String-java.lang.String-">hasVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Check whether or not this execution has variable set with the given name,
 Searching for the variable is done in all scopes that are visible to the
 given execution (including parent scopes).</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#hasVariableLocal-java.lang.String-java.lang.String-">hasVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Check whether or not this execution has a local variable set with the given
 name.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#messageEventReceived-java.lang.String-java.lang.String-">messageEventReceived</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Notifies the process engine that a message event with name 'messageName'
 has been received and has been correlated to an execution with id
 'executionId'.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#messageEventReceived-java.lang.String-java.lang.String-java.util.Map-">messageEventReceived</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</code>
<div class="block">Notifies the process engine that a message event with the name
 'messageName' has been received and has been correlated to an execution
 with id 'executionId'.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#messageEventReceivedAsync-java.lang.String-java.lang.String-">messageEventReceivedAsync</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Notifies the process engine that a message event with the name
 'messageName' has been received and has been correlated to an execution
 with id 'executionId'.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#removeEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">removeEventListener</a></span>(<a href="../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToRemove)</code>
<div class="block">Removes the given listener from this dispatcher.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#removeVariable-java.lang.String-java.lang.String-">removeVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Removes a variable for an execution.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#removeVariableLocal-java.lang.String-java.lang.String-">removeVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Removes a variable for an execution (not considering parent scopes).</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#removeVariables-java.lang.String-java.util.Collection-">removeVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
               <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Removes variables for an execution.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#removeVariablesLocal-java.lang.String-java.util.Collection-">removeVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Remove variables for an execution (not considering parent scopes).</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#setProcessInstanceName-java.lang.String-java.lang.String-">setProcessInstanceName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Sets the name for the process instance with the given id.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#setVariable-java.lang.String-java.lang.String-java.lang.Object-">setVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Update or create a variable for an execution.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#setVariableLocal-java.lang.String-java.lang.String-java.lang.Object-">setVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Update or create a variable for an execution (not considering parent
 scopes).</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#setVariables-java.lang.String-java.util.Map-">setVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Update or create given variables for an execution (including parent
 scopes).</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#setVariablesLocal-java.lang.String-java.util.Map-">setVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Update or create given variables for an execution (not considering parent
 scopes).</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signal-java.lang.String-">signal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Sends an external trigger to an activity instance that is waiting inside
 the given execution.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signal-java.lang.String-java.util.Map-">signal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
      <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</code>
<div class="block">Sends an external trigger to an activity instance that is waiting inside
 the given execution.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signal-java.lang.String-java.util.Map-java.util.Map-">signal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
      <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables,
      <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</code>
<div class="block">Similar to <code>#trigger(String, Map)</code>, but with an extra parameter that allows to pass
 transient variables.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceived-java.lang.String-">signalEventReceived</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName)</code>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceived-java.lang.String-java.util.Map-">signalEventReceived</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</code>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceived-java.lang.String-java.lang.String-">signalEventReceived</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceived-java.lang.String-java.lang.String-java.util.Map-">signalEventReceived</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</code>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceivedAsync-java.lang.String-">signalEventReceivedAsync</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName)</code>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceivedAsync-java.lang.String-java.lang.String-">signalEventReceivedAsync</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceivedAsyncWithTenantId-java.lang.String-java.lang.String-">signalEventReceivedAsyncWithTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceivedAsync-java.lang.String-"><code>signalEventReceivedAsync(String)</code></a>, but within the context of one tenant.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceivedWithTenantId-java.lang.String-java.util.Map-java.lang.String-">signalEventReceivedWithTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <code>#signalEventReceived(String, Map<String, Object>)</code>, but within the context of one tenant.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceivedWithTenantId-java.lang.String-java.lang.String-">signalEventReceivedWithTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceived-java.lang.String-"><code>signalEventReceived(String)</code></a>, but within the context of one tenant.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceById-java.lang.String-">startProcessInstanceById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Starts a new process instance in the exactly specified version of the
 process definition with the given id.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceById-java.lang.String-java.util.Map-">startProcessInstanceById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Starts a new process instance in the exactly specified version of the
 process definition with the given id.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceById-java.lang.String-java.lang.String-">startProcessInstanceById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</code>
<div class="block">Starts a new process instance in the exactly specified version of the
 process definition with the given id.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceById-java.lang.String-java.lang.String-java.util.Map-">startProcessInstanceById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Starts a new process instance in the exactly specified version of the
 process definition with the given id.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-">startProcessInstanceByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Starts a new process instance in the latest version of the process
 definition with the given key.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-java.util.Map-">startProcessInstanceByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Starts a new process instance in the latest version of the process
 definition with the given key</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-java.lang.String-">startProcessInstanceByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</code>
<div class="block">Starts a new process instance in the latest version of the process
 definition with the given key.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-java.lang.String-java.util.Map-">startProcessInstanceByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Starts a new process instance in the latest version of the process
 definition with the given key.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKeyAndTenantId-java.lang.String-java.util.Map-java.lang.String-">startProcessInstanceByKeyAndTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-java.util.Map-"><code>startProcessInstanceByKey(String, Map)</code></a>, but using a specific tenant identifier.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKeyAndTenantId-java.lang.String-java.lang.String-">startProcessInstanceByKeyAndTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-"><code>startProcessInstanceByKey(String)</code></a>, but using a specific tenant identifier.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKeyAndTenantId-java.lang.String-java.lang.String-java.util.Map-java.lang.String-">startProcessInstanceByKeyAndTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-java.lang.String-java.util.Map-"><code>startProcessInstanceByKey(String, String, Map)</code></a>, but using a specific tenant identifier.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKeyAndTenantId-java.lang.String-java.lang.String-java.lang.String-">startProcessInstanceByKeyAndTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-java.lang.String-"><code>startProcessInstanceByKey(String, String)</code></a>, but using a specific tenant identifier.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-">startProcessInstanceByMessage</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</code>
<div class="block">
 Signals the process engine that a message is received and starts a new
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-java.util.Map-">startProcessInstanceByMessage</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</code>
<div class="block">
 Signals the process engine that a message is received and starts a new
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-java.lang.String-">startProcessInstanceByMessage</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</code>
<div class="block">
 Signals the process engine that a message is received and starts a new
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-java.lang.String-java.util.Map-">startProcessInstanceByMessage</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</code>
<div class="block">
 Signals the process engine that a message is received and starts a new
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessageAndTenantId-java.lang.String-java.util.Map-java.lang.String-">startProcessInstanceByMessageAndTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <code>RuntimeService#startProcessInstanceByMessage(String, Map<String, Object>)</code>, but with tenant context.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessageAndTenantId-java.lang.String-java.lang.String-">startProcessInstanceByMessageAndTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-"><code>startProcessInstanceByMessage(String)</code></a>, but with tenant context.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessageAndTenantId-java.lang.String-java.lang.String-java.util.Map-java.lang.String-">startProcessInstanceByMessageAndTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <code>RuntimeService#startProcessInstanceByMessage(String, String, Map<String, Object>)</code>, but with tenant context.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessageAndTenantId-java.lang.String-java.lang.String-java.lang.String-">startProcessInstanceByMessageAndTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-java.lang.String-"><code>startProcessInstanceByMessage(String, String)</code></a>, but with tenant context.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#suspendProcessInstanceById-java.lang.String-">suspendProcessInstanceById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Suspends the process instance with the given id.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RuntimeService.html#updateBusinessKey-java.lang.String-java.lang.String-">updateBusinessKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</code>
<div class="block">Updates the business key for the provided process instance</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createProcessInstanceBuilder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProcessInstanceBuilder</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;createProcessInstanceBuilder()</pre>
<div class="block">Create a <a href="../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceBuilder</code></a>, that allows to set various options for starting a process instance,
 as an alternative to the various startProcessInstanceByXX methods.</div>
</li>
</ul>
<a name="startProcessInstanceByKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByKey</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</pre>
<div class="block">Starts a new process instance in the latest version of the process
 definition with the given key.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionKey</code> - key of process definition, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process definition is deployed with the given key.</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceByKey-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByKey</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</pre>
<div class="block">Starts a new process instance in the latest version of the process
 definition with the given key.
 
 A business key can be provided to associate the process instance with a
 certain identifier that has a clear business meaning. For example in an
 order process, the business key could be an order id. This business key can
 then be used to easily look up that process instance , see
 <a href="../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceBusinessKey-java.lang.String-"><code>ProcessInstanceQuery.processInstanceBusinessKey(String)</code></a>. Providing
 such a business key is definitely a best practice.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionKey</code> - key of process definition, cannot be null.</dd>
<dd><code>businessKey</code> - a key that uniquely identifies the process instance in the context
          or the given process definition.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process definition is deployed with the given key.</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceByKey-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByKey</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Starts a new process instance in the latest version of the process
 definition with the given key</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionKey</code> - key of process definition, cannot be null.</dd>
<dd><code>variables</code> - the variables to pass, can be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process definition is deployed with the given key.</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceByKey-java.lang.String-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByKey</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Starts a new process instance in the latest version of the process
 definition with the given key.
 
 A business key can be provided to associate the process instance with a
 certain identifier that has a clear business meaning. For example in an
 order process, the business key could be an order id. This business key can
 then be used to easily look up that process instance , see
 <a href="../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceBusinessKey-java.lang.String-"><code>ProcessInstanceQuery.processInstanceBusinessKey(String)</code></a>. Providing
 such a business key is definitely a best practice.
 
 The combination of processdefinitionKey-businessKey must be unique.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionKey</code> - key of process definition, cannot be null.</dd>
<dd><code>variables</code> - the variables to pass, can be null.</dd>
<dd><code>businessKey</code> - a key that uniquely identifies the process instance in the context
          or the given process definition.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process definition is deployed with the given key.</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceByKeyAndTenantId-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByKeyAndTenantId</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByKeyAndTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-"><code>startProcessInstanceByKey(String)</code></a>, but using a specific tenant identifier.</div>
</li>
</ul>
<a name="startProcessInstanceByKeyAndTenantId-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByKeyAndTenantId</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByKeyAndTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-java.lang.String-"><code>startProcessInstanceByKey(String, String)</code></a>, but using a specific tenant identifier.</div>
</li>
</ul>
<a name="startProcessInstanceByKeyAndTenantId-java.lang.String-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByKeyAndTenantId</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByKeyAndTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-java.util.Map-"><code>startProcessInstanceByKey(String, Map)</code></a>, but using a specific tenant identifier.</div>
</li>
</ul>
<a name="startProcessInstanceByKeyAndTenantId-java.lang.String-java.lang.String-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByKeyAndTenantId</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByKeyAndTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables,
                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByKey-java.lang.String-java.lang.String-java.util.Map-"><code>startProcessInstanceByKey(String, String, Map)</code></a>, but using a specific tenant identifier.</div>
</li>
</ul>
<a name="startProcessInstanceById-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceById</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Starts a new process instance in the exactly specified version of the
 process definition with the given id.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - the id of the process definition, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process definition is deployed with the given key.</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceById-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceById</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</pre>
<div class="block">Starts a new process instance in the exactly specified version of the
 process definition with the given id.
 
 A business key can be provided to associate the process instance with a
 certain identifier that has a clear business meaning. For example in an
 order process, the business key could be an order id. This business key can
 then be used to easily look up that process instance , see
 <a href="../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceBusinessKey-java.lang.String-"><code>ProcessInstanceQuery.processInstanceBusinessKey(String)</code></a>. Providing
 such a business key is definitely a best practice.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - the id of the process definition, cannot be null.</dd>
<dd><code>businessKey</code> - a key that uniquely identifies the process instance in the context
          or the given process definition.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process definition is deployed with the given key.</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceById-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceById</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Starts a new process instance in the exactly specified version of the
 process definition with the given id.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - the id of the process definition, cannot be null.</dd>
<dd><code>variables</code> - variables to be passed, can be null</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process definition is deployed with the given key.</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceById-java.lang.String-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceById</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Starts a new process instance in the exactly specified version of the
 process definition with the given id.
 
 A business key can be provided to associate the process instance with a
 certain identifier that has a clear business meaning. For example in an
 order process, the business key could be an order id. This business key can
 then be used to easily look up that process instance , see
 <a href="../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceBusinessKey-java.lang.String-"><code>ProcessInstanceQuery.processInstanceBusinessKey(String)</code></a>. Providing
 such a business key is definitely a best practice.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - the id of the process definition, cannot be null.</dd>
<dd><code>variables</code> - variables to be passed, can be null</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process definition is deployed with the given key.</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceByMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByMessage</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByMessage(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</pre>
<div class="block"><p>
 Signals the process engine that a message is received and starts a new
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>.
 </p>
 
 <p>
 Calling this method can have two different outcomes:
 <ul>
 <li>If the message name is associated with a message start event, a new
 process instance is started.</li>
 <li>If no subscription to a message with the given name exists,
 <a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine"><code>ActivitiException</code></a> is thrown</li>
 </ul>
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>messageName</code> - the 'name' of the message as specified as an attribute on the
          bpmn20 <code>&lt;message name="messageName" /&gt;</code> element.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a> object representing the started process
         instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>ActivitiExeception</code> - if no subscription to a message with the given name exists</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.9</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceByMessageAndTenantId-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByMessageAndTenantId</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByMessageAndTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-"><code>startProcessInstanceByMessage(String)</code></a>, but with tenant context.</div>
</li>
</ul>
<a name="startProcessInstanceByMessage-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByMessage</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByMessage(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</pre>
<div class="block"><p>
 Signals the process engine that a message is received and starts a new
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>.
 </p>
 
 See <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-java.util.Map-"><code>startProcessInstanceByMessage(String, Map)</code></a>. This method allows
 specifying a business key.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>messageName</code> - the 'name' of the message as specified as an attribute on the
          bpmn20 <code>&lt;message name="messageName" /&gt;</code> element.</dd>
<dd><code>businessKey</code> - the business key which is added to the started process instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>ActivitiExeception</code> - if no subscription to a message with the given name exists</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.10</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceByMessageAndTenantId-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByMessageAndTenantId</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByMessageAndTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-java.lang.String-"><code>startProcessInstanceByMessage(String, String)</code></a>, but with tenant context.</div>
</li>
</ul>
<a name="startProcessInstanceByMessage-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByMessage</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByMessage(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</pre>
<div class="block"><p>
 Signals the process engine that a message is received and starts a new
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>.
 </p>
 
 See <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-"><code>startProcessInstanceByMessage(String)</code></a>. In addition, this
 method allows specifying a the payload of the message as a map of process
 variables.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>messageName</code> - the 'name' of the message as specified as an attribute on the
          bpmn20 <code>&lt;message name="messageName" /&gt;</code> element.</dd>
<dd><code>processVariables</code> - the 'payload' of the message. The variables are added as processes
          variables to the started process instance.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a> object representing the started process
         instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>ActivitiExeception</code> - if no subscription to a message with the given name exists</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.9</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceByMessageAndTenantId-java.lang.String-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByMessageAndTenantId</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByMessageAndTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <code>RuntimeService#startProcessInstanceByMessage(String, Map<String, Object>)</code>, but with tenant context.</div>
</li>
</ul>
<a name="startProcessInstanceByMessage-java.lang.String-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByMessage</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByMessage(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</pre>
<div class="block"><p>
 Signals the process engine that a message is received and starts a new
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>.
 </p>
 
 See <a href="../../../org/activiti/engine/RuntimeService.html#startProcessInstanceByMessage-java.lang.String-java.util.Map-"><code>startProcessInstanceByMessage(String, Map)</code></a>. In addition, this
 method allows specifying a business key.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>messageName</code> - the 'name' of the message as specified as an attribute on the
          bpmn20 <code>&lt;message name="messageName" /&gt;</code> element.</dd>
<dd><code>businessKey</code> - the business key which is added to the started process instance</dd>
<dd><code>processVariables</code> - the 'payload' of the message. The variables are added as processes
          variables to the started process instance.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a> object representing the started process
         instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>ActivitiExeception</code> - if no subscription to a message with the given name exists</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.9</dd>
</dl>
</li>
</ul>
<a name="startProcessInstanceByMessageAndTenantId-java.lang.String-java.lang.String-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startProcessInstanceByMessageAndTenantId</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;startProcessInstanceByMessageAndTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <code>RuntimeService#startProcessInstanceByMessage(String, String, Map<String, Object>)</code>, but with tenant context.</div>
</li>
</ul>
<a name="deleteProcessInstance-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteProcessInstance</h4>
<pre>void&nbsp;deleteProcessInstance(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deleteReason)</pre>
<div class="block">Delete an existing runtime process instance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of process instance to delete, cannot be null.</dd>
<dd><code>deleteReason</code> - reason for deleting, can be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process instance is found with the given id.</dd>
</dl>
</li>
</ul>
<a name="getActiveActivityIds-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActiveActivityIds</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getActiveActivityIds(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Finds the activity ids for all executions that are waiting in activities.
 This is a list because a single activity can be active multiple times.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of the execution, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution exists with the given executionId.</dd>
</dl>
</li>
</ul>
<a name="signal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signal</h4>
<pre>void&nbsp;signal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Sends an external trigger to an activity instance that is waiting inside
 the given execution.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution to signal, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="signal-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signal</h4>
<pre>void&nbsp;signal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</pre>
<div class="block">Sends an external trigger to an activity instance that is waiting inside
 the given execution.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution to signal, cannot be null.</dd>
<dd><code>processVariables</code> - a map of process variables</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="signal-java.lang.String-java.util.Map-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signal</h4>
<pre>void&nbsp;signal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables,
            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</pre>
<div class="block">Similar to <code>#trigger(String, Map)</code>, but with an extra parameter that allows to pass
 transient variables.</div>
</li>
</ul>
<a name="updateBusinessKey-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateBusinessKey</h4>
<pre>void&nbsp;updateBusinessKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</pre>
<div class="block">Updates the business key for the provided process instance</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance to set the business key, cannot be null</dd>
<dd><code>businessKey</code> - new businessKey value</dd>
</dl>
</li>
</ul>
<a name="addUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addUserIdentityLink</h4>
<pre>void&nbsp;addUserIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Involves a user with a process instance. The type of identity link is
 defined by the given identityLinkType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance, cannot be null.</dd>
<dd><code>userId</code> - id of the user involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identityLink, cannot be null (@see
          <a href="../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the process instance doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="addGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGroupIdentityLink</h4>
<pre>void&nbsp;addGroupIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Involves a group with a process instance. The type of identityLink is defined by the
 given identityLink.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance, cannot be null.</dd>
<dd><code>groupId</code> - id of the group to involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identity, cannot be null (@see <a href="../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the  process instance or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="addParticipantUser-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addParticipantUser</h4>
<pre>void&nbsp;addParticipantUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/RuntimeService.html#addUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>addUserIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance, cannot be null.</dd>
<dd><code>userId</code> - id of the user to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="addParticipantGroup-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addParticipantGroup</h4>
<pre>void&nbsp;addParticipantGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/RuntimeService.html#addGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>addGroupIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance, cannot be null.</dd>
<dd><code>groupId</code> - id of the group to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteParticipantUser-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteParticipantUser</h4>
<pre>void&nbsp;deleteParticipantUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/RuntimeService.html#deleteUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>deleteUserIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance, cannot be null.</dd>
<dd><code>userId</code> - id of the user to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteParticipantGroup-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteParticipantGroup</h4>
<pre>void&nbsp;deleteParticipantGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Convenience shorthand for <a href="../../../org/activiti/engine/RuntimeService.html#deleteGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-"><code>deleteGroupIdentityLink(String, String, String)</code></a>; with type <a href="../../../org/activiti/engine/task/IdentityLinkType.html#CANDIDATE"><code>IdentityLinkType.CANDIDATE</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance, cannot be null.</dd>
<dd><code>groupId</code> - id of the group to use as candidate, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteUserIdentityLink-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteUserIdentityLink</h4>
<pre>void&nbsp;deleteUserIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Removes the association between a user and a process instance for the given identityLinkType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance, cannot be null.</dd>
<dd><code>userId</code> - id of the user involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identityLink, cannot be null (@see <a href="../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteGroupIdentityLink-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteGroupIdentityLink</h4>
<pre>void&nbsp;deleteGroupIdentityLink(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;identityLinkType)</pre>
<div class="block">Removes the association between a group and a process instance for the given identityLinkType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance, cannot be null.</dd>
<dd><code>groupId</code> - id of the group to involve, cannot be null.</dd>
<dd><code>identityLinkType</code> - type of identity, cannot be null (@see <a href="../../../org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task"><code>IdentityLinkType</code></a>).</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the task or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="getIdentityLinksForProcessInstance-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIdentityLinksForProcessInstance</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task">IdentityLink</a>&gt;&nbsp;getIdentityLinksForProcessInstance(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;instanceId)</pre>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a>s associated with the given process
 instance. Such an <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a> informs how a certain user is
 involved with a process instance.</div>
</li>
</ul>
<a name="getVariables-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariables</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">All variables visible from the given execution scope (including parent
 scopes).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstances-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstances</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstances(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">All variables visible from the given execution scope (including parent scopes).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variable instances or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstancesByExecutionIds-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstancesByExecutionIds</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstancesByExecutionIds(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;executionIds)</pre>
<div class="block">All variables visible from the given execution scope (including parent
 scopes).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionIds</code> - ids of execution, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstances-java.lang.String-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstances</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstances(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                                                                                              boolean&nbsp;withLocalizationFallback)</pre>
<div class="block">All variables visible from the given execution scope (including parent scopes).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>locale</code> - locale the variable name and description should be returned in (if available).</dd>
<dd><code>withLocalizationFallback</code> - When true localization will fallback to more general locales including the default locale of the JVM if the specified locale is not found.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variable instances or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariablesLocal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariablesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">All variable values that are defined in the execution scope, without taking
 outer scopes into account. If you have many task local variables and you
 only need a few, consider using
 <a href="../../../org/activiti/engine/RuntimeService.html#getVariablesLocal-java.lang.String-java.util.Collection-"><code>getVariablesLocal(String, Collection)</code></a> for better performance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstancesLocal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstancesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstancesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">All variable values that are defined in the execution scope, without taking outer scopes into account. If you have many task local variables and you only need a few, consider using
 <a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstancesLocal-java.lang.String-java.util.Collection-"><code>getVariableInstancesLocal(String, Collection)</code></a> for better performance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstancesLocal-java.lang.String-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstancesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstancesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                                                                                                   boolean&nbsp;withLocalizationFallback)</pre>
<div class="block">All variable values that are defined in the execution scope, without taking outer scopes into account. If you have many task local variables and you only need a few, consider using
 <a href="../../../org/activiti/engine/RuntimeService.html#getVariableInstancesLocal-java.lang.String-java.util.Collection-"><code>getVariableInstancesLocal(String, Collection)</code></a> for better performance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>locale</code> - locale the variable name and description should be returned in (if available).</dd>
<dd><code>withLocalizationFallback</code> - When true localization will fallback to more general locales including the default locale of the JVM if the specified locale is not found.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariables-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariables</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">The variable values for all given variableNames, takes all variables into
 account which are visible from the given execution scope (including parent
 scopes).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableNames</code> - the collection of variable names that should be retrieved.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstances-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstances</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstances(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">The variable values for all given variableNames, takes all variables into account which are visible from the given execution scope (including parent scopes).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableNames</code> - the collection of variable names that should be retrieved.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstances-java.lang.String-java.util.Collection-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstances</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstances(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                                                                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                                                                                              boolean&nbsp;withLocalizationFallback)</pre>
<div class="block">The variable values for all given variableNames, takes all variables into account which are visible from the given execution scope (including parent scopes).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableNames</code> - the collection of variable names that should be retrieved.</dd>
<dd><code>locale</code> - locale the variable name and description should be returned in (if available).</dd>
<dd><code>withLocalizationFallback</code> - When true localization will fallback to more general locales including the default locale of the JVM if the specified locale is not found.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariablesLocal-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariablesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">The variable values for the given variableNames only taking the given
 execution scope into account, not looking in outer scopes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableNames</code> - the collection of variable names that should be retrieved.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstancesLocal-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstancesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstancesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">The variable values for the given variableNames only taking the given execution scope into account, not looking in outer scopes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableNames</code> - the collection of variable names that should be retrieved.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstancesLocal-java.lang.String-java.util.Collection-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstancesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstancesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                                                                                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                                                                                                   boolean&nbsp;withLocalizationFallback)</pre>
<div class="block">The variable values for the given variableNames only taking the given execution scope into account, not looking in outer scopes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableNames</code> - the collection of variable names that should be retrieved.</dd>
<dd><code>locale</code> - locale the variable name and description should be returned in (if available).</dd>
<dd><code>withLocalizationFallback</code> - When true localization will fallback to more general locales including the default locale of the JVM if the specified locale is not found.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variables or an empty map if no such variables are found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariable-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariable</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">The variable value. Searching for the variable is done in all scopes that
 are visible to the given execution (including parent scopes). Returns null
 when no variable value is found with the given name or when the value is
 set to null.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableName</code> - name of variable, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variable value or null if the variable is undefined or the
         value of the variable is null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstance-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstance</h4>
<pre>org.activiti.engine.impl.persistence.entity.VariableInstance&nbsp;getVariableInstance(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">The variable. Searching for the variable is done in all scopes that are visible to the given execution (including parent scopes). Returns null when no variable value is found with the given
 name or when the value is set to null.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableName</code> - name of variable, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variable or null if the variable is undefined.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstance-java.lang.String-java.lang.String-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstance</h4>
<pre>org.activiti.engine.impl.persistence.entity.VariableInstance&nbsp;getVariableInstance(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                                                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                                                                                 boolean&nbsp;withLocalizationFallback)</pre>
<div class="block">The variable. Searching for the variable is done in all scopes that are visible to the given execution (including parent scopes). Returns null when no variable value is found with the given
 name or when the value is set to null.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableName</code> - name of variable, cannot be null.</dd>
<dd><code>locale</code> - locale the variable name and description should be returned in (if available).</dd>
<dd><code>withLocalizationFallback</code> - When true localization will fallback to more general locales including the default locale of the JVM if the specified locale is not found.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variable or null if the variable is undefined.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariable-java.lang.String-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariable</h4>
<pre>&lt;T&gt;&nbsp;T&nbsp;getVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</pre>
<div class="block">The variable value. Searching for the variable is done in all scopes that
 are visible to the given execution (including parent scopes). Returns null
 when no variable value is found with the given name or when the value is
 set to null. Throws ClassCastException when cannot cast variable to
 given class</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableName</code> - name of variable, cannot be null.</dd>
<dd><code>variableClass</code> - name of variable, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variable value or null if the variable is undefined or the
         value of the variable is null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="hasVariable-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasVariable</h4>
<pre>boolean&nbsp;hasVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Check whether or not this execution has variable set with the given name,
 Searching for the variable is done in all scopes that are visible to the
 given execution (including parent scopes).</div>
</li>
</ul>
<a name="getVariableLocal-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">The variable value for an execution. Returns the value when the variable is
 set for the execution (and not searching parent scopes). Returns null when
 no variable value is found with the given name or when the value is set to
 null.</div>
</li>
</ul>
<a name="getVariableInstanceLocal-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstanceLocal</h4>
<pre>org.activiti.engine.impl.persistence.entity.VariableInstance&nbsp;getVariableInstanceLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">The variable for an execution. Returns the variable when it is set for the execution (and not searching parent scopes). Returns null when no variable is found with the given
 name or when the value is set to null.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableName</code> - name of variable, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variable or null if the variable is undefined.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableInstanceLocal-java.lang.String-java.lang.String-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstanceLocal</h4>
<pre>org.activiti.engine.impl.persistence.entity.VariableInstance&nbsp;getVariableInstanceLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale,
                                                                                      boolean&nbsp;withLocalizationFallback)</pre>
<div class="block">The variable for an execution. Returns the variable when it is set for the execution (and not searching parent scopes). Returns null when no variable is found with the given
 name or when the value is set to null.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution, cannot be null.</dd>
<dd><code>variableName</code> - name of variable, cannot be null.</dd>
<dd><code>locale</code> - locale the variable name and description should be returned in (if available).</dd>
<dd><code>withLocalizationFallback</code> - When true localization will fallback to more general locales including the default locale of the JVM if the specified locale is not found.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the variable or null if the variable is undefined.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="getVariableLocal-java.lang.String-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableLocal</h4>
<pre>&lt;T&gt;&nbsp;T&nbsp;getVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</pre>
<div class="block">The variable value for an execution. Returns the value casted to given class
 when the variable is set for the execution (and not searching parent scopes).
 Returns null when no variable value is found with the given name or when the
 value is set to null.</div>
</li>
</ul>
<a name="hasVariableLocal-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasVariableLocal</h4>
<pre>boolean&nbsp;hasVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Check whether or not this execution has a local variable set with the given
 name.</div>
</li>
</ul>
<a name="setVariable-java.lang.String-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariable</h4>
<pre>void&nbsp;setVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Update or create a variable for an execution.
 
 <p>
 The variable is set according to the algorithm as documented for
 <a href="../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>VariableScope.setVariable(String, Object)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution to set variable in, cannot be null.</dd>
<dd><code>variableName</code> - name of variable to set, cannot be null.</dd>
<dd><code>value</code> - value to set. When null is passed, the variable is not removed,
          only it's value will be set to null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>{@link VariableScope#setVariable(String, Object)}</code></a></dd>
</dl>
</li>
</ul>
<a name="setVariableLocal-java.lang.String-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariableLocal</h4>
<pre>void&nbsp;setVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Update or create a variable for an execution (not considering parent
 scopes). If the variable is not already existing, it will be created in the
 given execution.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution to set variable in, cannot be null.</dd>
<dd><code>variableName</code> - name of variable to set, cannot be null.</dd>
<dd><code>value</code> - value to set. When null is passed, the variable is not removed,
          only it's value will be set to null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="setVariables-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariables</h4>
<pre>void&nbsp;setVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Update or create given variables for an execution (including parent
 scopes).
 <p>
 Variables are set according to the algorithm as documented for
 <a href="../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-"><code>VariableScope.setVariables(Map)</code></a>, applied separately to each
 variable.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of the execution, cannot be null.</dd>
<dd><code>variables</code> - map containing name (key) and value of variables, can be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-"><code>{@link VariableScope#setVariables(Map)}</code></a></dd>
</dl>
</li>
</ul>
<a name="setVariablesLocal-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariablesLocal</h4>
<pre>void&nbsp;setVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Update or create given variables for an execution (not considering parent
 scopes). If the variables are not already existing, it will be created in
 the given execution.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of the execution, cannot be null.</dd>
<dd><code>variables</code> - map containing name (key) and value of variables, can be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no execution is found for the given executionId.</dd>
</dl>
</li>
</ul>
<a name="removeVariable-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariable</h4>
<pre>void&nbsp;removeVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Removes a variable for an execution.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution to remove variable in.</dd>
<dd><code>variableName</code> - name of variable to remove.</dd>
</dl>
</li>
</ul>
<a name="removeVariableLocal-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariableLocal</h4>
<pre>void&nbsp;removeVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Removes a variable for an execution (not considering parent scopes).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution to remove variable in.</dd>
<dd><code>variableName</code> - name of variable to remove.</dd>
</dl>
</li>
</ul>
<a name="removeVariables-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariables</h4>
<pre>void&nbsp;removeVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Removes variables for an execution.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution to remove variable in.</dd>
<dd><code>variableNames</code> - collection containing name of variables to remove.</dd>
</dl>
</li>
</ul>
<a name="removeVariablesLocal-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariablesLocal</h4>
<pre>void&nbsp;removeVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Remove variables for an execution (not considering parent scopes).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executionId</code> - id of execution to remove variable in.</dd>
<dd><code>variableNames</code> - collection containing name of variables to remove.</dd>
</dl>
</li>
</ul>
<a name="createExecutionQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createExecutionQuery</h4>
<pre><a href="../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a>&nbsp;createExecutionQuery()</pre>
<div class="block">Creates a new <a href="../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime"><code>ExecutionQuery</code></a> instance, that can be used to query
 the executions and process instances.</div>
</li>
</ul>
<a name="createNativeExecutionQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeExecutionQuery</h4>
<pre><a href="../../../org/activiti/engine/runtime/NativeExecutionQuery.html" title="interface in org.activiti.engine.runtime">NativeExecutionQuery</a>&nbsp;createNativeExecutionQuery()</pre>
<div class="block">creates a new <a href="../../../org/activiti/engine/runtime/NativeExecutionQuery.html" title="interface in org.activiti.engine.runtime"><code>NativeExecutionQuery</code></a> to query <a href="../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime"><code>Execution</code></a>s by
 SQL directly</div>
</li>
</ul>
<a name="createProcessInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProcessInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a>&nbsp;createProcessInstanceQuery()</pre>
<div class="block">Creates a new <a href="../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instance, that can be used to
 query process instances.</div>
</li>
</ul>
<a name="createNativeProcessInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeProcessInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/runtime/NativeProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">NativeProcessInstanceQuery</a>&nbsp;createNativeProcessInstanceQuery()</pre>
<div class="block">creates a new <a href="../../../org/activiti/engine/runtime/NativeProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>NativeProcessInstanceQuery</code></a> to query
 <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s by SQL directly</div>
</li>
</ul>
<a name="suspendProcessInstanceById-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suspendProcessInstanceById</h4>
<pre>void&nbsp;suspendProcessInstanceById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Suspends the process instance with the given id.
 
 If a process instance is in state suspended, activiti will not execute jobs
 (timers, messages) associated with this instance.
 
 If you have a process instance hierarchy, suspending one process instance
 form the hierarchy will not suspend other process instances form that
 hierarchy.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processInstance can be found.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - the process instance is already in state suspended.</dd>
</dl>
</li>
</ul>
<a name="activateProcessInstanceById-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activateProcessInstanceById</h4>
<pre>void&nbsp;activateProcessInstanceById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Activates the process instance with the given id.
 
 If you have a process instance hierarchy, suspending one process instance
 form the hierarchy will not suspend other process instances form that
 hierarchy.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processInstance can be found.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the process instance is already in state active.</dd>
</dl>
</li>
</ul>
<a name="signalEventReceived-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventReceived</h4>
<pre>void&nbsp;signalEventReceived(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName)</pre>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received. This method delivers the signal to all executions waiting on
 the signal.
 <p/>
 
 <strong>NOTE:</strong> The waiting executions are notified synchronously.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>signalName</code> - the name of the signal event</dd>
</dl>
</li>
</ul>
<a name="signalEventReceivedWithTenantId-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventReceivedWithTenantId</h4>
<pre>void&nbsp;signalEventReceivedWithTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceived-java.lang.String-"><code>signalEventReceived(String)</code></a>, but within the context of one tenant.</div>
</li>
</ul>
<a name="signalEventReceivedAsync-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventReceivedAsync</h4>
<pre>void&nbsp;signalEventReceivedAsync(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName)</pre>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received. This method delivers the signal to all executions waiting on
 the signal.
 <p/></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>signalName</code> - the name of the signal event</dd>
</dl>
</li>
</ul>
<a name="signalEventReceivedAsyncWithTenantId-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventReceivedAsyncWithTenantId</h4>
<pre>void&nbsp;signalEventReceivedAsyncWithTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RuntimeService.html#signalEventReceivedAsync-java.lang.String-"><code>signalEventReceivedAsync(String)</code></a>, but within the context of one tenant.</div>
</li>
</ul>
<a name="signalEventReceived-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventReceived</h4>
<pre>void&nbsp;signalEventReceived(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</pre>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received. This method delivers the signal to all executions waiting on
 the signal.
 <p/>
 
 <strong>NOTE:</strong> The waiting executions are notified synchronously.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>signalName</code> - the name of the signal event</dd>
<dd><code>processVariables</code> - a map of variables added to the execution(s)</dd>
</dl>
</li>
</ul>
<a name="signalEventReceivedWithTenantId-java.lang.String-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventReceivedWithTenantId</h4>
<pre>void&nbsp;signalEventReceivedWithTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables,
                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <code>#signalEventReceived(String, Map<String, Object>)</code>, but within the context of one tenant.</div>
</li>
</ul>
<a name="signalEventReceived-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventReceived</h4>
<pre>void&nbsp;signalEventReceived(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received. This method delivers the signal to a single execution, being
 the execution referenced by 'executionId'. The waiting execution is
 notified synchronously.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>signalName</code> - the name of the signal event</dd>
<dd><code>executionId</code> - the id of the execution to deliver the signal to</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such execution exists.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the execution has not subscribed to the signal.</dd>
</dl>
</li>
</ul>
<a name="signalEventReceived-java.lang.String-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventReceived</h4>
<pre>void&nbsp;signalEventReceived(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</pre>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received. This method delivers the signal to a single execution, being
 the execution referenced by 'executionId'. The waiting execution is
 notified synchronously.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>signalName</code> - the name of the signal event</dd>
<dd><code>executionId</code> - the id of the execution to deliver the signal to</dd>
<dd><code>processVariables</code> - a map of variables added to the execution(s)</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such execution exists.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the execution has not subscribed to the signal</dd>
</dl>
</li>
</ul>
<a name="signalEventReceivedAsync-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>signalEventReceivedAsync</h4>
<pre>void&nbsp;signalEventReceivedAsync(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Notifies the process engine that a signal event of name 'signalName' has
 been received. This method delivers the signal to a single execution, being
 the execution referenced by 'executionId'. The waiting execution is
 notified <strong>asynchronously</strong>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>signalName</code> - the name of the signal event</dd>
<dd><code>executionId</code> - the id of the execution to deliver the signal to</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such execution exists.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the execution has not subscribed to the signal.</dd>
</dl>
</li>
</ul>
<a name="messageEventReceived-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>messageEventReceived</h4>
<pre>void&nbsp;messageEventReceived(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Notifies the process engine that a message event with name 'messageName'
 has been received and has been correlated to an execution with id
 'executionId'.
 
 The waiting execution is notified synchronously.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>messageName</code> - the name of the message event</dd>
<dd><code>executionId</code> - the id of the execution to deliver the message to</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such execution exists.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the execution has not subscribed to the signal</dd>
</dl>
</li>
</ul>
<a name="messageEventReceived-java.lang.String-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>messageEventReceived</h4>
<pre>void&nbsp;messageEventReceived(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;processVariables)</pre>
<div class="block">Notifies the process engine that a message event with the name
 'messageName' has been received and has been correlated to an execution
 with id 'executionId'.
 
 The waiting execution is notified synchronously.
 
 <p>
 Variables are set for the scope of the execution of the message event
 subscribed to the message name. For example:
 <p>
 <li>The scope for an intermediate message event in the main process is that
 of the process instance</li>
 <li>The scope for an intermediate message event in a subprocess is that of
 the subprocess</li>
 <li>The scope for a boundary message event is that of the execution for the
 Activity the event is attached to</li>
 <p>
 Variables are set according to the algorithm as documented for
 <a href="../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-"><code>VariableScope.setVariables(Map)</code></a>, applied separately to each
 variable.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>messageName</code> - the name of the message event</dd>
<dd><code>executionId</code> - the id of the execution to deliver the message to</dd>
<dd><code>processVariables</code> - a map of variables added to the execution</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such execution exists.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the execution has not subscribed to the signal</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-"><code>{@link VariableScope#setVariables(Map)}</code></a></dd>
</dl>
</li>
</ul>
<a name="messageEventReceivedAsync-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>messageEventReceivedAsync</h4>
<pre>void&nbsp;messageEventReceivedAsync(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Notifies the process engine that a message event with the name
 'messageName' has been received and has been correlated to an execution
 with id 'executionId'.
 
 The waiting execution is notified <strong>asynchronously</strong>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>messageName</code> - the name of the message event</dd>
<dd><code>executionId</code> - the id of the execution to deliver the message to</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such execution exists.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the execution has not subscribed to the signal</dd>
</dl>
</li>
</ul>
<a name="addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEventListener</h4>
<pre>void&nbsp;addEventListener(<a href="../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd)</pre>
<div class="block">Adds an event-listener which will be notified of ALL events by the
 dispatcher.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listenerToAdd</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-org.activiti.engine.delegate.event.ActivitiEventType...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEventListener</h4>
<pre>void&nbsp;addEventListener(<a href="../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd,
                      <a href="../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>...&nbsp;types)</pre>
<div class="block">Adds an event-listener which will only be notified when an event occurs,
 which type is in the given types.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listenerToAdd</code> - the listener to add</dd>
<dd><code>types</code> - types of events the listener should be notified for</dd>
</dl>
</li>
</ul>
<a name="removeEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeEventListener</h4>
<pre>void&nbsp;removeEventListener(<a href="../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToRemove)</pre>
<div class="block">Removes the given listener from this dispatcher. The listener will no
 longer be notified, regardless of the type(s) it was registered for in the
 first place.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listenerToRemove</code> - listener to remove</dd>
</dl>
</li>
</ul>
<a name="dispatchEvent-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dispatchEvent</h4>
<pre>void&nbsp;dispatchEvent(<a href="../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block">Dispatches the given event to any listeners that are registered.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - event to dispatch.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if an exception occurs when dispatching the event or when the
           <a href="../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEventDispatcher</code></a> is disabled.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></code> - when the given event is not suitable for dispatching.</dd>
</dl>
</li>
</ul>
<a name="setProcessInstanceName-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProcessInstanceName</h4>
<pre>void&nbsp;setProcessInstanceName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Sets the name for the process instance with the given id.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processInstanceId</code> - id of the process instance to update</dd>
<dd><code>name</code> - new name for the process instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the given process instance does not exist.</dd>
</dl>
</li>
</ul>
<a name="getProcessInstanceEvents-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getProcessInstanceEvents</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/Event.html" title="interface in org.activiti.engine.task">Event</a>&gt;&nbsp;getProcessInstanceEvents(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">The all events related to the given Process Instance.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RuntimeService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/RuntimeService.html" target="_top">Frames</a></li>
<li><a href="RuntimeService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
