<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DelegateExecution (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DelegateExecution (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":38,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DelegateExecution.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/BusinessRuleTaskDelegate.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html" title="class in org.activiti.engine.delegate"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/delegate/DelegateExecution.html" target="_top">Frames</a></li>
<li><a href="DelegateExecution.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate</div>
<h2 title="Interface DelegateExecution" class="title">Interface DelegateExecution</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/delegate/VariableScope.html" title="interface in org.activiti.engine.delegate">VariableScope</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">DelegateExecution</span>
extends <a href="../../../../org/activiti/engine/delegate/VariableScope.html" title="interface in org.activiti.engine.delegate">VariableScope</a></pre>
<div class="block">Execution used in <a href="../../../../org/activiti/engine/delegate/JavaDelegate.html" title="interface in org.activiti.engine.delegate"><code>JavaDelegate</code></a>s and <a href="../../../../org/activiti/engine/delegate/ExecutionListener.html" title="interface in org.activiti.engine.delegate"><code>ExecutionListener</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getBusinessKey--">getBusinessKey</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">use <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getProcessBusinessKey--"><code>getProcessBusinessKey()</code></a> to get the business key for the process
             associated with this execution, regardless whether or not this execution is a 
             process-instance.</span></div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getCurrentActivityId--">getCurrentActivityId</a></span>()</code>
<div class="block">Gets the id of the current activity.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getCurrentActivityName--">getCurrentActivityName</a></span>()</code>
<div class="block">Gets the name of the current activity.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine">EngineServices</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getEngineServices--">getEngineServices</a></span>()</code>
<div class="block">All Activiti services can be accessed through this interface.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getEventName--">getEventName</a></span>()</code>
<div class="block">The <a href="../../../../org/activiti/engine/delegate/ExecutionListener.html#EVENTNAME_START"><code>event name</code></a> in case this execution is passed in for an <a href="../../../../org/activiti/engine/delegate/ExecutionListener.html" title="interface in org.activiti.engine.delegate"><code>ExecutionListener</code></a></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getId--">getId</a></span>()</code>
<div class="block">Unique id of this path of execution that can be used as a handle to provide external signals back into the engine after wait states.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getParentId--">getParentId</a></span>()</code>
<div class="block">Gets the id of the parent of this execution.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getProcessBusinessKey--">getProcessBusinessKey</a></span>()</code>
<div class="block">The business key for the process instance this execution is associated with.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getProcessDefinitionId--">getProcessDefinitionId</a></span>()</code>
<div class="block">The process definition key for the process instance this execution is associated with.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getProcessInstanceId--">getProcessInstanceId</a></span>()</code>
<div class="block">Reference to the overall process instance</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getSuperExecutionId--">getSuperExecutionId</a></span>()</code>
<div class="block">Gets the id of the calling execution.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getTenantId--">getTenantId</a></span>()</code>
<div class="block">Returns the tenant id, if any is set before on the process definition or process instance.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.delegate.VariableScope">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.delegate.<a href="../../../../org/activiti/engine/delegate/VariableScope.html" title="interface in org.activiti.engine.delegate">VariableScope</a></h3>
<code><a href="../../../../org/activiti/engine/delegate/VariableScope.html#createVariableLocal-java.lang.String-java.lang.Object-">createVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariable-java.lang.String-">getTransientVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariableLocal-java.lang.String-">getTransientVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariables--">getTransientVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariablesLocal--">getTransientVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-">getVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-boolean-">getVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-java.lang.Class-">getVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstance-java.lang.String-">getVariableInstance</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstance-java.lang.String-boolean-">getVariableInstance</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstanceLocal-java.lang.String-">getVariableInstanceLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstanceLocal-java.lang.String-boolean-">getVariableInstanceLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances--">getVariableInstances</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-">getVariableInstances</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-boolean-">getVariableInstances</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstancesLocal--">getVariableInstancesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstancesLocal-java.util.Collection-">getVariableInstancesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstancesLocal-java.util.Collection-boolean-">getVariableInstancesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-">getVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-boolean-">getVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-java.lang.Class-">getVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableNames--">getVariableNames</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableNamesLocal--">getVariableNamesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables--">getVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-">getVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-boolean-">getVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariablesLocal--">getVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariablesLocal-java.util.Collection-">getVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariablesLocal-java.util.Collection-boolean-">getVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariable-java.lang.String-">hasVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariableLocal-java.lang.String-">hasVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariables--">hasVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariablesLocal--">hasVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariable-java.lang.String-">removeTransientVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariableLocal-java.lang.String-">removeTransientVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariables--">removeTransientVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariablesLocal--">removeTransientVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariable-java.lang.String-">removeVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariableLocal-java.lang.String-">removeVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariables--">removeVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariables-java.util.Collection-">removeVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariablesLocal--">removeVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariablesLocal-java.util.Collection-">removeVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-">setTransientVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariableLocal-java.lang.String-java.lang.Object-">setTransientVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariables-java.util.Map-">setTransientVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariablesLocal-java.util.Map-">setTransientVariablesLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-">setVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-boolean-">setVariable</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-">setVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-boolean-">setVariableLocal</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-">setVariables</a>, <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariablesLocal-java.util.Map-">setVariablesLocal</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getId()</pre>
<div class="block">Unique id of this path of execution that can be used as a handle to provide external signals back into the engine after wait states.</div>
</li>
</ul>
<a name="getProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessInstanceId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessInstanceId()</pre>
<div class="block">Reference to the overall process instance</div>
</li>
</ul>
<a name="getEventName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEventName</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEventName()</pre>
<div class="block">The <a href="../../../../org/activiti/engine/delegate/ExecutionListener.html#EVENTNAME_START"><code>event name</code></a> in case this execution is passed in for an <a href="../../../../org/activiti/engine/delegate/ExecutionListener.html" title="interface in org.activiti.engine.delegate"><code>ExecutionListener</code></a></div>
</li>
</ul>
<a name="getBusinessKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusinessKey</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBusinessKey()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">use <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html#getProcessBusinessKey--"><code>getProcessBusinessKey()</code></a> to get the business key for the process
             associated with this execution, regardless whether or not this execution is a 
             process-instance.</span></div>
<div class="block">The business key for this execution. Only returns a value if the delegate execution
 is a process instance.</div>
</li>
</ul>
<a name="getProcessBusinessKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessBusinessKey</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessBusinessKey()</pre>
<div class="block">The business key for the process instance this execution is associated with.</div>
</li>
</ul>
<a name="getProcessDefinitionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDefinitionId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessDefinitionId()</pre>
<div class="block">The process definition key for the process instance this execution is associated with.</div>
</li>
</ul>
<a name="getParentId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getParentId()</pre>
<div class="block">Gets the id of the parent of this execution. If null, the execution represents a process-instance.</div>
</li>
</ul>
<a name="getSuperExecutionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuperExecutionId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getSuperExecutionId()</pre>
<div class="block">Gets the id of the calling execution. If not null, the execution is part of a subprocess.</div>
</li>
</ul>
<a name="getCurrentActivityId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentActivityId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCurrentActivityId()</pre>
<div class="block">Gets the id of the current activity.</div>
</li>
</ul>
<a name="getCurrentActivityName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentActivityName</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCurrentActivityName()</pre>
<div class="block">Gets the name of the current activity.</div>
</li>
</ul>
<a name="getTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTenantId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTenantId()</pre>
<div class="block">Returns the tenant id, if any is set before on the process definition or process instance.</div>
</li>
</ul>
<a name="getEngineServices--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getEngineServices</h4>
<pre><a href="../../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine">EngineServices</a>&nbsp;getEngineServices()</pre>
<div class="block">All Activiti services can be accessed through this interface.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DelegateExecution.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/BusinessRuleTaskDelegate.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/delegate/DelegateHelper.html" title="class in org.activiti.engine.delegate"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/delegate/DelegateExecution.html" target="_top">Frames</a></li>
<li><a href="DelegateExecution.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
