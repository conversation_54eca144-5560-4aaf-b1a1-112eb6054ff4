<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitiVariableEventImpl (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitiVariableEventImpl (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiVariableEventImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiSignalEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html" target="_top">Frames</a></li>
<li><a href="ActivitiVariableEventImpl.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate.event.impl</div>
<h2 title="Class ActivitiVariableEventImpl" class="title">Class ActivitiVariableEventImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">org.activiti.engine.delegate.event.impl.ActivitiEventImpl</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.delegate.event.impl.ActivitiVariableEventImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>, <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ActivitiVariableEventImpl</span>
extends <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a>
implements <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></pre>
<div class="block">Implementation of <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiVariableEvent</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Frederik Heremans</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#taskId">taskId</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#variableName">variableName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected org.activiti.engine.impl.variable.VariableType</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#variableType">variableType</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#variableValue">variableValue</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.activiti.engine.delegate.event.impl.ActivitiEventImpl">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.activiti.engine.delegate.event.impl.<a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a></h3>
<code><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#executionId">executionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#processDefinitionId">processDefinitionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#processInstanceId">processInstanceId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#type">type</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#ActivitiVariableEventImpl-org.activiti.engine.delegate.event.ActivitiEventType-">ActivitiVariableEventImpl</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#getTaskId--">getTaskId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#getVariableName--">getVariableName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.variable.VariableType</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#getVariableType--">getVariableType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#getVariableValue--">getVariableValue</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#setTaskId-java.lang.String-">setTaskId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#setVariableName-java.lang.String-">setVariableName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#setVariableType-org.activiti.engine.impl.variable.VariableType-">setVariableType</a></span>(org.activiti.engine.impl.variable.VariableType&nbsp;variableType)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html#setVariableValue-java.lang.Object-">setVariableValue</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.delegate.event.impl.ActivitiEventImpl">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.activiti.engine.delegate.event.impl.<a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a></h3>
<code><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getEngineServices--">getEngineServices</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getExecutionId--">getExecutionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getProcessDefinitionId--">getProcessDefinitionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getProcessInstanceId--">getProcessInstanceId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#getType--">getType</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#setExecutionId-java.lang.String-">setExecutionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#setProcessDefinitionId-java.lang.String-">setProcessDefinitionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#setProcessInstanceId-java.lang.String-">setProcessInstanceId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html#setType-org.activiti.engine.delegate.event.ActivitiEventType-">setType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.delegate.event.ActivitiVariableEvent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.delegate.event.<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></h3>
<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html#getExecutionId--">getExecutionId</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.delegate.event.ActivitiEvent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.delegate.event.<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a></h3>
<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getEngineServices--">getEngineServices</a>, <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getProcessDefinitionId--">getProcessDefinitionId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getProcessInstanceId--">getProcessInstanceId</a>, <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getType--">getType</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="variableName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableName</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> variableName</pre>
</li>
</ul>
<a name="variableValue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValue</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> variableValue</pre>
</li>
</ul>
<a name="variableType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableType</h4>
<pre>protected&nbsp;org.activiti.engine.impl.variable.VariableType variableType</pre>
</li>
</ul>
<a name="taskId">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>taskId</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> taskId</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ActivitiVariableEventImpl-org.activiti.engine.delegate.event.ActivitiEventType-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ActivitiVariableEventImpl</h4>
<pre>public&nbsp;ActivitiVariableEventImpl(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getVariableName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableName</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getVariableName()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html#getVariableName--">getVariableName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the variable involved.</dd>
</dl>
</li>
</ul>
<a name="setVariableName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariableName</h4>
<pre>public&nbsp;void&nbsp;setVariableName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
</li>
</ul>
<a name="getVariableValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableValue</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getVariableValue()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html#getVariableValue--">getVariableValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current value of the variable.</dd>
</dl>
</li>
</ul>
<a name="setVariableValue-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariableValue</h4>
<pre>public&nbsp;void&nbsp;setVariableValue(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
</li>
</ul>
<a name="getVariableType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableType</h4>
<pre>public&nbsp;org.activiti.engine.impl.variable.VariableType&nbsp;getVariableType()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html#getVariableType--">getVariableType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The <code>VariableType</code> of the variable.</dd>
</dl>
</li>
</ul>
<a name="setVariableType-org.activiti.engine.impl.variable.VariableType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariableType</h4>
<pre>public&nbsp;void&nbsp;setVariableType(org.activiti.engine.impl.variable.VariableType&nbsp;variableType)</pre>
</li>
</ul>
<a name="getTaskId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskId</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTaskId()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html#getTaskId--">getTaskId</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the id of the task the variable has been set on.</dd>
</dl>
</li>
</ul>
<a name="setTaskId-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTaskId</h4>
<pre>public&nbsp;void&nbsp;setTaskId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiVariableEventImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiSignalEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html" target="_top">Frames</a></li>
<li><a href="ActivitiVariableEventImpl.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
