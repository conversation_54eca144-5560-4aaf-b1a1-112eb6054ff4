<?xml version="1.0" encoding="UTF-8"?>

<project name="activiti.distro" default="distro">

	<property file="${user.home}/.flowable/build.properties" />

	<property name="flowable.version" value="7.1.1-SNAPSHOT" />
	<property name="target.distro.root" value="target/zip/flowable-${flowable.version}" />
	<property name="target.docs.root" value="${target.distro.root}/docs" />
	<property name="target.newsite.root" value="target/website" />
	<property name="flowable.website" value="../../flowable-website" />

	<condition property="mvn.executable" value="mvn.bat" else="mvn">
		<os family="windows" />
	</condition>

	<target name="clean">
		<delete dir="target" />
	</target>

	<target name="distro" depends="clean,
	   build.modules,
	   copy.static.resources,
	   copy.dependencies,
	   copy.webapps,
	   copy.docs,
	   copy.sql">
		<zip destfile="${target.distro.root}.zip">
			<fileset dir="target/zip">
				<include name="flowable-${flowable.version}/**" />
			</fileset>
		</zip>
	</target>

	<target name="build.modules">
		<condition property="nodocs.profile" value=",nodocs -Dskiptests" else="">
			<equals arg1="${nodocs}" arg2="true" />
		</condition>
		<exec executable="${mvn.executable}" dir=".." failonerror="true">
			<env key="MAVEN_OPTS" value="-Xmx1024m -Xms512m" />
			<arg line="-Pdistro${nodocs.profile} -DskipTests clean install" />
		</exec>
	</target>

	<target name="copy.static.resources">
		<copy todir="${target.distro.root}">
			<fileset dir="src" />
		</copy>
	</target>

	<target name="copy.dependencies">

		<!-- MOVE ALL LIBS TO libs -->
		<mkdir dir="${target.distro.root}/libs" />
		<copy todir="${target.distro.root}/libs" flatten="true">
			<fileset dir="../modules/flowable-engine/target">
				<include name="flowable-engine-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-engine-common-api/target">
				<include name="flowable-engine-common-api-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-engine-common/target">
				<include name="flowable-engine-common-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-spring-common/target">
                <include name="flowable-spring-common-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-spring/target">
				<include name="flowable-spring-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-spring-security/target">
				<include name="flowable-spring-security-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-bpmn-model/target">
				<include name="flowable-bpmn-model-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-bpmn-converter/target">
				<include name="flowable-bpmn-converter-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-process-validation/target">
				<include name="flowable-process-validation-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-secure-javascript/target">
				<include name="flowable-secure-javascript-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-identitylink-service-api/target">
                <include name="flowable-identitylink-service-api-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-identitylink-service/target">
                <include name="flowable-identitylink-service-*.jar" />
            </fileset>
            <fileset dir="../modules/flowable-entitylink-service-api/target">
                <include name="flowable-entitylink-service-api-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-entitylink-service/target">
                <include name="flowable-entitylink-service-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-eventsubscription-service-api/target">
                <include name="flowable-eventsubscription-service-api-*.jar" />
            </fileset>
            <fileset dir="../modules/flowable-eventsubscription-service/target">
                <include name="flowable-eventsubscription-service-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-job-service-api/target">
                <include name="flowable-job-service-api-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-job-service/target">
                <include name="flowable-job-service-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-job-spring-service/target">
                <include name="flowable-job-spring-service-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-batch-service-api/target">
                <include name="flowable-batch-service-api-*.jar" />
            </fileset>
            <fileset dir="../modules/flowable-batch-service/target">
                <include name="flowable-batch-service-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-task-service-api/target">
                <include name="flowable-task-service-api-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-task-service/target">
                <include name="flowable-task-service-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-variable-service-api/target">
                <include name="flowable-variable-service-api-*.jar" />
            </fileset>
            <fileset dir="../modules/flowable-variable-service/target">
                <include name="flowable-variable-service-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-api/target">
                <include name="flowable-cmmn-api-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-cdi/target">
                <include name="flowable-cmmn-cdi-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-case-validation/target">
                <include name="flowable-case-validation-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-converter/target">
                <include name="flowable-cmmn-converter-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-engine/target">
                <include name="flowable-cmmn-engine-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-engine-configurator/target">
                <include name="flowable-cmmn-engine-configurator-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-image-generator/target">
                <include name="flowable-cmmn-image-generator-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-model/target">
                <include name="flowable-cmmn-model-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-spring/target">
                <include name="flowable-cmmn-spring-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-spring-configurator/target">
                <include name="flowable-cmmn-spring-configurator-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-cmmn-rest/target">
			    <include name="flowable-cmmn-rest-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-app-engine-api/target">
                <include name="flowable-app-engine-api-*.jar" />
            </fileset>
            <fileset dir="../modules/flowable-app-engine/target">
                <include name="flowable-app-engine-*.jar" />
            </fileset>
            <fileset dir="../modules/flowable-app-engine-spring/target">
                <include name="flowable-app-engine-spring-*.jar" />
            </fileset>
            <fileset dir="../modules/flowable-app-engine-rest/target">
                <include name="flowable-app-engine-rest-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-dmn-model/target">
				<include name="flowable-dmn-model-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-dmn-xml-converter/target">
				<include name="flowable-dmn-xml-converter-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-dmn-api/target">
				<include name="flowable-dmn-api-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-dmn-image-generator/target">
				<include name="flowable-dmn-image-generator-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-dmn-engine/target">
				<include name="flowable-dmn-engine-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-dmn-engine-configurator/target">
				<include name="flowable-dmn-engine-configurator-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-dmn-spring/target">
				<include name="flowable-dmn-spring-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-dmn-spring-configurator/target">
				<include name="flowable-dmn-spring-configurator-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-dmn-rest/target">
				<include name="flowable-dmn-rest-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-idm-api/target">
				<include name="flowable-idm-api-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-idm-engine/target">
				<include name="flowable-idm-engine-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-idm-engine-configurator/target">
				<include name="flowable-idm-engine-configurator-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-idm-rest/target">
				<include name="flowable-idm-rest-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-idm-spring/target">
				<include name="flowable-idm-spring-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-idm-spring-configurator/target">
				<include name="flowable-idm-spring-configurator-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-event-registry/target">
                <include name="flowable-event-registry-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-event-registry-api/target">
                <include name="flowable-event-registry-api-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-event-registry-configurator/target">
                <include name="flowable-event-registry-configurator-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-event-registry-json-converter/target">
                <include name="flowable-event-registry-json-converter-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-event-registry-model/target">
                <include name="flowable-event-registry-model-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-event-registry-rest/target">
                <include name="flowable-event-registry-rest-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-event-registry-spring/target">
                <include name="flowable-event-registry-spring-*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-event-registry-spring-configurator/target">
                <include name="flowable-event-registry-spring-configurator*.jar" />
            </fileset>
			<fileset dir="../modules/flowable-form-model/target">
				<include name="flowable-form-model-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-form-api/target">
				<include name="flowable-form-api-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-content-api/target">
				<include name="flowable-content-api-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable5-compatibility/target">
				<include name="flowable5-compatibility-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable5-engine/target">
				<include name="flowable5-engine-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable5-spring-compatibility/target">
				<include name="flowable5-spring-compatibility-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable5-spring/target">
				<include name="flowable5-spring-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-bpmn-layout/target">
				<include name="flowable-bpmn-layout-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-image-generator/target">
				<include name="flowable-image-generator-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-ldap/target">
				<include name="flowable-ldap*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-common-rest/target">
				<include name="flowable-common-rest-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-external-job-rest/target">
				<include name="flowable-external-job-rest-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-rest/target">
				<include name="flowable-rest-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-camel/target">
				<include name="flowable-camel-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-cdi/target">
				<include name="flowable-cdi*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-cxf/target">
				<include name="flowable-cxf-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-http-common/target">
				<include name="flowable-http-common-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-http/target">
				<include name="flowable-http-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-mail/target">
				<include name="flowable-mail-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-spring-boot/flowable-spring-boot-starters/">
				<include name="**/flowable-spring-boot-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-osgi/target">
				<include name="flowable-osgi-*.jar" />
			</fileset>
			<fileset dir="../modules/flowable-jmx/target">
				<include name="flowable-jmx-*.jar" />
			</fileset>
		</copy>

	</target>

	<target name="copy.webapps">
		<mkdir dir="${target.distro.root}/wars" />
		<copy todir="${target.distro.root}/wars">
			<fileset dir="../modules/flowable-app-rest/target">
				<include name="flowable-rest*.war" />
			</fileset>
		</copy>
		<move file="${target.distro.root}/wars/flowable-rest.war" tofile="${target.distro.root}/wars/flowable-rest.war"/>
	</target>

	<target name="build.docs" unless="nodocs">
		<!-- JAVADOCS -->
		<exec executable="${mvn.executable}" dir="../modules/flowable-bpmn-model">
			<arg line="javadoc:javadoc" />
		</exec>
		<mkdir dir="${target.docs.root}/bpmn-model-javadocs" />
		<copy todir="${target.docs.root}/bpmn-model-javadocs">
			<fileset dir="../modules/flowable-bpmn-model/target/reports/apidocs" />
		</copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-engine">
			<arg line="-DexcludePackageNames=org.flowable.engine.impl* javadoc:javadoc" />
		</exec>
		<mkdir dir="${target.docs.root}/javadocs" />
		<copy todir="${target.docs.root}/javadocs">
			<fileset dir="../modules/flowable-engine/target/reports/apidocs" />
		</copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-cmmn-api">
            <arg line="-DexcludePackageNames=org.flowable.cmmn.engine.impl* javadoc:javadoc" />
        </exec>
        <mkdir dir="${target.docs.root}/cmmn-javadocs" />
        <copy todir="${target.docs.root}/cmmn-javadocs">
            <fileset dir="../modules/flowable-cmmn-api/target/reports/apidocs" />
        </copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-dmn-api">
            <arg line="-DexcludePackageNames=org.flowable.dmn.engine.impl* javadoc:javadoc" />
        </exec>
        <mkdir dir="${target.docs.root}/dmn-javadocs" />
        <copy todir="${target.docs.root}/dmn-javadocs">
            <fileset dir="../modules/flowable-dmn-api/target/reports/apidocs" />
        </copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-event-registry-api">
            <arg line="-DexcludePackageNames=org.flowable.eventregistry.impl* javadoc:javadoc" />
        </exec>
        <mkdir dir="${target.docs.root}/eventregistry-javadocs" />
        <copy todir="${target.docs.root}/eventregistry-javadocs">
            <fileset dir="../modules/flowable-event-registry-api/target/reports/apidocs" />
        </copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-task-service-api">
			<arg line="javadoc:javadoc" />
		</exec>
        <mkdir dir="${target.docs.root}/task-javadocs" />
        <copy todir="${target.docs.root}/task-javadocs">
            <fileset dir="../modules/flowable-task-service-api/target/reports/apidocs" />
        </copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-variable-service-api">
			<arg line="javadoc:javadoc" />
	    </exec>
        <mkdir dir="${target.docs.root}/variable-javadocs" />
        <copy todir="${target.docs.root}/variable-javadocs">
            <fileset dir="../modules/flowable-variable-service-api/target/reports/apidocs" />
        </copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-job-service-api">
			<arg line="javadoc:javadoc" />
        </exec>
        <mkdir dir="${target.docs.root}/job-javadocs" />
        <copy todir="${target.docs.root}/job-javadocs">
            <fileset dir="../modules/flowable-job-service-api/target/reports/apidocs" />
        </copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-identitylink-service-api">
            <arg line="javadoc:javadoc" />
        </exec>
        <mkdir dir="${target.docs.root}/identitylink-javadocs" />
        <copy todir="${target.docs.root}/identitylink-javadocs">
            <fileset dir="../modules/flowable-identitylink-service-api/target/reports/apidocs" />
        </copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-entitylink-service-api">
            <arg line="javadoc:javadoc" />
        </exec>
        <mkdir dir="${target.docs.root}/entitylink-javadocs" />
        <copy todir="${target.docs.root}/entitylink-javadocs">
            <fileset dir="../modules/flowable-entitylink-service-api/target/reports/apidocs" />
        </copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-eventsubscription-service-api">
            <arg line="javadoc:javadoc" />
        </exec>
        <mkdir dir="${target.docs.root}/eventsubscription-javadocs" />
        <copy todir="${target.docs.root}/eventsubscription-javadocs">
            <fileset dir="../modules/flowable-eventsubscription-service-api/target/reports/apidocs" />
        </copy>
		
		<exec executable="${mvn.executable}" dir="../modules/flowable-batch-service-api">
            <arg line="javadoc:javadoc" />
        </exec>
        <mkdir dir="${target.docs.root}/batch-javadocs" />
        <copy todir="${target.docs.root}/batch-javadocs">
            <fileset dir="../modules/flowable-batch-service-api/target/reports/apidocs" />
        </copy>
	</target>

	<target name="copy.docs" depends="build.docs" unless="nodocs">
		<!-- COPY XSD -->
		<copy todir="${target.docs.root}/xsd">
			<fileset dir="../modules/flowable-bpmn-converter/src/main/resources/org/flowable/impl/bpmn/parser">
				<include name="*.xsd" />
			</fileset>
		</copy>
	</target>

	<target name="publish.docs" depends="build.docs">
		<mkdir dir="${flowable.website}/javadocs" />
		<copy todir="${flowable.website}/javadocs" overwrite="true">
			<fileset dir="../modules/flowable-engine/target/site/apidocs" />
		</copy>
		<copy todir="${flowable.website}" overwrite="true">
			<fileset dir="src">
				<include name="readme.html" />
			</fileset>
		</copy>
	</target>

	<target name="copy.sql">
		<!-- COPY SQL -->
		<copy todir="${target.distro.root}/database">
			<fileset dir="sql">
				<include name="create/**" />
				<include name="drop/**" />
				<include name="upgrade/**" />
			</fileset>
		</copy>
	</target>

</project>
