# GRCOS Flowable Integration Deployment Guide

## Overview

This comprehensive deployment guide provides step-by-step instructions for deploying the GRCOS Flowable integration in production environments. The deployment includes Kubernetes orchestration, database setup, AI agent configuration, blockchain integration, and monitoring stack deployment.

## Prerequisites

### Infrastructure Requirements

#### Kubernetes Cluster
- **Version**: Kubernetes 1.28+
- **Nodes**: Minimum 5 nodes (3 control plane, 2+ worker nodes)
- **CPU**: 32+ cores total
- **Memory**: 128GB+ total RAM
- **Storage**: SSD with 10,000+ IOPS
- **Network**: 10Gbps+ internal networking

#### External Dependencies
- **PostgreSQL 15+**: Primary database cluster
- **MongoDB 7+**: OSCAL document storage
- **Redis 7+**: Caching and session management
- **Hyperledger Fabric 2.5+**: Blockchain network
- **Container Registry**: Private registry for GRCOS images

### Software Requirements

#### Required Tools
```bash
# Install required tools
kubectl version --client
helm version
docker version
terraform version
ansible --version
```

#### Access Requirements
- Kubernetes cluster admin access
- Container registry push/pull permissions
- Database admin credentials
- Blockchain network certificates
- DNS management access

## Pre-Deployment Setup

### 1. Namespace Creation

#### Create GRCOS Namespace
```yaml
# grcos-namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: grcos
  labels:
    name: grcos
    environment: production
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: grcos-resource-quota
  namespace: grcos
spec:
  hard:
    requests.cpu: "50"
    requests.memory: 100Gi
    limits.cpu: "100"
    limits.memory: 200Gi
    persistentvolumeclaims: "20"
```

```bash
kubectl apply -f grcos-namespace.yaml
```

### 2. Secret Management

#### Create Configuration Secrets
```yaml
# grcos-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: grcos-database-secrets
  namespace: grcos
type: Opaque
data:
  postgres-username: <base64-encoded-username>
  postgres-password: <base64-encoded-password>
  mongodb-username: <base64-encoded-username>
  mongodb-password: <base64-encoded-password>
  redis-password: <base64-encoded-password>
---
apiVersion: v1
kind: Secret
metadata:
  name: grcos-api-secrets
  namespace: grcos
type: Opaque
data:
  crew-ai-api-key: <base64-encoded-api-key>
  datagerry-api-key: <base64-encoded-api-key>
  wazuh-username: <base64-encoded-username>
  wazuh-password: <base64-encoded-password>
---
apiVersion: v1
kind: Secret
metadata:
  name: grcos-blockchain-secrets
  namespace: grcos
type: Opaque
data:
  fabric-user-cert: <base64-encoded-certificate>
  fabric-user-key: <base64-encoded-private-key>
  fabric-ca-cert: <base64-encoded-ca-certificate>
```

```bash
kubectl apply -f grcos-secrets.yaml
```

### 3. ConfigMap Creation

#### Application Configuration
```yaml
# grcos-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: grcos-flowable-config
  namespace: grcos
data:
  application.yml: |
    spring:
      profiles:
        active: kubernetes,production
      datasource:
        url: ******************************************************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
      redis:
        cluster:
          nodes:
            - redis-cluster.grcos.svc.cluster.local:6379
    
    flowable:
      database-schema-update: false
      async:
        executor:
          async-executor-activate: true
          default-async-job-acquire-wait-time: 10000
    
    grcos:
      ai:
        crew-ai:
          enabled: true
          api-key: ${CREW_AI_API_KEY}
      blockchain:
        hyperledger-fabric:
          enabled: true
          network-config: /config/fabric/network-config.yaml
      modules:
        datagerry:
          enabled: true
          api-url: http://datagerry-api.grcos.svc.cluster.local:4000
        opa:
          enabled: true
          server-url: http://opa-server.grcos.svc.cluster.local:8181
    
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      metrics:
        export:
          prometheus:
            enabled: true
```

```bash
kubectl apply -f grcos-configmap.yaml
```

## Core Deployment

### 1. Database Deployment

#### PostgreSQL Cluster
```yaml
# postgres-cluster.yaml
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: postgres-cluster
  namespace: grcos
spec:
  instances: 3
  
  postgresql:
    parameters:
      max_connections: "200"
      shared_buffers: "256MB"
      effective_cache_size: "1GB"
      work_mem: "4MB"
      maintenance_work_mem: "64MB"
      checkpoint_completion_target: "0.9"
      wal_buffers: "16MB"
      default_statistics_target: "100"
      random_page_cost: "1.1"
      effective_io_concurrency: "200"
  
  bootstrap:
    initdb:
      database: grcos_flowable
      owner: flowable_user
      secret:
        name: grcos-database-secrets
  
  storage:
    size: 100Gi
    storageClass: fast-ssd
  
  monitoring:
    enabled: true
  
  backup:
    retentionPolicy: "30d"
    barmanObjectStore:
      destinationPath: "s3://grcos-backups/postgres"
      s3Credentials:
        accessKeyId:
          name: backup-credentials
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: backup-credentials
          key: SECRET_ACCESS_KEY
```

#### MongoDB Deployment
```yaml
# mongodb-deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb
  namespace: grcos
spec:
  serviceName: mongodb
  replicas: 3
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:7
        ports:
        - containerPort: 27017
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: grcos-database-secrets
              key: mongodb-username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grcos-database-secrets
              key: mongodb-password
        volumeMounts:
        - name: mongodb-data
          mountPath: /data/db
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            cpu: 2000m
            memory: 4Gi
  volumeClaimTemplates:
  - metadata:
      name: mongodb-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 50Gi
```

### 2. Redis Cluster Deployment

#### Redis Cluster Configuration
```yaml
# redis-cluster.yaml
apiVersion: redis.redis.opstreelabs.in/v1beta1
kind: RedisCluster
metadata:
  name: redis-cluster
  namespace: grcos
spec:
  clusterSize: 6
  kubernetesConfig:
    image: redis:7-alpine
    imagePullPolicy: IfNotPresent
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 1000m
        memory: 2Gi
  storage:
    volumeClaimTemplate:
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: fast-ssd
        resources:
          requests:
            storage: 10Gi
  redisConfig:
    additionalRedisConfig: |
      maxmemory-policy allkeys-lru
      save 900 1
      save 300 10
      save 60 10000
```

### 3. GRCOS Flowable Engine Deployment

#### Main Application Deployment
```yaml
# grcos-flowable-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grcos-flowable
  namespace: grcos
  labels:
    app: grcos-flowable
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: grcos-flowable
  template:
    metadata:
      labels:
        app: grcos-flowable
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/actuator/prometheus"
    spec:
      serviceAccountName: grcos-flowable-sa
      containers:
      - name: grcos-flowable
        image: grcos/flowable-engine:1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: management
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes,production"
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: grcos-database-secrets
              key: postgres-username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grcos-database-secrets
              key: postgres-password
        - name: CREW_AI_API_KEY
          valueFrom:
            secretKeyRef:
              name: grcos-api-secrets
              key: crew-ai-api-key
        - name: DATAGERRY_API_KEY
          valueFrom:
            secretKeyRef:
              name: grcos-api-secrets
              key: datagerry-api-key
        volumeMounts:
        - name: config-volume
          mountPath: /config
        - name: fabric-certs
          mountPath: /config/fabric
          readOnly: true
        - name: workflow-definitions
          mountPath: /workflows
        resources:
          requests:
            cpu: 2000m
            memory: 4Gi
          limits:
            cpu: 4000m
            memory: 8Gi
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 9090
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 9090
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 12
      volumes:
      - name: config-volume
        configMap:
          name: grcos-flowable-config
      - name: fabric-certs
        secret:
          secretName: grcos-blockchain-secrets
      - name: workflow-definitions
        persistentVolumeClaim:
          claimName: workflow-definitions-pvc
      imagePullSecrets:
      - name: grcos-registry-secret
```

#### Service Configuration
```yaml
# grcos-flowable-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grcos-flowable-service
  namespace: grcos
  labels:
    app: grcos-flowable
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: management
  selector:
    app: grcos-flowable
---
apiVersion: v1
kind: Service
metadata:
  name: grcos-flowable-headless
  namespace: grcos
  labels:
    app: grcos-flowable
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: grcos-flowable
```

### 4. Ingress Configuration

#### NGINX Ingress
```yaml
# grcos-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: grcos-flowable-ingress
  namespace: grcos
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - grcos-workflows.company.com
    secretName: grcos-flowable-tls
  rules:
  - host: grcos-workflows.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grcos-flowable-service
            port:
              number: 8080
```

## Monitoring and Observability

### 1. Prometheus Monitoring

#### ServiceMonitor Configuration
```yaml
# grcos-servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: grcos-flowable-monitor
  namespace: grcos
  labels:
    app: grcos-flowable
spec:
  selector:
    matchLabels:
      app: grcos-flowable
  endpoints:
  - port: management
    path: /actuator/prometheus
    interval: 30s
    scrapeTimeout: 10s
```

### 2. Grafana Dashboards

#### Workflow Metrics Dashboard
```json
{
  "dashboard": {
    "title": "GRCOS Flowable Workflows",
    "panels": [
      {
        "title": "Active Process Instances",
        "type": "stat",
        "targets": [
          {
            "expr": "flowable_process_instances_active_total",
            "legendFormat": "Active Instances"
          }
        ]
      },
      {
        "title": "Workflow Execution Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(flowable_process_instances_completed_total[5m])",
            "legendFormat": "Completions/sec"
          }
        ]
      },
      {
        "title": "Task Completion Time",
        "type": "heatmap",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(flowable_task_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

## Deployment Execution

### 1. Pre-Deployment Validation

#### Validation Script
```bash
#!/bin/bash
# validate-deployment.sh

echo "Validating GRCOS Flowable deployment prerequisites..."

# Check Kubernetes connectivity
kubectl cluster-info || exit 1

# Check namespace
kubectl get namespace grcos || exit 1

# Check secrets
kubectl get secret grcos-database-secrets -n grcos || exit 1
kubectl get secret grcos-api-secrets -n grcos || exit 1
kubectl get secret grcos-blockchain-secrets -n grcos || exit 1

# Check storage classes
kubectl get storageclass fast-ssd || exit 1

# Check container registry access
docker pull grcos/flowable-engine:1.0.0 || exit 1

echo "Pre-deployment validation completed successfully!"
```

### 2. Deployment Execution

#### Deployment Script
```bash
#!/bin/bash
# deploy-grcos-flowable.sh

set -e

echo "Starting GRCOS Flowable deployment..."

# Deploy databases
echo "Deploying PostgreSQL cluster..."
kubectl apply -f postgres-cluster.yaml

echo "Deploying MongoDB..."
kubectl apply -f mongodb-deployment.yaml

echo "Deploying Redis cluster..."
kubectl apply -f redis-cluster.yaml

# Wait for databases to be ready
echo "Waiting for databases to be ready..."
kubectl wait --for=condition=Ready pod -l app=postgres-cluster -n grcos --timeout=300s
kubectl wait --for=condition=Ready pod -l app=mongodb -n grcos --timeout=300s
kubectl wait --for=condition=Ready pod -l app=redis-cluster -n grcos --timeout=300s

# Deploy GRCOS Flowable
echo "Deploying GRCOS Flowable engine..."
kubectl apply -f grcos-flowable-deployment.yaml
kubectl apply -f grcos-flowable-service.yaml

# Wait for deployment to be ready
echo "Waiting for GRCOS Flowable to be ready..."
kubectl wait --for=condition=Available deployment/grcos-flowable -n grcos --timeout=600s

# Deploy ingress
echo "Deploying ingress..."
kubectl apply -f grcos-ingress.yaml

# Deploy monitoring
echo "Deploying monitoring..."
kubectl apply -f grcos-servicemonitor.yaml

echo "GRCOS Flowable deployment completed successfully!"
echo "Access URL: https://grcos-workflows.company.com"
```

### 3. Post-Deployment Verification

#### Health Check Script
```bash
#!/bin/bash
# verify-deployment.sh

echo "Verifying GRCOS Flowable deployment..."

# Check pod status
kubectl get pods -n grcos -l app=grcos-flowable

# Check service endpoints
kubectl get endpoints -n grcos grcos-flowable-service

# Health check
HEALTH_URL="https://grcos-workflows.company.com/actuator/health"
curl -f $HEALTH_URL || exit 1

# Check metrics endpoint
METRICS_URL="https://grcos-workflows.company.com/actuator/prometheus"
curl -f $METRICS_URL | grep -q "flowable_" || exit 1

# Test workflow deployment
curl -X GET "https://grcos-workflows.company.com/flowable-rest/service/repository/process-definitions" \
  -H "Authorization: Bearer $API_TOKEN" || exit 1

echo "Deployment verification completed successfully!"
```

## Rollback Procedures

### Emergency Rollback

#### Rollback Script
```bash
#!/bin/bash
# rollback-grcos-flowable.sh

echo "Initiating GRCOS Flowable rollback..."

# Get previous deployment revision
PREVIOUS_REVISION=$(kubectl rollout history deployment/grcos-flowable -n grcos | tail -2 | head -1 | awk '{print $1}')

# Rollback deployment
kubectl rollout undo deployment/grcos-flowable -n grcos --to-revision=$PREVIOUS_REVISION

# Wait for rollback to complete
kubectl rollout status deployment/grcos-flowable -n grcos --timeout=300s

echo "Rollback completed successfully!"
```

## Maintenance Procedures

### 1. Database Maintenance

#### Backup Script
```bash
#!/bin/bash
# backup-databases.sh

# PostgreSQL backup
kubectl exec -n grcos postgres-cluster-1 -- pg_dump -U flowable_user grcos_flowable > grcos_flowable_backup_$(date +%Y%m%d_%H%M%S).sql

# MongoDB backup
kubectl exec -n grcos mongodb-0 -- mongodump --out /tmp/backup_$(date +%Y%m%d_%H%M%S)
```

### 2. Scaling Operations

#### Scale Up
```bash
# Scale up Flowable instances
kubectl scale deployment grcos-flowable --replicas=5 -n grcos

# Scale up database connections
kubectl patch cluster postgres-cluster -n grcos --type='merge' -p='{"spec":{"postgresql":{"parameters":{"max_connections":"400"}}}}'
```

### 3. Configuration Updates

#### Rolling Configuration Update
```bash
# Update ConfigMap
kubectl apply -f grcos-configmap.yaml

# Restart deployment to pick up new configuration
kubectl rollout restart deployment/grcos-flowable -n grcos
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database connectivity
kubectl exec -n grcos deployment/grcos-flowable -- nc -zv postgres-cluster 5432

# Check database logs
kubectl logs -n grcos postgres-cluster-1
```

#### 2. Memory Issues
```bash
# Check memory usage
kubectl top pods -n grcos

# Increase memory limits
kubectl patch deployment grcos-flowable -n grcos -p='{"spec":{"template":{"spec":{"containers":[{"name":"grcos-flowable","resources":{"limits":{"memory":"12Gi"}}}]}}}}'
```

#### 3. Workflow Deployment Issues
```bash
# Check workflow definitions
kubectl exec -n grcos deployment/grcos-flowable -- ls -la /workflows

# Validate BPMN files
kubectl exec -n grcos deployment/grcos-flowable -- flowable-validate /workflows/*.bpmn20.xml
```

This deployment guide provides a comprehensive approach to deploying GRCOS Flowable integration in production environments with proper monitoring, scaling, and maintenance procedures.
