<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DiagramNode (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DiagramNode (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DiagramNode.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/DiagramLayout.html" title="class in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/DiagramNode.html" target="_top">Frames</a></li>
<li><a href="DiagramNode.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.activiti.engine.repository.DiagramElement">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.repository</div>
<h2 title="Class DiagramNode" class="title">Class DiagramNode</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">org.activiti.engine.repository.DiagramElement</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.repository.DiagramNode</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DiagramNode</span>
extends <a href="../../../../org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">DiagramElement</a></pre>
<div class="block">Stores position and dimensions of a diagram node.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Falko Menge</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#org.activiti.engine.repository.DiagramNode">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.activiti.engine.repository.DiagramElement">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.activiti.engine.repository.<a href="../../../../org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">DiagramElement</a></h3>
<code><a href="../../../../org/activiti/engine/repository/DiagramElement.html#id">id</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#DiagramNode--">DiagramNode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#DiagramNode-java.lang.String-">DiagramNode</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#DiagramNode-java.lang.String-java.lang.Double-java.lang.Double-java.lang.Double-java.lang.Double-">DiagramNode</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;x,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;y,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;width,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;height)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#getHeight--">getHeight</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#getWidth--">getWidth</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#getX--">getX</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#getY--">getY</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#isEdge--">isEdge</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#isNode--">isNode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#setHeight-java.lang.Double-">setHeight</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;height)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#setWidth-java.lang.Double-">setWidth</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;width)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#setX-java.lang.Double-">setX</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;x)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#setY-java.lang.Double-">setY</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;y)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DiagramNode.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.repository.DiagramElement">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.activiti.engine.repository.<a href="../../../../org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">DiagramElement</a></h3>
<code><a href="../../../../org/activiti/engine/repository/DiagramElement.html#getId--">getId</a>, <a href="../../../../org/activiti/engine/repository/DiagramElement.html#setId-java.lang.String-">setId</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DiagramNode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DiagramNode</h4>
<pre>public&nbsp;DiagramNode()</pre>
</li>
</ul>
<a name="DiagramNode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DiagramNode</h4>
<pre>public&nbsp;DiagramNode(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</pre>
</li>
</ul>
<a name="DiagramNode-java.lang.String-java.lang.Double-java.lang.Double-java.lang.Double-java.lang.Double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DiagramNode</h4>
<pre>public&nbsp;DiagramNode(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;x,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;y,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;width,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;height)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getX()</pre>
</li>
</ul>
<a name="setX-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setX</h4>
<pre>public&nbsp;void&nbsp;setX(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;x)</pre>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getY()</pre>
</li>
</ul>
<a name="setY-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setY</h4>
<pre>public&nbsp;void&nbsp;setY(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;y)</pre>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getWidth()</pre>
</li>
</ul>
<a name="setWidth-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidth</h4>
<pre>public&nbsp;void&nbsp;setWidth(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;width)</pre>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getHeight()</pre>
</li>
</ul>
<a name="setHeight-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeight</h4>
<pre>public&nbsp;void&nbsp;setHeight(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;height)</pre>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/repository/DiagramElement.html#toString--">toString</a></code>&nbsp;in class&nbsp;<code><a href="../../../../org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">DiagramElement</a></code></dd>
</dl>
</li>
</ul>
<a name="isNode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNode</h4>
<pre>public&nbsp;boolean&nbsp;isNode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/repository/DiagramElement.html#isNode--">isNode</a></code>&nbsp;in class&nbsp;<code><a href="../../../../org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">DiagramElement</a></code></dd>
</dl>
</li>
</ul>
<a name="isEdge--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isEdge</h4>
<pre>public&nbsp;boolean&nbsp;isEdge()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/repository/DiagramElement.html#isEdge--">isEdge</a></code>&nbsp;in class&nbsp;<code><a href="../../../../org/activiti/engine/repository/DiagramElement.html" title="class in org.activiti.engine.repository">DiagramElement</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DiagramNode.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/DiagramLayout.html" title="class in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/DiagramNode.html" target="_top">Frames</a></li>
<li><a href="DiagramNode.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.activiti.engine.repository.DiagramElement">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
