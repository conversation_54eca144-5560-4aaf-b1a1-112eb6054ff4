<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <name>Flowable BOM</name>
    <description>Flowable BOM module</description>
    <groupId>org.flowable</groupId>
    <artifactId>flowable-bom</artifactId>
    <packaging>pom</packaging>
    <version>7.1.1-SNAPSHOT</version>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-app-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-app-engine-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-app-engine-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-app-engine-spring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-app-rest</artifactId>
                <type>war</type>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-batch-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-batch-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-bpmn-converter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-bpmn-layout</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-bpmn-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-camel</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-case-validation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cdi</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-cdi</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-converter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-engine-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-image-generator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-spring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cmmn-spring-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-common-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-content-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-cxf</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-dmn-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-dmn-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-dmn-engine-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-dmn-image-generator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-dmn-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-dmn-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-dmn-spring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-dmn-spring-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-dmn-xml-converter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-engine-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-engine-common-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-engine-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-entitylink-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-entitylink-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-event-registry</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-event-registry-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-event-registry-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-event-registry-json-converter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-event-registry-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-event-registry-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-event-registry-spring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-event-registry-spring-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-eventsubscription-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-eventsubscription-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-external-job-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-form-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-form-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-groovy-script-static-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-http-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-identitylink-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-identitylink-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-idm-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-idm-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-idm-engine-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-idm-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-idm-spring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-idm-spring-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-image-generator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-jmx</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-job-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-job-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-job-spring-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-ldap</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-ldap-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-mail</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-osgi</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-process-validation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-secure-javascript</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-configurator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-task-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-task-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-variable-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-variable-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring Boot Support  -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-autoconfigure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-actuator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-app-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-cmmn</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-cmmn-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-dmn</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-dmn-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-rest</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Flowable 5 Compatibility  -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable5-compatibility</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable5-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable5-spring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable5-spring-compatibility</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    
    <profiles>
        <profile>
            <id>deploy</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    
    <!-- Various information, not used by the build -->

    <url>http://www.flowable.org</url>
    <licenses>
        <license>
            <name>Apache v2</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
        </license>
    </licenses>
    <organization>
        <name>Flowable</name>
        <url>http://www.flowable.org</url>
    </organization>
    <developers>
        <developer>
            <name>Tijs Rademakers</name>
            <organization>Flowable</organization>
            <url>http://www.flowable.org</url>
        </developer>
        <developer>
            <name>Joram Barrez</name>
            <organization>Flowable</organization>
            <url>http://www.flowable.org</url>
        </developer>
        <developer>
            <name>Filip Hrisafov</name>
            <organization>Flowable</organization>
            <url>http://www.flowable.org</url>
        </developer>
        <developer>
            <name>Yvo Swillens</name>
            <organization>Flowable</organization>
            <url>http://www.flowable.org</url>
        </developer>
        <developer>
            <name>Josh Long</name>
            <organization>Pivotal</organization>
            <url>http://www.pivotal.com</url>
        </developer>
    </developers>
    <issueManagement>
        <system>Github issues</system>
        <url>https://github.com/flowable/flowable-engine/issues</url>
    </issueManagement>
    <scm>
        <url>**************:flowable/flowable-engine.git</url>
        <connection>scm:git:**************:flowable/flowable-engine.git</connection>
        <developerConnection>scm:git:**************:flowable/flowable-engine.git</developerConnection>
    </scm>
    <distributionManagement>
		<snapshotRepository>
			<id>sonatype-nexus-snapshots</id>
			<name>Sonatype Nexus Snapshots</name>
			<url>${distributionManagementSnapshotsUrl}</url>
		</snapshotRepository>
		<repository>
			<id>sonatype-nexus-staging</id>
			<name>Nexus Release Repository</name>
			<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>

</project>
