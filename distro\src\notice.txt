Flowable (http://www.flowable.org) includes work under the Apache License v2.0
requiring this NOTICE file to be provided.


Portions of this software (C) Copyright 2010-2016 Alfresco Software, Ltd, licensed under the Apache License v2.0.


The following Flowable software libraries are distributed under the Apache License Version 2.0 (the License):

flowable5-compatibility<version><.jar | -source.jar | -javadoc.jar>
flowable5-engine<version><.jar | -source.jar | -javadoc.jar>
flowable5-spring<version><.jar | -source.jar | -javadoc.jar>
flowable5-spring-compatibility<version><.jar | -source.jar | -javadoc.jar>
flowable-bpmn-converter<version><.jar | -source.jar | -javadoc.jar>
flowable-bpmn-layout<version><.jar | -source.jar | -javadoc.jar>
flowable-bpmn-model<version><.jar | -source.jar | -javadoc.jar>
flowable-camel<version><.jar | -source.jar | -javadoc.jar>
flowable-cdi<version><.jar | -source.jar | -javadoc.jar>
flowable-common-rest<version><.jar | -source.jar | -javadoc.jar>
flowable-content-api<version><.jar | -source.jar | -javadoc.jar>
flowable-cxf<version><.jar | -source.jar | -javadoc.jar>
flowable-dmn-api<version><.jar | -source.jar | -javadoc.jar>
flowable-dmn-engine<version><.jar | -source.jar | -javadoc.jar>
flowable-dmn-engine-configurator<version><.jar | -source.jar | -javadoc.jar>
flowable-dmn-model<version><.jar | -source.jar | -javadoc.jar>
flowable-dmn-rest<version><.jar | -source.jar | -javadoc.jar>
flowable-dmn-spring<version><.jar | -source.jar | -javadoc.jar>
flowable-dmn-spring-configurator<version><.jar | -source.jar | -javadoc.jar>
flowable-dmn-xml-converter<version><.jar | -source.jar | -javadoc.jar>
flowable-engine<version><.jar | -source.jar | -javadoc.jar>
flowable-engine-common<version><.jar | -source.jar | -javadoc.jar>
flowable-engine-common-api<version><.jar | -source.jar | -javadoc.jar>
flowable-event-registry<version><.jar | -source.jar | -javadoc.jar>
flowable-event-registry-api<version><.jar | -source.jar | -javadoc.jar>
flowable-event-registry-configurator<version><.jar | -source.jar | -javadoc.jar>
flowable-event-registry-json-converter<version><.jar | -source.jar | -javadoc.jar>
flowable-event-registry-model<version><.jar | -source.jar | -javadoc.jar>
flowable-event-registry-rest<version><.jar | -source.jar | -javadoc.jar>
flowable-event-registry-spring<version><.jar | -source.jar | -javadoc.jar>
flowable-event-registry-spring-configurator<version><.jar | -source.jar | -javadoc.jar>
flowable-form-api<version><.jar | -source.jar | -javadoc.jar>
flowable-form-model<version><.jar | -source.jar | -javadoc.jar>
flowable-http<version><.jar | -source.jar | -javadoc.jar>
flowable-image-generator<version><.jar | -source.jar | -javadoc.jar>
flowable-jmx<version><.jar | -source.jar | -javadoc.jar>
flowable-ldap<version><.jar | -source.jar | -javadoc.jar>
flowable-osgi<version><.jar | -source.jar | -javadoc.jar>
flowable-process-validation<version><.jar | -source.jar | -javadoc.jar>
flowable-rest<version><.jar | -source.jar | -javadoc.jar>
flowable-secure-javascript<version><.jar | -source.jar | -javadoc.jar>
flowable-spring<version><.jar | -source.jar | -javadoc.jar>
flowable-spring-boot-starter-actuator<version><.jar | -source.jar | -javadoc.jar>
flowable-spring-boot-starter-integration<version><.jar | -source.jar | -javadoc.jar>



You may not use these files except in compliance with the License.  You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0.
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an AS IS BASIS, WITHOUT WARRANTIES OR CONDITIONS OR ANY KIND, either express or implied.  See the License for the specific language governing permissions and limitations under the License.
This software package also includes third party components as listed below.
This software package includes changed source code of the following libraries:


Quartz
 * Location: http://www.quartz-scheduler.org/
 * CronExpression is included in flowable-engine-<version>.jar in package org.flowable.engine.impl.calendar
 * License: Apache V2


This software includes unchanged copies of the following libraries:

com.fasterxml.jackson.core        jackson-annotations         2.19.0          The Apache Software License, Version 2.0
com.fasterxml.jackson.core        jackson-core                2.19.0          The Apache Software License, Version 2.0
com.fasterxml.jackson.core        jackson-databind            2.19.0          The Apache Software License, Version 2.0
com.fasterxml.jackson.datatype    jackson-datatype-joda       2.19.0          The Apache Software License, Version 2.0
com.github.vlsi.mxgraph           jgraphx                     4.2.2           JGraph Ltd - 3 clause BSD license
com.h2database                    h2                          2.3.232         The H2 License, Version 1.0
com.fasterxml.uuid                java-uuid-generator         5.1.0           The Apache Software License, Version 2.0
commons-beanutils                 commons-beanutils           1.9.4           The Apache Software License, Version 2.0
commons-codec                     commons-codec               1.18.0          Apache License, Version 2.0
commons-collections               commons-collections         3.2.2           The Apache Software License, Version 2.0
commons-io                        commons-io                  2.19.0          The Apache Software License, Version 2.0
de.odysseus.juel                  juel-api                    2.2.7           The Apache Software License, Version 2.0
de.odysseus.juel                  juel-impl                   2.2.7           The Apache Software License, Version 2.0
de.odysseus.juel                  juel-spi                    2.2.7           The Apache Software License, Version 2.0
io.swagger                        swagger-annotations         1.6.2           Apache License 2.0
io.swagger                        swagger-models              1.6.2           Apache License 2.0
jakarta.activation                jakarta.activation-api      2.1.3           EDL 1.0
jakarta.mail                      jakarta.mail.api            2.1.3           EDL 1.0
joda-time                         joda-time                   2.13.0          Apache 2
org.apache.commons                commons-lang3               3.17.0          The Apache Software License, Version 2.0
org.apache.httpcomponents.client5 httpclient5                 5.4.4           Apache License, Version 2.0
org.apache.httpcomponents.core5   httpcore5                   5.4.4           Apache License, Version 2.0
org.apache.httpcomponents.core5   httpcore5-h2                5.4.4           Apache License, Version 2.0
org.apache.groovy                 groovy                      4.0.26          The Apache Software License, Version 2.0
org.apache.groovy                 groovy-jsr223               4.0.26          The Apache Software License, Version 2.0
org.eclipse.angus                 angus-activation            2.0.2           EDL 1.0 / EPL 2.0
org.eclipse.angus                 angus-mail                  2.0.3           EDL 1.0 / EPL 2.0
org.mybatis                       mybatis                     3.5.19          The Apache Software License, Version 2.0
org.slf4j                         jcl-over-slf4j              2.0.17          MIT License
org.slf4j                         slf4j-api                   2.0.17          MIT License
org.slf4j                         slf4j-reload4j              2.0.17          MIT License
org.springframework               spring-beans                6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-core                 6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-context              6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-jdbc                 6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-tx                   6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-web                  6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-webmvc               6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-aop                  6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-core                 6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-expression           6.2.7           The Apache Software License, Version 2.0
org.springframework               spring-orm                  6.2.7           The Apache Software License, Version 2.0
org.springframework.security      spring-security-config      6.5.0           The Apache Software License, Version 2.0
org.springframework.security      spring-security-core        6.5.0           The Apache Software License, Version 2.0
org.springframework.security      spring-security-crypto      6.5.0           The Apache Software License, Version 2.0
org.springframework.security      spring-security-web         6.5.0           The Apache Software License, Version 2.0
org.springframework.security      spring-security-ldap        6.5.0           The Apache Software License, Version 2.0
org.yaml                          snakeyaml                   2.2             The Apache Software License, Version 2.0

When using the Flowable Rest app, there are a number of additional libraries included, which are otherwise not a dependency of Flowable:

com.zaxxer                        HikariCP                    6.3.0            The Apache Software License, Version 2.0
jakarta.annotation                jakarta.annotation-api      2.1.1            EPL 2.0, GPL with classpath exception
jakarta.jms                       jakarta.jms-api             3.1.0            EPL 2.0, GPL with classpath exception
jakarta.mail                      jakarta.mail-api            2.1.3            EPL 2.0, GPL with classpath exception
org.openjdk.nashorn               nashorn-core                15.4             GPL with classpath exception
