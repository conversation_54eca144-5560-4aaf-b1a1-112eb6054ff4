name: Flowable Oracle Build

on:
  push:
    branches:
      - main
      - 'flowable-release-*'
env:
  MAVEN_ARGS: >-
    -Dmaven.javadoc.skip=true
    -B -V --no-transfer-progress
    -Dhttp.keepAlive=false -Dmaven.wagon.http.pool=false -Dmaven.wagon.httpconnectionManager.ttlSeconds=120

jobs:
  test_oracle:
    name: Oracle ${{ matrix.oracle }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        oracle: [ "18-slim-faststart", "21-slim-faststart", "23-slim-faststart" ]
        include:
          - oracle: 18-slim-faststart
            driverVersion: ********
            driverArtifact: ojdbc8
            serviceName: XEPDB1
            type: xe
          - oracle: 21-slim-faststart
            driverVersion: ********.1
            driverArtifact: ojdbc8
            serviceName: XEPDB1
            type: xe
          - oracle: 23-slim-faststart
            driverVersion: *********.01
            driverArtifact: ojdbc17
            serviceName: FREEPDB1
            type: free
    services:
      oracle:
        image: gvenzl/oracle-${{ matrix.type }}:${{ matrix.oracle }}
        env:
          ORACLE_PASSWORD: flowable
          APP_USER: flowable
          APP_USER_PASSWORD: flowable
        ports:
          - 1521/tcp
        options: >-
          --shm-size=2g
          --health-cmd healthcheck.sh
          --health-interval 20s
          --health-timeout 10s
          --health-retries 10
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 17
      - name: Test
        id: test
        # use oracle for the host here because we have specified a container for the job.
        # If we were running the job on the VM this would be localhost
        # '>-' is a special YAML syntax and means that new lines would be replaced with spaces
        # and new lines from the end would be removed
        run: >-
          ./mvnw clean install
          ${MAVEN_ARGS}
          -PcleanDb,oracle,distro
          -P'!include-spring-boot-samples'
          -Djdbc.url=***************************:${{ job.services.oracle.ports[1521] }}/${{ matrix.serviceName }}
          -Djdbc.username=flowable
          -Djdbc.password=flowable
          -Djdbc.driver=oracle.jdbc.driver.OracleDriver
          -Dspring.datasource.url=***************************:${{ job.services.oracle.ports[1521] }}/${{ matrix.serviceName }}
          -Dspring.datasource.username=flowable
          -Dspring.datasource.password=flowable
          -Doracle.jdbc.version=${{ matrix.driverVersion }}
          -Doracle.jdbc.artifact=${{ matrix.driverArtifact }}
          -Dmaven.test.redirectTestOutputToFile=false
      - name: Upload test artifacts
        uses: actions/upload-artifact@v4
        if: ${{ failure() && steps.test.conclusion == 'failure' }}
        with:
          name: surefire-test-reports-${{ matrix.oracle }}
          path: '**/target/surefire-reports/*'
