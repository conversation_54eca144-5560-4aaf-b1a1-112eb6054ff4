name: Release Helm Charts

on:
  push:
    branches:
      - flowable-helm

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config user.name "$GITHUB_ACTOR"
          git config user.email "$<EMAIL>"

      - name: Install Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.4.0

      - name: Run chart-releaser
        uses: helm/chart-releaser-action@v1.2.1
        with:
          charts_dir: k8s/flowable
          charts_repo_url: https://flowable.github.io/helm/
        env:
          CR_OWNER: flowable
          CR_GIT_REPO: flowable.github.io
          CR_PACKAGE_PATH: helm
          CR_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
