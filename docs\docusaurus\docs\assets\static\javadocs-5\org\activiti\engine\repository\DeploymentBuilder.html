<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DeploymentBuilder (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DeploymentBuilder (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DeploymentBuilder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/DeploymentBuilder.html" target="_top">Frames</a></li>
<li><a href="DeploymentBuilder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.repository</div>
<h2 title="Interface DeploymentBuilder" class="title">Interface DeploymentBuilder</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">DeploymentBuilder</span></pre>
<div class="block">Builder for creating new deployments.
 
 A builder instance can be obtained through <a href="../../../../org/activiti/engine/RepositoryService.html#createDeployment--"><code>RepositoryService.createDeployment()</code></a>.
 
 Multiple resources can be added to one deployment before calling the <a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#deploy--"><code>deploy()</code></a>
 operation.
 
 After deploying, no more changes can be made to the returned deployment
 and the builder instance can be disposed.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#activateProcessDefinitionsOn-java.util.Date-">activateProcessDefinitionsOn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block">Sets the date on which the process definitions contained in this deployment
 will be activated.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#addBpmnModel-java.lang.String-org.activiti.bpmn.model.BpmnModel-">addBpmnModel</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName,
            org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#addClasspathResource-java.lang.String-">addClasspathResource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resource)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#addInputStream-java.lang.String-java.io.InputStream-">addInputStream</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#addString-java.lang.String-java.lang.String-">addString</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName,
         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#addZipInputStream-java.util.zip.ZipInputStream-">addZipInputStream</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/zip/ZipInputStream.html?is-external=true" title="class or interface in java.util.zip">ZipInputStream</a>&nbsp;zipInputStream)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#category-java.lang.String-">category</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</code>
<div class="block">Gives the deployment the given category.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository">Deployment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#deploy--">deploy</a></span>()</code>
<div class="block">Deploys all provided sources to the Activiti engine.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#disableBpmnValidation--">disableBpmnValidation</a></span>()</code>
<div class="block">If called, no validation that the process definition is executable on the engine
 will be done against the process definition.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#disableSchemaValidation--">disableSchemaValidation</a></span>()</code>
<div class="block">If called, no XML schema validation against the BPMN 2.0 XSD.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#enableDuplicateFiltering--">enableDuplicateFiltering</a></span>()</code>
<div class="block">If set, this deployment will be compared to any previous deployment.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#name-java.lang.String-">name</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Gives the deployment the given name.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#tenantId-java.lang.String-">tenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Gives the deployment the given tenant id.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addInputStream-java.lang.String-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addInputStream</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;addInputStream(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName,
                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</pre>
</li>
</ul>
<a name="addClasspathResource-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClasspathResource</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;addClasspathResource(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resource)</pre>
</li>
</ul>
<a name="addString-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addString</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;addString(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
</li>
</ul>
<a name="addZipInputStream-java.util.zip.ZipInputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addZipInputStream</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;addZipInputStream(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/zip/ZipInputStream.html?is-external=true" title="class or interface in java.util.zip">ZipInputStream</a>&nbsp;zipInputStream)</pre>
</li>
</ul>
<a name="addBpmnModel-java.lang.String-org.activiti.bpmn.model.BpmnModel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addBpmnModel</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;addBpmnModel(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName,
                               org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</pre>
</li>
</ul>
<a name="disableSchemaValidation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableSchemaValidation</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;disableSchemaValidation()</pre>
<div class="block">If called, no XML schema validation against the BPMN 2.0 XSD.
 
 Not recommended in general.</div>
</li>
</ul>
<a name="disableBpmnValidation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableBpmnValidation</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;disableBpmnValidation()</pre>
<div class="block">If called, no validation that the process definition is executable on the engine
 will be done against the process definition.
 
 Not recommended in general.</div>
</li>
</ul>
<a name="name-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;name(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Gives the deployment the given name.</div>
</li>
</ul>
<a name="category-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>category</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;category(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</pre>
<div class="block">Gives the deployment the given category.</div>
</li>
</ul>
<a name="tenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;tenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Gives the deployment the given tenant id.</div>
</li>
</ul>
<a name="enableDuplicateFiltering--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableDuplicateFiltering</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;enableDuplicateFiltering()</pre>
<div class="block">If set, this deployment will be compared to any previous deployment.
 This means that every (non-generated) resource will be compared with the
 provided resources of this deployment.</div>
</li>
</ul>
<a name="activateProcessDefinitionsOn-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activateProcessDefinitionsOn</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;activateProcessDefinitionsOn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block">Sets the date on which the process definitions contained in this deployment
 will be activated. This means that all process definitions will be deployed
 as usual, but they will be suspended from the start until the given activation date.</div>
</li>
</ul>
<a name="deploy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>deploy</h4>
<pre><a href="../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository">Deployment</a>&nbsp;deploy()</pre>
<div class="block">Deploys all provided sources to the Activiti engine.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DeploymentBuilder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/DeploymentBuilder.html" target="_top">Frames</a></li>
<li><a href="DeploymentBuilder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
