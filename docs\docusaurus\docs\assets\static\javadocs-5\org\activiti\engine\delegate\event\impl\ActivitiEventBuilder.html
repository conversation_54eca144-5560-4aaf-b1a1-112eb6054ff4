<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitiEventBuilder (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitiEventBuilder (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEventBuilder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiErrorEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html" target="_top">Frames</a></li>
<li><a href="ActivitiEventBuilder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate.event.impl</div>
<h2 title="Class ActivitiEventBuilder" class="title">Class ActivitiEventBuilder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.delegate.event.impl.ActivitiEventBuilder</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ActivitiEventBuilder</span>
extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Builder class used to create <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> implementations.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Frederik Heremans</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#ActivitiEventBuilder--">ActivitiEventBuilder</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiActivityCancelledEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiActivityCancelledEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createActivityCancelledEvent-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.Object-">createActivityCancelledEvent</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityName,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityType,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;behaviourClass,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;cause)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiActivityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiActivityEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createActivityEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">createActivityEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityName,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityType,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;behaviourClass)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiCancelledEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiCancelledEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createCancelledEvent-java.lang.String-java.lang.String-java.lang.String-java.lang.Object-">createCancelledEvent</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;cause)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createEntityEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-">createEntityEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createEntityEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-java.lang.String-java.lang.String-java.lang.String-">createEntityEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createEntityExceptionEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-java.lang.Throwable-">createEntityExceptionEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;cause)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createEntityExceptionEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-java.lang.Throwable-java.lang.String-java.lang.String-java.lang.String-">createEntityExceptionEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;cause,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityWithVariablesEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityWithVariablesEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createEntityWithVariablesEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-java.util.Map-boolean-">createEntityWithVariablesEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&nbsp;variables,
                              boolean&nbsp;localScope)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiErrorEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiErrorEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createErrorEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">createErrorEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;errorCode,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.String-">createEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createGlobalEvent-org.activiti.engine.delegate.event.ActivitiEventType-">createGlobalEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiMembershipEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiMembershipEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createMembershipEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-">createMembershipEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiMessageEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiMessageEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createMessageEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.Object-java.lang.String-java.lang.String-java.lang.String-">createMessageEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;payload,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiProcessStartedEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiProcessStartedEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createProcessStartedEvent-java.lang.Object-java.util.Map-boolean-">createProcessStartedEvent</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&nbsp;variables,
                         boolean&nbsp;localScope)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiSequenceFlowTakenEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiSequenceFlowTakenEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createSequenceFlowTakenEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">createSequenceFlowTakenEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sequenceFlowId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceActivityId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceActivityName,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceActivityType,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceActivityBehaviorClass,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;targetActivityId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;targetActivityName,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;targetActivityType,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;targetActivityBehaviorClass)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiSignalEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiSignalEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createSignalEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.Object-java.lang.String-java.lang.String-java.lang.String-">createSignalEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;signalData,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#createVariableEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.Object-org.activiti.engine.impl.variable.VariableType-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">createVariableEvent</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue,
                   org.activiti.engine.impl.variable.VariableType&nbsp;variableType,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>protected static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html#populateEventWithCurrentContext-org.activiti.engine.delegate.event.impl.ActivitiEventImpl-">populateEventWithCurrentContext</a></span>(<a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a>&nbsp;event)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ActivitiEventBuilder--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ActivitiEventBuilder</h4>
<pre>public&nbsp;ActivitiEventBuilder()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createGlobalEvent-org.activiti.engine.delegate.event.ActivitiEventType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createGlobalEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;createGlobalEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - type of event</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> that doesn't have it's execution context-fields filled,
 as the event is a global event, independant of any running execution.</dd>
</dl>
</li>
</ul>
<a name="createEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;createEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
</li>
</ul>
<a name="createEntityEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEntityEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a>&nbsp;createEntityEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - type of event</dd>
<dd><code>entity</code> - the entity this event targets</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a>. In case an <code>ExecutionContext</code> is active, the execution related
 event fields will be populated. If not, execution details will be reteived from the <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><code>Object</code></a> if possible.</dd>
</dl>
</li>
</ul>
<a name="createProcessStartedEvent-java.lang.Object-java.util.Map-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProcessStartedEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiProcessStartedEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiProcessStartedEvent</a>&nbsp;createProcessStartedEvent(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&nbsp;variables,
                                                                    boolean&nbsp;localScope)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entity</code> - the entity this event targets</dd>
<dd><code>variables</code> - the variables associated with this entity</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a>. In case an <code>ExecutionContext</code> is active, the execution related
         event fields will be populated. If not, execution details will be reteived from the <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><code>Object</code></a> if
         possible.</dd>
</dl>
</li>
</ul>
<a name="createEntityWithVariablesEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-java.util.Map-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEntityWithVariablesEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityWithVariablesEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityWithVariablesEvent</a>&nbsp;createEntityWithVariablesEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                                                                              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&nbsp;variables,
                                                                              boolean&nbsp;localScope)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - type of event</dd>
<dd><code>entity</code> - the entity this event targets</dd>
<dd><code>variables</code> - the variables associated with this entity</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a>. In case an <code>ExecutionContext</code> is active, the execution related
 event fields will be populated. If not, execution details will be reteived from the <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><code>Object</code></a> if possible.</dd>
</dl>
</li>
</ul>
<a name="createSequenceFlowTakenEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createSequenceFlowTakenEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiSequenceFlowTakenEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiSequenceFlowTakenEvent</a>&nbsp;createSequenceFlowTakenEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sequenceFlowId,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceActivityId,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceActivityName,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceActivityType,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceActivityBehaviorClass,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;targetActivityId,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;targetActivityName,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;targetActivityType,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;targetActivityBehaviorClass)</pre>
</li>
</ul>
<a name="createEntityEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEntityEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a>&nbsp;createEntityEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - type of event</dd>
<dd><code>entity</code> - the entity this event targets</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a></dd>
</dl>
</li>
</ul>
<a name="createEntityExceptionEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEntityExceptionEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a>&nbsp;createEntityExceptionEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;cause)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - type of event</dd>
<dd><code>entity</code> - the entity this event targets</dd>
<dd><code>cause</code> - the cause of the event</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a> that is also instance of <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiExceptionEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiExceptionEvent</code></a>. 
 In case an <code>ExecutionContext</code> is active, the execution related event fields will be populated.</dd>
</dl>
</li>
</ul>
<a name="createEntityExceptionEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.Object-java.lang.Throwable-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEntityExceptionEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a>&nbsp;createEntityExceptionEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;entity,
                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;cause,
                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - type of event</dd>
<dd><code>entity</code> - the entity this event targets</dd>
<dd><code>cause</code> - the cause of the event</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a> that is also instance of <a href="../../../../../../org/activiti/engine/delegate/event/ActivitiExceptionEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiExceptionEvent</code></a>.</dd>
</dl>
</li>
</ul>
<a name="createActivityEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiActivityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiActivityEvent</a>&nbsp;createActivityEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityName,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityType,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;behaviourClass)</pre>
</li>
</ul>
<a name="createActivityCancelledEvent-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityCancelledEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiActivityCancelledEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiActivityCancelledEvent</a>&nbsp;createActivityCancelledEvent(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityName,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityType,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;behaviourClass,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;cause)</pre>
</li>
</ul>
<a name="createCancelledEvent-java.lang.String-java.lang.String-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCancelledEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiCancelledEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiCancelledEvent</a>&nbsp;createCancelledEvent(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;cause)</pre>
</li>
</ul>
<a name="createSignalEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.Object-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createSignalEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiSignalEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiSignalEvent</a>&nbsp;createSignalEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;signalName,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;signalData,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
</li>
</ul>
<a name="createMessageEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.Object-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMessageEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiMessageEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiMessageEvent</a>&nbsp;createMessageEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName,
                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;payload,
                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
</li>
</ul>
<a name="createErrorEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createErrorEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiErrorEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiErrorEvent</a>&nbsp;createErrorEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId,
                                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;errorCode,
                                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
</li>
</ul>
<a name="createVariableEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.Object-org.activiti.engine.impl.variable.VariableType-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createVariableEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a>&nbsp;createVariableEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue,
                                                        org.activiti.engine.impl.variable.VariableType&nbsp;variableType,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId,
                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
</li>
</ul>
<a name="createMembershipEvent-org.activiti.engine.delegate.event.ActivitiEventType-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMembershipEvent</h4>
<pre>public static&nbsp;<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiMembershipEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiMembershipEvent</a>&nbsp;createMembershipEvent(<a href="../../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;type,
                                                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId,
                                                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
</li>
</ul>
<a name="populateEventWithCurrentContext-org.activiti.engine.delegate.event.impl.ActivitiEventImpl-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>populateEventWithCurrentContext</h4>
<pre>protected static&nbsp;void&nbsp;populateEventWithCurrentContext(<a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a>&nbsp;event)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEventBuilder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiErrorEventImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html" title="class in org.activiti.engine.delegate.event.impl"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?org/activiti/engine/delegate/event/impl/ActivitiEventBuilder.html" target="_top">Frames</a></li>
<li><a href="ActivitiEventBuilder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
