<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitiEventDispatcher (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitiEventDispatcher (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEventDispatcher.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" target="_top">Frames</a></li>
<li><a href="ActivitiEventDispatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate.event</div>
<h2 title="Interface ActivitiEventDispatcher" class="title">Interface ActivitiEventDispatcher</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventDispatcherImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventDispatcherImpl</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ActivitiEventDispatcher</span></pre>
<div class="block">Dispatcher which allows for adding and removing <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEventListener</code></a>s to the Activiti Engine as well
 as dispatching <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> to all the listeners registered.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Frederik Heremans</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">addEventListener</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd)</code>
<div class="block">Adds an event-listener which will be notified of ALL events by the dispatcher.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-org.activiti.engine.delegate.event.ActivitiEventType...-">addEventListener</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd,
                <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>...&nbsp;types)</code>
<div class="block">Adds an event-listener which will only be notified when an event of the given types occurs.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#dispatchEvent-org.activiti.engine.delegate.event.ActivitiEvent-">dispatchEvent</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</code>
<div class="block">Dispatches the given event to any listeners that are registered.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#isEnabled--">isEnabled</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#removeEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">removeEventListener</a></span>(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToRemove)</code>
<div class="block">Removes the given listener from this dispatcher.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html#setEnabled-boolean-">setEnabled</a></span>(boolean&nbsp;enabled)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEventListener</h4>
<pre>void&nbsp;addEventListener(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd)</pre>
<div class="block">Adds an event-listener which will be notified of ALL events by the dispatcher.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listenerToAdd</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="addEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-org.activiti.engine.delegate.event.ActivitiEventType...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEventListener</h4>
<pre>void&nbsp;addEventListener(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToAdd,
                      <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>...&nbsp;types)</pre>
<div class="block">Adds an event-listener which will only be notified when an event of the given types occurs.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listenerToAdd</code> - the listener to add</dd>
<dd><code>types</code> - types of events the listener should be notified for</dd>
</dl>
</li>
</ul>
<a name="removeEventListener-org.activiti.engine.delegate.event.ActivitiEventListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeEventListener</h4>
<pre>void&nbsp;removeEventListener(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a>&nbsp;listenerToRemove)</pre>
<div class="block">Removes the given listener from this dispatcher. The listener will no longer be notified,
 regardless of the type(s) it was registered for in the first place.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listenerToRemove</code> - listener to remove</dd>
</dl>
</li>
</ul>
<a name="dispatchEvent-org.activiti.engine.delegate.event.ActivitiEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dispatchEvent</h4>
<pre>void&nbsp;dispatchEvent(<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a>&nbsp;event)</pre>
<div class="block">Dispatches the given event to any listeners that are registered.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - event to dispatch.</dd>
</dl>
</li>
</ul>
<a name="setEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnabled</h4>
<pre>void&nbsp;setEnabled(boolean&nbsp;enabled)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enabled</code> - true, if event dispatching should be enabled.</dd>
</dl>
</li>
</ul>
<a name="isEnabled--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isEnabled</h4>
<pre>boolean&nbsp;isEnabled()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true, if event dispatcher is enabled.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEventDispatcher.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" target="_top">Frames</a></li>
<li><a href="ActivitiEventDispatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
