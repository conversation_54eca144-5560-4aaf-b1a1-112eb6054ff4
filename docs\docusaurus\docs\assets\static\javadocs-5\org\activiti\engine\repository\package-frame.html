<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine.repository (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../org/activiti/engine/repository/package-summary.html" target="classFrame">org.activiti.engine.repository</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="Deployment.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">Deployment</span></a></li>
<li><a href="DeploymentBuilder.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">DeploymentBuilder</span></a></li>
<li><a href="DeploymentQuery.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">DeploymentQuery</span></a></li>
<li><a href="Model.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">Model</span></a></li>
<li><a href="ModelQuery.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">ModelQuery</span></a></li>
<li><a href="NativeDeploymentQuery.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">NativeDeploymentQuery</span></a></li>
<li><a href="NativeModelQuery.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">NativeModelQuery</span></a></li>
<li><a href="NativeProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">NativeProcessDefinitionQuery</span></a></li>
<li><a href="ProcessDefinition.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">ProcessDefinition</span></a></li>
<li><a href="ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository" target="classFrame"><span class="interfaceName">ProcessDefinitionQuery</span></a></li>
</ul>
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="DiagramEdge.html" title="class in org.activiti.engine.repository" target="classFrame">DiagramEdge</a></li>
<li><a href="DiagramEdgeWaypoint.html" title="class in org.activiti.engine.repository" target="classFrame">DiagramEdgeWaypoint</a></li>
<li><a href="DiagramElement.html" title="class in org.activiti.engine.repository" target="classFrame">DiagramElement</a></li>
<li><a href="DiagramLayout.html" title="class in org.activiti.engine.repository" target="classFrame">DiagramLayout</a></li>
<li><a href="DiagramNode.html" title="class in org.activiti.engine.repository" target="classFrame">DiagramNode</a></li>
</ul>
</div>
</body>
</html>
