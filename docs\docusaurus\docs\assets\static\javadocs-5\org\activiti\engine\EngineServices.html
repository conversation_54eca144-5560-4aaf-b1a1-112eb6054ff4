<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>EngineServices (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="EngineServices (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/EngineServices.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/DynamicBpmnService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/EngineServices.html" target="_top">Frames</a></li>
<li><a href="EngineServices.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Interface EngineServices" class="title">Interface EngineServices</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">EngineServices</span></pre>
<div class="block">Interface implemented by all classes that expose the Activiti services.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/DynamicBpmnService.html" title="interface in org.activiti.engine">DynamicBpmnService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/EngineServices.html#getDynamicBpmnService--">getDynamicBpmnService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine">FormService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/EngineServices.html#getFormService--">getFormService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/EngineServices.html#getHistoryService--">getHistoryService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/EngineServices.html#getIdentityService--">getIdentityService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/EngineServices.html#getManagementService--">getManagementService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/EngineServices.html#getProcessEngineConfiguration--">getProcessEngineConfiguration</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/EngineServices.html#getRepositoryService--">getRepositoryService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/EngineServices.html#getRuntimeService--">getRuntimeService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/EngineServices.html#getTaskService--">getTaskService</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getRepositoryService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRepositoryService</h4>
<pre><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a>&nbsp;getRepositoryService()</pre>
</li>
</ul>
<a name="getRuntimeService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuntimeService</h4>
<pre><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a>&nbsp;getRuntimeService()</pre>
</li>
</ul>
<a name="getFormService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFormService</h4>
<pre><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine">FormService</a>&nbsp;getFormService()</pre>
</li>
</ul>
<a name="getTaskService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskService</h4>
<pre><a href="../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a>&nbsp;getTaskService()</pre>
</li>
</ul>
<a name="getHistoryService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHistoryService</h4>
<pre><a href="../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a>&nbsp;getHistoryService()</pre>
</li>
</ul>
<a name="getIdentityService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIdentityService</h4>
<pre><a href="../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a>&nbsp;getIdentityService()</pre>
</li>
</ul>
<a name="getManagementService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManagementService</h4>
<pre><a href="../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a>&nbsp;getManagementService()</pre>
</li>
</ul>
<a name="getDynamicBpmnService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDynamicBpmnService</h4>
<pre><a href="../../../org/activiti/engine/DynamicBpmnService.html" title="interface in org.activiti.engine">DynamicBpmnService</a>&nbsp;getDynamicBpmnService()</pre>
</li>
</ul>
<a name="getProcessEngineConfiguration--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getProcessEngineConfiguration</h4>
<pre><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a>&nbsp;getProcessEngineConfiguration()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/EngineServices.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/DynamicBpmnService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/EngineServices.html" target="_top">Frames</a></li>
<li><a href="EngineServices.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
