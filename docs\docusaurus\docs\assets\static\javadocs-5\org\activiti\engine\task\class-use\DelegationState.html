<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:26 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class org.activiti.engine.task.DelegationState (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class org.activiti.engine.task.DelegationState (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/task/class-use/DelegationState.html" target="_top">Frames</a></li>
<li><a href="DelegationState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class org.activiti.engine.task.DelegationState" class="title">Uses of Class<br>org.activiti.engine.task.DelegationState</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine.delegate">org.activiti.engine.delegate</a></td>
<td class="colLast">
<div class="block">Interfaces used to include Java code in a process as the behavior of an activity 
    or as a listener to process events with <a href="../../../../../org/activiti/engine/delegate/JavaDelegate.html" title="interface in org.activiti.engine.delegate"><code>JavaDelegate</code></a>s.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.activiti.engine.task">org.activiti.engine.task</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="../../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine.delegate">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a> in <a href="../../../../../org/activiti/engine/delegate/package-summary.html">org.activiti.engine.delegate</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/delegate/package-summary.html">org.activiti.engine.delegate</a> that return <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></code></td>
<td class="colLast"><span class="typeNameLabel">DelegateTask.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/DelegateTask.html#getDelegationState--">getDelegationState</a></span>()</code>
<div class="block">The current <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a> for this task.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.task">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a> in <a href="../../../../../org/activiti/engine/task/package-summary.html">org.activiti.engine.task</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/task/package-summary.html">org.activiti.engine.task</a> that return <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></code></td>
<td class="colLast"><span class="typeNameLabel">Task.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/task/Task.html#getDelegationState--">getDelegationState</a></span>()</code>
<div class="block">The current <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a> for this task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></code></td>
<td class="colLast"><span class="typeNameLabel">DelegationState.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/task/DelegationState.html#valueOf-java.lang.String-">valueOf</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">DelegationState.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/task/DelegationState.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/task/package-summary.html">org.activiti.engine.task</a> with parameters of type <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Task.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/task/Task.html#setDelegationState-org.activiti.engine.task.DelegationState-">setDelegationState</a></span>(<a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a>&nbsp;delegationState)</code>
<div class="block">The current <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a> for this task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">TaskQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/task/TaskQuery.html#taskDelegationState-org.activiti.engine.task.DelegationState-">taskDelegationState</a></span>(<a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a>&nbsp;delegationState)</code>
<div class="block">Only select tasks with the given <a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/task/class-use/DelegationState.html" target="_top">Frames</a></li>
<li><a href="DelegationState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
