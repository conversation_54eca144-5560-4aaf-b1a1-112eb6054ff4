# Blockchain Audit Trail Setup for GRCOS Flowable

## Overview

This guide provides comprehensive instructions for implementing blockchain-based audit trails in the GRCOS Flowable integration. The blockchain audit system ensures immutable recording of all workflow activities, compliance decisions, and critical system events using Hyperledger Fabric as the underlying blockchain platform.

## Blockchain Architecture

### Hyperledger Fabric Network Setup

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        GRCOS Blockchain Audit Architecture                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        Application Layer                                    │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │  Flowable   │  │   GRCOS     │  │   Audit     │  │  Compliance │       │ │
│  │  │  Workflows  │  │  Modules    │  │  Service    │  │   Monitor   │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                     Blockchain Gateway Layer                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │   Fabric    │  │   Smart     │  │   Event     │  │   Query     │       │ │
│  │  │   Gateway   │  │ Contracts   │  │ Listeners   │  │  Service    │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                    Hyperledger Fabric Network                              │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │    Peer     │  │   Orderer   │  │     CA      │  │   Channel   │       │ │
│  │  │    Nodes    │  │   Nodes     │  │   Service   │  │   Ledger    │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Hyperledger Fabric Configuration

### Network Configuration

#### docker-compose-blockchain.yml
```yaml
version: '3.8'

networks:
  grcos-blockchain:
    driver: bridge

services:
  # Certificate Authority
  ca.grcos.com:
    image: hyperledger/fabric-ca:1.5.5
    environment:
      - FABRIC_CA_HOME=/etc/hyperledger/fabric-ca-server
      - FABRIC_CA_SERVER_CA_NAME=ca.grcos.com
      - FABRIC_CA_SERVER_TLS_ENABLED=true
      - FABRIC_CA_SERVER_PORT=7054
    ports:
      - "7054:7054"
    command: sh -c 'fabric-ca-server start -b admin:adminpw -d'
    volumes:
      - ./crypto-config/peerOrganizations/grcos.com/ca/:/etc/hyperledger/fabric-ca-server-config
    container_name: ca.grcos.com
    networks:
      - grcos-blockchain

  # Orderer Node
  orderer.grcos.com:
    image: hyperledger/fabric-orderer:2.4.7
    environment:
      - FABRIC_LOGGING_SPEC=INFO
      - ORDERER_GENERAL_LISTENADDRESS=0.0.0.0
      - ORDERER_GENERAL_LISTENPORT=7050
      - ORDERER_GENERAL_GENESISMETHOD=file
      - ORDERER_GENERAL_GENESISFILE=/var/hyperledger/orderer/orderer.genesis.block
      - ORDERER_GENERAL_LOCALMSPID=OrdererMSP
      - ORDERER_GENERAL_LOCALMSPDIR=/var/hyperledger/orderer/msp
      - ORDERER_GENERAL_TLS_ENABLED=true
      - ORDERER_GENERAL_TLS_PRIVATEKEY=/var/hyperledger/orderer/tls/server.key
      - ORDERER_GENERAL_TLS_CERTIFICATE=/var/hyperledger/orderer/tls/server.crt
      - ORDERER_GENERAL_TLS_ROOTCAS=[/var/hyperledger/orderer/tls/ca.crt]
      - ORDERER_GENERAL_CLUSTER_CLIENTCERTIFICATE=/var/hyperledger/orderer/tls/server.crt
      - ORDERER_GENERAL_CLUSTER_CLIENTPRIVATEKEY=/var/hyperledger/orderer/tls/server.key
      - ORDERER_GENERAL_CLUSTER_ROOTCAS=[/var/hyperledger/orderer/tls/ca.crt]
    working_dir: /opt/gopath/src/github.com/hyperledger/fabric
    command: orderer
    volumes:
      - ./channel-artifacts/genesis.block:/var/hyperledger/orderer/orderer.genesis.block
      - ./crypto-config/ordererOrganizations/grcos.com/orderers/orderer.grcos.com/msp:/var/hyperledger/orderer/msp
      - ./crypto-config/ordererOrganizations/grcos.com/orderers/orderer.grcos.com/tls/:/var/hyperledger/orderer/tls
    ports:
      - 7050:7050
    networks:
      - grcos-blockchain

  # Peer Node
  peer0.grcos.com:
    image: hyperledger/fabric-peer:2.4.7
    environment:
      - CORE_VM_ENDPOINT=unix:///host/var/run/docker.sock
      - CORE_VM_DOCKER_HOSTCONFIG_NETWORKMODE=grcos-blockchain
      - FABRIC_LOGGING_SPEC=INFO
      - CORE_PEER_TLS_ENABLED=true
      - CORE_PEER_PROFILE_ENABLED=true
      - CORE_PEER_TLS_CERT_FILE=/etc/hyperledger/fabric/tls/server.crt
      - CORE_PEER_TLS_KEY_FILE=/etc/hyperledger/fabric/tls/server.key
      - CORE_PEER_TLS_ROOTCERT_FILE=/etc/hyperledger/fabric/tls/ca.crt
      - CORE_PEER_ID=peer0.grcos.com
      - CORE_PEER_ADDRESS=peer0.grcos.com:7051
      - CORE_PEER_LISTENADDRESS=0.0.0.0:7051
      - CORE_PEER_CHAINCODEADDRESS=peer0.grcos.com:7052
      - CORE_PEER_CHAINCODELISTENADDRESS=0.0.0.0:7052
      - CORE_PEER_GOSSIP_BOOTSTRAP=peer0.grcos.com:7051
      - CORE_PEER_GOSSIP_EXTERNALENDPOINT=peer0.grcos.com:7051
      - CORE_PEER_LOCALMSPID=GRCOSMsp
    working_dir: /opt/gopath/src/github.com/hyperledger/fabric/peer
    command: peer node start
    volumes:
      - /var/run/:/host/var/run/
      - ./crypto-config/peerOrganizations/grcos.com/peers/peer0.grcos.com/msp:/etc/hyperledger/fabric/msp
      - ./crypto-config/peerOrganizations/grcos.com/peers/peer0.grcos.com/tls:/etc/hyperledger/fabric/tls
      - peer0.grcos.com:/var/hyperledger/production
    ports:
      - 7051:7051
    networks:
      - grcos-blockchain

volumes:
  peer0.grcos.com:
```

### Smart Contract Implementation

#### WorkflowAuditContract.java
```java
@Contract(
    name = "WorkflowAuditContract",
    info = @Info(
        title = "GRCOS Workflow Audit Contract",
        description = "Smart contract for recording immutable workflow audit trails",
        version = "1.0.0"
    )
)
@Default
public class WorkflowAuditContract implements ContractInterface {
    
    private final Genson genson = new Genson();
    
    @Transaction(intent = Transaction.TYPE.SUBMIT)
    public void recordWorkflowEvent(Context ctx, String eventId, String eventData) {
        ChaincodeStub stub = ctx.getStub();
        
        try {
            // Parse event data
            WorkflowAuditEvent event = genson.deserialize(eventData, WorkflowAuditEvent.class);
            
            // Validate event
            validateWorkflowEvent(event);
            
            // Add blockchain metadata
            event.setBlockchainTimestamp(Instant.now());
            event.setTransactionId(stub.getTxId());
            event.setChannelId(stub.getChannelId());
            
            // Store event on ledger
            String eventKey = createEventKey(event);
            stub.putStringState(eventKey, genson.serialize(event));
            
            // Emit blockchain event
            stub.setEvent("WorkflowEventRecorded", genson.serialize(event).getBytes());
            
        } catch (Exception e) {
            throw new ChaincodeException("Failed to record workflow event: " + e.getMessage());
        }
    }
    
    @Transaction(intent = Transaction.TYPE.SUBMIT)
    public void recordComplianceDecision(Context ctx, String decisionId, String decisionData) {
        ChaincodeStub stub = ctx.getStub();
        
        try {
            ComplianceDecision decision = genson.deserialize(decisionData, ComplianceDecision.class);
            
            // Validate decision
            validateComplianceDecision(decision);
            
            // Add immutability proof
            decision.setImmutabilityHash(calculateHash(decision));
            decision.setBlockchainTimestamp(Instant.now());
            decision.setTransactionId(stub.getTxId());
            
            // Store decision
            String decisionKey = createDecisionKey(decision);
            stub.putStringState(decisionKey, genson.serialize(decision));
            
            // Update compliance metrics
            updateComplianceMetrics(ctx, decision);
            
            // Emit event
            stub.setEvent("ComplianceDecisionRecorded", genson.serialize(decision).getBytes());
            
        } catch (Exception e) {
            throw new ChaincodeException("Failed to record compliance decision: " + e.getMessage());
        }
    }
    
    @Transaction(intent = Transaction.TYPE.EVALUATE)
    public String queryWorkflowEvents(Context ctx, String processInstanceId, 
                                    String startTime, String endTime) {
        ChaincodeStub stub = ctx.getStub();
        
        try {
            // Build query
            String query = buildWorkflowEventQuery(processInstanceId, startTime, endTime);
            
            // Execute query
            QueryResultsIterator<KeyValue> results = stub.getQueryResult(query);
            
            // Process results
            List<WorkflowAuditEvent> events = new ArrayList<>();
            for (KeyValue result : results) {
                WorkflowAuditEvent event = genson.deserialize(
                    result.getStringValue(), WorkflowAuditEvent.class);
                events.add(event);
            }
            
            return genson.serialize(events);
            
        } catch (Exception e) {
            throw new ChaincodeException("Failed to query workflow events: " + e.getMessage());
        }
    }
    
    @Transaction(intent = Transaction.TYPE.EVALUATE)
    public String verifyAuditTrail(Context ctx, String processInstanceId) {
        ChaincodeStub stub = ctx.getStub();
        
        try {
            // Get all events for process
            List<WorkflowAuditEvent> events = getProcessEvents(ctx, processInstanceId);
            
            // Verify event chain integrity
            AuditTrailVerification verification = verifyEventChain(events);
            
            // Check for tampering
            TamperingCheck tamperingCheck = checkForTampering(events);
            
            // Generate verification report
            AuditVerificationReport report = AuditVerificationReport.builder()
                .processInstanceId(processInstanceId)
                .eventCount(events.size())
                .chainIntegrity(verification.isValid())
                .tamperingDetected(tamperingCheck.isTampered())
                .verificationTimestamp(Instant.now())
                .verificationDetails(verification.getDetails())
                .build();
            
            return genson.serialize(report);
            
        } catch (Exception e) {
            throw new ChaincodeException("Failed to verify audit trail: " + e.getMessage());
        }
    }
    
    private void validateWorkflowEvent(WorkflowAuditEvent event) {
        if (event.getProcessInstanceId() == null || event.getProcessInstanceId().isEmpty()) {
            throw new ChaincodeException("Process instance ID is required");
        }
        
        if (event.getEventType() == null) {
            throw new ChaincodeException("Event type is required");
        }
        
        if (event.getTimestamp() == null) {
            throw new ChaincodeException("Event timestamp is required");
        }
        
        // Additional validation based on event type
        switch (event.getEventType()) {
            case PROCESS_STARTED:
                validateProcessStartedEvent(event);
                break;
            case TASK_COMPLETED:
                validateTaskCompletedEvent(event);
                break;
            case DECISION_MADE:
                validateDecisionMadeEvent(event);
                break;
        }
    }
    
    private String createEventKey(WorkflowAuditEvent event) {
        return String.format("EVENT_%s_%s_%d", 
            event.getProcessInstanceId(),
            event.getEventType().name(),
            event.getTimestamp().toEpochMilli());
    }
    
    private String calculateHash(Object data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(genson.serialize(data).getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new ChaincodeException("Failed to calculate hash: " + e.getMessage());
        }
    }
    
    private String buildWorkflowEventQuery(String processInstanceId, String startTime, String endTime) {
        return String.format(
            "{\"selector\":{" +
            "\"processInstanceId\":\"%s\"," +
            "\"timestamp\":{\"$gte\":\"%s\",\"$lte\":\"%s\"}" +
            "},\"sort\":[{\"timestamp\":\"asc\"}]}",
            processInstanceId, startTime, endTime
        );
    }
}
```

## Blockchain Integration Service

### Fabric Gateway Service

#### BlockchainAuditService.java
```java
@Service
@Transactional
public class BlockchainAuditService {
    
    @Autowired
    private FabricGatewayConfig fabricConfig;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private Gateway gateway;
    private Network network;
    private Contract contract;
    
    @PostConstruct
    public void initializeBlockchainConnection() {
        try {
            // Build gateway connection
            Gateway.Builder builder = Gateway.createBuilder();
            
            // Configure identity
            Path walletPath = Paths.get(fabricConfig.getWalletPath());
            Wallet wallet = Wallets.newFileSystemWallet(walletPath);
            
            // Load network configuration
            Path networkConfigPath = Paths.get(fabricConfig.getNetworkConfigPath());
            
            gateway = builder
                .identity(wallet, fabricConfig.getUserName())
                .networkConfig(networkConfigPath)
                .discovery(true)
                .connect();
            
            // Get network and contract
            network = gateway.getNetwork(fabricConfig.getChannelName());
            contract = network.getContract(fabricConfig.getContractName());
            
            logger.info("Blockchain connection initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize blockchain connection", e);
            throw new BlockchainInitializationException("Blockchain initialization failed", e);
        }
    }
    
    @PreDestroy
    public void closeBlockchainConnection() {
        if (gateway != null) {
            gateway.close();
        }
    }
    
    public void recordWorkflowEvent(WorkflowAuditEvent event) {
        try {
            String eventData = objectMapper.writeValueAsString(event);
            
            // Submit transaction to blockchain
            byte[] result = contract.submitTransaction(
                "recordWorkflowEvent",
                event.getEventId(),
                eventData
            );
            
            logger.debug("Workflow event recorded on blockchain: {}", event.getEventId());
            
        } catch (Exception e) {
            logger.error("Failed to record workflow event on blockchain", e);
            throw new BlockchainRecordingException("Failed to record workflow event", e);
        }
    }
    
    public void recordComplianceDecision(ComplianceDecision decision) {
        try {
            String decisionData = objectMapper.writeValueAsString(decision);
            
            // Submit transaction to blockchain
            byte[] result = contract.submitTransaction(
                "recordComplianceDecision",
                decision.getDecisionId(),
                decisionData
            );
            
            logger.debug("Compliance decision recorded on blockchain: {}", decision.getDecisionId());
            
        } catch (Exception e) {
            logger.error("Failed to record compliance decision on blockchain", e);
            throw new BlockchainRecordingException("Failed to record compliance decision", e);
        }
    }
    
    public List<WorkflowAuditEvent> queryWorkflowEvents(String processInstanceId, 
                                                       Instant startTime, Instant endTime) {
        try {
            byte[] result = contract.evaluateTransaction(
                "queryWorkflowEvents",
                processInstanceId,
                startTime.toString(),
                endTime.toString()
            );
            
            String resultJson = new String(result);
            return objectMapper.readValue(resultJson, 
                new TypeReference<List<WorkflowAuditEvent>>() {});
            
        } catch (Exception e) {
            logger.error("Failed to query workflow events from blockchain", e);
            throw new BlockchainQueryException("Failed to query workflow events", e);
        }
    }
    
    public AuditVerificationReport verifyAuditTrail(String processInstanceId) {
        try {
            byte[] result = contract.evaluateTransaction(
                "verifyAuditTrail",
                processInstanceId
            );
            
            String resultJson = new String(result);
            return objectMapper.readValue(resultJson, AuditVerificationReport.class);
            
        } catch (Exception e) {
            logger.error("Failed to verify audit trail on blockchain", e);
            throw new BlockchainVerificationException("Failed to verify audit trail", e);
        }
    }
    
    @Async
    public CompletableFuture<Void> recordEventAsync(WorkflowAuditEvent event) {
        return CompletableFuture.runAsync(() -> recordWorkflowEvent(event));
    }
    
    @Async
    public CompletableFuture<Void> recordDecisionAsync(ComplianceDecision decision) {
        return CompletableFuture.runAsync(() -> recordComplianceDecision(decision));
    }
}
```

### Blockchain Event Listener

#### BlockchainAuditEventListener.java
```java
@Component
public class BlockchainAuditEventListener implements FlowableEventListener {
    
    @Autowired
    private BlockchainAuditService blockchainAuditService;
    
    @Autowired
    private AuditEventFactory auditEventFactory;
    
    @Override
    public void onEvent(FlowableEvent event) {
        try {
            // Create audit event from Flowable event
            WorkflowAuditEvent auditEvent = auditEventFactory.createAuditEvent(event);
            
            if (auditEvent != null && shouldRecordEvent(event)) {
                // Record event on blockchain asynchronously
                blockchainAuditService.recordEventAsync(auditEvent);
            }
            
        } catch (Exception e) {
            logger.error("Failed to process blockchain audit event", e);
            // Don't fail the workflow if blockchain recording fails
        }
    }
    
    private boolean shouldRecordEvent(FlowableEvent event) {
        // Define which events should be recorded on blockchain
        return switch (event.getType()) {
            case PROCESS_STARTED,
                 PROCESS_COMPLETED,
                 PROCESS_CANCELLED,
                 TASK_CREATED,
                 TASK_COMPLETED,
                 TASK_ASSIGNED,
                 VARIABLE_CREATED,
                 VARIABLE_UPDATED -> true;
            default -> false;
        };
    }
    
    @Override
    public boolean isFailOnException() {
        return false; // Don't fail workflow if blockchain recording fails
    }
}
```

### Audit Event Factory

#### AuditEventFactory.java
```java
@Component
public class AuditEventFactory {
    
    public WorkflowAuditEvent createAuditEvent(FlowableEvent flowableEvent) {
        return switch (flowableEvent.getType()) {
            case PROCESS_STARTED -> createProcessStartedEvent((FlowableProcessEvent) flowableEvent);
            case PROCESS_COMPLETED -> createProcessCompletedEvent((FlowableProcessEvent) flowableEvent);
            case TASK_CREATED -> createTaskCreatedEvent((FlowableEntityEvent) flowableEvent);
            case TASK_COMPLETED -> createTaskCompletedEvent((FlowableEntityEvent) flowableEvent);
            case VARIABLE_CREATED, VARIABLE_UPDATED -> createVariableEvent((FlowableVariableEvent) flowableEvent);
            default -> null;
        };
    }
    
    private WorkflowAuditEvent createProcessStartedEvent(FlowableProcessEvent event) {
        return WorkflowAuditEvent.builder()
            .eventId(UUID.randomUUID().toString())
            .processInstanceId(event.getProcessInstanceId())
            .processDefinitionId(event.getProcessDefinitionId())
            .eventType(AuditEventType.PROCESS_STARTED)
            .timestamp(Instant.now())
            .userId(getCurrentUserId())
            .eventData(Map.of(
                "processDefinitionKey", event.getProcessDefinitionId(),
                "businessKey", getBusinessKey(event),
                "startVariables", getProcessVariables(event)
            ))
            .build();
    }
    
    private WorkflowAuditEvent createTaskCompletedEvent(FlowableEntityEvent event) {
        Task task = (Task) event.getEntity();
        
        return WorkflowAuditEvent.builder()
            .eventId(UUID.randomUUID().toString())
            .processInstanceId(event.getProcessInstanceId())
            .processDefinitionId(event.getProcessDefinitionId())
            .eventType(AuditEventType.TASK_COMPLETED)
            .timestamp(Instant.now())
            .userId(getCurrentUserId())
            .eventData(Map.of(
                "taskId", task.getId(),
                "taskName", task.getName(),
                "assignee", task.getAssignee(),
                "duration", calculateTaskDuration(task),
                "formData", getTaskFormData(task)
            ))
            .build();
    }
    
    private String getCurrentUserId() {
        // Get current user from security context
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return auth != null ? auth.getName() : "system";
    }
}
```

This blockchain audit setup provides comprehensive immutable recording of all workflow activities and compliance decisions, ensuring complete auditability and tamper-proof compliance evidence.
