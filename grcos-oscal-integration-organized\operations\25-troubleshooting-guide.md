# GRCOS OSCAL Integration Troubleshooting Guide

## Overview

This guide provides comprehensive troubleshooting procedures for common issues encountered in GRCOS OSCAL integration. It covers diagnostic procedures, resolution steps, and preventive measures for maintaining optimal system performance.

## Common Issues and Resolutions

### OSCAL Document Validation Errors

#### Issue: Schema Validation Failures
```
Error: OSCAL document fails schema validation
Symptoms: 
- Document creation/update operations fail
- Validation error messages in logs
- API returns 400 Bad Request with validation details
```

**Diagnostic Steps:**
```bash
# Check OSCAL schema version compatibility
kubectl logs -n grcos-services deployment/grcos-oscal-service | grep "schema"

# Validate document manually
curl -X POST https://api.grcos.example.com/api/v1/oscal/validate \
  -H "Content-Type: application/json" \
  -d @problematic-document.json

# Check schema files
kubectl exec -n grcos-services grcos-oscal-service-0 -- ls -la /etc/oscal/schemas/
```

**Resolution Steps:**
1. **Update OSCAL Version**: Ensure document uses correct OSCAL version (1.1.3)
```json
{
  "metadata": {
    "oscal-version": "1.1.3"
  }
}
```

2. **Fix Required Fields**: Add missing required fields
```bash
# Common missing fields
- uuid (required for all documents)
- metadata.title
- metadata.version
- metadata.last-modified
```

3. **Validate UUID Format**: Ensure UUIDs are properly formatted
```python
import uuid
# Valid UUID format: 550e8400-e29b-41d4-a716-************
valid_uuid = str(uuid.uuid4())
```

#### Issue: Business Rule Validation Failures
```
Error: Document violates GRCOS business rules
Symptoms:
- Schema validation passes but business validation fails
- Custom validation error messages
```

**Resolution Steps:**
1. **Check Control References**: Ensure all control-id references exist
2. **Validate Component UUIDs**: Verify component references are valid
3. **Review Implementation Status**: Ensure status values are from allowed list

### Blockchain Integration Issues

#### Issue: Blockchain Registration Failures
```
Error: Failed to register OSCAL document on blockchain
Symptoms:
- Document saves locally but blockchain registration fails
- Blockchain transaction errors in logs
- Missing blockchain_hash in document metadata
```

**Diagnostic Steps:**
```bash
# Check blockchain network status
kubectl get pods -n grcos-blockchain
kubectl logs -n grcos-blockchain peer0-grcos-org-0

# Test blockchain connectivity
kubectl exec -n grcos-blockchain peer0-grcos-org-0 -- peer channel list

# Check chaincode status
kubectl exec -n grcos-blockchain peer0-grcos-org-0 -- peer chaincode list --installed
```

**Resolution Steps:**
1. **Restart Blockchain Services**:
```bash
kubectl rollout restart deployment/peer0-grcos-org -n grcos-blockchain
kubectl rollout restart deployment/peer1-grcos-org -n grcos-blockchain
kubectl rollout restart deployment/peer2-grcos-org -n grcos-blockchain
```

2. **Verify Network Configuration**:
```bash
# Check network configuration
kubectl get configmap blockchain-config -n grcos-services -o yaml

# Verify TLS certificates
kubectl get secret blockchain-secret -n grcos-services -o yaml
```

3. **Reinstall Chaincode** (if necessary):
```bash
# Package and install chaincode
peer lifecycle chaincode package oscal-compliance.tar.gz \
  --path ./chaincode/oscal-compliance \
  --lang golang \
  --label oscal-compliance_1.1

# Install on all peers
for i in {0..2}; do
  peer lifecycle chaincode install oscal-compliance.tar.gz \
    --peerAddresses peer${i}-grcos-org:7051
done
```

#### Issue: Blockchain Synchronization Problems
```
Error: Blockchain nodes out of sync
Symptoms:
- Inconsistent blockchain state across peers
- Transaction failures
- Block height differences between peers
```

**Diagnostic Steps:**
```bash
# Check block height on all peers
for i in {0..2}; do
  echo "Peer $i block height:"
  kubectl exec -n grcos-blockchain peer${i}-grcos-org-0 -- \
    peer channel getinfo -c compliance-channel
done

# Check peer logs for sync issues
kubectl logs -n grcos-blockchain peer0-grcos-org-0 | grep -i sync
```

**Resolution Steps:**
1. **Force Peer Synchronization**:
```bash
# Restart lagging peers
kubectl delete pod peer1-grcos-org-0 -n grcos-blockchain
kubectl delete pod peer2-grcos-org-0 -n grcos-blockchain
```

2. **Rebuild Peer from Snapshot** (if severely out of sync):
```bash
# Stop peer
kubectl scale deployment peer2-grcos-org --replicas=0 -n grcos-blockchain

# Clear peer data
kubectl exec -n grcos-blockchain peer0-grcos-org-0 -- rm -rf /var/hyperledger/production/ledgersData

# Restart peer
kubectl scale deployment peer2-grcos-org --replicas=1 -n grcos-blockchain
```

### AI Agent Communication Issues

#### Issue: Agent Communication Failures
```
Error: AI agents unable to communicate
Symptoms:
- Agent tasks timeout
- Missing agent responses
- Workflow orchestration failures
```

**Diagnostic Steps:**
```bash
# Check agent pod status
kubectl get pods -n grcos-services -l app=grcos-ai-agents

# Review agent logs
kubectl logs -n grcos-services deployment/grcos-ai-agents -c system-agent
kubectl logs -n grcos-services deployment/grcos-ai-agents -c compliance-agent

# Test message queue connectivity
kubectl exec -n grcos-services kafka-0 -- kafka-topics.sh --list --bootstrap-server localhost:9092
```

**Resolution Steps:**
1. **Restart Agent Services**:
```bash
kubectl rollout restart deployment/grcos-ai-agents -n grcos-services
```

2. **Check Message Queue Health**:
```bash
# Verify Kafka cluster
kubectl get pods -n grcos-services -l app=kafka

# Check topic configuration
kubectl exec -n grcos-services kafka-0 -- kafka-topics.sh \
  --describe --topic oscal-events --bootstrap-server localhost:9092
```

3. **Verify Agent Configuration**:
```bash
# Check agent configuration
kubectl get configmap ai-agent-config -n grcos-services -o yaml

# Validate CrewAI configuration
kubectl exec -n grcos-services grcos-ai-agents-0 -c system-agent -- \
  python -c "import yaml; print(yaml.safe_load(open('/etc/crewai/system-agent-config.yaml')))"
```

### Database Connectivity Issues

#### Issue: MongoDB Connection Failures
```
Error: Cannot connect to MongoDB
Symptoms:
- Database connection timeouts
- OSCAL document save/retrieve failures
- MongoDB authentication errors
```

**Diagnostic Steps:**
```bash
# Check MongoDB cluster status
kubectl get pods -n grcos-services -l app=mongodb

# Test database connectivity
kubectl exec -n grcos-services mongodb-cluster-0 -- \
  mongo --eval "db.adminCommand('ismaster')"

# Check database logs
kubectl logs -n grcos-services mongodb-cluster-0
```

**Resolution Steps:**
1. **Restart MongoDB Cluster**:
```bash
# Rolling restart of MongoDB cluster
kubectl rollout restart statefulset/mongodb-cluster -n grcos-services
```

2. **Verify Database Credentials**:
```bash
# Check database secret
kubectl get secret database-secret -n grcos-services -o yaml

# Test authentication
kubectl exec -n grcos-services mongodb-cluster-0 -- \
  mongo -u admin -p ${MONGODB_PASSWORD} --authenticationDatabase admin
```

3. **Check Storage Issues**:
```bash
# Verify persistent volume claims
kubectl get pvc -n grcos-services

# Check storage capacity
kubectl describe pvc mongodb-storage-mongodb-cluster-0 -n grcos-services
```

#### Issue: Redis Cache Problems
```
Error: Redis cache unavailable
Symptoms:
- Slow OSCAL document retrieval
- Cache miss errors in logs
- Performance degradation
```

**Diagnostic Steps:**
```bash
# Check Redis cluster status
kubectl get pods -n grcos-services -l app=redis

# Test Redis connectivity
kubectl exec -n grcos-services redis-cluster-0 -- redis-cli ping

# Check Redis memory usage
kubectl exec -n grcos-services redis-cluster-0 -- redis-cli info memory
```

**Resolution Steps:**
1. **Clear Redis Cache**:
```bash
kubectl exec -n grcos-services redis-cluster-0 -- redis-cli flushall
```

2. **Increase Redis Memory**:
```yaml
# Update Redis deployment
spec:
  template:
    spec:
      containers:
      - name: redis
        resources:
          limits:
            memory: "4Gi"
          requests:
            memory: "2Gi"
```

### Performance Issues

#### Issue: Slow OSCAL Document Processing
```
Error: OSCAL operations taking too long
Symptoms:
- API response times > 5 seconds
- Document validation timeouts
- User interface slowness
```

**Diagnostic Steps:**
```bash
# Check service resource usage
kubectl top pods -n grcos-services

# Monitor API response times
curl -w "@curl-format.txt" -o /dev/null -s https://api.grcos.example.com/api/v1/oscal/catalogs

# Check database query performance
kubectl exec -n grcos-services mongodb-cluster-0 -- \
  mongo grcos_oscal --eval "db.oscal_documents.getIndexes()"
```

**Resolution Steps:**
1. **Scale Services Horizontally**:
```bash
kubectl scale deployment grcos-oscal-service --replicas=5 -n grcos-services
```

2. **Optimize Database Indexes**:
```javascript
// Create performance indexes
db.oscal_documents.createIndex({"uuid": 1})
db.oscal_documents.createIndex({"document_type": 1, "metadata.last-modified": -1})
db.oscal_documents.createIndex({"content.catalog.groups.controls.id": 1})
```

3. **Increase Resource Limits**:
```yaml
resources:
  limits:
    cpu: "2000m"
    memory: "4Gi"
  requests:
    cpu: "1000m"
    memory: "2Gi"
```

## Monitoring and Alerting

### Health Check Scripts

#### Comprehensive Health Check
```bash
#!/bin/bash
# grcos-health-check.sh

echo "GRCOS OSCAL Integration Health Check"
echo "===================================="

# Check service availability
echo "1. Checking service availability..."
services=("grcos-oscal-service" "grcos-ai-agents" "mongodb-cluster" "redis-cluster")
for service in "${services[@]}"; do
  status=$(kubectl get pods -n grcos-services -l app=$service -o jsonpath='{.items[0].status.phase}')
  echo "  $service: $status"
done

# Check blockchain network
echo "2. Checking blockchain network..."
peer_count=$(kubectl get pods -n grcos-blockchain -l app=peer -o jsonpath='{.items[*].status.phase}' | wc -w)
echo "  Active peers: $peer_count"

# Test API endpoints
echo "3. Testing API endpoints..."
api_status=$(curl -s -o /dev/null -w "%{http_code}" https://api.grcos.example.com/health)
echo "  API health endpoint: $api_status"

# Check database connectivity
echo "4. Checking database connectivity..."
mongo_status=$(kubectl exec -n grcos-services mongodb-cluster-0 -- mongo --quiet --eval "db.adminCommand('ping').ok" 2>/dev/null)
echo "  MongoDB: $mongo_status"

redis_status=$(kubectl exec -n grcos-services redis-cluster-0 -- redis-cli ping 2>/dev/null)
echo "  Redis: $redis_status"

echo "Health check complete."
```

### Log Analysis Tools

#### Log Aggregation Script
```bash
#!/bin/bash
# collect-logs.sh

timestamp=$(date +%Y%m%d_%H%M%S)
log_dir="grcos_logs_$timestamp"
mkdir -p $log_dir

echo "Collecting GRCOS logs..."

# Collect service logs
kubectl logs -n grcos-services deployment/grcos-oscal-service > $log_dir/oscal-service.log
kubectl logs -n grcos-services deployment/grcos-ai-agents > $log_dir/ai-agents.log

# Collect blockchain logs
kubectl logs -n grcos-blockchain peer0-grcos-org-0 > $log_dir/blockchain-peer0.log

# Collect database logs
kubectl logs -n grcos-services mongodb-cluster-0 > $log_dir/mongodb.log
kubectl logs -n grcos-services redis-cluster-0 > $log_dir/redis.log

# Create archive
tar -czf grcos_logs_$timestamp.tar.gz $log_dir/
echo "Logs collected in grcos_logs_$timestamp.tar.gz"
```

## Emergency Procedures

### Service Recovery

#### Complete System Recovery
```bash
#!/bin/bash
# emergency-recovery.sh

echo "GRCOS Emergency Recovery Procedure"
echo "================================="

# 1. Stop all services
kubectl scale deployment grcos-oscal-service --replicas=0 -n grcos-services
kubectl scale deployment grcos-ai-agents --replicas=0 -n grcos-services

# 2. Backup current state
kubectl exec -n grcos-services mongodb-cluster-0 -- mongodump --out /tmp/backup

# 3. Restart infrastructure services
kubectl rollout restart statefulset/mongodb-cluster -n grcos-services
kubectl rollout restart deployment/redis-cluster -n grcos-services

# 4. Wait for infrastructure to be ready
kubectl wait --for=condition=ready pod -l app=mongodb -n grcos-services --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n grcos-services --timeout=300s

# 5. Restart application services
kubectl scale deployment grcos-oscal-service --replicas=3 -n grcos-services
kubectl scale deployment grcos-ai-agents --replicas=2 -n grcos-services

# 6. Verify recovery
./grcos-health-check.sh

echo "Emergency recovery complete."
```

### Data Recovery Procedures

#### OSCAL Document Recovery
```bash
#!/bin/bash
# recover-oscal-documents.sh

# Recover from blockchain if database is corrupted
echo "Recovering OSCAL documents from blockchain..."

# Get list of registered documents
kubectl exec -n grcos-blockchain peer0-grcos-org-0 -- \
  peer chaincode query -C compliance-channel -n oscal-compliance \
  -c '{"function":"GetAllDocuments","Args":[]}' > blockchain_documents.json

# Restore documents to database
python3 restore_from_blockchain.py blockchain_documents.json

echo "Document recovery complete."
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Support Team
