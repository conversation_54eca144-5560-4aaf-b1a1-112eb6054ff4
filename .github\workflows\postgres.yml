name: Flowable Postgres Build

on:
  push:
    branches:
      - main
      - 'flowable-release-*'

env:
  MAVEN_ARGS: >-
    -Dmaven.javadoc.skip=true
    -B -V --no-transfer-progress
    -Dhttp.keepAlive=false -Dmaven.wagon.http.pool=false -Dmaven.wagon.httpconnectionManager.ttlSeconds=120

jobs:
  test_postgres:
    name: Postgres ${{ matrix.postgres }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        postgres: [14, 15, 16, 17]
    services:
      postgres:
        image: postgres:${{ matrix.postgres }}
        env:
          POSTGRES_PASSWORD: flowable
          POSTGRES_USER: flowable
        ports:
          - 5432:5432
        # needed because the postgres container does not provide a health check
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5 --tmpfs /var/lib/postgresql/data:rw
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 17
      - name: Test
        # use postgres for the host here because we have specified a container for the job.
        # If we were running the job on the VM this would be localhost
        # '>-' is a special YAML syntax and means that new lines would be replaced with spaces
        # and new lines from the end would be removed
        run: >-
          ./mvnw clean install
          ${MAVEN_ARGS}
          -PcleanDb,postgresql,distro
          -P'!include-spring-boot-samples'
          -Djdbc.url=***************************:${{ job.services.postgres.ports[5432] }}/flowable
          -Djdbc.username=flowable
          -Djdbc.password=flowable
          -Djdbc.driver=org.postgresql.Driver
          -Dspring.datasource.url=***************************:${{ job.services.postgres.ports[5432] }}/flowable
          -Dspring.datasource.username=flowable
          -Dspring.datasource.password=flowable
          -Dmaven.test.redirectTestOutputToFile=false
