<html>
<head>
<title>Flowable DOCS</title>
<script type="text/javascript">
// Closure-wrapped for security.
(function () {
    var anchorMap = {
"_automatic_resource_deployment": "../dmn/ch04-Spring#automatic-resource-deployment",
"_configuration": "../dmn/ch02-Configuration#configuration",
"_configuration_2": "../dmn/ch07-REST#configuration",
"_create_a_new_dmn_deployment": "../dmn/ch07-REST#create-a-new-dmn-deployment",
"_creating_a_dmn_definition": "../dmn/ch06-DMN-Introduction#creating-a-dmn-definition",
"_creating_a_single_app": "../dmn/ch05-Deployment#creating-a-single-app",
"_custom_flowable_function_delegates": "../dmn/ch02-Configuration#custom-flowable-function-delegates",
"_custom_properties": "../dmn/ch02-Configuration#custom-properties",
"_decision_tables": "../dmn/ch07-REST#decision-tables",
"_delete_a_dmn_deployment": "../dmn/ch07-REST#delete-a-dmn-deployment",
"_deploying_programmatically": "../dmn/ch05-Deployment#deploying-programmatically",
"_deployment": "../dmn/ch07-REST#deployment",
"_dmn_11_xml": "../dmn/ch06-DMN-Introduction#dmn-11-xml",
"_dmn_definitions_decisions_and_decision_tables": "../dmn/ch05-Deployment#dmn-definitions-decisions-and-decision-tables",
"_dmn_definitions": "../dmn/ch05-Deployment#dmn-definitions",
"_dmn_rule_service": "../dmn/ch07-REST#dmn-rule-service",
"_dmnenginefactorybean": "../dmn/ch04-Spring#dmnenginefactorybean",
"_download": "../oss-introduction#download",
"_engine": "../dmn/ch07-REST#engine",
"_error_response_body": "../dmn/ch07-REST#error-response-body",
"_exception_strategy": "../dmn/ch03-API#exception-strategy",
"_execute_a_decision": "../dmn/ch07-REST#execute-a-decision",
"_execute_a_single_result_decision": "../dmn/ch07-REST#execute-a-single-result-decision",
"_experimental_features": "../oss-introduction#experimental-features",
"_general_flowable_rest_principles": "../dmn/ch07-REST#general-flowable-rest-principles",
"_get_a_decision_table_dmn_model": "../dmn/ch07-REST#get-a-decision-table-dmn-model",
"_get_a_decision_table_resource_content": "../dmn/ch07-REST#get-a-decision-table-resource-content",
"_get_a_decision_table": "../dmn/ch07-REST#get-a-decision-table",
"_get_a_dmn_deployment_resource_content": "../dmn/ch07-REST#get-a-dmn-deployment-resource-content",
"_get_a_dmn_deployment": "../dmn/ch07-REST#get-a-dmn-deployment",
"_get_dmn_engine_info": "../dmn/ch07-REST#get-dmn-engine-info",
"_hit_policy": "../dmn/ch06-DMN-Introduction#hit-policy",
"_ide": "../oss-introduction#ide",
"_input_and_output_expressions": "../dmn/ch06-DMN-Introduction#input-and-output-expressions",
"_installation_and_authentication": "../dmn/ch07-REST#installation-and-authentication",
"_internal_implementation_classes": "../oss-introduction#internal-implementation-classes",
"_introduction": "../oss-introduction",
"_java_classes": "../dmn/ch05-Deployment#java-classes",
"_jdk_8": "../oss-introduction#jdk-8",
"_jndi_properties": "../dmn/ch02-Configuration#jndi-properties",
"_json_body_parameters": "../dmn/ch07-REST#json-body-parameters",
"_json_query_variable_format": "../dmn/ch07-REST#json-query-variable-format",
"_license": "../oss-introduction#license",
"_list_of_decision_tables": "../dmn/ch07-REST#list-of-decision-tables",
"_list_of_dmn_deployments": "../dmn/ch07-REST#list-of-dmn-deployments",
"_methods_and_return_codes": "../dmn/ch07-REST#methods-and-return-codes",
"_paging_and_sorting": "../dmn/ch07-REST#paging-and-sorting",
"_reporting_problems": "../oss-introduction#reporting-problems",
"_request_parameters": "../dmn/ch07-REST#request-parameters",
"_required_software": "../oss-introduction#required-software",
"_rest_url_query_parameters": "../dmn/ch07-REST#rest-url-query-parameters",
"_rules": "../dmn/ch06-DMN-Introduction#rules",
"_sources": "../oss-introduction#sources",
"_url_fragments": "../dmn/ch07-REST#url-fragments",
"_usage_in_tomcat": "../dmn/ch07-REST#usage-in-tomcat",
"_use_in_a_bpmn20_process": "../dmn/ch06-DMN-Introduction#use-in-a-bpmn20-process",
"_variable_representation": "../dmn/ch07-REST#variable-representation",
"_versioning_strategy": "../oss-introduction#versioning-strategy",
"apiEngine": "../dmn/ch03-API#the-dmn-engine-api-and-services",
"apiProcessEngineInWebApp": "../dmn/ch03-API#the-dmn-engine-in-a-web-application",
"apiUnitTesting": "../dmn/ch03-API#unit-testing",
"bpmn20": "../dmn/ch06-DMN-Introduction#dmn-1.1-introduction",
"chapterApi": "../dmn/ch03-API#the-flowable-dmn-api",
"chDeployment": "../dmn/ch05-Deployment#deployment",
"configuration": "../dmn/ch02-Configuration#configuration",
"configurationClasses": "../dmn/ch02-Configuration#plug-into-process-engine",
"configurationRoot": "../dmn/ch02-Configuration#dmnengineconfiguration-bean",
"creatingDatabaseTable": "../dmn/ch02-Configuration#creating-the-database-tables",
"database.tables.explained": "../dmn/ch02-Configuration#database-table-names-explained",
"databaseConfiguration": "../dmn/ch02-Configuration#database-configuration",
"databaseTypes": "../dmn/ch02-Configuration#supported-databases",
"databaseUpgrade": "../dmn/ch02-Configuration#database-upgrade",
"deploymentCategory": "../dmn/ch05-Deployment#category",
"dmnDefiningDecision": "../dmn/ch06-DMN-Introduction#what-is-a-dmn-definition",
"download": "../oss-introduction#download",
"experimental": "../oss-introduction#experimental-features",
"internal": "../oss-introduction#internal-implementation-classes",
"jndi_configuration": "../dmn/ch02-Configuration#configuration",
"jndiDatasourceConfig": "../dmn/ch02-Configuration#jndi-datasource-configuration",
"license": "../oss-introduction#license",
"loggingConfiguration": "../dmn/ch02-Configuration#logging",
"processDefinitionCacheConfiguration": "../dmn/ch02-Configuration#deployment-cache-configuration",
"queryAPI": "../dmn/ch03-API#query-api",
"reporting.problems": "../oss-introduction#reporting-problems",
"required.software": "../oss-introduction#required-software",
"restApiChapter": "../dmn/ch07-REST#dmn-rest-api",
"restJsonBody": "../dmn/ch07-REST#json-body-parameters",
"restPagingAndSort": "../dmn/ch07-REST#paging-and-sorting",
"restQueryVariable": "../dmn/ch07-REST#json-query-variable-format",
"restUsageInTomcat": "../dmn/ch07-REST-rest#usage-in-tomcat",
"restVariables": "../dmn/ch07-REST#variable-representation",
"sources": "../oss-introduction#sources",
"springintegration": "../dmn/ch04-Spring#spring-integration",
"springUnitTest": "../dmn/ch04-Spring#unit-testing",
"strictMode": "../dmn/ch02-Configuration#strict-mode",
"supporteddatabases": "../dmn/ch02-Configuration#supported-databases",
"versioningOfDMNDefinitions": "../dmn/ch05-Deployment#versioning-of-dmn-decisions",
"whatIsDmn": "../dmn/ch06-DMN-Introduction#what-is-dmn"
    }
    /*
    * Best practice for extracting hashes:
    * https://stackoverflow.com/a/10076097/151365
    */
    var hash = window.location.hash.substring(1);
    if (hash) {
        /*
        * Best practice for javascript redirects: 
        * https://stackoverflow.com/a/506004/151365
        */
        window.location.replace(anchorMap[hash]);
    }
    else {
        window.location.replace("../dmn/ch02-Configuration/");
    }
})();
</script>
</head>
<body>
<h2>Redirecting...</h2>
</body>
</html>
