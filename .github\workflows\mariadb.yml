name: Flowable MariaDB Build

on:
  push:
    branches:
      - main
      - 'flowable-release-*'

env:
  MAVEN_ARGS: >-
    -Dmaven.javadoc.skip=true
    -B -V --no-transfer-progress
    -Dhttp.keepAlive=false -Dmaven.wagon.http.pool=false -Dmaven.wagon.httpconnectionManager.ttlSeconds=120

# We explicitly don't use a container for running the job since there is some connectivity issues to MariaDB if that is done
jobs:
  test_mariadb:
    name: MariaDB ${{ matrix.mariadb }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        mariadb: [10.6, 11.4]
    services:
      mariadb:
        image: mariadb:${{ matrix.mariadb }}
        env:
          MARIADB_DATABASE: flowable
          MARIADB_USER: flowable
          MARIADB_PASSWORD: flowable
          MARIADB_ROOT_PASSWORD: flowable
        ports:
          - 3306/tcp
        # needed because the mariadb container does not provide a health check
        options: --health-cmd="mariadb-admin -uflowable -pflowable status" --health-interval 10s --health-timeout 5s --health-retries 5 --tmpfs /var/lib/mariadb:rw
    steps:
      - name: "Set MariaDB collation"
        run: docker exec ${{ job.services.mariadb.id }} sh -c 'mariadb --user=flowable --password=flowable --database=flowable --execute="alter database flowable character set utf8mb4 collate utf8mb4_bin"'
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 17
      - name: Test
        # use localhost for the host here because we have specified a vm for the job.
        # '>-' is a special YAML syntax and means that new lines would be replaced with spaces
        # and new lines from the end would be removed
        run: >-
          ./mvnw clean install
          ${MAVEN_ARGS}
          -PcleanDb,mariadb,distro
          -P'!include-spring-boot-samples'
          -Djdbc.url=************************:${{ job.services.mariadb.ports[3306] }}/flowable?characterEncoding=UTF-8
          -Djdbc.username=flowable
          -Djdbc.password=flowable
          -Djdbc.driver=org.mariadb.jdbc.Driver
          -Dspring.datasource.url=************************:${{ job.services.mariadb.ports[3306] }}/flowable?characterEncoding=UTF-8
          -Dspring.datasource.username=flowable
          -Dspring.datasource.password=flowable
          -Dmaven.test.redirectTestOutputToFile=false
