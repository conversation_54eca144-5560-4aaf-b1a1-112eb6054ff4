# Monitoring and Analytics Setup for GRCOS Flowable

## Overview

This guide provides comprehensive instructions for implementing monitoring, metrics collection, and analytics for the GRCOS Flowable integration. The setup includes real-time performance monitoring, compliance analytics, workflow optimization insights, and comprehensive dashboards using Prometheus, Grafana, and custom analytics services.

## Monitoring Architecture

### Observability Stack

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        GRCOS Monitoring & Analytics Architecture                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        Visualization Layer                                  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │   Grafana   │  │   Custom    │  │ Executive   │  │ Compliance  │       │ │
│  │  │ Dashboards  │  │ Analytics   │  │ Dashboard   │  │ Dashboard   │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                     Analytics Processing Layer                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │ Prometheus  │  │ ClickHouse  │  │   Apache    │  │   Machine   │       │ │
│  │  │   TSDB      │  │ Analytics   │  │    Spark    │  │  Learning   │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                     Data Collection Layer                                  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │  Micrometer │  │   Custom    │  │   Event     │  │    Log      │       │ │
│  │  │   Metrics   │  │  Collectors │  │ Listeners   │  │ Aggregators │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                      GRCOS Flowable Engine                                 │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Metrics Collection Setup

### Micrometer Configuration

#### MetricsConfiguration.java
```java
@Configuration
@EnableConfigurationProperties(MetricsProperties.class)
public class MetricsConfiguration {
    
    @Bean
    public MeterRegistry meterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }
    
    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }
    
    @Bean
    public CountedAspect countedAspect(MeterRegistry registry) {
        return new CountedAspect(registry);
    }
    
    @Bean
    public GRCOSWorkflowMetrics workflowMetrics(MeterRegistry registry) {
        return new GRCOSWorkflowMetrics(registry);
    }
    
    @Bean
    public ComplianceMetrics complianceMetrics(MeterRegistry registry) {
        return new ComplianceMetrics(registry);
    }
    
    @Bean
    public PerformanceMetrics performanceMetrics(MeterRegistry registry) {
        return new PerformanceMetrics(registry);
    }
    
    @Bean
    public SecurityMetrics securityMetrics(MeterRegistry registry) {
        return new SecurityMetrics(registry);
    }
}
```

### Custom Metrics Implementation

#### GRCOSWorkflowMetrics.java
```java
@Component
public class GRCOSWorkflowMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // Counters
    private final Counter processStartedCounter;
    private final Counter processCompletedCounter;
    private final Counter processFailedCounter;
    private final Counter taskCreatedCounter;
    private final Counter taskCompletedCounter;
    
    // Timers
    private final Timer processExecutionTimer;
    private final Timer taskExecutionTimer;
    private final Timer serviceTaskTimer;
    private final Timer userTaskTimer;
    
    // Gauges
    private final AtomicInteger activeProcesses = new AtomicInteger(0);
    private final AtomicInteger pendingTasks = new AtomicInteger(0);
    private final AtomicInteger overdueTasks = new AtomicInteger(0);
    
    // Distributions
    private final DistributionSummary processComplexity;
    private final DistributionSummary taskAssignmentTime;
    
    public GRCOSWorkflowMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // Initialize counters
        this.processStartedCounter = Counter.builder("grcos.workflow.processes.started")
            .description("Number of workflow processes started")
            .register(meterRegistry);
            
        this.processCompletedCounter = Counter.builder("grcos.workflow.processes.completed")
            .description("Number of workflow processes completed")
            .register(meterRegistry);
            
        this.processFailedCounter = Counter.builder("grcos.workflow.processes.failed")
            .description("Number of workflow processes failed")
            .register(meterRegistry);
            
        this.taskCreatedCounter = Counter.builder("grcos.workflow.tasks.created")
            .description("Number of tasks created")
            .register(meterRegistry);
            
        this.taskCompletedCounter = Counter.builder("grcos.workflow.tasks.completed")
            .description("Number of tasks completed")
            .register(meterRegistry);
        
        // Initialize timers
        this.processExecutionTimer = Timer.builder("grcos.workflow.process.execution.time")
            .description("Time taken to execute workflow processes")
            .register(meterRegistry);
            
        this.taskExecutionTimer = Timer.builder("grcos.workflow.task.execution.time")
            .description("Time taken to execute tasks")
            .register(meterRegistry);
            
        this.serviceTaskTimer = Timer.builder("grcos.workflow.servicetask.execution.time")
            .description("Time taken to execute service tasks")
            .register(meterRegistry);
            
        this.userTaskTimer = Timer.builder("grcos.workflow.usertask.execution.time")
            .description("Time taken to execute user tasks")
            .register(meterRegistry);
        
        // Initialize gauges
        Gauge.builder("grcos.workflow.processes.active")
            .description("Number of active workflow processes")
            .register(meterRegistry, activeProcesses, AtomicInteger::get);
            
        Gauge.builder("grcos.workflow.tasks.pending")
            .description("Number of pending tasks")
            .register(meterRegistry, pendingTasks, AtomicInteger::get);
            
        Gauge.builder("grcos.workflow.tasks.overdue")
            .description("Number of overdue tasks")
            .register(meterRegistry, overdueTasks, AtomicInteger::get);
        
        // Initialize distributions
        this.processComplexity = DistributionSummary.builder("grcos.workflow.process.complexity")
            .description("Complexity score of workflow processes")
            .register(meterRegistry);
            
        this.taskAssignmentTime = DistributionSummary.builder("grcos.workflow.task.assignment.time")
            .description("Time taken to assign tasks")
            .register(meterRegistry);
    }
    
    public void recordProcessStarted(String processDefinitionKey, Map<String, String> tags) {
        processStartedCounter.increment(
            Tags.of("processDefinitionKey", processDefinitionKey)
                .and(Tags.of(tags))
        );
        activeProcesses.incrementAndGet();
    }
    
    public void recordProcessCompleted(String processDefinitionKey, Duration duration, 
                                     Map<String, String> tags) {
        processCompletedCounter.increment(
            Tags.of("processDefinitionKey", processDefinitionKey)
                .and(Tags.of(tags))
        );
        processExecutionTimer.record(duration, 
            Tags.of("processDefinitionKey", processDefinitionKey)
                .and(Tags.of(tags))
        );
        activeProcesses.decrementAndGet();
    }
    
    public void recordTaskCompleted(String taskDefinitionKey, String assignee, 
                                  Duration duration, Map<String, String> tags) {
        taskCompletedCounter.increment(
            Tags.of("taskDefinitionKey", taskDefinitionKey)
                .and("assignee", assignee)
                .and(Tags.of(tags))
        );
        taskExecutionTimer.record(duration,
            Tags.of("taskDefinitionKey", taskDefinitionKey)
                .and("assignee", assignee)
                .and(Tags.of(tags))
        );
        pendingTasks.decrementAndGet();
    }
    
    public void recordServiceTaskExecution(String taskName, Duration duration, 
                                         boolean success, Map<String, String> tags) {
        serviceTaskTimer.record(duration,
            Tags.of("taskName", taskName)
                .and("success", String.valueOf(success))
                .and(Tags.of(tags))
        );
    }
    
    public void updateActiveProcessCount(int count) {
        activeProcesses.set(count);
    }
    
    public void updatePendingTaskCount(int count) {
        pendingTasks.set(count);
    }
    
    public void updateOverdueTaskCount(int count) {
        overdueTasks.set(count);
    }
}
```

### Compliance Metrics

#### ComplianceMetrics.java
```java
@Component
public class ComplianceMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // Compliance-specific counters
    private final Counter assessmentsStartedCounter;
    private final Counter assessmentsCompletedCounter;
    private final Counter findingsCreatedCounter;
    private final Counter findingsResolvedCounter;
    private final Counter controlsImplementedCounter;
    private final Counter controlsTestedCounter;
    
    // Compliance timers
    private final Timer assessmentExecutionTimer;
    private final Timer findingResolutionTimer;
    private final Timer controlImplementationTimer;
    
    // Compliance gauges
    private final AtomicDouble overallComplianceScore = new AtomicDouble(0.0);
    private final AtomicInteger openFindings = new AtomicInteger(0);
    private final AtomicInteger criticalFindings = new AtomicInteger(0);
    
    public ComplianceMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // Initialize counters
        this.assessmentsStartedCounter = Counter.builder("grcos.compliance.assessments.started")
            .description("Number of compliance assessments started")
            .register(meterRegistry);
            
        this.assessmentsCompletedCounter = Counter.builder("grcos.compliance.assessments.completed")
            .description("Number of compliance assessments completed")
            .register(meterRegistry);
            
        this.findingsCreatedCounter = Counter.builder("grcos.compliance.findings.created")
            .description("Number of compliance findings created")
            .register(meterRegistry);
            
        this.findingsResolvedCounter = Counter.builder("grcos.compliance.findings.resolved")
            .description("Number of compliance findings resolved")
            .register(meterRegistry);
            
        this.controlsImplementedCounter = Counter.builder("grcos.compliance.controls.implemented")
            .description("Number of security controls implemented")
            .register(meterRegistry);
            
        this.controlsTestedCounter = Counter.builder("grcos.compliance.controls.tested")
            .description("Number of security controls tested")
            .register(meterRegistry);
        
        // Initialize timers
        this.assessmentExecutionTimer = Timer.builder("grcos.compliance.assessment.execution.time")
            .description("Time taken to execute compliance assessments")
            .register(meterRegistry);
            
        this.findingResolutionTimer = Timer.builder("grcos.compliance.finding.resolution.time")
            .description("Time taken to resolve compliance findings")
            .register(meterRegistry);
            
        this.controlImplementationTimer = Timer.builder("grcos.compliance.control.implementation.time")
            .description("Time taken to implement security controls")
            .register(meterRegistry);
        
        // Initialize gauges
        Gauge.builder("grcos.compliance.score.overall")
            .description("Overall compliance score")
            .register(meterRegistry, overallComplianceScore, AtomicDouble::get);
            
        Gauge.builder("grcos.compliance.findings.open")
            .description("Number of open compliance findings")
            .register(meterRegistry, openFindings, AtomicInteger::get);
            
        Gauge.builder("grcos.compliance.findings.critical")
            .description("Number of critical compliance findings")
            .register(meterRegistry, criticalFindings, AtomicInteger::get);
    }
    
    public void recordAssessmentStarted(String frameworkId, String systemId) {
        assessmentsStartedCounter.increment(
            Tags.of("framework", frameworkId, "system", systemId)
        );
    }
    
    public void recordAssessmentCompleted(String frameworkId, String systemId, 
                                        Duration duration, double score) {
        assessmentsCompletedCounter.increment(
            Tags.of("framework", frameworkId, "system", systemId)
        );
        assessmentExecutionTimer.record(duration,
            Tags.of("framework", frameworkId, "system", systemId)
        );
    }
    
    public void recordFindingCreated(String severity, String category, String systemId) {
        findingsCreatedCounter.increment(
            Tags.of("severity", severity, "category", category, "system", systemId)
        );
        
        if ("critical".equalsIgnoreCase(severity)) {
            criticalFindings.incrementAndGet();
        }
        openFindings.incrementAndGet();
    }
    
    public void recordFindingResolved(String severity, String category, 
                                    Duration resolutionTime, String systemId) {
        findingsResolvedCounter.increment(
            Tags.of("severity", severity, "category", category, "system", systemId)
        );
        findingResolutionTimer.record(resolutionTime,
            Tags.of("severity", severity, "category", category, "system", systemId)
        );
        
        if ("critical".equalsIgnoreCase(severity)) {
            criticalFindings.decrementAndGet();
        }
        openFindings.decrementAndGet();
    }
    
    public void updateComplianceScore(double score) {
        overallComplianceScore.set(score);
    }
}
```

## Event-Driven Metrics Collection

### Workflow Event Listener

#### MetricsCollectionEventListener.java
```java
@Component
public class MetricsCollectionEventListener implements FlowableEventListener {
    
    @Autowired
    private GRCOSWorkflowMetrics workflowMetrics;
    
    @Autowired
    private ComplianceMetrics complianceMetrics;
    
    @Autowired
    private PerformanceMetrics performanceMetrics;
    
    private final Map<String, Instant> processStartTimes = new ConcurrentHashMap<>();
    private final Map<String, Instant> taskStartTimes = new ConcurrentHashMap<>();
    
    @Override
    public void onEvent(FlowableEvent event) {
        try {
            switch (event.getType()) {
                case PROCESS_STARTED:
                    handleProcessStarted((FlowableProcessEvent) event);
                    break;
                case PROCESS_COMPLETED:
                    handleProcessCompleted((FlowableProcessEvent) event);
                    break;
                case PROCESS_CANCELLED:
                    handleProcessCancelled((FlowableProcessEvent) event);
                    break;
                case TASK_CREATED:
                    handleTaskCreated((FlowableEntityEvent) event);
                    break;
                case TASK_COMPLETED:
                    handleTaskCompleted((FlowableEntityEvent) event);
                    break;
                case VARIABLE_CREATED:
                case VARIABLE_UPDATED:
                    handleVariableEvent((FlowableVariableEvent) event);
                    break;
                case JOB_EXECUTION_SUCCESS:
                    handleJobSuccess((FlowableEntityEvent) event);
                    break;
                case JOB_EXECUTION_FAILURE:
                    handleJobFailure((FlowableEntityEvent) event);
                    break;
            }
        } catch (Exception e) {
            logger.error("Error processing metrics event", e);
        }
    }
    
    private void handleProcessStarted(FlowableProcessEvent event) {
        String processInstanceId = event.getProcessInstanceId();
        String processDefinitionKey = extractProcessDefinitionKey(event.getProcessDefinitionId());
        
        processStartTimes.put(processInstanceId, Instant.now());
        
        Map<String, String> tags = extractProcessTags(event);
        workflowMetrics.recordProcessStarted(processDefinitionKey, tags);
        
        // Record compliance-specific metrics if applicable
        if (isComplianceProcess(processDefinitionKey)) {
            String frameworkId = extractFrameworkId(event);
            String systemId = extractSystemId(event);
            complianceMetrics.recordAssessmentStarted(frameworkId, systemId);
        }
    }
    
    private void handleProcessCompleted(FlowableProcessEvent event) {
        String processInstanceId = event.getProcessInstanceId();
        String processDefinitionKey = extractProcessDefinitionKey(event.getProcessDefinitionId());
        
        Instant startTime = processStartTimes.remove(processInstanceId);
        if (startTime != null) {
            Duration duration = Duration.between(startTime, Instant.now());
            Map<String, String> tags = extractProcessTags(event);
            workflowMetrics.recordProcessCompleted(processDefinitionKey, duration, tags);
            
            // Record compliance completion if applicable
            if (isComplianceProcess(processDefinitionKey)) {
                String frameworkId = extractFrameworkId(event);
                String systemId = extractSystemId(event);
                double score = extractComplianceScore(event);
                complianceMetrics.recordAssessmentCompleted(frameworkId, systemId, duration, score);
            }
        }
    }
    
    private void handleTaskCreated(FlowableEntityEvent event) {
        Task task = (Task) event.getEntity();
        String taskId = task.getId();
        
        taskStartTimes.put(taskId, Instant.now());
        
        Map<String, String> tags = extractTaskTags(task);
        workflowMetrics.recordTaskCreated(task.getTaskDefinitionKey(), tags);
    }
    
    private void handleTaskCompleted(FlowableEntityEvent event) {
        Task task = (Task) event.getEntity();
        String taskId = task.getId();
        
        Instant startTime = taskStartTimes.remove(taskId);
        if (startTime != null) {
            Duration duration = Duration.between(startTime, Instant.now());
            Map<String, String> tags = extractTaskTags(task);
            workflowMetrics.recordTaskCompleted(
                task.getTaskDefinitionKey(), 
                task.getAssignee(), 
                duration, 
                tags
            );
        }
    }
    
    private void handleVariableEvent(FlowableVariableEvent event) {
        // Track specific variables that indicate compliance events
        String variableName = event.getVariableName();
        Object variableValue = event.getVariableValue();
        
        if ("complianceScore".equals(variableName) && variableValue instanceof Number) {
            double score = ((Number) variableValue).doubleValue();
            complianceMetrics.updateComplianceScore(score);
        } else if ("findingCreated".equals(variableName) && variableValue instanceof Map) {
            Map<String, Object> findingData = (Map<String, Object>) variableValue;
            recordFindingCreated(findingData);
        } else if ("findingResolved".equals(variableName) && variableValue instanceof Map) {
            Map<String, Object> findingData = (Map<String, Object>) variableValue;
            recordFindingResolved(findingData);
        }
    }
    
    private Map<String, String> extractProcessTags(FlowableProcessEvent event) {
        Map<String, String> tags = new HashMap<>();
        
        // Extract business key
        String businessKey = getBusinessKey(event.getProcessInstanceId());
        if (businessKey != null) {
            tags.put("businessKey", businessKey);
        }
        
        // Extract process category
        String category = extractProcessCategory(event.getProcessDefinitionId());
        if (category != null) {
            tags.put("category", category);
        }
        
        return tags;
    }
    
    private boolean isComplianceProcess(String processDefinitionKey) {
        return processDefinitionKey.contains("assessment") ||
               processDefinitionKey.contains("compliance") ||
               processDefinitionKey.contains("audit");
    }
    
    @Override
    public boolean isFailOnException() {
        return false; // Don't fail workflow if metrics collection fails
    }
}
```

## Analytics Service

### Workflow Analytics Service

#### WorkflowAnalyticsService.java
```java
@Service
public class WorkflowAnalyticsService {
    
    @Autowired
    private HistoryService historyService;
    
    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @Autowired
    private AnalyticsRepository analyticsRepository;
    
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void updateRealTimeMetrics() {
        try {
            // Update active process count
            long activeProcesses = runtimeService.createProcessInstanceQuery().count();
            workflowMetrics.updateActiveProcessCount((int) activeProcesses);
            
            // Update pending task count
            long pendingTasks = taskService.createTaskQuery().count();
            workflowMetrics.updatePendingTaskCount((int) pendingTasks);
            
            // Update overdue task count
            long overdueTasks = taskService.createTaskQuery()
                .taskDueBefore(new Date())
                .count();
            workflowMetrics.updateOverdueTaskCount((int) overdueTasks);
            
        } catch (Exception e) {
            logger.error("Failed to update real-time metrics", e);
        }
    }
    
    @Scheduled(cron = "0 0 * * * *") // Every hour
    public void generateHourlyAnalytics() {
        try {
            Instant endTime = Instant.now();
            Instant startTime = endTime.minus(1, ChronoUnit.HOURS);
            
            // Generate process analytics
            ProcessAnalytics processAnalytics = generateProcessAnalytics(startTime, endTime);
            analyticsRepository.saveProcessAnalytics(processAnalytics);
            
            // Generate task analytics
            TaskAnalytics taskAnalytics = generateTaskAnalytics(startTime, endTime);
            analyticsRepository.saveTaskAnalytics(taskAnalytics);
            
            // Generate performance analytics
            PerformanceAnalytics performanceAnalytics = generatePerformanceAnalytics(startTime, endTime);
            analyticsRepository.savePerformanceAnalytics(performanceAnalytics);
            
        } catch (Exception e) {
            logger.error("Failed to generate hourly analytics", e);
        }
    }
    
    public ProcessAnalytics generateProcessAnalytics(Instant startTime, Instant endTime) {
        // Query historical process instances
        List<HistoricProcessInstance> processes = historyService
            .createHistoricProcessInstanceQuery()
            .startedAfter(Date.from(startTime))
            .startedBefore(Date.from(endTime))
            .list();
        
        // Calculate metrics
        long totalProcesses = processes.size();
        long completedProcesses = processes.stream()
            .mapToLong(p -> p.getEndTime() != null ? 1 : 0)
            .sum();
        
        double completionRate = totalProcesses > 0 ? 
            (double) completedProcesses / totalProcesses : 0.0;
        
        // Calculate average duration
        OptionalDouble avgDuration = processes.stream()
            .filter(p -> p.getEndTime() != null)
            .mapToLong(p -> p.getDurationInMillis())
            .average();
        
        // Group by process definition
        Map<String, Long> processByDefinition = processes.stream()
            .collect(Collectors.groupingBy(
                HistoricProcessInstance::getProcessDefinitionKey,
                Collectors.counting()
            ));
        
        return ProcessAnalytics.builder()
            .timeRange(TimeRange.of(startTime, endTime))
            .totalProcesses(totalProcesses)
            .completedProcesses(completedProcesses)
            .completionRate(completionRate)
            .averageDuration(avgDuration.orElse(0.0))
            .processByDefinition(processByDefinition)
            .build();
    }
    
    public TaskAnalytics generateTaskAnalytics(Instant startTime, Instant endTime) {
        // Query historical task instances
        List<HistoricTaskInstance> tasks = historyService
            .createHistoricTaskInstanceQuery()
            .taskCreatedAfter(Date.from(startTime))
            .taskCreatedBefore(Date.from(endTime))
            .list();
        
        // Calculate metrics
        long totalTasks = tasks.size();
        long completedTasks = tasks.stream()
            .mapToLong(t -> t.getEndTime() != null ? 1 : 0)
            .sum();
        
        // Calculate average assignment time
        OptionalDouble avgAssignmentTime = tasks.stream()
            .filter(t -> t.getAssignee() != null && t.getClaimTime() != null)
            .mapToLong(t -> t.getClaimTime().getTime() - t.getCreateTime().getTime())
            .average();
        
        // Calculate average completion time
        OptionalDouble avgCompletionTime = tasks.stream()
            .filter(t -> t.getEndTime() != null)
            .mapToLong(HistoricTaskInstance::getDurationInMillis)
            .average();
        
        // Group by assignee
        Map<String, Long> tasksByAssignee = tasks.stream()
            .filter(t -> t.getAssignee() != null)
            .collect(Collectors.groupingBy(
                HistoricTaskInstance::getAssignee,
                Collectors.counting()
            ));
        
        return TaskAnalytics.builder()
            .timeRange(TimeRange.of(startTime, endTime))
            .totalTasks(totalTasks)
            .completedTasks(completedTasks)
            .averageAssignmentTime(avgAssignmentTime.orElse(0.0))
            .averageCompletionTime(avgCompletionTime.orElse(0.0))
            .tasksByAssignee(tasksByAssignee)
            .build();
    }
    
    public WorkflowPerformanceReport generatePerformanceReport(String processDefinitionKey, 
                                                             Instant startTime, Instant endTime) {
        // Get process instances for the definition
        List<HistoricProcessInstance> processes = historyService
            .createHistoricProcessInstanceQuery()
            .processDefinitionKey(processDefinitionKey)
            .startedAfter(Date.from(startTime))
            .startedBefore(Date.from(endTime))
            .list();
        
        // Analyze performance patterns
        PerformancePatternAnalysis patterns = analyzePerformancePatterns(processes);
        
        // Identify bottlenecks
        List<WorkflowBottleneck> bottlenecks = identifyBottlenecks(processes);
        
        // Generate optimization recommendations
        List<OptimizationRecommendation> recommendations = 
            generateOptimizationRecommendations(patterns, bottlenecks);
        
        return WorkflowPerformanceReport.builder()
            .processDefinitionKey(processDefinitionKey)
            .timeRange(TimeRange.of(startTime, endTime))
            .processCount(processes.size())
            .patterns(patterns)
            .bottlenecks(bottlenecks)
            .recommendations(recommendations)
            .generatedAt(Instant.now())
            .build();
    }
}
```

This comprehensive monitoring and analytics setup provides real-time visibility into workflow performance, compliance metrics, and system health with actionable insights for continuous optimization.
