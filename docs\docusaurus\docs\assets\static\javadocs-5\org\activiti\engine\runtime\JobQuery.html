<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>JobQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JobQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":38,"i2":38,"i3":6,"i4":38,"i5":38,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/JobQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/Job.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/runtime/NativeExecutionQuery.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/JobQuery.html" target="_top">Frames</a></li>
<li><a href="JobQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.runtime</div>
<h2 title="Interface JobQuery" class="title">Interface JobQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>,<a href="../../../../org/activiti/engine/runtime/Job.html" title="interface in org.activiti.engine.runtime">Job</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">JobQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>,<a href="../../../../org/activiti/engine/runtime/Job.html" title="interface in org.activiti.engine.runtime">Job</a>&gt;</pre>
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/runtime/Job.html" title="interface in org.activiti.engine.runtime"><code>Job</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez, Falko Menge</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#duedateHigherThan-java.util.Date-">duedateHigherThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block">Only select jobs where the duedate is higher then the given date.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#duedateHigherThen-java.util.Date-">duedateHigherThen</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#duedateHigherThenOrEquals-java.util.Date-">duedateHigherThenOrEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#duedateLowerThan-java.util.Date-">duedateLowerThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block">Only select jobs where the duedate is lower than the given date.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#duedateLowerThen-java.util.Date-">duedateLowerThen</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#duedateLowerThenOrEquals-java.util.Date-">duedateLowerThenOrEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#exceptionMessage-java.lang.String-">exceptionMessage</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;exceptionMessage)</code>
<div class="block">Only select jobs that failed due to an exception with the given message.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#executable--">executable</a></span>()</code>
<div class="block">Only select jobs which are executable, 
 ie.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#executionId-java.lang.String-">executionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Only select jobs which exist for the given execution</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#jobId-java.lang.String-">jobId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId)</code>
<div class="block">Only select jobs with the given id</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#jobTenantId-java.lang.String-">jobTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select jobs that have the given tenant id.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#jobTenantIdLike-java.lang.String-">jobTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select jobs with a tenant id like the given one.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#jobWithoutTenantId--">jobWithoutTenantId</a></span>()</code>
<div class="block">Only select jobs that do not have a tenant id.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#messages--">messages</a></span>()</code>
<div class="block">Only select jobs that are messages.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#noRetriesLeft--">noRetriesLeft</a></span>()</code>
<div class="block">Only select jobs which have no retries left</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#orderByExecutionId--">orderByExecutionId</a></span>()</code>
<div class="block">Order by execution id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#orderByJobDuedate--">orderByJobDuedate</a></span>()</code>
<div class="block">Order by duedate (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#orderByJobId--">orderByJobId</a></span>()</code>
<div class="block">Order by job id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#orderByJobRetries--">orderByJobRetries</a></span>()</code>
<div class="block">Order by retries (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>
<div class="block">Order by process instance id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionid)</code>
<div class="block">Only select jobs which exist for the given process definition id</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Only select jobs which exist for the given process instance.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#timers--">timers</a></span>()</code>
<div class="block">Only select jobs that are timers.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#withException--">withException</a></span>()</code>
<div class="block">Only select jobs that failed due to an exception.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/JobQuery.html#withRetriesLeft--">withRetriesLeft</a></span>()</code>
<div class="block">Only select jobs which have retries left</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="jobId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jobId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;jobId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId)</pre>
<div class="block">Only select jobs with the given id</div>
</li>
</ul>
<a name="processInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;processInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Only select jobs which exist for the given process instance.</div>
</li>
</ul>
<a name="executionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;executionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Only select jobs which exist for the given execution</div>
</li>
</ul>
<a name="processDefinitionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;processDefinitionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionid)</pre>
<div class="block">Only select jobs which exist for the given process definition id</div>
</li>
</ul>
<a name="withRetriesLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withRetriesLeft</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;withRetriesLeft()</pre>
<div class="block">Only select jobs which have retries left</div>
</li>
</ul>
<a name="noRetriesLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>noRetriesLeft</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;noRetriesLeft()</pre>
<div class="block">Only select jobs which have no retries left</div>
</li>
</ul>
<a name="executable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executable</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;executable()</pre>
<div class="block">Only select jobs which are executable, 
 ie. retries &gt; 0 and duedate is null or duedate is in the past</div>
</li>
</ul>
<a name="timers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>timers</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;timers()</pre>
<div class="block">Only select jobs that are timers. 
 Cannot be used together with <a href="../../../../org/activiti/engine/runtime/JobQuery.html#messages--"><code>messages()</code></a></div>
</li>
</ul>
<a name="messages--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>messages</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;messages()</pre>
<div class="block">Only select jobs that are messages. 
 Cannot be used together with <a href="../../../../org/activiti/engine/runtime/JobQuery.html#timers--"><code>timers()</code></a></div>
</li>
</ul>
<a name="duedateLowerThan-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>duedateLowerThan</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;duedateLowerThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block">Only select jobs where the duedate is lower than the given date.</div>
</li>
</ul>
<a name="duedateHigherThan-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>duedateHigherThan</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;duedateHigherThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block">Only select jobs where the duedate is higher then the given date.</div>
</li>
</ul>
<a name="duedateLowerThen-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>duedateLowerThen</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;duedateLowerThen(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">Only select jobs where the duedate is lower then the given date.</div>
</li>
</ul>
<a name="duedateLowerThenOrEquals-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>duedateLowerThenOrEquals</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;duedateLowerThenOrEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">Only select jobs where the duedate is lower then or equals the given date.</div>
</li>
</ul>
<a name="duedateHigherThen-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>duedateHigherThen</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;duedateHigherThen(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">Only select jobs where the duedate is higher then the given date.</div>
</li>
</ul>
<a name="duedateHigherThenOrEquals-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>duedateHigherThenOrEquals</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;duedateHigherThenOrEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">Only select jobs where the duedate is higher then or equals the given date.</div>
</li>
</ul>
<a name="withException--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withException</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;withException()</pre>
<div class="block">Only select jobs that failed due to an exception.</div>
</li>
</ul>
<a name="exceptionMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exceptionMessage</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;exceptionMessage(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;exceptionMessage)</pre>
<div class="block">Only select jobs that failed due to an exception with the given message.</div>
</li>
</ul>
<a name="jobTenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jobTenantId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;jobTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Only select jobs that have the given tenant id.</div>
</li>
</ul>
<a name="jobTenantIdLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jobTenantIdLike</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;jobTenantIdLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</pre>
<div class="block">Only select jobs with a tenant id like the given one.</div>
</li>
</ul>
<a name="jobWithoutTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jobWithoutTenantId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;jobWithoutTenantId()</pre>
<div class="block">Only select jobs that do not have a tenant id.</div>
</li>
</ul>
<a name="orderByJobId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByJobId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;orderByJobId()</pre>
<div class="block">Order by job id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByJobDuedate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByJobDuedate</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;orderByJobDuedate()</pre>
<div class="block">Order by duedate (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByJobRetries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByJobRetries</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;orderByJobRetries()</pre>
<div class="block">Order by retries (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;orderByProcessInstanceId()</pre>
<div class="block">Order by process instance id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByExecutionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByExecutionId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;orderByExecutionId()</pre>
<div class="block">Order by execution id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTenantId--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByTenantId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;orderByTenantId()</pre>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/JobQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/Job.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/runtime/NativeExecutionQuery.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/JobQuery.html" target="_top">Frames</a></li>
<li><a href="JobQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
