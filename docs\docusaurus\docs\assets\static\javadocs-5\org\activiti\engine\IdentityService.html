<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IdentityService (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IdentityService (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IdentityService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/JobNotFoundException.html" title="class in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/IdentityService.html" target="_top">Frames</a></li>
<li><a href="IdentityService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Interface IdentityService" class="title">Interface IdentityService</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IdentityService</span></pre>
<div class="block">Service to manage <a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s and <a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#checkPassword-java.lang.String-java.lang.String-">checkPassword</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;password)</code>
<div class="block">Checks if the password is valid for the given user.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#createGroupQuery--">createGroupQuery</a></span>()</code>
<div class="block">Creates a <a href="../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity"><code>GroupQuery</code></a> thats allows to programmatically query the groups.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#createMembership-java.lang.String-java.lang.String-">createMembership</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/identity/NativeGroupQuery.html" title="interface in org.activiti.engine.identity">NativeGroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#createNativeGroupQuery--">createNativeGroupQuery</a></span>()</code>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for tasks.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/identity/NativeUserQuery.html" title="interface in org.activiti.engine.identity">NativeUserQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#createNativeUserQuery--">createNativeUserQuery</a></span>()</code>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for tasks.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/identity/UserQuery.html" title="interface in org.activiti.engine.identity">UserQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#createUserQuery--">createUserQuery</a></span>()</code>
<div class="block">Creates a <a href="../../../org/activiti/engine/identity/UserQuery.html" title="interface in org.activiti.engine.identity"><code>UserQuery</code></a> that allows to programmatically query the users.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#deleteGroup-java.lang.String-">deleteGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Deletes the group.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#deleteMembership-java.lang.String-java.lang.String-">deleteMembership</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Delete the membership of the user in the group.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#deleteUser-java.lang.String-">deleteUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#deleteUserInfo-java.lang.String-java.lang.String-">deleteUserInfo</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</code>
<div class="block">Delete an entry of the generic extensibility key-value pairs associated with a user</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#getUserInfo-java.lang.String-java.lang.String-">getUserInfo</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</code>
<div class="block">Generic extensibility key-value pairs associated with a user</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#getUserInfoKeys-java.lang.String-">getUserInfoKeys</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Generic extensibility keys associated with a user</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/identity/Picture.html" title="class in org.activiti.engine.identity">Picture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#getUserPicture-java.lang.String-">getUserPicture</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Retrieves the picture for a given user.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity">Group</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#newGroup-java.lang.String-">newGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Creates a new group.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity">User</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#newUser-java.lang.String-">newUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Creates a new user.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#saveGroup-org.activiti.engine.identity.Group-">saveGroup</a></span>(<a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity">Group</a>&nbsp;group)</code>
<div class="block">Saves the group.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#saveUser-org.activiti.engine.identity.User-">saveUser</a></span>(<a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity">User</a>&nbsp;user)</code>
<div class="block">Saves the user.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#setAuthenticatedUserId-java.lang.String-">setAuthenticatedUserId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;authenticatedUserId)</code>
<div class="block">Passes the authenticated user id for this particular thread.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#setUserInfo-java.lang.String-java.lang.String-java.lang.String-">setUserInfo</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Generic extensibility key-value pairs associated with a user</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/IdentityService.html#setUserPicture-java.lang.String-org.activiti.engine.identity.Picture-">setUserPicture</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
              <a href="../../../org/activiti/engine/identity/Picture.html" title="class in org.activiti.engine.identity">Picture</a>&nbsp;picture)</code>
<div class="block">Sets the picture for a given user.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="newUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newUser</h4>
<pre><a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity">User</a>&nbsp;newUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Creates a new user. The user is transient and must be saved using 
 <a href="../../../org/activiti/engine/IdentityService.html#saveUser-org.activiti.engine.identity.User-"><code>saveUser(User)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>userId</code> - id for the new user, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="saveUser-org.activiti.engine.identity.User-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveUser</h4>
<pre>void&nbsp;saveUser(<a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity">User</a>&nbsp;user)</pre>
<div class="block">Saves the user. If the user already existed, the user is updated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>user</code> - user to save, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/RuntimeException.html?is-external=true" title="class or interface in java.lang">RuntimeException</a></code> - when a user with the same name already exists.</dd>
</dl>
</li>
</ul>
<a name="createUserQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserQuery</h4>
<pre><a href="../../../org/activiti/engine/identity/UserQuery.html" title="interface in org.activiti.engine.identity">UserQuery</a>&nbsp;createUserQuery()</pre>
<div class="block">Creates a <a href="../../../org/activiti/engine/identity/UserQuery.html" title="interface in org.activiti.engine.identity"><code>UserQuery</code></a> that allows to programmatically query the users.</div>
</li>
</ul>
<a name="createNativeUserQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeUserQuery</h4>
<pre><a href="../../../org/activiti/engine/identity/NativeUserQuery.html" title="interface in org.activiti.engine.identity">NativeUserQuery</a>&nbsp;createNativeUserQuery()</pre>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for tasks.</div>
</li>
</ul>
<a name="deleteUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteUser</h4>
<pre>void&nbsp;deleteUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>userId</code> - id of user to delete, cannot be null. When an id is passed
 for an unexisting user, this operation is ignored.</dd>
</dl>
</li>
</ul>
<a name="newGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newGroup</h4>
<pre><a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity">Group</a>&nbsp;newGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Creates a new group. The group is transient and must be saved using 
 <a href="../../../org/activiti/engine/IdentityService.html#saveGroup-org.activiti.engine.identity.Group-"><code>saveGroup(Group)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>groupId</code> - id for the new group, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="createGroupQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createGroupQuery</h4>
<pre><a href="../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;createGroupQuery()</pre>
<div class="block">Creates a <a href="../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity"><code>GroupQuery</code></a> thats allows to programmatically query the groups.</div>
</li>
</ul>
<a name="createNativeGroupQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeGroupQuery</h4>
<pre><a href="../../../org/activiti/engine/identity/NativeGroupQuery.html" title="interface in org.activiti.engine.identity">NativeGroupQuery</a>&nbsp;createNativeGroupQuery()</pre>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for tasks.</div>
</li>
</ul>
<a name="saveGroup-org.activiti.engine.identity.Group-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveGroup</h4>
<pre>void&nbsp;saveGroup(<a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity">Group</a>&nbsp;group)</pre>
<div class="block">Saves the group. If the group already existed, the group is updated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>group</code> - group to save. Cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/RuntimeException.html?is-external=true" title="class or interface in java.lang">RuntimeException</a></code> - when a group with the same name already exists.</dd>
</dl>
</li>
</ul>
<a name="deleteGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteGroup</h4>
<pre>void&nbsp;deleteGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Deletes the group. When no group exists with the given id, this operation
 is ignored.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>groupId</code> - id of the group that should be deleted, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="createMembership-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMembership</h4>
<pre>void&nbsp;createMembership(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>userId</code> - the userId, cannot be null.</dd>
<dd><code>groupId</code> - the groupId, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/RuntimeException.html?is-external=true" title="class or interface in java.lang">RuntimeException</a></code> - when the given user or group doesn't exist or when the user
 is already member of the group.</dd>
</dl>
</li>
</ul>
<a name="deleteMembership-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteMembership</h4>
<pre>void&nbsp;deleteMembership(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Delete the membership of the user in the group. When the group or user don't exist 
 or when the user is not a member of the group, this operation is ignored.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>userId</code> - the user's id, cannot be null.</dd>
<dd><code>groupId</code> - the group's id, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="checkPassword-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkPassword</h4>
<pre>boolean&nbsp;checkPassword(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;password)</pre>
<div class="block">Checks if the password is valid for the given user. Arguments userId
 and password are nullsafe.</div>
</li>
</ul>
<a name="setAuthenticatedUserId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAuthenticatedUserId</h4>
<pre>void&nbsp;setAuthenticatedUserId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;authenticatedUserId)</pre>
<div class="block">Passes the authenticated user id for this particular thread.
 All service method (from any service) invocations done by the same
 thread will have access to this authenticatedUserId.</div>
</li>
</ul>
<a name="setUserPicture-java.lang.String-org.activiti.engine.identity.Picture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUserPicture</h4>
<pre>void&nbsp;setUserPicture(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                    <a href="../../../org/activiti/engine/identity/Picture.html" title="class in org.activiti.engine.identity">Picture</a>&nbsp;picture)</pre>
<div class="block">Sets the picture for a given user.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>picture</code> - can be null to delete the picture.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if the user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="getUserPicture-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUserPicture</h4>
<pre><a href="../../../org/activiti/engine/identity/Picture.html" title="class in org.activiti.engine.identity">Picture</a>&nbsp;getUserPicture(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Retrieves the picture for a given user.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if the user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="setUserInfo-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUserInfo</h4>
<pre>void&nbsp;setUserInfo(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Generic extensibility key-value pairs associated with a user</div>
</li>
</ul>
<a name="getUserInfo-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUserInfo</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getUserInfo(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</pre>
<div class="block">Generic extensibility key-value pairs associated with a user</div>
</li>
</ul>
<a name="getUserInfoKeys-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUserInfoKeys</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getUserInfoKeys(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Generic extensibility keys associated with a user</div>
</li>
</ul>
<a name="deleteUserInfo-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>deleteUserInfo</h4>
<pre>void&nbsp;deleteUserInfo(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</pre>
<div class="block">Delete an entry of the generic extensibility key-value pairs associated with a user</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IdentityService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/JobNotFoundException.html" title="class in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/IdentityService.html" target="_top">Frames</a></li>
<li><a href="IdentityService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
