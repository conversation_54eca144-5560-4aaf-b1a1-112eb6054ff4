<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine.runtime (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.activiti.engine.runtime (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../org/activiti/engine/task/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.activiti.engine.runtime</h1>
<div class="docSummary">
<div class="block">Classes related to the <a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/Clock.html" title="interface in org.activiti.engine.runtime">Clock</a></td>
<td class="colLast">
<div class="block">This interface provides full access to the clock</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/ClockReader.html" title="interface in org.activiti.engine.runtime">ClockReader</a></td>
<td class="colLast">
<div class="block">This interface provides clock reading functionality</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime">Execution</a></td>
<td class="colLast">
<div class="block">Represent a 'path of execution' in a process instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/ExecutionQuery.html" title="interface in org.activiti.engine.runtime">ExecutionQuery</a></td>
<td class="colLast">
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime"><code>Execution</code></a>s.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/Job.html" title="interface in org.activiti.engine.runtime">Job</a></td>
<td class="colLast">
<div class="block">Represents one job (timer, message, etc.).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></td>
<td class="colLast">
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/runtime/Job.html" title="interface in org.activiti.engine.runtime"><code>Job</code></a>s.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/NativeExecutionQuery.html" title="interface in org.activiti.engine.runtime">NativeExecutionQuery</a></td>
<td class="colLast">
<div class="block">Allows querying of <a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime"><code>Execution</code></a>s via native (SQL) queries</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/NativeProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">NativeProcessInstanceQuery</a></td>
<td class="colLast">
<div class="block">Allows querying of <a href="../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s via native (SQL) queries</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></td>
<td class="colLast">
<div class="block">Represents one execution of a  <a href="../../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><code>ProcessDefinition</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></td>
<td class="colLast">
<div class="block">Helper for starting new ProcessInstance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></td>
<td class="colLast">
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package org.activiti.engine.runtime Description">Package org.activiti.engine.runtime Description</h2>
<div class="block">Classes related to the <a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../org/activiti/engine/task/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
