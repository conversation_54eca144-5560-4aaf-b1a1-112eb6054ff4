<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ScriptTaskPropertiesParser (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScriptTaskPropertiesParser (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScriptTaskPropertiesParser.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html" title="interface in org.activiti.engine.dynamic"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/dynamic/UserTaskPropertiesParser.html" title="class in org.activiti.engine.dynamic"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/dynamic/ScriptTaskPropertiesParser.html" target="_top">Frames</a></li>
<li><a href="ScriptTaskPropertiesParser.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.dynamic</div>
<h2 title="Class ScriptTaskPropertiesParser" class="title">Class ScriptTaskPropertiesParser</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html" title="class in org.activiti.engine.dynamic">org.activiti.engine.dynamic.BasePropertiesParser</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.dynamic.ScriptTaskPropertiesParser</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../org/activiti/engine/dynamic/PropertiesParser.html" title="interface in org.activiti.engine.dynamic">PropertiesParser</a>, <a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html" title="interface in org.activiti.engine.dynamic">PropertiesParserConstants</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine">DynamicBpmnConstants</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ScriptTaskPropertiesParser</span>
extends <a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html" title="class in org.activiti.engine.dynamic">BasePropertiesParser</a></pre>
<div class="block">Created by Pardo David on 5/12/2016.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.activiti.engine.DynamicBpmnConstants">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.activiti.engine.<a href="../../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine">DynamicBpmnConstants</a></h3>
<code><a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#BPMN_NODE">BPMN_NODE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_DESCRIPTION">LOCALIZATION_DESCRIPTION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_LANGUAGE">LOCALIZATION_LANGUAGE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_NAME">LOCALIZATION_NAME</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_NODE">LOCALIZATION_NODE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SCRIPT_TASK_SCRIPT">SCRIPT_TASK_SCRIPT</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SEQUENCE_FLOW_CONDITION">SEQUENCE_FLOW_CONDITION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_CLASS_NAME">SERVICE_TASK_CLASS_NAME</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_DELEGATE_EXPRESSION">SERVICE_TASK_DELEGATE_EXPRESSION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_EXPRESSION">SERVICE_TASK_EXPRESSION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#TASK_SKIP_EXPRESSION">TASK_SKIP_EXPRESSION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_ASSIGNEE">USER_TASK_ASSIGNEE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_GROUPS">USER_TASK_CANDIDATE_GROUPS</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS">USER_TASK_CANDIDATE_USERS</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CATEGORY">USER_TASK_CATEGORY</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_DESCRIPTION">USER_TASK_DESCRIPTION</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_DUEDATE">USER_TASK_DUEDATE</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_FORM_KEY">USER_TASK_FORM_KEY</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_NAME">USER_TASK_NAME</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_OWNER">USER_TASK_OWNER</a>, <a href="../../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_PRIORITY">USER_TASK_PRIORITY</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.activiti.engine.dynamic.PropertiesParserConstants">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.activiti.engine.dynamic.<a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html" title="interface in org.activiti.engine.dynamic">PropertiesParserConstants</a></h3>
<code><a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html#BPMN_MODEL_VALUE">BPMN_MODEL_VALUE</a>, <a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html#DYNAMIC_VALUE">DYNAMIC_VALUE</a>, <a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html#ELEMENT_ID">ELEMENT_ID</a>, <a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html#ELEMENT_PROPERTIES">ELEMENT_PROPERTIES</a>, <a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html#ELEMENT_TYPE">ELEMENT_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/dynamic/ScriptTaskPropertiesParser.html#ScriptTaskPropertiesParser--">ScriptTaskPropertiesParser</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/dynamic/ScriptTaskPropertiesParser.html#createPropertiesNode-org.activiti.bpmn.model.FlowElement-com.fasterxml.jackson.databind.node.ObjectNode-com.fasterxml.jackson.databind.ObjectMapper-">createPropertiesNode</a></span>(org.activiti.bpmn.model.FlowElement&nbsp;flowElement,
                    com.fasterxml.jackson.databind.node.ObjectNode&nbsp;flowElementNode,
                    com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/dynamic/ScriptTaskPropertiesParser.html#supports-org.activiti.bpmn.model.FlowElement-">supports</a></span>(org.activiti.bpmn.model.FlowElement&nbsp;flowElement)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.dynamic.BasePropertiesParser">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.activiti.engine.dynamic.<a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html" title="class in org.activiti.engine.dynamic">BasePropertiesParser</a></h3>
<code><a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html#parseElement-org.activiti.bpmn.model.FlowElement-com.fasterxml.jackson.databind.node.ObjectNode-com.fasterxml.jackson.databind.ObjectMapper-">parseElement</a>, <a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html#putPropertyValue-java.lang.String-com.fasterxml.jackson.databind.JsonNode-com.fasterxml.jackson.databind.node.ObjectNode-">putPropertyValue</a>, <a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html#putPropertyValue-java.lang.String-java.util.List-com.fasterxml.jackson.databind.node.ObjectNode-">putPropertyValue</a>, <a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html#putPropertyValue-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">putPropertyValue</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScriptTaskPropertiesParser--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScriptTaskPropertiesParser</h4>
<pre>public&nbsp;ScriptTaskPropertiesParser()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createPropertiesNode-org.activiti.bpmn.model.FlowElement-com.fasterxml.jackson.databind.node.ObjectNode-com.fasterxml.jackson.databind.ObjectMapper-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPropertiesNode</h4>
<pre>protected&nbsp;com.fasterxml.jackson.databind.node.ObjectNode&nbsp;createPropertiesNode(org.activiti.bpmn.model.FlowElement&nbsp;flowElement,
                                                                              com.fasterxml.jackson.databind.node.ObjectNode&nbsp;flowElementNode,
                                                                              com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html#createPropertiesNode-org.activiti.bpmn.model.FlowElement-com.fasterxml.jackson.databind.node.ObjectNode-com.fasterxml.jackson.databind.ObjectMapper-">createPropertiesNode</a></code>&nbsp;in class&nbsp;<code><a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html" title="class in org.activiti.engine.dynamic">BasePropertiesParser</a></code></dd>
</dl>
</li>
</ul>
<a name="supports-org.activiti.bpmn.model.FlowElement-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>supports</h4>
<pre>public&nbsp;boolean&nbsp;supports(org.activiti.bpmn.model.FlowElement&nbsp;flowElement)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/dynamic/PropertiesParser.html#supports-org.activiti.bpmn.model.FlowElement-">supports</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../org/activiti/engine/dynamic/PropertiesParser.html" title="interface in org.activiti.engine.dynamic">PropertiesParser</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html#supports-org.activiti.bpmn.model.FlowElement-">supports</a></code>&nbsp;in class&nbsp;<code><a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html" title="class in org.activiti.engine.dynamic">BasePropertiesParser</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScriptTaskPropertiesParser.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html" title="interface in org.activiti.engine.dynamic"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/dynamic/UserTaskPropertiesParser.html" title="class in org.activiti.engine.dynamic"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/dynamic/ScriptTaskPropertiesParser.html" target="_top">Frames</a></li>
<li><a href="ScriptTaskPropertiesParser.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
