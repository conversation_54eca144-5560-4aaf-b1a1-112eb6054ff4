# Controls Module Flowable Integration

## Overview

The Controls Module integration with Flowable automates control implementation, testing, and continuous monitoring workflows. This integration provides end-to-end automation for control lifecycle management, from initial implementation through ongoing assessment and optimization.

## Integration Architecture

### Control Management Workflow Patterns

#### 1. Control Implementation Workflow
Automated process for implementing security controls with validation and testing.

#### 2. Control Testing Workflow
Comprehensive testing of control effectiveness with automated and manual validation.

#### 3. Control Monitoring Workflow
Continuous monitoring of control performance with automated alerting and remediation.

#### 4. Control Optimization Workflow
AI-driven optimization of control configurations based on performance data.

## Control Service Integration

### Service Task Implementation

#### ControlManagerTask.java
```java
@Component("controlManagerTask")
public class ControlManagerTask extends AbstractGRCOSServiceTask {
    
    @Autowired
    private ControlService controlService;
    
    @Autowired
    private OSCALService oscalService;
    
    @Autowired
    private AssessmentAgent assessmentAgent;
    
    @Override
    protected void validateExecution(DelegateExecution execution) throws ValidationException {
        String operation = (String) execution.getVariable("controlOperation");
        String controlId = (String) execution.getVariable("controlId");
        
        if (operation == null || operation.trim().isEmpty()) {
            throw new ValidationException("Control operation is required");
        }
        
        if (controlId == null || controlId.trim().isEmpty()) {
            throw new ValidationException("Control ID is required");
        }
        
        if (!isValidControlOperation(operation)) {
            throw new ValidationException("Invalid control operation: " + operation);
        }
    }
    
    @Override
    protected TaskExecutionResult executeTask(DelegateExecution execution) throws TaskExecutionException {
        String operation = (String) execution.getVariable("controlOperation");
        
        try {
            switch (operation) {
                case "implement_control":
                    return implementControl(execution);
                case "test_control":
                    return testControl(execution);
                case "monitor_control":
                    return monitorControl(execution);
                case "optimize_control":
                    return optimizeControl(execution);
                case "assess_effectiveness":
                    return assessControlEffectiveness(execution);
                case "update_implementation":
                    return updateControlImplementation(execution);
                default:
                    throw new TaskExecutionException("Unsupported control operation: " + operation);
            }
        } catch (Exception e) {
            throw new TaskExecutionException("Control operation failed", e);
        }
    }
    
    private TaskExecutionResult implementControl(DelegateExecution execution) {
        String controlId = (String) execution.getVariable("controlId");
        String systemId = (String) execution.getVariable("systemId");
        Map<String, Object> implementationParams = (Map<String, Object>) 
            execution.getVariable("implementationParameters");
        
        // Get control definition from OSCAL
        OSCALControl control = oscalService.getControl(controlId);
        
        // Get system component information
        OSCALComponent component = oscalService.getComponent(systemId);
        
        // Create implementation plan
        ControlImplementationPlan plan = assessmentAgent.createImplementationPlan(
            control, component, implementationParams);
        
        // Execute implementation steps
        List<ImplementationResult> results = new ArrayList<>();
        for (ImplementationStep step : plan.getSteps()) {
            ImplementationResult result = executeImplementationStep(step, component);
            results.add(result);
            
            if (!result.isSuccess()) {
                break; // Stop on first failure
            }
        }
        
        // Validate implementation
        ImplementationValidationResult validation = validateControlImplementation(
            control, component, results);
        
        // Create OSCAL implementation record
        OSCALImplementedRequirement implementation = oscalService.createImplementedRequirement(
            control, component, validation);
        
        // Update control status
        controlService.updateControlStatus(controlId, systemId, 
            validation.isValid() ? "implemented" : "implementation-failed");
        
        Map<String, Object> taskResults = new HashMap<>();
        taskResults.put("controlId", controlId);
        taskResults.put("systemId", systemId);
        taskResults.put("implementationId", implementation.getUuid());
        taskResults.put("implementationValid", validation.isValid());
        taskResults.put("implementationSteps", results.size());
        taskResults.put("successfulSteps", results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum());
        taskResults.put("validationResults", validation.toMap());
        
        return TaskExecutionResult.success(taskResults);
    }
    
    private TaskExecutionResult testControl(DelegateExecution execution) {
        String controlId = (String) execution.getVariable("controlId");
        String systemId = (String) execution.getVariable("systemId");
        String testType = (String) execution.getVariable("testType");
        
        // Get control and implementation details
        OSCALControl control = oscalService.getControl(controlId);
        OSCALImplementedRequirement implementation = oscalService.getImplementedRequirement(
            controlId, systemId);
        
        // Determine test procedures
        List<TestProcedure> procedures = assessmentAgent.determineTestProcedures(
            control, implementation, testType);
        
        // Execute test procedures
        List<TestResult> testResults = new ArrayList<>();
        for (TestProcedure procedure : procedures) {
            TestResult result = executeTestProcedure(procedure, control, implementation);
            testResults.add(result);
        }
        
        // Analyze test results
        ControlTestAnalysis analysis = assessmentAgent.analyzeTestResults(
            control, testResults);
        
        // Generate test report
        ControlTestReport report = generateControlTestReport(
            control, implementation, testResults, analysis);
        
        // Update control test status
        controlService.updateControlTestStatus(controlId, systemId, analysis);
        
        // Create OSCAL observation
        OSCALObservation observation = oscalService.createObservation(
            control, testResults, analysis);
        
        Map<String, Object> taskResults = new HashMap<>();
        taskResults.put("controlId", controlId);
        taskResults.put("systemId", systemId);
        taskResults.put("testType", testType);
        taskResults.put("testsPassed", analysis.getPassedTests());
        taskResults.put("testsFailed", analysis.getFailedTests());
        taskResults.put("overallResult", analysis.getOverallResult());
        taskResults.put("effectiveness", analysis.getEffectivenessScore());
        taskResults.put("reportId", report.getId());
        taskResults.put("observationUuid", observation.getUuid());
        
        return TaskExecutionResult.success(taskResults);
    }
    
    private TaskExecutionResult monitorControl(DelegateExecution execution) {
        String controlId = (String) execution.getVariable("controlId");
        String systemId = (String) execution.getVariable("systemId");
        
        // Get control monitoring configuration
        ControlMonitoringConfig config = controlService.getMonitoringConfig(controlId, systemId);
        
        // Collect monitoring data
        ControlMonitoringData data = collectControlMonitoringData(config);
        
        // Analyze monitoring data
        ControlMonitoringAnalysis analysis = assessmentAgent.analyzeMonitoringData(
            controlId, data);
        
        // Check for anomalies or degradation
        List<ControlAnomaly> anomalies = assessmentAgent.detectControlAnomalies(
            controlId, data, analysis);
        
        // Update control health status
        ControlHealthStatus healthStatus = calculateControlHealth(analysis, anomalies);
        controlService.updateControlHealth(controlId, systemId, healthStatus);
        
        // Generate alerts if necessary
        List<ControlAlert> alerts = generateControlAlerts(anomalies, healthStatus);
        
        Map<String, Object> taskResults = new HashMap<>();
        taskResults.put("controlId", controlId);
        taskResults.put("systemId", systemId);
        taskResults.put("healthStatus", healthStatus.getStatus());
        taskResults.put("healthScore", healthStatus.getScore());
        taskResults.put("anomaliesDetected", anomalies.size());
        taskResults.put("alertsGenerated", alerts.size());
        taskResults.put("monitoringTimestamp", System.currentTimeMillis());
        
        return TaskExecutionResult.success(taskResults);
    }
    
    private TaskExecutionResult assessControlEffectiveness(DelegateExecution execution) {
        String controlId = (String) execution.getVariable("controlId");
        String systemId = (String) execution.getVariable("systemId");
        
        // Get control implementation and historical data
        OSCALImplementedRequirement implementation = oscalService.getImplementedRequirement(
            controlId, systemId);
        ControlHistoricalData historicalData = controlService.getHistoricalData(
            controlId, systemId);
        
        // Perform effectiveness assessment
        ControlEffectivenessAssessment assessment = assessmentAgent.assessControlEffectiveness(
            controlId, implementation, historicalData);
        
        // Generate effectiveness metrics
        ControlEffectivenessMetrics metrics = calculateEffectivenessMetrics(assessment);
        
        // Create recommendations for improvement
        List<ControlImprovement> improvements = assessmentAgent.recommendControlImprovements(
            assessment, metrics);
        
        // Update control effectiveness rating
        controlService.updateEffectivenessRating(controlId, systemId, 
            assessment.getEffectivenessRating());
        
        // Create OSCAL finding if effectiveness is below threshold
        OSCALFinding finding = null;
        if (assessment.getEffectivenessRating() < 0.7) {
            finding = oscalService.createEffectivenessFinding(
                controlId, assessment, improvements);
        }
        
        Map<String, Object> taskResults = new HashMap<>();
        taskResults.put("controlId", controlId);
        taskResults.put("systemId", systemId);
        taskResults.put("effectivenessRating", assessment.getEffectivenessRating());
        taskResults.put("effectivenessLevel", assessment.getEffectivenessLevel());
        taskResults.put("metrics", metrics.toMap());
        taskResults.put("improvements", improvements.size());
        taskResults.put("findingCreated", finding != null);
        if (finding != null) {
            taskResults.put("findingUuid", finding.getUuid());
        }
        
        return TaskExecutionResult.success(taskResults);
    }
    
    private ImplementationResult executeImplementationStep(ImplementationStep step, 
                                                          OSCALComponent component) {
        try {
            switch (step.getType()) {
                case "CONFIGURATION":
                    return executeConfigurationStep(step, component);
                case "DEPLOYMENT":
                    return executeDeploymentStep(step, component);
                case "INTEGRATION":
                    return executeIntegrationStep(step, component);
                case "VALIDATION":
                    return executeValidationStep(step, component);
                default:
                    throw new UnsupportedOperationException("Unknown step type: " + step.getType());
            }
        } catch (Exception e) {
            return ImplementationResult.failed(step, e.getMessage());
        }
    }
    
    private TestResult executeTestProcedure(TestProcedure procedure, OSCALControl control,
                                          OSCALImplementedRequirement implementation) {
        try {
            switch (procedure.getType()) {
                case "AUTOMATED":
                    return executeAutomatedTest(procedure, control, implementation);
                case "MANUAL":
                    return executeManualTest(procedure, control, implementation);
                case "INTERVIEW":
                    return executeInterviewTest(procedure, control, implementation);
                case "EXAMINE":
                    return executeExaminationTest(procedure, control, implementation);
                default:
                    throw new UnsupportedOperationException("Unknown test type: " + procedure.getType());
            }
        } catch (Exception e) {
            return TestResult.failed(procedure, e.getMessage());
        }
    }
}
```

### Control Implementation Workflow

#### control-implementation.bpmn20.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://grcos.com/workflows">
  
  <process id="control-implementation" name="Control Implementation Workflow" isExecutable="true">
    
    <!-- Start Event -->
    <startEvent id="start" name="Control Implementation Request">
      <extensionElements>
        <flowable:formProperty id="controlId" name="Control ID" type="string" required="true"/>
        <flowable:formProperty id="systemId" name="System ID" type="string" required="true"/>
        <flowable:formProperty id="implementationApproach" name="Implementation Approach" type="enum" required="true">
          <flowable:value id="automated" name="Automated"/>
          <flowable:value id="manual" name="Manual"/>
          <flowable:value id="hybrid" name="Hybrid"/>
        </flowable:formProperty>
        <flowable:formProperty id="priority" name="Priority" type="enum" required="true">
          <flowable:value id="low" name="Low"/>
          <flowable:value id="medium" name="Medium"/>
          <flowable:value id="high" name="High"/>
          <flowable:value id="critical" name="Critical"/>
        </flowable:formProperty>
      </extensionElements>
    </startEvent>
    
    <!-- Design Implementation -->
    <userTask id="design-implementation" name="Design Control Implementation"
              flowable:candidateGroups="security-engineers">
      <documentation>Design the technical implementation for the control</documentation>
      <extensionElements>
        <flowable:formProperty id="implementationDesign" name="Implementation Design" type="string" required="true"/>
        <flowable:formProperty id="technicalRequirements" name="Technical Requirements" type="string" required="true"/>
        <flowable:formProperty id="dependencies" name="Dependencies" type="string"/>
        <flowable:formProperty id="riskAssessment" name="Risk Assessment" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Review Implementation Design -->
    <userTask id="review-design" name="Review Implementation Design"
              flowable:candidateGroups="security-architects">
      <documentation>Review and approve the implementation design</documentation>
      <extensionElements>
        <flowable:formProperty id="reviewDecision" name="Review Decision" type="enum" required="true">
          <flowable:value id="approved" name="Approved"/>
          <flowable:value id="rejected" name="Rejected"/>
          <flowable:value id="needs-revision" name="Needs Revision"/>
        </flowable:formProperty>
        <flowable:formProperty id="reviewComments" name="Review Comments" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Review Decision Gateway -->
    <exclusiveGateway id="review-gateway" name="Design Review Decision"/>
    
    <!-- Implement Control -->
    <serviceTask id="implement-control" name="Implement Control"
                 flowable:class="com.grcos.workflow.ControlManagerTask">
      <extensionElements>
        <flowable:field name="controlOperation">
          <flowable:string>implement_control</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Implementation Success Gateway -->
    <exclusiveGateway id="implementation-gateway" name="Implementation Success"/>
    
    <!-- Test Control Implementation -->
    <serviceTask id="test-implementation" name="Test Control Implementation"
                 flowable:class="com.grcos.workflow.ControlManagerTask">
      <extensionElements>
        <flowable:field name="controlOperation">
          <flowable:string>test_control</flowable:string>
        </flowable:field>
        <flowable:field name="testType">
          <flowable:string>implementation</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Test Results Gateway -->
    <exclusiveGateway id="test-gateway" name="Test Results"/>
    
    <!-- Document Implementation -->
    <serviceTask id="document-implementation" name="Document Implementation"
                 flowable:class="com.grcos.workflow.DocumentationTask">
      <extensionElements>
        <flowable:field name="documentationType">
          <flowable:string>control-implementation</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Update OSCAL SSP -->
    <serviceTask id="update-ssp" name="Update System Security Plan"
                 flowable:class="com.grcos.workflow.OSCALUpdateTask">
      <extensionElements>
        <flowable:field name="updateType">
          <flowable:string>implemented-requirement</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Setup Monitoring -->
    <serviceTask id="setup-monitoring" name="Setup Control Monitoring"
                 flowable:class="com.grcos.workflow.MonitoringSetupTask"/>
    
    <!-- Notify Stakeholders -->
    <serviceTask id="notify-stakeholders" name="Notify Stakeholders"
                 flowable:class="com.grcos.workflow.NotificationTask">
      <extensionElements>
        <flowable:field name="notificationType">
          <flowable:string>control-implementation-complete</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Remediation Task -->
    <userTask id="remediate-issues" name="Remediate Implementation Issues"
              flowable:candidateGroups="security-engineers">
      <documentation>Address issues found during implementation or testing</documentation>
      <extensionElements>
        <flowable:formProperty id="issuesFound" name="Issues Found" type="string" required="true"/>
        <flowable:formProperty id="remediationActions" name="Remediation Actions" type="string" required="true"/>
        <flowable:formProperty id="retestRequired" name="Retest Required" type="boolean" required="true"/>
      </extensionElements>
    </userTask>
    
    <!-- End Events -->
    <endEvent id="implementation-complete" name="Implementation Complete"/>
    <endEvent id="implementation-failed" name="Implementation Failed"/>
    
    <!-- Sequence Flows -->
    <sequenceFlow id="flow1" sourceRef="start" targetRef="design-implementation"/>
    <sequenceFlow id="flow2" sourceRef="design-implementation" targetRef="review-design"/>
    <sequenceFlow id="flow3" sourceRef="review-design" targetRef="review-gateway"/>
    
    <!-- Review Decision Flows -->
    <sequenceFlow id="flow4" sourceRef="review-gateway" targetRef="implement-control">
      <conditionExpression>${reviewDecision == 'approved'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow5" sourceRef="review-gateway" targetRef="design-implementation">
      <conditionExpression>${reviewDecision == 'needs-revision'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow6" sourceRef="review-gateway" targetRef="implementation-failed">
      <conditionExpression>${reviewDecision == 'rejected'}</conditionExpression>
    </sequenceFlow>
    
    <!-- Implementation Flows -->
    <sequenceFlow id="flow7" sourceRef="implement-control" targetRef="implementation-gateway"/>
    <sequenceFlow id="flow8" sourceRef="implementation-gateway" targetRef="test-implementation">
      <conditionExpression>${implementationValid == true}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow9" sourceRef="implementation-gateway" targetRef="remediate-issues">
      <conditionExpression>${implementationValid == false}</conditionExpression>
    </sequenceFlow>
    
    <!-- Test Flows -->
    <sequenceFlow id="flow10" sourceRef="test-implementation" targetRef="test-gateway"/>
    <sequenceFlow id="flow11" sourceRef="test-gateway" targetRef="document-implementation">
      <conditionExpression>${overallResult == 'pass'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow12" sourceRef="test-gateway" targetRef="remediate-issues">
      <conditionExpression>${overallResult == 'fail'}</conditionExpression>
    </sequenceFlow>
    
    <!-- Remediation Flows -->
    <sequenceFlow id="flow13" sourceRef="remediate-issues" targetRef="test-implementation">
      <conditionExpression>${retestRequired == true}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow14" sourceRef="remediate-issues" targetRef="implement-control">
      <conditionExpression>${retestRequired == false}</conditionExpression>
    </sequenceFlow>
    
    <!-- Completion Flows -->
    <sequenceFlow id="flow15" sourceRef="document-implementation" targetRef="update-ssp"/>
    <sequenceFlow id="flow16" sourceRef="update-ssp" targetRef="setup-monitoring"/>
    <sequenceFlow id="flow17" sourceRef="setup-monitoring" targetRef="notify-stakeholders"/>
    <sequenceFlow id="flow18" sourceRef="notify-stakeholders" targetRef="implementation-complete"/>
    
  </process>
</definitions>
```

### Control Testing Workflow

#### control-testing.bpmn20.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://grcos.com/workflows">
  
  <process id="control-testing" name="Control Testing Workflow" isExecutable="true">
    
    <!-- Timer Start Event - Scheduled Testing -->
    <startEvent id="scheduled-test" name="Scheduled Control Test">
      <timerEventDefinition>
        <timeCycle>R/P30D</timeCycle> <!-- Every 30 days -->
      </timerEventDefinition>
    </startEvent>
    
    <!-- Get Controls for Testing -->
    <serviceTask id="get-controls" name="Get Controls for Testing"
                 flowable:class="com.grcos.workflow.ControlDiscoveryTask">
      <extensionElements>
        <flowable:field name="testingDue">
          <flowable:string>true</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Multi-Instance Testing -->
    <subProcess id="test-controls" name="Test Controls">
      <multiInstanceLoopCharacteristics isSequential="false" 
                                       flowable:collection="controlsToTest" 
                                       flowable:elementVariable="currentControl"/>
      
      <!-- Determine Test Type -->
      <serviceTask id="determine-test-type" name="Determine Test Type"
                   flowable:class="com.grcos.workflow.TestTypeDecisionTask"/>
      
      <!-- Test Type Gateway -->
      <exclusiveGateway id="test-type-gateway" name="Test Type"/>
      
      <!-- Automated Testing -->
      <serviceTask id="automated-test" name="Automated Control Test"
                   flowable:class="com.grcos.workflow.ControlManagerTask">
        <extensionElements>
          <flowable:field name="controlOperation">
            <flowable:string>test_control</flowable:string>
          </flowable:field>
          <flowable:field name="testType">
            <flowable:string>automated</flowable:string>
          </flowable:field>
        </extensionElements>
      </serviceTask>
      
      <!-- Manual Testing -->
      <userTask id="manual-test" name="Manual Control Test"
                flowable:candidateGroups="security-testers">
        <documentation>Perform manual testing of the control</documentation>
        <extensionElements>
          <flowable:formProperty id="testProcedure" name="Test Procedure" type="string" required="true"/>
          <flowable:formProperty id="testResults" name="Test Results" type="string" required="true"/>
          <flowable:formProperty id="testOutcome" name="Test Outcome" type="enum" required="true">
            <flowable:value id="pass" name="Pass"/>
            <flowable:value id="fail" name="Fail"/>
            <flowable:value id="inconclusive" name="Inconclusive"/>
          </flowable:formProperty>
          <flowable:formProperty id="evidence" name="Evidence" type="string"/>
        </extensionElements>
      </userTask>
      
      <!-- Join Gateway -->
      <exclusiveGateway id="test-join" name="Test Complete"/>
      
      <!-- Assess Effectiveness -->
      <serviceTask id="assess-effectiveness" name="Assess Control Effectiveness"
                   flowable:class="com.grcos.workflow.ControlManagerTask">
        <extensionElements>
          <flowable:field name="controlOperation">
            <flowable:string>assess_effectiveness</flowable:string>
          </flowable:field>
        </extensionElements>
      </serviceTask>
      
      <!-- Subprocess Flows -->
      <sequenceFlow id="sub-flow1" sourceRef="determine-test-type" targetRef="test-type-gateway"/>
      <sequenceFlow id="sub-flow2" sourceRef="test-type-gateway" targetRef="automated-test">
        <conditionExpression>${testType == 'automated'}</conditionExpression>
      </sequenceFlow>
      <sequenceFlow id="sub-flow3" sourceRef="test-type-gateway" targetRef="manual-test">
        <conditionExpression>${testType == 'manual'}</conditionExpression>
      </sequenceFlow>
      <sequenceFlow id="sub-flow4" sourceRef="automated-test" targetRef="test-join"/>
      <sequenceFlow id="sub-flow5" sourceRef="manual-test" targetRef="test-join"/>
      <sequenceFlow id="sub-flow6" sourceRef="test-join" targetRef="assess-effectiveness"/>
      
    </subProcess>
    
    <!-- Generate Testing Report -->
    <serviceTask id="generate-report" name="Generate Testing Report"
                 flowable:class="com.grcos.workflow.TestingReportGeneratorTask"/>
    
    <!-- Identify Failed Controls -->
    <serviceTask id="identify-failures" name="Identify Failed Controls"
                 flowable:class="com.grcos.workflow.FailureAnalysisTask"/>
    
    <!-- Failure Gateway -->
    <exclusiveGateway id="failure-gateway" name="Failures Found?"/>
    
    <!-- Create Remediation Tasks -->
    <serviceTask id="create-remediation" name="Create Remediation Tasks"
                 flowable:class="com.grcos.workflow.RemediationTaskCreatorTask"/>
    
    <!-- Update Control Status -->
    <serviceTask id="update-status" name="Update Control Status"
                 flowable:class="com.grcos.workflow.ControlStatusUpdateTask"/>
    
    <!-- End Event -->
    <endEvent id="testing-complete" name="Control Testing Complete"/>
    
    <!-- Main Process Flows -->
    <sequenceFlow id="flow1" sourceRef="scheduled-test" targetRef="get-controls"/>
    <sequenceFlow id="flow2" sourceRef="get-controls" targetRef="test-controls"/>
    <sequenceFlow id="flow3" sourceRef="test-controls" targetRef="generate-report"/>
    <sequenceFlow id="flow4" sourceRef="generate-report" targetRef="identify-failures"/>
    <sequenceFlow id="flow5" sourceRef="identify-failures" targetRef="failure-gateway"/>
    <sequenceFlow id="flow6" sourceRef="failure-gateway" targetRef="create-remediation">
      <conditionExpression>${failuresFound == true}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow7" sourceRef="failure-gateway" targetRef="update-status">
      <conditionExpression>${failuresFound == false}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow8" sourceRef="create-remediation" targetRef="update-status"/>
    <sequenceFlow id="flow9" sourceRef="update-status" targetRef="testing-complete"/>
    
  </process>
</definitions>
```

## Control Performance Analytics

### Control Metrics Service

```java
@Service
public class ControlMetricsService {
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    private final Counter controlImplementationCounter;
    private final Timer controlTestingTimer;
    private final Gauge controlEffectivenessGauge;
    
    public ControlMetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.controlImplementationCounter = Counter.builder("grcos.controls.implementations.total")
            .description("Total number of control implementations")
            .register(meterRegistry);
        this.controlTestingTimer = Timer.builder("grcos.controls.testing.duration")
            .description("Control testing duration")
            .register(meterRegistry);
        this.controlEffectivenessGauge = Gauge.builder("grcos.controls.effectiveness.average")
            .description("Average control effectiveness score")
            .register(meterRegistry, this, ControlMetricsService::getAverageEffectiveness);
    }
    
    public void recordControlImplementation(String controlId, String status) {
        controlImplementationCounter.increment(
            Tags.of("controlId", controlId, "status", status)
        );
    }
    
    public void recordControlTesting(String controlId, Duration duration, String result) {
        controlTestingTimer.record(duration, 
            Tags.of("controlId", controlId, "result", result)
        );
    }
    
    private double getAverageEffectiveness() {
        return controlService.getAverageEffectivenessScore();
    }
}
```

This Controls Module integration provides comprehensive automation for control lifecycle management with continuous monitoring and AI-driven optimization capabilities.
