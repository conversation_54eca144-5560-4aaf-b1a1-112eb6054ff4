update ACT_GE_PROPERTY set VALUE_ = '6.3.1.0' where NAME_ = 'identitylink.schema.version';

update ACT_GE_PROPERTY set VALUE_ = '6.3.1.0' where NAME_ = 'task.schema.version';

update ACT_GE_PROPERTY set VALUE_ = '6.3.1.0' where NAME_ = 'variable.schema.version';

alter table ACT_RU_HISTORY_JOB add SCOPE_TYPE_ nvarchar(255);

update ACT_GE_PROPERTY set VALUE_ = '6.3.1.0' where NAME_ = 'job.schema.version';
alter table ACT_RE_DEPLOYMENT add PARENT_DEPLOYMENT_ID_ varchar(255);

update ACT_GE_PROPERTY set VALUE_ = '6.3.1.0' where NAME_ = 'schema.version';

update ACT_ID_PROPERTY set VALUE_ = '6.3.1.0' where NAME_ = 'schema.version';

CREATE TABLE [ACT_APP_DATABASECHANGELOGLOCK] ([ID] [int] NOT NULL, [LOCKED] [bit] NOT NULL, [LOCKGRANTED] [datetime2](3), [LOCKEDBY] [nvarchar](255), CONSTRAINT [PK_ACT_APP_DATABASECHANGELOGLOCK] PRIMARY KEY ([ID]))

DELETE FROM [ACT_APP_DATABASECHANGELOGLOCK]

INSERT INTO [ACT_APP_DATABASECHANGELOGLOCK] ([ID], [LOCKED]) VALUES (1, 0)

UPDATE [ACT_APP_DATABASECHANGELOGLOCK] SET [LOCKED] = 1, [LOCKEDBY] = '*********** (***********)', [LOCKGRANTED] = '2019-03-13T21:42:47.881' WHERE [ID] = 1 AND [LOCKED] = 0

CREATE TABLE [ACT_APP_DATABASECHANGELOG] ([ID] [nvarchar](255) NOT NULL, [AUTHOR] [nvarchar](255) NOT NULL, [FILENAME] [nvarchar](255) NOT NULL, [DATEEXECUTED] [datetime2](3) NOT NULL, [ORDEREXECUTED] [int] NOT NULL, [EXECTYPE] [nvarchar](10) NOT NULL, [MD5SUM] [nvarchar](35), [DESCRIPTION] [nvarchar](255), [COMMENTS] [nvarchar](255), [TAG] [nvarchar](255), [LIQUIBASE] [nvarchar](20), [CONTEXTS] [nvarchar](255), [LABELS] [nvarchar](255), [DEPLOYMENT_ID] [nvarchar](10))

CREATE TABLE [ACT_APP_DEPLOYMENT] ([ID_] [varchar](255) NOT NULL, [NAME_] [varchar](255), [CATEGORY_] [varchar](255), [KEY_] [varchar](255), [DEPLOY_TIME_] [datetime], [TENANT_ID_] [varchar](255) CONSTRAINT [DF_ACT_APP_DEPLOYMENT_TENANT_ID_] DEFAULT '', CONSTRAINT [PK_ACT_APP_DEPLOYMENT] PRIMARY KEY ([ID_]))

CREATE TABLE [ACT_APP_DEPLOYMENT_RESOURCE] ([ID_] [varchar](255) NOT NULL, [NAME_] [varchar](255), [DEPLOYMENT_ID_] [varchar](255), [RESOURCE_BYTES_] [varbinary](MAX), CONSTRAINT [PK_APP_DEPLOYMENT_RESOURCE] PRIMARY KEY ([ID_]))

ALTER TABLE [ACT_APP_DEPLOYMENT_RESOURCE] ADD CONSTRAINT [ACT_FK_APP_RSRC_DPL] FOREIGN KEY ([DEPLOYMENT_ID_]) REFERENCES [ACT_APP_DEPLOYMENT] ([ID_])

CREATE NONCLUSTERED INDEX ACT_IDX_APP_RSRC_DPL ON [ACT_APP_DEPLOYMENT_RESOURCE]([DEPLOYMENT_ID_])

CREATE TABLE [ACT_APP_APPDEF] ([ID_] [varchar](255) NOT NULL, [REV_] [int] NOT NULL, [NAME_] [varchar](255), [KEY_] [varchar](255) NOT NULL, [VERSION_] [int] NOT NULL, [CATEGORY_] [varchar](255), [DEPLOYMENT_ID_] [varchar](255), [RESOURCE_NAME_] [varchar](4000), [DESCRIPTION_] [varchar](4000), [TENANT_ID_] [varchar](255) CONSTRAINT [DF_ACT_APP_APPDEF_TENANT_ID_] DEFAULT '', CONSTRAINT [PK_ACT_APP_APPDEF] PRIMARY KEY ([ID_]))

ALTER TABLE [ACT_APP_APPDEF] ADD CONSTRAINT [ACT_FK_APP_DEF_DPLY] FOREIGN KEY ([DEPLOYMENT_ID_]) REFERENCES [ACT_APP_DEPLOYMENT] ([ID_])

CREATE NONCLUSTERED INDEX ACT_IDX_APP_DEF_DPLY ON [ACT_APP_APPDEF]([DEPLOYMENT_ID_])

INSERT INTO [ACT_APP_DATABASECHANGELOG] ([ID], [AUTHOR], [FILENAME], [DATEEXECUTED], [ORDEREXECUTED], [MD5SUM], [DESCRIPTION], [COMMENTS], [EXECTYPE], [CONTEXTS], [LABELS], [LIQUIBASE], [DEPLOYMENT_ID]) VALUES ('1', 'flowable', 'org/flowable/app/db/liquibase/flowable-app-db-changelog.xml', GETDATE(), 1, '7:ec9776f6c57a3953c7d27499108df3d1', 'createTable tableName=ACT_APP_DEPLOYMENT; createTable tableName=ACT_APP_DEPLOYMENT_RESOURCE; addForeignKeyConstraint baseTableName=ACT_APP_DEPLOYMENT_RESOURCE, constraintName=ACT_FK_APP_RSRC_DPL, referencedTableName=ACT_APP_DEPLOYMENT; createIndex...', '', 'EXECUTED', NULL, NULL, '3.5.3', '2509768357')

UPDATE [ACT_APP_DATABASECHANGELOGLOCK] SET [LOCKED] = 0, [LOCKEDBY] = NULL, [LOCKGRANTED] = NULL WHERE [ID] = 1



UPDATE [ACT_CMMN_DATABASECHANGELOGLOCK] SET [LOCKED] = 1, [LOCKEDBY] = '*********** (***********)', [LOCKGRANTED] = '2019-03-13T21:42:54.868' WHERE [ID] = 1 AND [LOCKED] = 0

CREATE TABLE [ACT_CMMN_HI_PLAN_ITEM_INST] ([ID_] [varchar](255) NOT NULL, [REV_] [int] NOT NULL, [NAME_] [varchar](255), [STATE_] [varchar](255), [CASE_DEF_ID_] [varchar](255), [CASE_INST_ID_] [varchar](255), [STAGE_INST_ID_] [varchar](255), [IS_STAGE_] [bit], [ELEMENT_ID_] [varchar](255), [ITEM_DEFINITION_ID_] [varchar](255), [ITEM_DEFINITION_TYPE_] [varchar](255), [CREATED_TIME_] [datetime], [LAST_AVAILABLE_TIME_] [datetime], [LAST_ENABLED_TIME_] [datetime], [LAST_DISABLED_TIME_] [datetime], [LAST_STARTED_TIME_] [datetime], [LAST_SUSPENDED_TIME_] [datetime], [COMPLETED_TIME_] [datetime], [OCCURRED_TIME_] [datetime], [TERMINATED_TIME_] [datetime], [EXIT_TIME_] [datetime], [ENDED_TIME_] [datetime], [LAST_UPDATED_TIME_] [datetime], [START_USER_ID_] [varchar](255), [REFERENCE_ID_] [varchar](255), [REFERENCE_TYPE_] [varchar](255), [TENANT_ID_] [varchar](255) CONSTRAINT [DF_ACT_CMMN_HI_PLAN_ITEM_INST_TENANT_ID_] DEFAULT '', CONSTRAINT [PK_ACT_CMMN_HI_PLAN_ITEM_INST] PRIMARY KEY ([ID_]))

ALTER TABLE [ACT_CMMN_RU_MIL_INST] ADD [TENANT_ID_] [varchar](255) CONSTRAINT DF_ACT_CMMN_RU_MIL_INST_TENANT_ID_ DEFAULT ''

ALTER TABLE [ACT_CMMN_HI_MIL_INST] ADD [TENANT_ID_] [varchar](255) CONSTRAINT DF_ACT_CMMN_HI_MIL_INST_TENANT_ID_ DEFAULT ''

INSERT INTO [ACT_CMMN_DATABASECHANGELOG] ([ID], [AUTHOR], [FILENAME], [DATEEXECUTED], [ORDEREXECUTED], [MD5SUM], [DESCRIPTION], [COMMENTS], [EXECTYPE], [CONTEXTS], [LABELS], [LIQUIBASE], [DEPLOYMENT_ID]) VALUES ('4', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', GETDATE(), 4, '7:894e6e444f72422bf34e4ade89dc8451', 'createTable tableName=ACT_CMMN_HI_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_MIL_INST; addColumn tableName=ACT_CMMN_HI_MIL_INST', '', 'EXECUTED', NULL, NULL, '3.5.3', '2509775252')

UPDATE [ACT_CMMN_DATABASECHANGELOGLOCK] SET [LOCKED] = 0, [LOCKEDBY] = NULL, [LOCKGRANTED] = NULL WHERE [ID] = 1



UPDATE [ACT_DMN_DATABASECHANGELOGLOCK] SET [LOCKED] = 1, [LOCKEDBY] = '*********** (***********)', [LOCKGRANTED] = '2019-03-13T21:43:01.912' WHERE [ID] = 1 AND [LOCKED] = 0

ALTER TABLE [ACT_DMN_DECISION_TABLE] DROP COLUMN [PARENT_DEPLOYMENT_ID_]

INSERT INTO [ACT_DMN_DATABASECHANGELOG] ([ID], [AUTHOR], [FILENAME], [DATEEXECUTED], [ORDEREXECUTED], [MD5SUM], [DESCRIPTION], [COMMENTS], [EXECTYPE], [CONTEXTS], [LABELS], [LIQUIBASE], [DEPLOYMENT_ID]) VALUES ('4', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', GETDATE(), 4, '7:b8d3d5a3efb71aef7578e1130a38fde2', 'dropColumn columnName=PARENT_DEPLOYMENT_ID_, tableName=ACT_DMN_DECISION_TABLE', '', 'EXECUTED', NULL, NULL, '3.5.3', '2509782205')

UPDATE [ACT_DMN_DATABASECHANGELOGLOCK] SET [LOCKED] = 0, [LOCKEDBY] = NULL, [LOCKGRANTED] = NULL WHERE [ID] = 1



UPDATE [ACT_FO_DATABASECHANGELOGLOCK] SET [LOCKED] = 1, [LOCKEDBY] = '*********** (***********)', [LOCKGRANTED] = '2019-03-13T21:43:08.783' WHERE [ID] = 1 AND [LOCKED] = 0

ALTER TABLE [ACT_FO_FORM_DEFINITION] DROP COLUMN [PARENT_DEPLOYMENT_ID_]

INSERT INTO [ACT_FO_DATABASECHANGELOG] ([ID], [AUTHOR], [FILENAME], [DATEEXECUTED], [ORDEREXECUTED], [MD5SUM], [DESCRIPTION], [COMMENTS], [EXECTYPE], [CONTEXTS], [LABELS], [LIQUIBASE], [DEPLOYMENT_ID]) VALUES ('3', 'flowable', 'org/flowable/form/db/liquibase/flowable-form-db-changelog.xml', GETDATE(), 3, '7:6d80a1fd28201ae354e73bd7c5cf8595', 'dropColumn columnName=PARENT_DEPLOYMENT_ID_, tableName=ACT_FO_FORM_DEFINITION', '', 'EXECUTED', NULL, NULL, '3.5.3', '2509789092')

UPDATE [ACT_FO_DATABASECHANGELOGLOCK] SET [LOCKED] = 0, [LOCKEDBY] = NULL, [LOCKGRANTED] = NULL WHERE [ID] = 1



UPDATE [ACT_CO_DATABASECHANGELOGLOCK] SET [LOCKED] = 1, [LOCKEDBY] = '*********** (***********)', [LOCKGRANTED] = '2019-03-13T21:43:15.683' WHERE [ID] = 1 AND [LOCKED] = 0

UPDATE [ACT_CO_DATABASECHANGELOGLOCK] SET [LOCKED] = 0, [LOCKEDBY] = NULL, [LOCKGRANTED] = NULL WHERE [ID] = 1

