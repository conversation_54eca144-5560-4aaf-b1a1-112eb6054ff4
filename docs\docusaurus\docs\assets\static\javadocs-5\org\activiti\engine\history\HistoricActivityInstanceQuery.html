<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HistoricActivityInstanceQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HistoricActivityInstanceQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricActivityInstanceQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricData.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricActivityInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricActivityInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.history</div>
<h2 title="Interface HistoricActivityInstanceQuery" class="title">Interface HistoricActivityInstanceQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history">HistoricActivityInstance</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">HistoricActivityInstanceQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history">HistoricActivityInstance</a>&gt;</pre>
<div class="block">Programmatic querying for <a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#activityId-java.lang.String-">activityId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId)</code>
<div class="block">Only select historic activity instances for the given activity (id from BPMN 2.0 XML)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#activityInstanceId-java.lang.String-">activityInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityInstanceId)</code>
<div class="block">Only select historic activity instances with the given id (primary key within history tables).</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#activityName-java.lang.String-">activityName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityName)</code>
<div class="block">Only select historic activity instances for activities with the given name</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#activityTenantId-java.lang.String-">activityTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select historic activity instances that have the given tenant id.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#activityTenantIdLike-java.lang.String-">activityTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select historic activity instances with a tenant id like the given one.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#activityType-java.lang.String-">activityType</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityType)</code>
<div class="block">Only select historic activity instances for activities with the given activity type</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#activityWithoutTenantId--">activityWithoutTenantId</a></span>()</code>
<div class="block">Only select historic activity instances that do not have a tenant id.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#executionId-java.lang.String-">executionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Only select historic activity instances for the given execution</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#finished--">finished</a></span>()</code>
<div class="block">Only select historic activity instances that are finished.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByActivityId--">orderByActivityId</a></span>()</code>
<div class="block">Order by activityId (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByActivityName--">orderByActivityName</a></span>()</code>
<div class="block">Order by activityName (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByActivityType--">orderByActivityType</a></span>()</code>
<div class="block">Order by activityType (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByExecutionId--">orderByExecutionId</a></span>()</code>
<div class="block">Order by executionId (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByHistoricActivityInstanceDuration--">orderByHistoricActivityInstanceDuration</a></span>()</code>
<div class="block">Order by duration (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByHistoricActivityInstanceEndTime--">orderByHistoricActivityInstanceEndTime</a></span>()</code>
<div class="block">Order by end (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByHistoricActivityInstanceId--">orderByHistoricActivityInstanceId</a></span>()</code>
<div class="block">Order by id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByHistoricActivityInstanceStartTime--">orderByHistoricActivityInstanceStartTime</a></span>()</code>
<div class="block">Order by start (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByProcessDefinitionId--">orderByProcessDefinitionId</a></span>()</code>
<div class="block">Order by processDefinitionId (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>
<div class="block">Order by processInstanceId (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Only select historic activity instances for the given process definition</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Only select historic activity instances with the given process instance.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#taskAssignee-java.lang.String-">taskAssignee</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Only select historic activity instances for userTask activities assigned to the given user</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html#unfinished--">unfinished</a></span>()</code>
<div class="block">Only select historic activity instances that are not finished yet.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="activityInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;activityInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityInstanceId)</pre>
<div class="block">Only select historic activity instances with the given id (primary key within history tables).</div>
</li>
</ul>
<a name="processInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;processInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Only select historic activity instances with the given process instance.
 ProcessInstance) ids and {@link HistoricProcessInstance} ids match.</div>
</li>
</ul>
<a name="processDefinitionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;processDefinitionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Only select historic activity instances for the given process definition</div>
</li>
</ul>
<a name="executionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;executionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Only select historic activity instances for the given execution</div>
</li>
</ul>
<a name="activityId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;activityId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId)</pre>
<div class="block">Only select historic activity instances for the given activity (id from BPMN 2.0 XML)</div>
</li>
</ul>
<a name="activityName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityName</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;activityName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityName)</pre>
<div class="block">Only select historic activity instances for activities with the given name</div>
</li>
</ul>
<a name="activityType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityType</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;activityType(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityType)</pre>
<div class="block">Only select historic activity instances for activities with the given activity type</div>
</li>
</ul>
<a name="taskAssignee-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskAssignee</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;taskAssignee(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Only select historic activity instances for userTask activities assigned to the given user</div>
</li>
</ul>
<a name="finished--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finished</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;finished()</pre>
<div class="block">Only select historic activity instances that are finished.</div>
</li>
</ul>
<a name="unfinished--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unfinished</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;unfinished()</pre>
<div class="block">Only select historic activity instances that are not finished yet.</div>
</li>
</ul>
<a name="activityTenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityTenantId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;activityTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Only select historic activity instances that have the given tenant id.</div>
</li>
</ul>
<a name="activityTenantIdLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityTenantIdLike</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;activityTenantIdLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</pre>
<div class="block">Only select historic activity instances with a tenant id like the given one.</div>
</li>
</ul>
<a name="activityWithoutTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityWithoutTenantId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;activityWithoutTenantId()</pre>
<div class="block">Only select historic activity instances that do not have a tenant id.</div>
</li>
</ul>
<a name="orderByHistoricActivityInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByHistoricActivityInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByHistoricActivityInstanceId()</pre>
<div class="block">Order by id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByProcessInstanceId()</pre>
<div class="block">Order by processInstanceId (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByExecutionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByExecutionId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByExecutionId()</pre>
<div class="block">Order by executionId (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByActivityId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByActivityId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByActivityId()</pre>
<div class="block">Order by activityId (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByActivityName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByActivityName</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByActivityName()</pre>
<div class="block">Order by activityName (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByActivityType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByActivityType</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByActivityType()</pre>
<div class="block">Order by activityType (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByHistoricActivityInstanceStartTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByHistoricActivityInstanceStartTime</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByHistoricActivityInstanceStartTime()</pre>
<div class="block">Order by start (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByHistoricActivityInstanceEndTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByHistoricActivityInstanceEndTime</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByHistoricActivityInstanceEndTime()</pre>
<div class="block">Order by end (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByHistoricActivityInstanceDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByHistoricActivityInstanceDuration</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByHistoricActivityInstanceDuration()</pre>
<div class="block">Order by duration (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessDefinitionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByProcessDefinitionId()</pre>
<div class="block">Order by processDefinitionId (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTenantId--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByTenantId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;orderByTenantId()</pre>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricActivityInstanceQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricData.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricActivityInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricActivityInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
