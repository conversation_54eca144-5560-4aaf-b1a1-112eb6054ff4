<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine.history Class Hierarchy (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.activiti.engine.history Class Hierarchy (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/form/package-tree.html">Prev</a></li>
<li><a href="../../../../org/activiti/engine/identity/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package org.activiti.engine.history</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricData.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricData</span></a>
<ul>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricActivityInstance</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricDetail</span></a>
<ul>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricFormProperty.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricFormProperty</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricVariableUpdate.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricVariableUpdate</span></a></li>
</ul>
</li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricTaskInstance</span></a> (also extends org.activiti.engine.task.<a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a>)</li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricVariableInstance</span></a></li>
</ul>
</li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricIdentityLink</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricProcessInstance</span></a></li>
<li type="circle">org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><span class="typeNameLink">NativeQuery</span></a>&lt;T,U&gt;
<ul>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/NativeHistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">NativeHistoricActivityInstanceQuery</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/NativeHistoricDetailQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">NativeHistoricDetailQuery</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/NativeHistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">NativeHistoricProcessInstanceQuery</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/NativeHistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">NativeHistoricTaskInstanceQuery</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/NativeHistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">NativeHistoricVariableInstanceQuery</span></a></li>
</ul>
</li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">ProcessInstanceHistoryLog</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">ProcessInstanceHistoryLogQuery</span></a></li>
<li type="circle">org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query"><span class="typeNameLink">Query</span></a>&lt;T,U&gt;
<ul>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricActivityInstanceQuery</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricDetailQuery</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricProcessInstanceQuery</span></a></li>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricVariableInstanceQuery</span></a></li>
<li type="circle">org.activiti.engine.task.<a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task"><span class="typeNameLink">TaskInfoQuery</span></a>&lt;T,V&gt;
<ul>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricTaskInstanceQuery</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">org.activiti.engine.task.<a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task"><span class="typeNameLink">TaskInfo</span></a>
<ul>
<li type="circle">org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">HistoricTaskInstance</span></a> (also extends org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricData.html" title="interface in org.activiti.engine.history">HistoricData</a>)</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/form/package-tree.html">Prev</a></li>
<li><a href="../../../../org/activiti/engine/identity/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
