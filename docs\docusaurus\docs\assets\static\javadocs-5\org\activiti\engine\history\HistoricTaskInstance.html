<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HistoricTaskInstance (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HistoricTaskInstance (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricTaskInstance.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricTaskInstance.html" target="_top">Frames</a></li>
<li><a href="HistoricTaskInstance.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.history</div>
<h2 title="Interface HistoricTaskInstance" class="title">Interface HistoricTaskInstance</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/history/HistoricData.html" title="interface in org.activiti.engine.history">HistoricData</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">HistoricTaskInstance</span>
extends <a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a>, <a href="../../../../org/activiti/engine/history/HistoricData.html" title="interface in org.activiti.engine.history">HistoricData</a></pre>
<div class="block">Represents a historic task instance (waiting, finished or deleted) that is stored permanent for 
 statistics, audit and other business intelligence purposes.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getClaimTime--">getClaimTime</a></span>()</code>
<div class="block">Time when the task was claimed.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getDeleteReason--">getDeleteReason</a></span>()</code>
<div class="block">The reason why this task was deleted {'completed' | 'deleted' | any other user defined string }.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getDurationInMillis--">getDurationInMillis</a></span>()</code>
<div class="block">Difference between <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getEndTime--"><code>getEndTime()</code></a> and <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getStartTime--"><code>getStartTime()</code></a> in milliseconds.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getEndTime--">getEndTime</a></span>()</code>
<div class="block">Time when the task was deleted or completed.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getStartTime--">getStartTime</a></span>()</code>
<div class="block">Time when the task started.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getWorkTimeInMillis--">getWorkTimeInMillis</a></span>()</code>
<div class="block">Difference between <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getEndTime--"><code>getEndTime()</code></a> and <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getClaimTime--"><code>getClaimTime()</code></a> in milliseconds.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#setLocalizedDescription-java.lang.String-">setLocalizedDescription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</code>
<div class="block">Sets an optional localized description for the task.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#setLocalizedName-java.lang.String-">setLocalizedName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Sets an optional localized name for the task.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.task.TaskInfo">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.task.<a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a></h3>
<code><a href="../../../../org/activiti/engine/task/TaskInfo.html#getAssignee--">getAssignee</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getCategory--">getCategory</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getCreateTime--">getCreateTime</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getDescription--">getDescription</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getDueDate--">getDueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getExecutionId--">getExecutionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getFormKey--">getFormKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getId--">getId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getName--">getName</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getOwner--">getOwner</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getParentTaskId--">getParentTaskId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getPriority--">getPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getProcessDefinitionId--">getProcessDefinitionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getProcessInstanceId--">getProcessInstanceId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getProcessVariables--">getProcessVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getTaskDefinitionKey--">getTaskDefinitionKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getTaskLocalVariables--">getTaskLocalVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getTenantId--">getTenantId</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.history.HistoricData">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.history.<a href="../../../../org/activiti/engine/history/HistoricData.html" title="interface in org.activiti.engine.history">HistoricData</a></h3>
<code><a href="../../../../org/activiti/engine/history/HistoricData.html#getTime--">getTime</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDeleteReason--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeleteReason</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDeleteReason()</pre>
<div class="block">The reason why this task was deleted {'completed' | 'deleted' | any other user defined string }.</div>
</li>
</ul>
<a name="getStartTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartTime</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;getStartTime()</pre>
<div class="block">Time when the task started.</div>
</li>
</ul>
<a name="getEndTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndTime</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;getEndTime()</pre>
<div class="block">Time when the task was deleted or completed.</div>
</li>
</ul>
<a name="getDurationInMillis--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDurationInMillis</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getDurationInMillis()</pre>
<div class="block">Difference between <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getEndTime--"><code>getEndTime()</code></a> and <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getStartTime--"><code>getStartTime()</code></a> in milliseconds.</div>
</li>
</ul>
<a name="getWorkTimeInMillis--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkTimeInMillis</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getWorkTimeInMillis()</pre>
<div class="block">Difference between <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getEndTime--"><code>getEndTime()</code></a> and <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html#getClaimTime--"><code>getClaimTime()</code></a> in milliseconds.</div>
</li>
</ul>
<a name="getClaimTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClaimTime</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;getClaimTime()</pre>
<div class="block">Time when the task was claimed.</div>
</li>
</ul>
<a name="setLocalizedName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocalizedName</h4>
<pre>void&nbsp;setLocalizedName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Sets an optional localized name for the task.</div>
</li>
</ul>
<a name="setLocalizedDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setLocalizedDescription</h4>
<pre>void&nbsp;setLocalizedDescription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</pre>
<div class="block">Sets an optional localized description for the task.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricTaskInstance.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricTaskInstance.html" target="_top">Frames</a></li>
<li><a href="HistoricTaskInstance.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
