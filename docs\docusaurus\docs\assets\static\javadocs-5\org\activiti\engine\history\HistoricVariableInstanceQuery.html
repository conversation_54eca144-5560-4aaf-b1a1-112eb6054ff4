<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HistoricVariableInstanceQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HistoricVariableInstanceQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricVariableInstanceQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricVariableUpdate.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricVariableInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricVariableInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.history</div>
<h2 title="Interface HistoricVariableInstanceQuery" class="title">Interface HistoricVariableInstanceQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history">HistoricVariableInstance</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">HistoricVariableInstanceQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history">HistoricVariableInstance</a>&gt;</pre>
<div class="block">Programmatic querying for <a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><code>HistoricVariableInstance</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Christian Lipphardt (camunda)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#excludeTaskVariables--">excludeTaskVariables</a></span>()</code>
<div class="block">Only select historic process variables which were not set task-local.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#excludeVariableInitialization--">excludeVariableInitialization</a></span>()</code>
<div class="block">Don't initialize variable values.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#executionId-java.lang.String-">executionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Only select historic process variables with the given id.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#executionIds-java.util.Set-">executionIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;executionIds)</code>
<div class="block">Only select historic process variables whose id is in the given set of ids.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#id-java.lang.String-">id</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</code>
<div class="block">Only select a historic variable with the given id.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#orderByVariableName--">orderByVariableName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Only select historic process variables with the given process instance.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#taskId-java.lang.String-">taskId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Only select historic process variables with the given task.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#taskIds-java.util.Set-">taskIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds)</code>
<div class="block">Only select historic process variables whose id is in the given set of ids.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#variableName-java.lang.String-">variableName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Only select historic process variables with the given variable name.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#variableNameLike-java.lang.String-">variableNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableNameLike)</code>
<div class="block">Only select historic process variables where the given variable name is like.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#variableValueEquals-java.lang.String-java.lang.Object-">variableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">only select historic process variables with the given name and value</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#variableValueLike-java.lang.String-java.lang.String-">variableValueLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableValue)</code>
<div class="block">only select historic process variables like the given name and value</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#variableValueLikeIgnoreCase-java.lang.String-java.lang.String-">variableValueLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableValue)</code>
<div class="block">only select historic process variables like the given name and value (case insensitive)</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html#variableValueNotEquals-java.lang.String-java.lang.Object-">variableValueNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">only select historic process variables that don't have the given name and value</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="id-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>id</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;id(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</pre>
<div class="block">Only select a historic variable with the given id.</div>
</li>
</ul>
<a name="processInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;processInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Only select historic process variables with the given process instance.</div>
</li>
</ul>
<a name="executionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;executionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Only select historic process variables with the given id.</div>
</li>
</ul>
<a name="executionIds-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionIds</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;executionIds(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;executionIds)</pre>
<div class="block">Only select historic process variables whose id is in the given set of ids.</div>
</li>
</ul>
<a name="taskId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;taskId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">Only select historic process variables with the given task.</div>
</li>
</ul>
<a name="taskIds-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskIds</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;taskIds(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskIds)</pre>
<div class="block">Only select historic process variables whose id is in the given set of ids.</div>
</li>
</ul>
<a name="variableName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableName</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;variableName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Only select historic process variables with the given variable name.</div>
</li>
</ul>
<a name="variableNameLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableNameLike</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;variableNameLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableNameLike)</pre>
<div class="block">Only select historic process variables where the given variable name is like.</div>
</li>
</ul>
<a name="excludeTaskVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeTaskVariables</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;excludeTaskVariables()</pre>
<div class="block">Only select historic process variables which were not set task-local.</div>
</li>
</ul>
<a name="excludeVariableInitialization--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeVariableInitialization</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;excludeVariableInitialization()</pre>
<div class="block">Don't initialize variable values. This is foremost a way to deal with variable delete queries</div>
</li>
</ul>
<a name="variableValueEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;variableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">only select historic process variables with the given name and value</div>
</li>
</ul>
<a name="variableValueNotEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueNotEquals</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;variableValueNotEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">only select historic process variables that don't have the given name and value</div>
</li>
</ul>
<a name="variableValueLike-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLike</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;variableValueLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableValue)</pre>
<div class="block">only select historic process variables like the given name and value</div>
</li>
</ul>
<a name="variableValueLikeIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;variableValueLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableValue)</pre>
<div class="block">only select historic process variables like the given name and value (case insensitive)</div>
</li>
</ul>
<a name="orderByProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;orderByProcessInstanceId()</pre>
</li>
</ul>
<a name="orderByVariableName--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByVariableName</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;orderByVariableName()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricVariableInstanceQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricVariableUpdate.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricVariableInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricVariableInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
