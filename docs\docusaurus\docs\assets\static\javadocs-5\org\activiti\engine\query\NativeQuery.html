<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>NativeQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NativeQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/NativeQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/query/NativeQuery.html" target="_top">Frames</a></li>
<li><a href="NativeQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.query</div>
<h2 title="Interface NativeQuery" class="title">Interface NativeQuery&lt;T extends NativeQuery&lt;?,?&gt;,U&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/repository/NativeDeploymentQuery.html" title="interface in org.activiti.engine.repository">NativeDeploymentQuery</a>, <a href="../../../../org/activiti/engine/runtime/NativeExecutionQuery.html" title="interface in org.activiti.engine.runtime">NativeExecutionQuery</a>, <a href="../../../../org/activiti/engine/identity/NativeGroupQuery.html" title="interface in org.activiti.engine.identity">NativeGroupQuery</a>, <a href="../../../../org/activiti/engine/history/NativeHistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricActivityInstanceQuery</a>, <a href="../../../../org/activiti/engine/history/NativeHistoricDetailQuery.html" title="interface in org.activiti.engine.history">NativeHistoricDetailQuery</a>, <a href="../../../../org/activiti/engine/history/NativeHistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricProcessInstanceQuery</a>, <a href="../../../../org/activiti/engine/history/NativeHistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricTaskInstanceQuery</a>, <a href="../../../../org/activiti/engine/history/NativeHistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricVariableInstanceQuery</a>, <a href="../../../../org/activiti/engine/repository/NativeModelQuery.html" title="interface in org.activiti.engine.repository">NativeModelQuery</a>, <a href="../../../../org/activiti/engine/repository/NativeProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">NativeProcessDefinitionQuery</a>, <a href="../../../../org/activiti/engine/runtime/NativeProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">NativeProcessInstanceQuery</a>, <a href="../../../../org/activiti/engine/task/NativeTaskQuery.html" title="interface in org.activiti.engine.task">NativeTaskQuery</a>, <a href="../../../../org/activiti/engine/identity/NativeUserQuery.html" title="interface in org.activiti.engine.identity">NativeUserQuery</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">NativeQuery&lt;T extends NativeQuery&lt;?,?&gt;,U&gt;</span></pre>
<div class="block">Describes basic methods for doing native queries</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Bernd Ruecker (camunda)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/query/NativeQuery.html#count--">count</a></span>()</code>
<div class="block">Executes the query and returns the number of results</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">U</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/query/NativeQuery.html#list--">list</a></span>()</code>
<div class="block">Executes the query and get a list of entities as the result.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">U</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/query/NativeQuery.html#listPage-int-int-">listPage</a></span>(int&nbsp;firstResult,
        int&nbsp;maxResults)</code>
<div class="block">Executes the query and get a list of entities as the result.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/query/NativeQuery.html#parameter-java.lang.String-java.lang.Object-">parameter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Add parameter to be replaced in query for index, e.g.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">U</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/query/NativeQuery.html#singleResult--">singleResult</a></span>()</code>
<div class="block">Executes the query and returns the resulting entity or null if no
 entity matches the query criteria.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/query/NativeQuery.html#sql-java.lang.String-">sql</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;selectClause)</code>
<div class="block">Hand in the SQL statement you want to execute.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="sql-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sql</h4>
<pre><a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">T</a>&nbsp;sql(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;selectClause)</pre>
<div class="block">Hand in the SQL statement you want to execute. BEWARE: if you need a count you have to hand in a count() statement
 yourself, otherwise the result will be treated as lost of Activiti entities.
 
 If you need paging you have to insert the pagination code yourself. We skipped doing this for you
 as this is done really different on some databases (especially MS-SQL / DB2)</div>
</li>
</ul>
<a name="parameter-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parameter</h4>
<pre><a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">T</a>&nbsp;parameter(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Add parameter to be replaced in query for index, e.g. :param1, :myParam, ...</div>
</li>
</ul>
<a name="count--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>count</h4>
<pre>long&nbsp;count()</pre>
<div class="block">Executes the query and returns the number of results</div>
</li>
</ul>
<a name="singleResult--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>singleResult</h4>
<pre><a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">U</a>&nbsp;singleResult()</pre>
<div class="block">Executes the query and returns the resulting entity or null if no
 entity matches the query criteria.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - when the query results in more than one
 entities.</dd>
</dl>
</li>
</ul>
<a name="list--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>list</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">U</a>&gt;&nbsp;list()</pre>
<div class="block">Executes the query and get a list of entities as the result.</div>
</li>
</ul>
<a name="listPage-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>listPage</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/activiti/engine/query/NativeQuery.html" title="type parameter in NativeQuery">U</a>&gt;&nbsp;listPage(int&nbsp;firstResult,
                 int&nbsp;maxResults)</pre>
<div class="block">Executes the query and get a list of entities as the result.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/NativeQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/query/NativeQuery.html" target="_top">Frames</a></li>
<li><a href="NativeQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
