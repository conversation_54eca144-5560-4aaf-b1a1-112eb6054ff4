<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine.dynamic (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.activiti.engine.dynamic (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/event/impl/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../org/activiti/engine/event/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/dynamic/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.activiti.engine.dynamic</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/dynamic/PropertiesParser.html" title="interface in org.activiti.engine.dynamic">PropertiesParser</a></td>
<td class="colLast">
<div class="block">Created by Pardo David on 5/12/2016.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/dynamic/PropertiesParserConstants.html" title="interface in org.activiti.engine.dynamic">PropertiesParserConstants</a></td>
<td class="colLast">
<div class="block">Created by Pardo David on 5/12/2016.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/dynamic/BasePropertiesParser.html" title="class in org.activiti.engine.dynamic">BasePropertiesParser</a></td>
<td class="colLast">
<div class="block">Created by Pardo David on 5/12/2016.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/dynamic/DefaultPropertiesParser.html" title="class in org.activiti.engine.dynamic">DefaultPropertiesParser</a></td>
<td class="colLast">
<div class="block">Used for <code>FlowElement</code> currently not supported by the <a href="../../../../org/activiti/engine/DynamicBpmnService.html" title="interface in org.activiti.engine"><code>DynamicBpmnService</code></a>
 and elements who are not parsed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html" title="class in org.activiti.engine.dynamic">DynamicProcessDefinitionSummary</a></td>
<td class="colLast">
<div class="block">Pojo class who can be used to check information between <a href="../../../../org/activiti/engine/DynamicBpmnService.html#getProcessDefinitionInfo-java.lang.String-"><code>DynamicBpmnService.getProcessDefinitionInfo(String)</code></a>
 and <code>BpmnModel</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/dynamic/ScriptTaskPropertiesParser.html" title="class in org.activiti.engine.dynamic">ScriptTaskPropertiesParser</a></td>
<td class="colLast">
<div class="block">Created by Pardo David on 5/12/2016.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/dynamic/UserTaskPropertiesParser.html" title="class in org.activiti.engine.dynamic">UserTaskPropertiesParser</a></td>
<td class="colLast">
<div class="block">Created by Pardo David on 5/12/2016.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/event/impl/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../org/activiti/engine/event/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/dynamic/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
