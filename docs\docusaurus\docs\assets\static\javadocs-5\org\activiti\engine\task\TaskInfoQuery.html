<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TaskInfoQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TaskInfoQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":38,"i3":38,"i4":38,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6,"i44":6,"i45":6,"i46":6,"i47":6,"i48":6,"i49":6,"i50":6,"i51":6,"i52":6,"i53":6,"i54":6,"i55":6,"i56":6,"i57":6,"i58":6,"i59":6,"i60":6,"i61":6,"i62":6,"i63":6,"i64":6,"i65":6,"i66":6,"i67":6,"i68":6,"i69":6,"i70":6,"i71":6,"i72":6,"i73":6,"i74":6,"i75":6,"i76":6,"i77":6,"i78":6,"i79":6,"i80":6,"i81":6,"i82":6,"i83":6,"i84":6,"i85":6,"i86":6,"i87":6,"i88":6,"i89":6,"i90":6,"i91":6,"i92":6,"i93":6,"i94":6,"i95":6,"i96":6,"i97":6,"i98":6,"i99":38,"i100":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TaskInfoQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/task/TaskInfoQueryWrapper.html" title="class in org.activiti.engine.task"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/task/TaskInfoQuery.html" target="_top">Frames</a></li>
<li><a href="TaskInfoQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.task</div>
<h2 title="Interface TaskInfoQuery" class="title">Interface TaskInfoQuery&lt;T extends TaskInfoQuery&lt;?,?&gt;,V extends <a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a>&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;T,V&gt;</dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>, <a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">TaskInfoQuery&lt;T extends TaskInfoQuery&lt;?,?&gt;,V extends <a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a>&gt;</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;T,V&gt;</pre>
<div class="block">Interface containing shared methods between the <a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task"><code>TaskQuery</code></a> and the <a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstanceQuery</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#deploymentId-java.lang.String-">deploymentId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block">Only select tasks which are part of a process instance which has the given
 deployment id.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#deploymentIdIn-java.util.List-">deploymentIdIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;deploymentIds)</code>
<div class="block">Only select tasks which are part of a process instance which has the given
 deployment id.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#dueAfter-java.util.Date-">dueAfter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#dueBefore-java.util.Date-">dueBefore</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#dueDate-java.util.Date-">dueDate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#endOr--">endOr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#executionId-java.lang.String-">executionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Only select tasks for the given execution.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#includeProcessVariables--">includeProcessVariables</a></span>()</code>
<div class="block">Include global task variables in the task query result</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#includeTaskLocalVariables--">includeTaskLocalVariables</a></span>()</code>
<div class="block">Include local task variables in the task query result</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#limitTaskVariables-java.lang.Integer-">limitTaskVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;taskVariablesLimit)</code>
<div class="block">Limit task variables</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#locale-java.lang.String-">locale</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale)</code>
<div class="block">Localize task name and description to specified locale.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#or--">or</a></span>()</code>
<div class="block">All query clauses called will be added to a single or-statement.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByDueDateNullsFirst--">orderByDueDateNullsFirst</a></span>()</code>
<div class="block">Order by due date (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByDueDateNullsLast--">orderByDueDateNullsLast</a></span>()</code>
<div class="block">Order by due date (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByExecutionId--">orderByExecutionId</a></span>()</code>
<div class="block">Order by execution id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByProcessDefinitionId--">orderByProcessDefinitionId</a></span>()</code>
<div class="block">Order by process definition id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>
<div class="block">Order by process instance id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskAssignee--">orderByTaskAssignee</a></span>()</code>
<div class="block">Order by assignee (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskCreateTime--">orderByTaskCreateTime</a></span>()</code>
<div class="block">Order by the time on which the tasks were created (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskDefinitionKey--">orderByTaskDefinitionKey</a></span>()</code>
<div class="block">Order by task definition key (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskDescription--">orderByTaskDescription</a></span>()</code>
<div class="block">Order by description (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskDueDate--">orderByTaskDueDate</a></span>()</code>
<div class="block">Order by task due date (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskId--">orderByTaskId</a></span>()</code>
<div class="block">Order by task id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskName--">orderByTaskName</a></span>()</code>
<div class="block">Order by task name (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskOwner--">orderByTaskOwner</a></span>()</code>
<div class="block">Order by task owner (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskPriority--">orderByTaskPriority</a></span>()</code>
<div class="block">Order by priority (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processCategoryIn-java.util.List-">processCategoryIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processCategoryInList)</code>
<div class="block">Only select tasks which are part of a process instance whose definition
 belongs to the category which is present in the given list.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processCategoryNotIn-java.util.List-">processCategoryNotIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processCategoryNotInList)</code>
<div class="block">Only select tasks which are part of a process instance whose definition does not
 belong to the category which is present in the given list.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Only select tasks which are part of a process instance which has the given
 process definition id.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Only select tasks which are part of a process instance which has the given
 process definition key.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyIn-java.util.List-">processDefinitionKeyIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionKeys)</code>
<div class="block">Only select tasks that have a process definition for which the key is present in the given list</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyLike-java.lang.String-">processDefinitionKeyLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKeyLike)</code>
<div class="block">Only select tasks which are part of a process instance which has a
 process definition key like the given value.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyLikeIgnoreCase-java.lang.String-">processDefinitionKeyLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKeyLikeIgnoreCase)</code>
<div class="block">Only select tasks which are part of a process instance which has a
 process definition key like the given value.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionName-java.lang.String-">processDefinitionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</code>
<div class="block">Only select tasks which are part of a process instance which has the given
 process definition name.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionNameLike-java.lang.String-">processDefinitionNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionNameLike)</code>
<div class="block">Only select tasks which are part of a process instance which has a
 process definition name like the given value.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKey-java.lang.String-">processInstanceBusinessKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey)</code>
<div class="block">Only select tasks foe the given business key</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKeyLike-java.lang.String-">processInstanceBusinessKeyLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKeyLike)</code>
<div class="block">Only select tasks with a business key  like the given value
 The syntax is that of SQL: for example usage: processInstanceBusinessKeyLike("%activiti%").</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKeyLikeIgnoreCase-java.lang.String-">processInstanceBusinessKeyLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKeyLikeIgnoreCase)</code>
<div class="block">Only select tasks with a business key  like the given value
 The syntax is that of SQL: for example usage: processInstanceBusinessKeyLike("%activiti%").</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Only select tasks for the given process instance id.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceIdIn-java.util.List-">processInstanceIdIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processInstanceIds)</code>
<div class="block">Only select tasks for the given process ids.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueEquals-java.lang.Object-">processVariableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Only select tasks which are part of a process that has at least one variable
 with the given value.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueEquals-java.lang.String-java.lang.Object-">processVariableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Only select tasks which are part of a process that has a variable
 with the given name set to the given value.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">processVariableValueEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select tasks which are part of a process that has a local string variable which 
 is not the given value, case insensitive.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueGreaterThan-java.lang.String-java.lang.Object-">processVariableValueGreaterThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a global variable value greater than the
 passed value when they ended.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">processVariableValueGreaterThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a global variable value greater than or
 equal to the passed value when they ended.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLessThan-java.lang.String-java.lang.Object-">processVariableValueLessThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a global variable value less than the
 passed value when the ended.Booleans,
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type
 wrappers) are not supported.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLessThanOrEqual-java.lang.String-java.lang.Object-">processVariableValueLessThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a global variable value less than or equal
 to the passed value when they ended.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLike-java.lang.String-java.lang.String-">processVariableValueLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a global variable value like the given value
 when they ended.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">processVariableValueLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a global variable value like the given value (case insensitive)
 when they ended.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueNotEquals-java.lang.String-java.lang.Object-">processVariableValueNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Only select tasks which have a variable with the given name, but
 with a different value than the passed value.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">processVariableValueNotEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select tasks which are part of a process that has a string variable with 
 the given value, case insensitive.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssignee-java.lang.String-">taskAssignee</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee)</code>
<div class="block">Only select tasks which are assigned to the given user.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeIds-java.util.List-">taskAssigneeIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;assigneeListIds)</code>
<div class="block">Only select tasks with an assignee that is in the given list</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeLike-java.lang.String-">taskAssigneeLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assigneeLike)</code>
<div class="block">Only select tasks which were last assigned to an assignee like
 the given value.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeLikeIgnoreCase-java.lang.String-">taskAssigneeLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assigneeLikeIgnoreCase)</code>
<div class="block">Only select tasks which were last assigned to an assignee like
 the given value.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateGroup-java.lang.String-">taskCandidateGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateGroup)</code>
<div class="block">Only select tasks for which users in the given group are candidates.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateGroupIn-java.util.List-">taskCandidateGroupIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateGroups)</code>
<div class="block">Only select tasks for which the 'candidateGroup' is one of the given groups.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateUser-java.lang.String-">taskCandidateUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateUser)</code>
<div class="block">Only select tasks for which the given user is a candidate.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCategory-java.lang.String-">taskCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</code>
<div class="block">Only select tasks with the given category.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCreatedAfter-java.util.Date-">taskCreatedAfter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;after)</code>
<div class="block">Only select tasks that are created after the given date.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCreatedBefore-java.util.Date-">taskCreatedBefore</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;before)</code>
<div class="block">Only select tasks that are created before the given date.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCreatedOn-java.util.Date-">taskCreatedOn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;createTime)</code>
<div class="block">Only select tasks that are created on the given date.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDefinitionKey-java.lang.String-">taskDefinitionKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</code>
<div class="block">Only select tasks with the given taskDefinitionKey.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDefinitionKeyLike-java.lang.String-">taskDefinitionKeyLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;keyLike)</code>
<div class="block">Only select tasks with a taskDefinitionKey that match the given parameter.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescription-java.lang.String-">taskDescription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</code>
<div class="block">Only select tasks with the given description.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescriptionLike-java.lang.String-">taskDescriptionLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;descriptionLike)</code>
<div class="block">Only select tasks with a description matching the parameter .</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescriptionLikeIgnoreCase-java.lang.String-">taskDescriptionLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;descriptionLike)</code>
<div class="block">Only select tasks with a description matching the parameter .</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDueAfter-java.util.Date-">taskDueAfter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>&nbsp;</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDueBefore-java.util.Date-">taskDueBefore</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>&nbsp;</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDueDate-java.util.Date-">taskDueDate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>&nbsp;</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskId-java.lang.String-">taskId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Only select tasks with the given task id (in practice, there will be
 maximum one of this kind)</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskInvolvedUser-java.lang.String-">taskInvolvedUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;involvedUser)</code>
<div class="block">Only select tasks for which there exist an <a href="../../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a> with the given user, including tasks which have been 
 assigned to the given user (assignee) or owned by the given user (owner).</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskMaxPriority-java.lang.Integer-">taskMaxPriority</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;maxPriority)</code>
<div class="block">Only select tasks with the given priority or lower.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskMinPriority-java.lang.Integer-">taskMinPriority</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minPriority)</code>
<div class="block">Only select tasks with the given priority or higher.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskName-java.lang.String-">taskName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Only select tasks with the given name</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameIn-java.util.List-">taskNameIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;nameList)</code>
<div class="block">Only select tasks with a name that is in the given list</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameInIgnoreCase-java.util.List-">taskNameInIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;nameList)</code>
<div class="block">Only select tasks with a name that is in the given list

 This method, unlike the <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameIn-java.util.List-"><code>taskNameIn(List)</code></a> method will
 not take in account the upper/lower case: both the input parameters as the column value are
 lowercased when the query is executed.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameLike-java.lang.String-">taskNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLike)</code>
<div class="block">Only select tasks with a name matching the parameter.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameLikeIgnoreCase-java.lang.String-">taskNameLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLike)</code>
<div class="block">Only select tasks with a name matching the parameter.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwner-java.lang.String-">taskOwner</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner)</code>
<div class="block">Only select tasks for which the given user is the owner.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwnerLike-java.lang.String-">taskOwnerLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ownerLike)</code>
<div class="block">Only select tasks which were last assigned to an owner like
 the given value.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwnerLikeIgnoreCase-java.lang.String-">taskOwnerLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ownerLikeIgnoreCase)</code>
<div class="block">Only select tasks which were last assigned to an owner like
 the given value.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskPriority-java.lang.Integer-">taskPriority</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;priority)</code>
<div class="block">Only select tasks with the given priority.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskTenantId-java.lang.String-">taskTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select tasks that have the given tenant id.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskTenantIdLike-java.lang.String-">taskTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select tasks with a tenant id like the given one.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueEquals-java.lang.Object-">taskVariableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Only select tasks which have at least one local task variable with the given value.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueEquals-java.lang.String-java.lang.Object-">taskVariableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Only select tasks which have a local task variable with the given name
 set to the given value.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">taskVariableValueEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a local string variable with the given value, 
 case insensitive.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueGreaterThan-java.lang.String-java.lang.Object-">taskVariableValueGreaterThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a local variable value greater than the
 passed value when they ended.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">taskVariableValueGreaterThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a local variable value greater than or
 equal to the passed value when they ended.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLessThan-java.lang.String-java.lang.Object-">taskVariableValueLessThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a local variable value less than the
 passed value when the ended.Booleans,
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type
 wrappers) are not supported.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLessThanOrEqual-java.lang.String-java.lang.Object-">taskVariableValueLessThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a local variable value less than or equal
 to the passed value when they ended.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLike-java.lang.String-java.lang.String-">taskVariableValueLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a local variable value like the given value
 when they ended.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">taskVariableValueLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a local variable value like the given value (case insensitive)
 when they ended.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueNotEquals-java.lang.String-java.lang.Object-">taskVariableValueNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Only select tasks which have a local task variable with the given name, but
 with a different value than the passed value.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">taskVariableValueNotEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select tasks which have a local string variable with is not the given value, 
 case insensitive.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskWithoutTenantId--">taskWithoutTenantId</a></span>()</code>
<div class="block">Only select tasks that do not have a tenant id.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#withLocalizationFallback--">withLocalizationFallback</a></span>()</code>
<div class="block">Instruct localization to fallback to more general locales including the default locale of the JVM if the specified locale is not found.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#withoutDueDate--">withoutDueDate</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#withoutTaskDueDate--">withoutTaskDueDate</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="taskId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">Only select tasks with the given task id (in practice, there will be
 maximum one of this kind)</div>
</li>
</ul>
<a name="taskName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskName</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Only select tasks with the given name</div>
</li>
</ul>
<a name="taskNameIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskNameIn</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskNameIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;nameList)</pre>
<div class="block">Only select tasks with a name that is in the given list</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></code> - When passed name list is empty or <code>null</code> or contains <code>null String</code>.</dd>
</dl>
</li>
</ul>
<a name="taskNameInIgnoreCase-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskNameInIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskNameInIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;nameList)</pre>
<div class="block">Only select tasks with a name that is in the given list

 This method, unlike the <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameIn-java.util.List-"><code>taskNameIn(List)</code></a> method will
 not take in account the upper/lower case: both the input parameters as the column value are
 lowercased when the query is executed.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></code> - When passed name list is empty or <code>null</code> or contains <code>null String</code>.</dd>
</dl>
</li>
</ul>
<a name="taskNameLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskNameLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskNameLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLike)</pre>
<div class="block">Only select tasks with a name matching the parameter.
  The syntax is that of SQL: for example usage: nameLike(%activiti%)</div>
</li>
</ul>
<a name="taskNameLikeIgnoreCase-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskNameLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskNameLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLike)</pre>
<div class="block">Only select tasks with a name matching the parameter.
  The syntax is that of SQL: for example usage: nameLike(%activiti%)
  
  This method, unlike the <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameLike-java.lang.String-"><code>taskNameLike(String)</code></a> method will 
  not take in account the upper/lower case: both the input parameter as the column value are
  lowercased when the query is executed.</div>
</li>
</ul>
<a name="taskDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDescription</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskDescription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</pre>
<div class="block">Only select tasks with the given description.</div>
</li>
</ul>
<a name="taskDescriptionLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDescriptionLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskDescriptionLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;descriptionLike)</pre>
<div class="block">Only select tasks with a description matching the parameter .
  The syntax is that of SQL: for example usage: descriptionLike(%activiti%)</div>
</li>
</ul>
<a name="taskDescriptionLikeIgnoreCase-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDescriptionLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskDescriptionLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;descriptionLike)</pre>
<div class="block">Only select tasks with a description matching the parameter .
  The syntax is that of SQL: for example usage: descriptionLike(%activiti%)
  
  This method, unlike the <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescriptionLike-java.lang.String-"><code>taskDescriptionLike(String)</code></a> method will 
  not take in account the upper/lower case: both the input parameter as the column value are
  lowercased when the query is executed.</div>
</li>
</ul>
<a name="taskPriority-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskPriority</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskPriority(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;priority)</pre>
<div class="block">Only select tasks with the given priority.</div>
</li>
</ul>
<a name="taskMinPriority-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskMinPriority</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskMinPriority(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minPriority)</pre>
<div class="block">Only select tasks with the given priority or higher.</div>
</li>
</ul>
<a name="taskMaxPriority-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskMaxPriority</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskMaxPriority(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;maxPriority)</pre>
<div class="block">Only select tasks with the given priority or lower.</div>
</li>
</ul>
<a name="taskAssignee-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskAssignee</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskAssignee(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee)</pre>
<div class="block">Only select tasks which are assigned to the given user.</div>
</li>
</ul>
<a name="taskAssigneeLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskAssigneeLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskAssigneeLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assigneeLike)</pre>
<div class="block">Only select tasks which were last assigned to an assignee like
 the given value.
 The syntax that should be used is the same as in SQL, eg. %activiti%.</div>
</li>
</ul>
<a name="taskAssigneeLikeIgnoreCase-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskAssigneeLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskAssigneeLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assigneeLikeIgnoreCase)</pre>
<div class="block">Only select tasks which were last assigned to an assignee like
 the given value.
 The syntax that should be used is the same as in SQL, eg. %activiti%.
 
 This method, unlike the <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeLike-java.lang.String-"><code>taskAssigneeLike(String)</code></a> method will 
 not take in account the upper/lower case: both the input parameter as the column value are
 lowercased when the query is executed.</div>
</li>
</ul>
<a name="taskAssigneeIds-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskAssigneeIds</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskAssigneeIds(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;assigneeListIds)</pre>
<div class="block">Only select tasks with an assignee that is in the given list</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>FlowableIllegalArgumentException</code> - When passed name list is empty or <code>null</code> or contains <code>null String</code>.</dd>
</dl>
</li>
</ul>
<a name="taskOwner-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskOwner</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskOwner(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner)</pre>
<div class="block">Only select tasks for which the given user is the owner.</div>
</li>
</ul>
<a name="taskOwnerLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskOwnerLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskOwnerLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ownerLike)</pre>
<div class="block">Only select tasks which were last assigned to an owner like
 the given value.
 The syntax that should be used is the same as in SQL, eg. %activiti%.</div>
</li>
</ul>
<a name="taskOwnerLikeIgnoreCase-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskOwnerLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskOwnerLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ownerLikeIgnoreCase)</pre>
<div class="block">Only select tasks which were last assigned to an owner like
 the given value.
 The syntax that should be used is the same as in SQL, eg. %activiti%.
 
 This method, unlike the <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwnerLike-java.lang.String-"><code>taskOwnerLike(String)</code></a> method will 
 not take in account the upper/lower case: both the input parameter as the column value are
 lowercased when the query is executed.</div>
</li>
</ul>
<a name="taskCandidateUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCandidateUser</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskCandidateUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateUser)</pre>
<div class="block">Only select tasks for which the given user is a candidate.</div>
</li>
</ul>
<a name="taskInvolvedUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskInvolvedUser</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskInvolvedUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;involvedUser)</pre>
<div class="block">Only select tasks for which there exist an <a href="../../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a> with the given user, including tasks which have been 
 assigned to the given user (assignee) or owned by the given user (owner).</div>
</li>
</ul>
<a name="taskCandidateGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCandidateGroup</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskCandidateGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateGroup)</pre>
<div class="block">Only select tasks for which users in the given group are candidates.</div>
</li>
</ul>
<a name="taskCandidateGroupIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCandidateGroupIn</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskCandidateGroupIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateGroups)</pre>
<div class="block">Only select tasks for which the 'candidateGroup' is one of the given groups.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></code> - When query is executed and <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateGroup-java.lang.String-"><code>taskCandidateGroup(String)</code></a> or 
     <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateUser-java.lang.String-"><code>taskCandidateUser(String)</code></a> has been executed on the query instance. 
   When passed group list is empty or <code>null</code>.</dd>
</dl>
</li>
</ul>
<a name="taskTenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskTenantId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Only select tasks that have the given tenant id.</div>
</li>
</ul>
<a name="taskTenantIdLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskTenantIdLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskTenantIdLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</pre>
<div class="block">Only select tasks with a tenant id like the given one.</div>
</li>
</ul>
<a name="taskWithoutTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskWithoutTenantId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskWithoutTenantId()</pre>
<div class="block">Only select tasks that do not have a tenant id.</div>
</li>
</ul>
<a name="processInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Only select tasks for the given process instance id.</div>
</li>
</ul>
<a name="processInstanceIdIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceIdIn</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processInstanceIdIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processInstanceIds)</pre>
<div class="block">Only select tasks for the given process ids.</div>
</li>
</ul>
<a name="processInstanceBusinessKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceBusinessKey</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processInstanceBusinessKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey)</pre>
<div class="block">Only select tasks foe the given business key</div>
</li>
</ul>
<a name="processInstanceBusinessKeyLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceBusinessKeyLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processInstanceBusinessKeyLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKeyLike)</pre>
<div class="block">Only select tasks with a business key  like the given value
 The syntax is that of SQL: for example usage: processInstanceBusinessKeyLike("%activiti%").</div>
</li>
</ul>
<a name="processInstanceBusinessKeyLikeIgnoreCase-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceBusinessKeyLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processInstanceBusinessKeyLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKeyLikeIgnoreCase)</pre>
<div class="block">Only select tasks with a business key  like the given value
 The syntax is that of SQL: for example usage: processInstanceBusinessKeyLike("%activiti%"). 
 
 This method, unlike the <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKeyLike-java.lang.String-"><code>processInstanceBusinessKeyLike(String)</code></a> method will 
 not take in account the upper/lower case: both the input parameter as the column value are
 lowercased when the query is executed.</div>
</li>
</ul>
<a name="executionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;executionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Only select tasks for the given execution.</div>
</li>
</ul>
<a name="taskCreatedOn-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCreatedOn</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskCreatedOn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;createTime)</pre>
<div class="block">Only select tasks that are created on the given date.</div>
</li>
</ul>
<a name="taskCreatedBefore-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCreatedBefore</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskCreatedBefore(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;before)</pre>
<div class="block">Only select tasks that are created before the given date.</div>
</li>
</ul>
<a name="taskCreatedAfter-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCreatedAfter</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskCreatedAfter(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;after)</pre>
<div class="block">Only select tasks that are created after the given date.</div>
</li>
</ul>
<a name="taskCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCategory</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</pre>
<div class="block">Only select tasks with the given category.</div>
</li>
</ul>
<a name="taskDefinitionKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskDefinitionKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</pre>
<div class="block">Only select tasks with the given taskDefinitionKey.
 The task definition key is the id of the userTask:
 &lt;userTask id="xxx" .../&gt;</div>
</li>
</ul>
<a name="taskDefinitionKeyLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDefinitionKeyLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskDefinitionKeyLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;keyLike)</pre>
<div class="block">Only select tasks with a taskDefinitionKey that match the given parameter.
  The syntax is that of SQL: for example usage: taskDefinitionKeyLike("%activiti%").
 The task definition key is the id of the userTask:
 &lt;userTask id="xxx" .../&gt;</div>
</li>
</ul>
<a name="dueDate-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dueDate</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;dueDate(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">Only select tasks with the given due date.</div>
</li>
</ul>
<a name="taskDueDate-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDueDate</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskDueDate(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</pre>
</li>
</ul>
<a name="dueBefore-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dueBefore</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;dueBefore(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">Only select tasks which have a due date before the given date.</div>
</li>
</ul>
<a name="taskDueBefore-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDueBefore</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskDueBefore(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</pre>
</li>
</ul>
<a name="dueAfter-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dueAfter</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;dueAfter(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">Only select tasks which have a due date after the given date.</div>
</li>
</ul>
<a name="taskDueAfter-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDueAfter</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskDueAfter(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</pre>
</li>
</ul>
<a name="withoutDueDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withoutDueDate</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;withoutDueDate()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">Only select tasks with no due date.</div>
</li>
</ul>
<a name="withoutTaskDueDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withoutTaskDueDate</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;withoutTaskDueDate()</pre>
</li>
</ul>
<a name="processDefinitionKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processDefinitionKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</pre>
<div class="block">Only select tasks which are part of a process instance which has the given
 process definition key.</div>
</li>
</ul>
<a name="processDefinitionKeyLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKeyLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processDefinitionKeyLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKeyLike)</pre>
<div class="block">Only select tasks which are part of a process instance which has a
 process definition key like the given value.
 The syntax that should be used is the same as in SQL, eg. %activiti%.</div>
</li>
</ul>
<a name="processDefinitionKeyLikeIgnoreCase-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKeyLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processDefinitionKeyLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKeyLikeIgnoreCase)</pre>
<div class="block">Only select tasks which are part of a process instance which has a
 process definition key like the given value.
 The syntax that should be used is the same as in SQL, eg. %activiti%.
 
 This method, unlike the <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyLike-java.lang.String-"><code>processDefinitionKeyLike(String)</code></a> method will 
  not take in account the upper/lower case: both the input parameter as the column value are
  lowercased when the query is executed.</div>
</li>
</ul>
<a name="processDefinitionKeyIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKeyIn</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processDefinitionKeyIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionKeys)</pre>
<div class="block">Only select tasks that have a process definition for which the key is present in the given list</div>
</li>
</ul>
<a name="processDefinitionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processDefinitionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Only select tasks which are part of a process instance which has the given
 process definition id.</div>
</li>
</ul>
<a name="processDefinitionName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionName</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processDefinitionName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</pre>
<div class="block">Only select tasks which are part of a process instance which has the given
 process definition name.</div>
</li>
</ul>
<a name="processDefinitionNameLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionNameLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processDefinitionNameLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionNameLike)</pre>
<div class="block">Only select tasks which are part of a process instance which has a
 process definition name like the given value.
 The syntax that should be used is the same as in SQL, eg. %activiti%.</div>
</li>
</ul>
<a name="processCategoryIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processCategoryIn</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processCategoryIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processCategoryInList)</pre>
<div class="block">Only select tasks which are part of a process instance whose definition
 belongs to the category which is present in the given list.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processCategoryInList</code> - </dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></code> - When passed category list is empty or <code>null</code> or contains <code>null String</code>.</dd>
</dl>
</li>
</ul>
<a name="processCategoryNotIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processCategoryNotIn</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processCategoryNotIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processCategoryNotInList)</pre>
<div class="block">Only select tasks which are part of a process instance whose definition does not
 belong to the category which is present in the given list.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processCategoryNotInList</code> - </dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></code> - When passed category list is empty or <code>null</code> or contains <code>null String</code>.</dd>
</dl>
</li>
</ul>
<a name="deploymentId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;deploymentId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</pre>
<div class="block">Only select tasks which are part of a process instance which has the given
 deployment id.</div>
</li>
</ul>
<a name="deploymentIdIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentIdIn</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;deploymentIdIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;deploymentIds)</pre>
<div class="block">Only select tasks which are part of a process instance which has the given
 deployment id.</div>
</li>
</ul>
<a name="taskVariableValueEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Only select tasks which have a local task variable with the given name
 set to the given value.</div>
</li>
</ul>
<a name="taskVariableValueEquals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Only select tasks which have at least one local task variable with the given value.</div>
</li>
</ul>
<a name="taskVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueEqualsIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueEqualsIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a local string variable with the given value, 
 case insensitive.
 <p>
 This method only works if your database has encoding/collation that supports case-sensitive
 queries. For example, use "collate UTF-8" on MySQL and for MSSQL, select one of the case-sensitive Collations 
 available (<a href="http://msdn.microsoft.com/en-us/library/ms144250(v=sql.105).aspx">MSDN Server Collation Reference</a>).
 </p></div>
</li>
</ul>
<a name="taskVariableValueNotEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueNotEquals</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueNotEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Only select tasks which have a local task variable with the given name, but
 with a different value than the passed value.
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
</li>
</ul>
<a name="taskVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueNotEqualsIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueNotEqualsIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a local string variable with is not the given value, 
 case insensitive.
 <p>
 This method only works if your database has encoding/collation that supports case-sensitive
 queries. For example, use "collate UTF-8" on MySQL and for MSSQL, select one of the case-sensitive Collations 
 available (<a href="http://msdn.microsoft.com/en-us/library/ms144250(v=sql.105).aspx">MSDN Server Collation Reference</a>).
 </p></div>
</li>
</ul>
<a name="taskVariableValueGreaterThan-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueGreaterThan</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueGreaterThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a local variable value greater than the
 passed value when they ended. Booleans, Byte-arrays and
 <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are
 not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="taskVariableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueGreaterThanOrEqual</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueGreaterThanOrEqual(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a local variable value greater than or
 equal to the passed value when they ended. Booleans, Byte-arrays and
 <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are
 not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="taskVariableValueLessThan-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueLessThan</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueLessThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a local variable value less than the
 passed value when the ended.Booleans,
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type
 wrappers) are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="taskVariableValueLessThanOrEqual-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueLessThanOrEqual</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueLessThanOrEqual(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a local variable value less than or equal
 to the passed value when they ended. Booleans,
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type
 wrappers) are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="taskVariableValueLike-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a local variable value like the given value
 when they ended. This can be used on string variables only.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null. The string can include the
          wildcard character '%' to express like-strategy: starts with
          (string%), ends with (%string) or contains (%string%).</dd>
</dl>
</li>
</ul>
<a name="taskVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskVariableValueLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;taskVariableValueLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a local variable value like the given value (case insensitive)
 when they ended. This can be used on string variables only.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null. The string can include the
          wildcard character '%' to express like-strategy: starts with
          (string%), ends with (%string) or contains (%string%).</dd>
</dl>
</li>
</ul>
<a name="processVariableValueEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Only select tasks which are part of a process that has a variable
 with the given name set to the given value.</div>
</li>
</ul>
<a name="processVariableValueEquals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Only select tasks which are part of a process that has at least one variable
 with the given value.</div>
</li>
</ul>
<a name="processVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueEqualsIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueEqualsIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select tasks which are part of a process that has a local string variable which 
 is not the given value, case insensitive.
 <p>
 This method only works if your database has encoding/collation that supports case-sensitive
 queries. For example, use "collate UTF-8" on MySQL and for MSSQL, select one of the case-sensitive Collations 
 available (<a href="http://msdn.microsoft.com/en-us/library/ms144250(v=sql.105).aspx">MSDN Server Collation Reference</a>).
 </p></div>
</li>
</ul>
<a name="processVariableValueNotEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueNotEquals</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueNotEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Only select tasks which have a variable with the given name, but
 with a different value than the passed value.
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers)
 are not supported.</div>
</li>
</ul>
<a name="processVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueNotEqualsIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueNotEqualsIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select tasks which are part of a process that has a string variable with 
 the given value, case insensitive.
 <p>
 This method only works if your database has encoding/collation that supports case-sensitive
 queries. For example, use "collate UTF-8" on MySQL and for MSSQL, select one of the case-sensitive Collations 
 available (<a href="http://msdn.microsoft.com/en-us/library/ms144250(v=sql.105).aspx">MSDN Server Collation Reference</a>).
 </p></div>
</li>
</ul>
<a name="processVariableValueGreaterThan-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueGreaterThan</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueGreaterThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a global variable value greater than the
 passed value when they ended. Booleans, Byte-arrays and
 <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are
 not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="processVariableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueGreaterThanOrEqual</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueGreaterThanOrEqual(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a global variable value greater than or
 equal to the passed value when they ended. Booleans, Byte-arrays and
 <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are
 not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="processVariableValueLessThan-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueLessThan</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueLessThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a global variable value less than the
 passed value when the ended.Booleans,
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type
 wrappers) are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="processVariableValueLessThanOrEqual-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueLessThanOrEqual</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueLessThanOrEqual(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a global variable value less than or equal
 to the passed value when they ended. Booleans,
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type
 wrappers) are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="processVariableValueLike-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueLike</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a global variable value like the given value
 when they ended. This can be used on string variables only.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null. The string can include the
          wildcard character '%' to express like-strategy: starts with
          (string%), ends with (%string) or contains (%string%).</dd>
</dl>
</li>
</ul>
<a name="processVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processVariableValueLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;processVariableValueLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select tasks which have a global variable value like the given value (case insensitive)
 when they ended. This can be used on string variables only.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null. The string can include the
          wildcard character '%' to express like-strategy: starts with
          (string%), ends with (%string) or contains (%string%).</dd>
</dl>
</li>
</ul>
<a name="includeTaskLocalVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeTaskLocalVariables</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;includeTaskLocalVariables()</pre>
<div class="block">Include local task variables in the task query result</div>
</li>
</ul>
<a name="includeProcessVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeProcessVariables</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;includeProcessVariables()</pre>
<div class="block">Include global task variables in the task query result</div>
</li>
</ul>
<a name="limitTaskVariables-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>limitTaskVariables</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;limitTaskVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;taskVariablesLimit)</pre>
<div class="block">Limit task variables</div>
</li>
</ul>
<a name="locale-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>locale</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;locale(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale)</pre>
<div class="block">Localize task name and description to specified locale.</div>
</li>
</ul>
<a name="withLocalizationFallback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withLocalizationFallback</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;withLocalizationFallback()</pre>
<div class="block">Instruct localization to fallback to more general locales including the default locale of the JVM if the specified locale is not found.</div>
</li>
</ul>
<a name="or--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>or</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;or()</pre>
<div class="block">All query clauses called will be added to a single or-statement. This or-statement will be
 included with the other already existing clauses in the query, joined by an 'and'.
 
 Calling endOr() will add all clauses to the regular query again. Calling or() after endOr() has been called
 will result in an exception.</div>
</li>
</ul>
<a name="endOr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endOr</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;endOr()</pre>
</li>
</ul>
<a name="orderByTaskId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTaskId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTaskId()</pre>
<div class="block">Order by task id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTaskName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTaskName</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTaskName()</pre>
<div class="block">Order by task name (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTaskDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTaskDescription</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTaskDescription()</pre>
<div class="block">Order by description (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTaskPriority--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTaskPriority</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTaskPriority()</pre>
<div class="block">Order by priority (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTaskAssignee--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTaskAssignee</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTaskAssignee()</pre>
<div class="block">Order by assignee (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTaskCreateTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTaskCreateTime</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTaskCreateTime()</pre>
<div class="block">Order by the time on which the tasks were created (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByProcessInstanceId()</pre>
<div class="block">Order by process instance id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByExecutionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByExecutionId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByExecutionId()</pre>
<div class="block">Order by execution id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessDefinitionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByProcessDefinitionId()</pre>
<div class="block">Order by process definition id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTaskDueDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTaskDueDate</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTaskDueDate()</pre>
<div class="block">Order by task due date (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTaskOwner--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTaskOwner</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTaskOwner()</pre>
<div class="block">Order by task owner (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTaskDefinitionKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTaskDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTaskDefinitionKey()</pre>
<div class="block">Order by task definition key (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTenantId</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByTenantId()</pre>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByDueDateNullsFirst--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByDueDateNullsFirst</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByDueDateNullsFirst()</pre>
<div class="block">Order by due date (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).
 If any of the tasks have null for the due date, these will be first in the result.</div>
</li>
</ul>
<a name="orderByDueDateNullsLast--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByDueDateNullsLast</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="type parameter in TaskInfoQuery">T</a>&nbsp;orderByDueDateNullsLast()</pre>
<div class="block">Order by due date (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).
 If any of the tasks have null for the due date, these will be last in the result.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TaskInfoQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/task/TaskInfoQueryWrapper.html" title="class in org.activiti.engine.task"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/task/TaskInfoQuery.html" target="_top">Frames</a></li>
<li><a href="TaskInfoQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
