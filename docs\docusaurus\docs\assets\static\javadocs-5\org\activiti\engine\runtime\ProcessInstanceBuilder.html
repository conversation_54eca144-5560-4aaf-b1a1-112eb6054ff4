<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProcessInstanceBuilder (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProcessInstanceBuilder (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessInstanceBuilder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/ProcessInstanceBuilder.html" target="_top">Frames</a></li>
<li><a href="ProcessInstanceBuilder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.runtime</div>
<h2 title="Interface ProcessInstanceBuilder" class="title">Interface ProcessInstanceBuilder</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ProcessInstanceBuilder</span></pre>
<div class="block">Helper for starting new ProcessInstance.
 
 An instance can be obtained through <a href="../../../../org/activiti/engine/RuntimeService.html#createProcessInstanceBuilder--"><code>RuntimeService.createProcessInstanceBuilder()</code></a>.
 
 processDefinitionId or processDefinitionKey should be set before calling <a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#start--"><code>start()</code></a>
 to start a process instance.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Bassam Al-Sarori</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#businessKey-java.lang.String-">businessKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</code>
<div class="block">Set the businessKey of process instance</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#messageName-java.lang.String-">messageName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</code>
<div class="block">Set the message name that needs to be used to look up the process definition that needs to be used to start the process instance.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#name-java.lang.String-">name</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceName)</code>
<div class="block">Set the name of process instance</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Set the id of the process definition</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Set the key of the process definition, latest version of the process definition with the given key.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#start--">start</a></span>()</code>
<div class="block">Start the process instance</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#tenantId-java.lang.String-">tenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Set the tenantId of process instance</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#transientVariable-java.lang.String-java.lang.Object-">transientVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Adds a transient variable to the process instance</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#transientVariables-java.util.Map-">transientVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</code>
<div class="block">Sets the transient variables</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#variable-java.lang.String-java.lang.Object-">variable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Adds a variable to the process instance</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html#variables-java.util.Map-">variables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Sets the process variables</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="processDefinitionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;processDefinitionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Set the id of the process definition</div>
</li>
</ul>
<a name="processDefinitionKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;processDefinitionKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</pre>
<div class="block">Set the key of the process definition, latest version of the process definition with the given key. 
 If processDefinitionId was set this will be ignored</div>
</li>
</ul>
<a name="messageName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>messageName</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;messageName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</pre>
<div class="block">Set the message name that needs to be used to look up the process definition that needs to be used to start the process instance.</div>
</li>
</ul>
<a name="name-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;name(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceName)</pre>
<div class="block">Set the name of process instance</div>
</li>
</ul>
<a name="businessKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>businessKey</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;businessKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;businessKey)</pre>
<div class="block">Set the businessKey of process instance</div>
</li>
</ul>
<a name="tenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tenantId</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;tenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Set the tenantId of process instance</div>
</li>
</ul>
<a name="variables-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variables</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;variables(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Sets the process variables</div>
</li>
</ul>
<a name="variable-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variable</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;variable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Adds a variable to the process instance</div>
</li>
</ul>
<a name="transientVariables-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>transientVariables</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;transientVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</pre>
<div class="block">Sets the transient variables</div>
</li>
</ul>
<a name="transientVariable-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>transientVariable</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstanceBuilder.html" title="interface in org.activiti.engine.runtime">ProcessInstanceBuilder</a>&nbsp;transientVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Adds a transient variable to the process instance</div>
</li>
</ul>
<a name="start--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>start</h4>
<pre><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime">ProcessInstance</a>&nbsp;start()</pre>
<div class="block">Start the process instance</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></code> - if processDefinitionKey and processDefinitionId are null</dd>
<dd><code><a href="../../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no process definition is deployed with the given processDefinitionKey or processDefinitionId</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessInstanceBuilder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/runtime/ProcessInstanceBuilder.html" target="_top">Frames</a></li>
<li><a href="ProcessInstanceBuilder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
