/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.flowable.batch.api;

/**
 * <AUTHOR>
public interface BatchPartBuilder {

    BatchPartBuilder type(String type);

    BatchPartBuilder searchKey(String searchKey);

    BatchPartBuilder searchKey2(String searchKey2);

    BatchPartBuilder status(String status);

    BatchPartBuilder scopeId(String scopeId);

    BatchPartBuilder subScopeId(String subScopeId);

    BatchPartBuilder scopeType(String scopeType);

    BatchPart create();
}
