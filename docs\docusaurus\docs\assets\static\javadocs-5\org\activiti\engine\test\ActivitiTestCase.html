<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitiTestCase (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitiTestCase (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiTestCase.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/test/ActivitiRule.html" title="class in org.activiti.engine.test"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/test/Deployment.html" title="annotation in org.activiti.engine.test"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/test/ActivitiTestCase.html" target="_top">Frames</a></li>
<li><a href="ActivitiTestCase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.test</div>
<h2 title="Class ActivitiTestCase" class="title">Class ActivitiTestCase</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>junit.framework.Assert</li>
<li>
<ul class="inheritance">
<li>junit.framework.TestCase</li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.test.ActivitiTestCase</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>junit.framework.Test</dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">ActivitiTestCase</span>
extends junit.framework.TestCase</pre>
<div class="block">Convenience for ProcessEngine and services initialization in the form of a JUnit base class.
 
 <p>Usage: <code>public class YourTest extends ActivitiTestCase</code></p>

 <p>The ProcessEngine and the services available to subclasses through protected member fields.  
 The processEngine will be initialized by default with the activiti.cfg.xml resource 
 on the classpath.  To specify a different configuration file, override the 
 <a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#getConfigurationResource--"><code>getConfigurationResource()</code></a> method.
 Process engines will be cached statically.  The first time the setUp is called for a given 
 configuration resource, the process engine will be constructed.</p>
 
 <p>You can declare a deployment with the <a href="../../../../org/activiti/engine/test/Deployment.html" title="annotation in org.activiti.engine.test"><code>Deployment</code></a> annotation.
 This base class will make sure that this deployment gets deployed in the 
 setUp and <code>cascade deleted</code>
 in the tearDown.
 </p>
 
 <p>This class also lets you <a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#setCurrentTime-java.util.Date-"><code>set the current time used by the 
 process engine</code></a>. This can be handy to control the exact time that is used by the engine
 in order to verify e.g. e.g. due dates of timers.  Or start, end and duration times
 in the history service.  In the tearDown, the internal clock will automatically be 
 reset to use the current system time rather then the time that was set during 
 a test method.
 </p></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#configurationResource">configurationResource</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#deploymentId">deploymentId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine">FormService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#formService">formService</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#historicDataService">historicDataService</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#identityService">identityService</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#managementService">managementService</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#processEngine">processEngine</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#processEngineConfiguration">processEngineConfiguration</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#repositoryService">repositoryService</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#runtimeService">runtimeService</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#taskService">taskService</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#ActivitiTestCase--">ActivitiTestCase</a></span>()</code>
<div class="block">uses 'activiti.cfg.xml' as it's configuration resource</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#assertProcessEnded-java.lang.String-">assertProcessEnded</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#closeProcessEngines--">closeProcessEngines</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#getConfigurationResource--">getConfigurationResource</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#getMockSupport--">getMockSupport</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#initializeMockSupport--">initializeMockSupport</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#initializeProcessEngine--">initializeProcessEngine</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#initializeServices--">initializeServices</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#mockSupport--">mockSupport</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#runTest--">runTest</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#setConfigurationResource-java.lang.String-">setConfigurationResource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;configurationResource)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#setCurrentTime-java.util.Date-">setCurrentTime</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;currentTime)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#setUp--">setUp</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/ActivitiTestCase.html#tearDown--">tearDown</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.junit.framework.TestCase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;junit.framework.TestCase</h3>
<code>assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertEquals, assertFalse, assertFalse, assertNotNull, assertNotNull, assertNotSame, assertNotSame, assertNull, assertNull, assertSame, assertSame, assertTrue, assertTrue, countTestCases, createResult, fail, fail, failNotEquals, failNotSame, failSame, format, getName, run, run, runBare, setName, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="configurationResource">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configurationResource</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> configurationResource</pre>
</li>
</ul>
<a name="deploymentId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentId</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> deploymentId</pre>
</li>
</ul>
<a name="processEngineConfiguration">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processEngineConfiguration</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a> processEngineConfiguration</pre>
</li>
</ul>
<a name="processEngine">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processEngine</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a> processEngine</pre>
</li>
</ul>
<a name="repositoryService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>repositoryService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a> repositoryService</pre>
</li>
</ul>
<a name="runtimeService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runtimeService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a> runtimeService</pre>
</li>
</ul>
<a name="taskService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a> taskService</pre>
</li>
</ul>
<a name="historicDataService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>historicDataService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a> historicDataService</pre>
</li>
</ul>
<a name="identityService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>identityService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a> identityService</pre>
</li>
</ul>
<a name="managementService">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>managementService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a> managementService</pre>
</li>
</ul>
<a name="formService">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>formService</h4>
<pre>protected&nbsp;<a href="../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine">FormService</a> formService</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ActivitiTestCase--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ActivitiTestCase</h4>
<pre>public&nbsp;ActivitiTestCase()</pre>
<div class="block">uses 'activiti.cfg.xml' as it's configuration resource</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="assertProcessEnded-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assertProcessEnded</h4>
<pre>public&nbsp;void&nbsp;assertProcessEnded(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
</li>
</ul>
<a name="setUp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUp</h4>
<pre>protected&nbsp;void&nbsp;setUp()
              throws <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>setUp</code>&nbsp;in class&nbsp;<code>junit.framework.TestCase</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code></dd>
</dl>
</li>
</ul>
<a name="runTest--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runTest</h4>
<pre>protected&nbsp;void&nbsp;runTest()
                throws <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a></pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>runTest</code>&nbsp;in class&nbsp;<code>junit.framework.TestCase</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a></code></dd>
</dl>
</li>
</ul>
<a name="initializeProcessEngine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initializeProcessEngine</h4>
<pre>protected&nbsp;void&nbsp;initializeProcessEngine()</pre>
</li>
</ul>
<a name="initializeServices--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initializeServices</h4>
<pre>protected&nbsp;void&nbsp;initializeServices()</pre>
</li>
</ul>
<a name="initializeMockSupport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initializeMockSupport</h4>
<pre>protected&nbsp;void&nbsp;initializeMockSupport()</pre>
</li>
</ul>
<a name="tearDown--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tearDown</h4>
<pre>protected&nbsp;void&nbsp;tearDown()
                 throws <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>tearDown</code>&nbsp;in class&nbsp;<code>junit.framework.TestCase</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code></dd>
</dl>
</li>
</ul>
<a name="closeProcessEngines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeProcessEngines</h4>
<pre>public static&nbsp;void&nbsp;closeProcessEngines()</pre>
</li>
</ul>
<a name="setCurrentTime-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrentTime</h4>
<pre>public&nbsp;void&nbsp;setCurrentTime(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;currentTime)</pre>
</li>
</ul>
<a name="getConfigurationResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigurationResource</h4>
<pre>public&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getConfigurationResource()</pre>
</li>
</ul>
<a name="setConfigurationResource-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfigurationResource</h4>
<pre>public&nbsp;void&nbsp;setConfigurationResource(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;configurationResource)</pre>
</li>
</ul>
<a name="getMockSupport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMockSupport</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a>&nbsp;getMockSupport()</pre>
</li>
</ul>
<a name="mockSupport--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>mockSupport</h4>
<pre>public&nbsp;<a href="../../../../org/activiti/engine/test/mock/ActivitiMockSupport.html" title="class in org.activiti.engine.test.mock">ActivitiMockSupport</a>&nbsp;mockSupport()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiTestCase.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/test/ActivitiRule.html" title="class in org.activiti.engine.test"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/test/Deployment.html" title="annotation in org.activiti.engine.test"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/test/ActivitiTestCase.html" target="_top">Frames</a></li>
<li><a href="ActivitiTestCase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
