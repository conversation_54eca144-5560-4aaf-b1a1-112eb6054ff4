<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DeploymentQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DeploymentQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DeploymentQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/repository/DiagramEdge.html" title="class in org.activiti.engine.repository"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/DeploymentQuery.html" target="_top">Frames</a></li>
<li><a href="DeploymentQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.repository</div>
<h2 title="Interface DeploymentQuery" class="title">Interface DeploymentQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>,<a href="../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository">Deployment</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">DeploymentQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>,<a href="../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository">Deployment</a>&gt;</pre>
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s.
 
 Note that it is impossible to retrieve the deployment resources through the
 results of this operation, since that would cause a huge transfer of
 (possibly) unneeded bytes over the wire.
 
 To retrieve the actual bytes of a deployment resource use the operations on
 the <a href="../../../../org/activiti/engine/RepositoryService.html#getDeploymentResourceNames-java.lang.String-"><code>RepositoryService.getDeploymentResourceNames(String)</code></a> and
 <a href="../../../../org/activiti/engine/RepositoryService.html#getResourceAsStream-java.lang.String-java.lang.String-"><code>RepositoryService.getResourceAsStream(String, String)</code></a></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#deploymentCategory-java.lang.String-">deploymentCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</code>
<div class="block">Only select deployments with the given category.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#deploymentCategoryNotEquals-java.lang.String-">deploymentCategoryNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;categoryNotEquals)</code>
<div class="block">Only select deployments that have a different category then the given one.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#deploymentId-java.lang.String-">deploymentId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block">Only select deployments with the given deployment id.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#deploymentName-java.lang.String-">deploymentName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Only select deployments with the given name.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#deploymentNameLike-java.lang.String-">deploymentNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLike)</code>
<div class="block">Only select deployments with a name like the given string.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#deploymentTenantId-java.lang.String-">deploymentTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select deployment that have the given tenant id.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#deploymentTenantIdLike-java.lang.String-">deploymentTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select deployments with a tenant id like the given one.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#deploymentWithoutTenantId--">deploymentWithoutTenantId</a></span>()</code>
<div class="block">Only select deployments that do not have a tenant id.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#orderByDeploymentId--">orderByDeploymentId</a></span>()</code>
<div class="block">Order by deployment id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#orderByDeploymenTime--">orderByDeploymenTime</a></span>()</code>
<div class="block">Order by deployment time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#orderByDeploymentName--">orderByDeploymentName</a></span>()</code>
<div class="block">Order by deployment name (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</code>
<div class="block">Only select deployments with the given process definition key.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html#processDefinitionKeyLike-java.lang.String-">processDefinitionKeyLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;keyLike)</code>
<div class="block">Only select deployments with a process definition key like the given
 string.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="deploymentId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentId</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;deploymentId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</pre>
<div class="block">Only select deployments with the given deployment id.</div>
</li>
</ul>
<a name="deploymentName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentName</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;deploymentName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Only select deployments with the given name.</div>
</li>
</ul>
<a name="deploymentNameLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentNameLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;deploymentNameLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLike)</pre>
<div class="block">Only select deployments with a name like the given string.</div>
</li>
</ul>
<a name="deploymentCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentCategory</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;deploymentCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</pre>
<div class="block">Only select deployments with the given category.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#category-java.lang.String-"><code>DeploymentBuilder.category(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="deploymentCategoryNotEquals-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentCategoryNotEquals</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;deploymentCategoryNotEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;categoryNotEquals)</pre>
<div class="block">Only select deployments that have a different category then the given one.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#category-java.lang.String-"><code>DeploymentBuilder.category(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="deploymentTenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentTenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;deploymentTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Only select deployment that have the given tenant id.</div>
</li>
</ul>
<a name="deploymentTenantIdLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentTenantIdLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;deploymentTenantIdLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</pre>
<div class="block">Only select deployments with a tenant id like the given one.</div>
</li>
</ul>
<a name="deploymentWithoutTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentWithoutTenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;deploymentWithoutTenantId()</pre>
<div class="block">Only select deployments that do not have a tenant id.</div>
</li>
</ul>
<a name="processDefinitionKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;processDefinitionKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key)</pre>
<div class="block">Only select deployments with the given process definition key.</div>
</li>
</ul>
<a name="processDefinitionKeyLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKeyLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;processDefinitionKeyLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;keyLike)</pre>
<div class="block">Only select deployments with a process definition key like the given
 string.</div>
</li>
</ul>
<a name="orderByDeploymentId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByDeploymentId</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;orderByDeploymentId()</pre>
<div class="block">Order by deployment id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByDeploymentName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByDeploymentName</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;orderByDeploymentName()</pre>
<div class="block">Order by deployment name (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByDeploymenTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByDeploymenTime</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;orderByDeploymenTime()</pre>
<div class="block">Order by deployment time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTenantId--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByTenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;orderByTenantId()</pre>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DeploymentQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/repository/DiagramEdge.html" title="class in org.activiti.engine.repository"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/DeploymentQuery.html" target="_top">Frames</a></li>
<li><a href="DeploymentQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
