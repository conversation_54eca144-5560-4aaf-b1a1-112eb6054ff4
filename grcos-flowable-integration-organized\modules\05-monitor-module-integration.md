# Monitor Module Flowable Integration

## Overview

The Monitor Module integration with Flowable automates security event response, incident management workflows, and real-time threat detection processes. This integration connects Wazuh SIEM with intelligent workflow automation to ensure rapid response to security events and automated incident handling.

## Integration Architecture

### Security Event Workflow Patterns

#### 1. Security Event Processing Workflow
Automated workflow for processing security events from Wazuh SIEM and determining appropriate response actions.

#### 2. Incident Response Workflow
Structured incident response process with automated escalation and stakeholder notification.

#### 3. Threat Hunting Workflow
Proactive threat hunting processes with AI-assisted analysis and investigation.

#### 4. Compliance Monitoring Workflow
Continuous monitoring for compliance violations with automated remediation triggers.

## Wazuh SIEM Integration

### Service Task Implementation

#### SecurityEventProcessorTask.java
```java
@Component("securityEventProcessorTask")
public class SecurityEventProcessorTask extends AbstractGRCOSServiceTask {
    
    @Autowired
    private WazuhService wazuhService;
    
    @Autowired
    private ThreatIntelligenceService threatIntelService;
    
    @Autowired
    private SecurityAgent securityAgent;
    
    @Override
    protected void validateExecution(DelegateExecution execution) throws ValidationException {
        String operation = (String) execution.getVariable("securityOperation");
        
        if (operation == null || operation.trim().isEmpty()) {
            throw new ValidationException("Security operation is required");
        }
        
        if (!isValidSecurityOperation(operation)) {
            throw new ValidationException("Invalid security operation: " + operation);
        }
    }
    
    @Override
    protected TaskExecutionResult executeTask(DelegateExecution execution) throws TaskExecutionException {
        String operation = (String) execution.getVariable("securityOperation");
        
        try {
            switch (operation) {
                case "process_security_event":
                    return processSecurityEvent(execution);
                case "analyze_threat":
                    return analyzeThreat(execution);
                case "investigate_incident":
                    return investigateIncident(execution);
                case "execute_response":
                    return executeSecurityResponse(execution);
                case "update_threat_intelligence":
                    return updateThreatIntelligence(execution);
                default:
                    throw new TaskExecutionException("Unsupported security operation: " + operation);
            }
        } catch (Exception e) {
            throw new TaskExecutionException("Security operation failed", e);
        }
    }
    
    private TaskExecutionResult processSecurityEvent(DelegateExecution execution) {
        String eventId = (String) execution.getVariable("eventId");
        Map<String, Object> eventData = (Map<String, Object>) execution.getVariable("eventData");
        
        // Get security event from Wazuh
        WazuhAlert alert = wazuhService.getAlert(eventId);
        
        // Enrich event with threat intelligence
        ThreatContext threatContext = threatIntelService.enrichEvent(alert);
        
        // Analyze event severity and impact
        SecurityEventAnalysis analysis = securityAgent.analyzeSecurityEvent(alert, threatContext);
        
        // Determine response actions
        List<ResponseAction> responseActions = securityAgent.determineResponseActions(analysis);
        
        // Update event status
        wazuhService.updateAlertStatus(eventId, "processed");
        
        Map<String, Object> results = new HashMap<>();
        results.put("eventId", eventId);
        results.put("severity", analysis.getSeverity());
        results.put("riskScore", analysis.getRiskScore());
        results.put("threatIndicators", analysis.getThreatIndicators());
        results.put("responseActions", responseActions);
        results.put("requiresIncident", analysis.requiresIncidentCreation());
        
        return TaskExecutionResult.success(results);
    }
    
    private TaskExecutionResult analyzeThreat(DelegateExecution execution) {
        String threatId = (String) execution.getVariable("threatId");
        Map<String, Object> threatData = (Map<String, Object>) execution.getVariable("threatData");
        
        // Perform threat analysis
        ThreatAnalysisResult analysis = securityAgent.performThreatAnalysis(threatId, threatData);
        
        // Check against known threat patterns
        List<ThreatPattern> matchingPatterns = threatIntelService.findMatchingPatterns(threatData);
        
        // Generate threat indicators
        List<ThreatIndicator> indicators = securityAgent.generateThreatIndicators(analysis);
        
        // Update threat intelligence database
        threatIntelService.updateThreatIntelligence(threatId, analysis, indicators);
        
        Map<String, Object> results = new HashMap<>();
        results.put("threatId", threatId);
        results.put("analysisResult", analysis.getResult());
        results.put("confidence", analysis.getConfidence());
        results.put("matchingPatterns", matchingPatterns);
        results.put("indicators", indicators);
        results.put("recommendedActions", analysis.getRecommendedActions());
        
        return TaskExecutionResult.success(results);
    }
    
    private TaskExecutionResult investigateIncident(DelegateExecution execution) {
        String incidentId = (String) execution.getVariable("incidentId");
        
        // Get incident details
        SecurityIncident incident = wazuhService.getIncident(incidentId);
        
        // Perform automated investigation
        IncidentInvestigationResult investigation = securityAgent.investigateIncident(incident);
        
        // Collect additional evidence
        List<Evidence> evidence = collectIncidentEvidence(incident, investigation);
        
        // Generate investigation report
        IncidentReport report = generateIncidentReport(incident, investigation, evidence);
        
        // Update incident with investigation results
        wazuhService.updateIncident(incidentId, investigation, evidence);
        
        Map<String, Object> results = new HashMap<>();
        results.put("incidentId", incidentId);
        results.put("investigationStatus", investigation.getStatus());
        results.put("findings", investigation.getFindings());
        results.put("evidence", evidence);
        results.put("reportId", report.getId());
        results.put("nextActions", investigation.getNextActions());
        
        return TaskExecutionResult.success(results);
    }
    
    private TaskExecutionResult executeSecurityResponse(DelegateExecution execution) {
        String responseId = (String) execution.getVariable("responseId");
        List<ResponseAction> actions = (List<ResponseAction>) execution.getVariable("responseActions");
        
        List<ResponseResult> results = new ArrayList<>();
        
        for (ResponseAction action : actions) {
            try {
                ResponseResult result = executeResponseAction(action);
                results.add(result);
                
                // Record action execution
                wazuhService.recordResponseAction(responseId, action, result);
                
            } catch (Exception e) {
                logger.error("Failed to execute response action: {}", action.getType(), e);
                results.add(ResponseResult.failed(action, e.getMessage()));
            }
        }
        
        // Evaluate overall response effectiveness
        ResponseEffectiveness effectiveness = evaluateResponseEffectiveness(results);
        
        Map<String, Object> taskResults = new HashMap<>();
        taskResults.put("responseId", responseId);
        taskResults.put("executedActions", results.size());
        taskResults.put("successfulActions", results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum());
        taskResults.put("effectiveness", effectiveness);
        taskResults.put("responseComplete", effectiveness.isComplete());
        
        return TaskExecutionResult.success(taskResults);
    }
    
    private ResponseResult executeResponseAction(ResponseAction action) {
        switch (action.getType()) {
            case "BLOCK_IP":
                return executeIpBlockAction(action);
            case "QUARANTINE_HOST":
                return executeHostQuarantineAction(action);
            case "DISABLE_USER":
                return executeUserDisableAction(action);
            case "ISOLATE_NETWORK":
                return executeNetworkIsolationAction(action);
            case "COLLECT_FORENSICS":
                return executeForensicsCollectionAction(action);
            default:
                throw new UnsupportedOperationException("Unknown response action type: " + action.getType());
        }
    }
    
    private ResponseResult executeIpBlockAction(ResponseAction action) {
        String ipAddress = action.getParameter("ipAddress");
        
        // Execute IP blocking through security tools
        boolean success = wazuhService.blockIpAddress(ipAddress);
        
        return ResponseResult.builder()
            .action(action)
            .success(success)
            .message(success ? "IP blocked successfully" : "Failed to block IP")
            .executionTime(System.currentTimeMillis())
            .build();
    }
}
```

### Security Event Workflows

#### security-event-processing.bpmn20.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://grcos.com/workflows">
  
  <process id="security-event-processing" name="Security Event Processing" isExecutable="true">
    
    <!-- Message Start Event for Security Alerts -->
    <startEvent id="security-alert-received" name="Security Alert Received">
      <messageEventDefinition messageRef="SecurityAlertMessage"/>
    </startEvent>
    
    <!-- Process Security Event -->
    <serviceTask id="process-event" name="Process Security Event"
                 flowable:class="com.grcos.workflow.SecurityEventProcessorTask">
      <extensionElements>
        <flowable:field name="securityOperation">
          <flowable:string>process_security_event</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Risk Assessment Gateway -->
    <exclusiveGateway id="risk-assessment" name="Risk Assessment"/>
    
    <!-- High Risk Path -->
    <serviceTask id="create-incident" name="Create Security Incident"
                 flowable:class="com.grcos.workflow.IncidentCreationTask">
      <extensionElements>
        <flowable:field name="incidentType">
          <flowable:string>security</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Call Incident Response Workflow -->
    <callActivity id="incident-response" name="Incident Response Workflow"
                  calledElement="incident-response-workflow">
      <extensionElements>
        <flowable:in source="incidentId" target="incidentId"/>
        <flowable:in source="severity" target="severity"/>
        <flowable:out source="responseComplete" target="incidentResolved"/>
      </extensionElements>
    </callActivity>
    
    <!-- Medium Risk Path -->
    <serviceTask id="automated-response" name="Execute Automated Response"
                 flowable:class="com.grcos.workflow.SecurityEventProcessorTask">
      <extensionElements>
        <flowable:field name="securityOperation">
          <flowable:string>execute_response</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Low Risk Path -->
    <serviceTask id="log-event" name="Log Security Event"
                 flowable:class="com.grcos.workflow.EventLoggingTask"/>
    
    <!-- Update Threat Intelligence -->
    <serviceTask id="update-threat-intel" name="Update Threat Intelligence"
                 flowable:class="com.grcos.workflow.SecurityEventProcessorTask">
      <extensionElements>
        <flowable:field name="securityOperation">
          <flowable:string>update_threat_intelligence</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Notification Task -->
    <serviceTask id="notify-security-team" name="Notify Security Team"
                 flowable:class="com.grcos.workflow.NotificationTask">
      <extensionElements>
        <flowable:field name="notificationType">
          <flowable:string>security-event-processed</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- End Event -->
    <endEvent id="event-processed" name="Security Event Processed"/>
    
    <!-- Sequence Flows -->
    <sequenceFlow id="flow1" sourceRef="security-alert-received" targetRef="process-event"/>
    <sequenceFlow id="flow2" sourceRef="process-event" targetRef="risk-assessment"/>
    
    <!-- High Risk Flow -->
    <sequenceFlow id="flow3" sourceRef="risk-assessment" targetRef="create-incident">
      <conditionExpression>${riskScore >= 8}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow4" sourceRef="create-incident" targetRef="incident-response"/>
    <sequenceFlow id="flow5" sourceRef="incident-response" targetRef="update-threat-intel"/>
    
    <!-- Medium Risk Flow -->
    <sequenceFlow id="flow6" sourceRef="risk-assessment" targetRef="automated-response">
      <conditionExpression>${riskScore >= 5 && riskScore < 8}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow7" sourceRef="automated-response" targetRef="notify-security-team"/>
    
    <!-- Low Risk Flow -->
    <sequenceFlow id="flow8" sourceRef="risk-assessment" targetRef="log-event">
      <conditionExpression>${riskScore < 5}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow9" sourceRef="log-event" targetRef="update-threat-intel"/>
    
    <!-- Common Flows -->
    <sequenceFlow id="flow10" sourceRef="update-threat-intel" targetRef="event-processed"/>
    <sequenceFlow id="flow11" sourceRef="notify-security-team" targetRef="update-threat-intel"/>
    
  </process>
  
  <!-- Message Definition -->
  <message id="SecurityAlertMessage" name="SecurityAlert"/>
  
</definitions>
```

#### incident-response-workflow.bpmn20.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://grcos.com/workflows">
  
  <process id="incident-response-workflow" name="Incident Response Workflow" isExecutable="true">
    
    <!-- Start Event -->
    <startEvent id="incident-start" name="Incident Response Started"/>
    
    <!-- Initial Assessment -->
    <userTask id="initial-assessment" name="Initial Incident Assessment"
              flowable:candidateGroups="security-analysts">
      <documentation>Perform initial assessment of the security incident</documentation>
      <extensionElements>
        <flowable:formProperty id="incidentType" name="Incident Type" type="enum" required="true">
          <flowable:value id="malware" name="Malware"/>
          <flowable:value id="data-breach" name="Data Breach"/>
          <flowable:value id="unauthorized-access" name="Unauthorized Access"/>
          <flowable:value id="ddos" name="DDoS Attack"/>
          <flowable:value id="insider-threat" name="Insider Threat"/>
        </flowable:formProperty>
        <flowable:formProperty id="impactAssessment" name="Impact Assessment" type="enum" required="true">
          <flowable:value id="low" name="Low"/>
          <flowable:value id="medium" name="Medium"/>
          <flowable:value id="high" name="High"/>
          <flowable:value id="critical" name="Critical"/>
        </flowable:formProperty>
        <flowable:formProperty id="affectedSystems" name="Affected Systems" type="string" required="true"/>
        <flowable:formProperty id="initialFindings" name="Initial Findings" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Severity Gateway -->
    <exclusiveGateway id="severity-gateway" name="Incident Severity"/>
    
    <!-- Critical Incident Path -->
    <userTask id="escalate-to-ciso" name="Escalate to CISO"
              flowable:assignee="ciso">
      <documentation>Critical incident requires CISO involvement</documentation>
      <extensionElements>
        <flowable:formProperty id="cisoDecision" name="CISO Decision" type="enum" required="true">
          <flowable:value id="activate-crisis-team" name="Activate Crisis Team"/>
          <flowable:value id="standard-response" name="Standard Response"/>
          <flowable:value id="external-support" name="Engage External Support"/>
        </flowable:formProperty>
        <flowable:formProperty id="cisoNotes" name="CISO Notes" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Parallel Investigation and Containment -->
    <parallelGateway id="parallel-start" name="Start Parallel Activities"/>
    
    <!-- Investigation Branch -->
    <serviceTask id="automated-investigation" name="Automated Investigation"
                 flowable:class="com.grcos.workflow.SecurityEventProcessorTask">
      <extensionElements>
        <flowable:field name="securityOperation">
          <flowable:string>investigate_incident</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <userTask id="manual-investigation" name="Manual Investigation"
              flowable:candidateGroups="incident-responders">
      <documentation>Perform detailed manual investigation</documentation>
      <extensionElements>
        <flowable:formProperty id="investigationFindings" name="Investigation Findings" type="string" required="true"/>
        <flowable:formProperty id="rootCause" name="Root Cause" type="string"/>
        <flowable:formProperty id="evidenceCollected" name="Evidence Collected" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Containment Branch -->
    <serviceTask id="automated-containment" name="Automated Containment"
                 flowable:class="com.grcos.workflow.SecurityEventProcessorTask">
      <extensionElements>
        <flowable:field name="securityOperation">
          <flowable:string>execute_response</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <userTask id="manual-containment" name="Manual Containment Actions"
              flowable:candidateGroups="incident-responders">
      <documentation>Execute manual containment actions</documentation>
      <extensionElements>
        <flowable:formProperty id="containmentActions" name="Containment Actions Taken" type="string" required="true"/>
        <flowable:formProperty id="containmentEffective" name="Containment Effective" type="boolean" required="true"/>
      </extensionElements>
    </userTask>
    
    <!-- Parallel End -->
    <parallelGateway id="parallel-end" name="End Parallel Activities"/>
    
    <!-- Eradication and Recovery -->
    <userTask id="eradication-recovery" name="Eradication and Recovery"
              flowable:candidateGroups="system-administrators,incident-responders">
      <documentation>Eradicate threat and recover affected systems</documentation>
      <extensionElements>
        <flowable:formProperty id="eradicationSteps" name="Eradication Steps" type="string" required="true"/>
        <flowable:formProperty id="recoveryActions" name="Recovery Actions" type="string" required="true"/>
        <flowable:formProperty id="systemsRestored" name="Systems Restored" type="boolean" required="true"/>
      </extensionElements>
    </userTask>
    
    <!-- Post-Incident Activities -->
    <serviceTask id="generate-incident-report" name="Generate Incident Report"
                 flowable:class="com.grcos.workflow.IncidentReportGeneratorTask"/>
    
    <userTask id="lessons-learned" name="Lessons Learned Review"
              flowable:candidateGroups="security-team">
      <documentation>Conduct lessons learned review</documentation>
      <extensionElements>
        <flowable:formProperty id="lessonsLearned" name="Lessons Learned" type="string" required="true"/>
        <flowable:formProperty id="improvementActions" name="Improvement Actions" type="string"/>
        <flowable:formProperty id="policyUpdates" name="Policy Updates Required" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Close Incident -->
    <serviceTask id="close-incident" name="Close Incident"
                 flowable:class="com.grcos.workflow.IncidentClosureTask"/>
    
    <!-- End Event -->
    <endEvent id="incident-closed" name="Incident Closed"/>
    
    <!-- Sequence Flows -->
    <sequenceFlow id="flow1" sourceRef="incident-start" targetRef="initial-assessment"/>
    <sequenceFlow id="flow2" sourceRef="initial-assessment" targetRef="severity-gateway"/>
    
    <!-- Critical Path -->
    <sequenceFlow id="flow3" sourceRef="severity-gateway" targetRef="escalate-to-ciso">
      <conditionExpression>${impactAssessment == 'critical'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow4" sourceRef="escalate-to-ciso" targetRef="parallel-start"/>
    
    <!-- Standard Path -->
    <sequenceFlow id="flow5" sourceRef="severity-gateway" targetRef="parallel-start">
      <conditionExpression>${impactAssessment != 'critical'}</conditionExpression>
    </sequenceFlow>
    
    <!-- Parallel Flows -->
    <sequenceFlow id="flow6" sourceRef="parallel-start" targetRef="automated-investigation"/>
    <sequenceFlow id="flow7" sourceRef="parallel-start" targetRef="automated-containment"/>
    <sequenceFlow id="flow8" sourceRef="automated-investigation" targetRef="manual-investigation"/>
    <sequenceFlow id="flow9" sourceRef="automated-containment" targetRef="manual-containment"/>
    <sequenceFlow id="flow10" sourceRef="manual-investigation" targetRef="parallel-end"/>
    <sequenceFlow id="flow11" sourceRef="manual-containment" targetRef="parallel-end"/>
    
    <!-- Post-Parallel Flows -->
    <sequenceFlow id="flow12" sourceRef="parallel-end" targetRef="eradication-recovery"/>
    <sequenceFlow id="flow13" sourceRef="eradication-recovery" targetRef="generate-incident-report"/>
    <sequenceFlow id="flow14" sourceRef="generate-incident-report" targetRef="lessons-learned"/>
    <sequenceFlow id="flow15" sourceRef="lessons-learned" targetRef="close-incident"/>
    <sequenceFlow id="flow16" sourceRef="close-incident" targetRef="incident-closed"/>
    
  </process>
</definitions>
```

## Event-Driven Integration

### Wazuh Event Listener

```java
@Component
public class WazuhEventListener {
    
    @Autowired
    private FlowableRuntimeService runtimeService;
    
    @Autowired
    private SecurityAgent securityAgent;
    
    @KafkaListener(topics = "wazuh.alerts")
    public void handleWazuhAlert(WazuhAlert alert) {
        try {
            // Analyze alert severity
            AlertAnalysis analysis = securityAgent.analyzeAlert(alert);
            
            if (analysis.requiresWorkflowTrigger()) {
                Map<String, Object> variables = new HashMap<>();
                variables.put("eventId", alert.getId());
                variables.put("eventData", alert.toMap());
                variables.put("severity", analysis.getSeverity());
                variables.put("riskScore", analysis.getRiskScore());
                variables.put("alertTimestamp", alert.getTimestamp());
                
                // Start security event processing workflow
                ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
                    "security-event-processing",
                    alert.getId(),
                    variables
                );
                
                logger.info("Started security event workflow for alert: {} (Process: {})", 
                           alert.getId(), processInstance.getId());
            }
            
        } catch (Exception e) {
            logger.error("Failed to process Wazuh alert: {}", alert.getId(), e);
        }
    }
    
    @KafkaListener(topics = "wazuh.incidents")
    public void handleWazuhIncident(WazuhIncident incident) {
        try {
            Map<String, Object> variables = new HashMap<>();
            variables.put("incidentId", incident.getId());
            variables.put("incidentType", incident.getType());
            variables.put("severity", incident.getSeverity());
            variables.put("affectedSystems", incident.getAffectedSystems());
            
            // Start incident response workflow
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
                "incident-response-workflow",
                incident.getId(),
                variables
            );
            
            logger.info("Started incident response workflow for incident: {} (Process: {})", 
                       incident.getId(), processInstance.getId());
            
        } catch (Exception e) {
            logger.error("Failed to process Wazuh incident: {}", incident.getId(), e);
        }
    }
}
```

## Performance Monitoring

### Security Workflow Metrics

```java
@Component
public class SecurityWorkflowMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter securityEventCounter;
    private final Timer incidentResponseTimer;
    private final Gauge activeIncidentsGauge;
    
    public SecurityWorkflowMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.securityEventCounter = Counter.builder("grcos.security.events.total")
            .description("Total number of security events processed")
            .register(meterRegistry);
        this.incidentResponseTimer = Timer.builder("grcos.security.incident.response.duration")
            .description("Incident response workflow duration")
            .register(meterRegistry);
        this.activeIncidentsGauge = Gauge.builder("grcos.security.incidents.active")
            .description("Number of active security incidents")
            .register(meterRegistry, this, SecurityWorkflowMetrics::getActiveIncidentCount);
    }
    
    public void recordSecurityEvent(String eventType, String severity) {
        securityEventCounter.increment(
            Tags.of("type", eventType, "severity", severity)
        );
    }
    
    public void recordIncidentResponse(Duration duration, String incidentType) {
        incidentResponseTimer.record(duration, Tags.of("type", incidentType));
    }
    
    private double getActiveIncidentCount() {
        return runtimeService.createProcessInstanceQuery()
            .processDefinitionKey("incident-response-workflow")
            .active()
            .count();
    }
}
```

This Monitor Module integration provides comprehensive automation for security event processing and incident response while maintaining rapid response times and complete audit trails.
