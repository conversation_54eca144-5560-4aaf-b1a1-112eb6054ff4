version: '3.6'
services:
  flowable-rest-app:
    image: flowable/flowable-rest
    depends_on:
      - flowable-db
    environment:
      - SERVER_PORT=8080
      - SPRING_DATASOURCE_DRIVER-CLASS-NAME=org.postgresql.Driver
      - SPRING_DATASOURCE_URL=*******************************************
      - SPRING_DATASOURCE_USERNAME=flowable
      - SPRING_DATASOURCE_PASSWORD=flowable
      - FLOWABLE_REST_APP_ADMIN_USER-ID=rest-admin
      - FLOWABLE_REST_APP_ADMIN_PASSWORD=test
      - FLOWABLE_REST_APP_ADMIN_FIRST-NAME=Rest
      - FLOWABLE_REST_APP_ADMIN_LAST-NAME=Admin
    expose:
      - "9977"
    entrypoint: ["./wait-for-something.sh", "flowable-db", "5432", "PostgreSQL", "/flowable-entrypoint.sh"]
  flowable-db:
    image: postgres:9.6-alpine
    container_name: flowable-postgres
    environment:
        - POSTGRES_PASSWORD=flowable
        - POSTGRES_USER=flowable
        - POSTGRES_DB=flowable
    ports:
        - 5433:5432
    command: postgres
  flowable-lb:
    image: dockercloud/haproxy
    links:
      - flowable-rest-app
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - STATS_AUTH=flowable:flowable
    ports:
      - 8080:80
      - 8081:1936
