<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitiEvent (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitiEvent (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEvent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiErrorEvent.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/ActivitiEvent.html" target="_top">Frames</a></li>
<li><a href="ActivitiEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate.event</div>
<h2 title="Interface ActivitiEvent" class="title">Interface ActivitiEvent</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../../org/activiti/engine/delegate/event/ActivitiActivityCancelledEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiActivityCancelledEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiActivityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiActivityEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiCancelledEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiCancelledEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEntityWithVariablesEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityWithVariablesEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiErrorEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiErrorEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiMembershipEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiMembershipEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiMessageEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiMessageEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiProcessStartedEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiProcessStartedEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiSequenceFlowTakenEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiSequenceFlowTakenEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiSignalEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiSignalEvent</a>, <a href="../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiActivityCancelledEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiActivityCancelledEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiActivityEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiActivityEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiEntityEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEntityEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiEntityExceptionEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEntityExceptionEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiEntityWithVariablesEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEntityWithVariablesEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiErrorEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiErrorEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiMembershipEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiMembershipEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiMessageEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiMessageEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiProcessCancelledEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiProcessCancelledEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiProcessStartedEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiProcessStartedEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiSequenceFlowTakenEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiSequenceFlowTakenEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiSignalEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiSignalEventImpl</a>, <a href="../../../../../org/activiti/engine/delegate/event/impl/ActivitiVariableEventImpl.html" title="class in org.activiti.engine.delegate.event.impl">ActivitiVariableEventImpl</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ActivitiEvent</span></pre>
<div class="block">Describes an event that occurred in the Activiti Engine which is dispatched to external
 listeners, if any.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Frederik Heremans</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine">EngineServices</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getEngineServices--">getEngineServices</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getExecutionId--">getExecutionId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getProcessDefinitionId--">getProcessDefinitionId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getProcessInstanceId--">getProcessInstanceId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html#getType--">getType</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;getType()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>type of event.</dd>
</dl>
</li>
</ul>
<a name="getExecutionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExecutionId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getExecutionId()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the id of the execution this event is associated with. Returns null, if the event
 was not dispatched from within an active execution.</dd>
</dl>
</li>
</ul>
<a name="getProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessInstanceId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessInstanceId()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the id of the process instance this event is associated with. Returns null, if the event
 was not dispatched from within an active execution.</dd>
</dl>
</li>
</ul>
<a name="getProcessDefinitionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDefinitionId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessDefinitionId()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the id of the process definition this event is associated with. Returns null, if the event
 was not dispatched from within an active execution.</dd>
</dl>
</li>
</ul>
<a name="getEngineServices--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getEngineServices</h4>
<pre><a href="../../../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine">EngineServices</a>&nbsp;getEngineServices()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the <a href="../../../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine"><code>EngineServices</code></a> associated to the engine this event
 originated from. Returns null, when not called from within a listener call or when no
 Activiti context is active.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEvent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiErrorEvent.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/ActivitiEvent.html" target="_top">Frames</a></li>
<li><a href="ActivitiEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
