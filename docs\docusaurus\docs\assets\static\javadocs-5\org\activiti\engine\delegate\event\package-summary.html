<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine.delegate.event (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.activiti.engine.delegate.event (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../../org/activiti/engine/delegate/event/impl/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.activiti.engine.delegate.event</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiActivityCancelledEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiActivityCancelledEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> related to cancel event being sent to an activity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiActivityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiActivityEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> related to an activity within an execution;</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiCancelledEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiCancelledEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> related to cancel event being sent when activiti
 object is cancelled.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> related to a single entity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEntityWithVariablesEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEntityWithVariablesEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a> related to a single entity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiErrorEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiErrorEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> related to an error being sent to an activity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiEvent</a></td>
<td class="colLast">
<div class="block">Describes an event that occurred in the Activiti Engine which is dispatched to external
 listeners, if any.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventDispatcher.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventDispatcher</a></td>
<td class="colLast">
<div class="block">Dispatcher which allows for adding and removing <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEventListener</code></a>s to the Activiti Engine as well
 as dispatching <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> to all the listeners registered.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event">ActivitiEventListener</a></td>
<td class="colLast">
<div class="block">Describes a class that listens for <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a>s dispatched by the engine.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiExceptionEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiExceptionEvent</a></td>
<td class="colLast">
<div class="block">Indicates the <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> also contains information about a <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang"><code>Throwable</code></a>
 that occurred, triggering the event.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiMembershipEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiMembershipEvent</a></td>
<td class="colLast">
<div class="block">An event related to group memberships.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiMessageEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiMessageEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> related to an message being sent to an activity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiProcessStartedEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiProcessStartedEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> related to start event being sent when activiti process
 instance is started.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiSequenceFlowTakenEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiSequenceFlowTakenEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> that indicates a certain sequence flow has been taken.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiSignalEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiSignalEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> related to a signal being sent to an activity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiVariableEvent.html" title="interface in org.activiti.engine.delegate.event">ActivitiVariableEvent</a></td>
<td class="colLast">
<div class="block">An <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a> related to a single variable.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/BaseEntityEventListener.html" title="class in org.activiti.engine.delegate.event">BaseEntityEventListener</a></td>
<td class="colLast">
<div class="block">Base event listener that can be used when implementing an
 <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEventListener</code></a> to get notified when an entity is created,
 updated, deleted or if another entity-related event occurs.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a></td>
<td class="colLast">
<div class="block">Enumeration containing all possible types of <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a>s.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../../org/activiti/engine/delegate/event/impl/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
