<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HistoricProcessInstanceQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HistoricProcessInstanceQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6,"i44":6,"i45":6,"i46":6,"i47":6,"i48":6,"i49":6,"i50":6,"i51":6,"i52":6,"i53":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricProcessInstanceQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricProcessInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricProcessInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.history</div>
<h2 title="Interface HistoricProcessInstanceQuery" class="title">Interface HistoricProcessInstanceQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history">HistoricProcessInstance</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">HistoricProcessInstanceQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history">HistoricProcessInstance</a>&gt;</pre>
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez, Tijs Rademakers, Falko Menge</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#deleted--">deleted</a></span>()</code>
<div class="block">Only select historic process instances that are deleted.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#deploymentId-java.lang.String-">deploymentId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block">Only select historic process instances that are defined by a process
 definition with the given deployment identifier.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#deploymentIdIn-java.util.List-">deploymentIdIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;deploymentIds)</code>
<div class="block">Only select historic process instances that are defined by a process
 definition with one of the given deployment identifiers.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#endOr--">endOr</a></span>()</code>
<div class="block">End an OR statement.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#excludeSubprocesses-boolean-">excludeSubprocesses</a></span>(boolean&nbsp;excludeSubprocesses)</code>
<div class="block">Exclude sub processes from the query result;</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#finished--">finished</a></span>()</code>
<div class="block">Only select historic process instances that are completely finished.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#finishedAfter-java.util.Date-">finishedAfter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block">Only select historic process instances that were started after the given date.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#finishedBefore-java.util.Date-">finishedBefore</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block">Only select historic process instances that were started before the given date.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#includeProcessVariables--">includeProcessVariables</a></span>()</code>
<div class="block">Include process variables in the process query result</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#involvedUser-java.lang.String-">involvedUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Only select the historic process instances with which the user with the given id is involved.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#limitProcessInstanceVariables-java.lang.Integer-">limitProcessInstanceVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processInstanceVariablesLimit)</code>
<div class="block">Limit process instance variables</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#locale-java.lang.String-">locale</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale)</code>
<div class="block">Localize historic process name and description to specified locale.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#notDeleted--">notDeleted</a></span>()</code>
<div class="block">Only select historic process instance that are not deleted.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#or--">or</a></span>()</code>
<div class="block">Begin an OR statement.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#orderByProcessDefinitionId--">orderByProcessDefinitionId</a></span>()</code>
<div class="block">Order by the process definition id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#orderByProcessInstanceBusinessKey--">orderByProcessInstanceBusinessKey</a></span>()</code>
<div class="block">Order by the business key (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#orderByProcessInstanceDuration--">orderByProcessInstanceDuration</a></span>()</code>
<div class="block">Order by the duration of the process instance (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#orderByProcessInstanceEndTime--">orderByProcessInstanceEndTime</a></span>()</code>
<div class="block">Order by the end time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>
<div class="block">Order by the process instance id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#orderByProcessInstanceStartTime--">orderByProcessInstanceStartTime</a></span>()</code>
<div class="block">Order by the start time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processDefinitionCategory-java.lang.String-">processDefinitionCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategory)</code>
<div class="block">Only select historic process instances whose process definition category is processDefinitionCategory.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Only select historic process instances for the given process definition</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Only select historic process instances that are defined by a process
 definition with the given key.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processDefinitionKeyIn-java.util.List-">processDefinitionKeyIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionKeys)</code>
<div class="block">Only select historic process instances that are defined by a process
 definition with one of the given process definition keys.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processDefinitionKeyNotIn-java.util.List-">processDefinitionKeyNotIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionKeys)</code>
<div class="block">Only select historic process instances that don't have a process-definition of which the key is present in the given list</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processDefinitionName-java.lang.String-">processDefinitionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</code>
<div class="block">Select process historic instances whose process definition name is processDefinitionName</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processDefinitionVersion-java.lang.Integer-">processDefinitionVersion</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select historic process instances with a certain process definition version.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processInstanceBusinessKey-java.lang.String-">processInstanceBusinessKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey)</code>
<div class="block">Only select historic process instances with the given business key</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Only select historic process instances with the given process instance.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processInstanceIds-java.util.Set-">processInstanceIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processInstanceIds)</code>
<div class="block">Only select historic process instances whose id is in the given set of ids.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processInstanceName-java.lang.String-">processInstanceName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Only select process instances with the given name.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processInstanceNameLike-java.lang.String-">processInstanceNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLike)</code>
<div class="block">Only select process instances with a name like the given value.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processInstanceNameLikeIgnoreCase-java.lang.String-">processInstanceNameLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLikeIgnoreCase)</code>
<div class="block">Only select process instances with a name like the given value, ignoring upper/lower case.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processInstanceTenantId-java.lang.String-">processInstanceTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select process instances that have the given tenant id.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processInstanceTenantIdLike-java.lang.String-">processInstanceTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select process instances with a tenant id like the given one.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processInstanceWithoutTenantId--">processInstanceWithoutTenantId</a></span>()</code>
<div class="block">Only select process instances that do not have a tenant id.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#startedAfter-java.util.Date-">startedAfter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block">Only select historic process instances that were started after the given date.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#startedBefore-java.util.Date-">startedBefore</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block">Only select historic process instances that were started before the given date.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#startedBy-java.lang.String-">startedBy</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Only select historic process instance that are started by the given user.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#superProcessInstanceId-java.lang.String-">superProcessInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;superProcessInstanceId)</code>
<div class="block">Only select historic process instances started by the given process
 instance.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#unfinished--">unfinished</a></span>()</code>
<div class="block">Only select historic process instance that are not yet finished.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueEquals-java.lang.Object-">variableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which had at least one global variable with the given value
 when they ended.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueEquals-java.lang.String-java.lang.Object-">variableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which had a global variable with the given value
 when they ended.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">variableValueEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select historic process instances which have a local string variable with the 
 given value, case insensitive.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueGreaterThan-java.lang.String-java.lang.Object-">variableValueGreaterThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which had a global variable value greater than the
 passed value when they ended.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">variableValueGreaterThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which had a global variable value greater than or
 equal to the passed value when they ended.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueLessThan-java.lang.String-java.lang.Object-">variableValueLessThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which had a global variable value less than the
 passed value when the ended.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueLessThanOrEqual-java.lang.String-java.lang.Object-">variableValueLessThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which has a global variable value less than or equal
 to the passed value when they ended.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueLike-java.lang.String-java.lang.String-">variableValueLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select process instances which had global variable value like the given value
 when they ended.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueLikeIgnoreCase-java.lang.String-java.lang.String-">variableValueLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select process instances which had global variable value like (case insensitive)
 the given value when they ended.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#variableValueNotEquals-java.lang.String-java.lang.Object-">variableValueNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which had a global variable with the given name, but
 with a different value than the passed value when they ended.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#withJobException--">withJobException</a></span>()</code>
<div class="block">Only select process instances that failed due to an exception happening during a job execution.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#withLocalizationFallback--">withLocalizationFallback</a></span>()</code>
<div class="block">Instruct localization to fallback to more general locales including the default locale of the JVM if the specified locale is not found.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="processInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Only select historic process instances with the given process instance.
 ProcessInstance) ids and {@link HistoricProcessInstance} ids match.</div>
</li>
</ul>
<a name="processInstanceIds-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceIds</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processInstanceIds(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processInstanceIds)</pre>
<div class="block">Only select historic process instances whose id is in the given set of ids.
 ProcessInstance) ids and {@link HistoricProcessInstance} ids match.</div>
</li>
</ul>
<a name="processDefinitionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processDefinitionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Only select historic process instances for the given process definition</div>
</li>
</ul>
<a name="processDefinitionKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processDefinitionKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</pre>
<div class="block">Only select historic process instances that are defined by a process
 definition with the given key.</div>
</li>
</ul>
<a name="processDefinitionKeyIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKeyIn</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processDefinitionKeyIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionKeys)</pre>
<div class="block">Only select historic process instances that are defined by a process
 definition with one of the given process definition keys.</div>
</li>
</ul>
<a name="processDefinitionKeyNotIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKeyNotIn</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processDefinitionKeyNotIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionKeys)</pre>
<div class="block">Only select historic process instances that don't have a process-definition of which the key is present in the given list</div>
</li>
</ul>
<a name="processDefinitionCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionCategory</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processDefinitionCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategory)</pre>
<div class="block">Only select historic process instances whose process definition category is processDefinitionCategory.</div>
</li>
</ul>
<a name="processDefinitionName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionName</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processDefinitionName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</pre>
<div class="block">Select process historic instances whose process definition name is processDefinitionName</div>
</li>
</ul>
<a name="processDefinitionVersion-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionVersion</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processDefinitionVersion(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</pre>
<div class="block">Only select historic process instances with a certain process definition version.
 Particulary useful when used in combination with <a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html#processDefinitionKey-java.lang.String-"><code>processDefinitionKey(String)</code></a></div>
</li>
</ul>
<a name="processInstanceBusinessKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceBusinessKey</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processInstanceBusinessKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey)</pre>
<div class="block">Only select historic process instances with the given business key</div>
</li>
</ul>
<a name="deploymentId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;deploymentId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</pre>
<div class="block">Only select historic process instances that are defined by a process
 definition with the given deployment identifier.</div>
</li>
</ul>
<a name="deploymentIdIn-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentIdIn</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;deploymentIdIn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;deploymentIds)</pre>
<div class="block">Only select historic process instances that are defined by a process
 definition with one of the given deployment identifiers.</div>
</li>
</ul>
<a name="finished--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finished</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;finished()</pre>
<div class="block">Only select historic process instances that are completely finished.</div>
</li>
</ul>
<a name="unfinished--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unfinished</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;unfinished()</pre>
<div class="block">Only select historic process instance that are not yet finished.</div>
</li>
</ul>
<a name="deleted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleted</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;deleted()</pre>
<div class="block">Only select historic process instances that are deleted.</div>
</li>
</ul>
<a name="notDeleted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>notDeleted</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;notDeleted()</pre>
<div class="block">Only select historic process instance that are not deleted.</div>
</li>
</ul>
<a name="involvedUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>involvedUser</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;involvedUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Only select the historic process instances with which the user with the given id is involved.</div>
</li>
</ul>
<a name="variableValueEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select process instances which had a global variable with the given value
 when they ended. The type only applies to already ended
 process instances, otherwise use a <a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instead! of
 variable is determined based on the value, using types configured in
 <code>ProcessEngineConfiguration#getVariableTypes()</code>. Byte-arrays and
 <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are
 not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - of the variable, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueEquals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueEquals</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select process instances which had at least one global variable with the given value
 when they ended. The type only applies to already ended
 process instances, otherwise use a <a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instead! of
 variable is determined based on the value, using types configured in
 <code>ProcessEngineConfiguration#getVariableTypes()</code>. Byte-arrays and
 <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are
 not supported.</div>
</li>
</ul>
<a name="variableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueEqualsIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueEqualsIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select historic process instances which have a local string variable with the 
 given value, case insensitive.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - name of the variable, cannot be null.</dd>
<dd><code>value</code> - value of the variable, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueNotEquals-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueNotEquals</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueNotEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select process instances which had a global variable with the given name, but
 with a different value than the passed value when they ended. Only select
 process instances which have a variable value greater than the passed
 value. Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not
 primitive type wrappers) are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - of the variable, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueGreaterThan-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueGreaterThan</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueGreaterThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select process instances which had a global variable value greater than the
 passed value when they ended. Booleans, Byte-arrays and
 <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are
 not supported. Only select process instances which have a variable value
 greater than the passed value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueGreaterThanOrEqual</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueGreaterThanOrEqual(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select process instances which had a global variable value greater than or
 equal to the passed value when they ended. Booleans, Byte-arrays and
 <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type wrappers) are
 not supported. Only applies to already ended process instances, otherwise
 use a <a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instead!</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueLessThan-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLessThan</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueLessThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select process instances which had a global variable value less than the
 passed value when the ended. Only applies to already ended process
 instances, otherwise use a <a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instead! Booleans,
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type
 wrappers) are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueLessThanOrEqual-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLessThanOrEqual</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueLessThanOrEqual(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Only select process instances which has a global variable value less than or equal
 to the passed value when they ended. Only applies to already ended process
 instances, otherwise use a <a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instead! Booleans,
 Byte-arrays and <a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io"><code>Serializable</code></a> objects (which are not primitive type
 wrappers) are not supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null.</dd>
</dl>
</li>
</ul>
<a name="variableValueLike-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLike</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select process instances which had global variable value like the given value
 when they ended. Only applies to already ended process instances, otherwise
 use a <a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instead! This can be used on string
 variables only.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null. The string can include the
          wildcard character '%' to express like-strategy: starts with
          (string%), ends with (%string) or contains (%string%).</dd>
</dl>
</li>
</ul>
<a name="variableValueLikeIgnoreCase-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableValueLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;variableValueLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Only select process instances which had global variable value like (case insensitive)
 the given value when they ended. Only applies to already ended process instances,
 otherwise use a <a href="../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instead! This can be used on string
 variables only.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - cannot be null.</dd>
<dd><code>value</code> - cannot be null. The string can include the
          wildcard character '%' to express like-strategy: starts with
          (string%), ends with (%string) or contains (%string%).</dd>
</dl>
</li>
</ul>
<a name="startedBefore-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startedBefore</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;startedBefore(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block">Only select historic process instances that were started before the given date.</div>
</li>
</ul>
<a name="startedAfter-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startedAfter</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;startedAfter(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block">Only select historic process instances that were started after the given date.</div>
</li>
</ul>
<a name="finishedBefore-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finishedBefore</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;finishedBefore(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block">Only select historic process instances that were started before the given date.</div>
</li>
</ul>
<a name="finishedAfter-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finishedAfter</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;finishedAfter(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</pre>
<div class="block">Only select historic process instances that were started after the given date.</div>
</li>
</ul>
<a name="startedBy-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startedBy</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;startedBy(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Only select historic process instance that are started by the given user.</div>
</li>
</ul>
<a name="processInstanceTenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceTenantId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processInstanceTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Only select process instances that have the given tenant id.</div>
</li>
</ul>
<a name="processInstanceTenantIdLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceTenantIdLike</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processInstanceTenantIdLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</pre>
<div class="block">Only select process instances with a tenant id like the given one.</div>
</li>
</ul>
<a name="processInstanceWithoutTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceWithoutTenantId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processInstanceWithoutTenantId()</pre>
<div class="block">Only select process instances that do not have a tenant id.</div>
</li>
</ul>
<a name="or--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>or</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;or()</pre>
<div class="block">Begin an OR statement. Make sure you invoke the endOr method at the end of your OR statement.
 Only one OR statement is allowed, for the second call to this method an exception will be thrown.</div>
</li>
</ul>
<a name="endOr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endOr</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;endOr()</pre>
<div class="block">End an OR statement. Only one OR statement is allowed, for the second call to this method an exception will be thrown.</div>
</li>
</ul>
<a name="orderByProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;orderByProcessInstanceId()</pre>
<div class="block">Order by the process instance id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessDefinitionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;orderByProcessDefinitionId()</pre>
<div class="block">Order by the process definition id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessInstanceBusinessKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceBusinessKey</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;orderByProcessInstanceBusinessKey()</pre>
<div class="block">Order by the business key (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessInstanceStartTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceStartTime</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;orderByProcessInstanceStartTime()</pre>
<div class="block">Order by the start time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessInstanceEndTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceEndTime</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;orderByProcessInstanceEndTime()</pre>
<div class="block">Order by the end time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessInstanceDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceDuration</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;orderByProcessInstanceDuration()</pre>
<div class="block">Order by the duration of the process instance (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByTenantId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;orderByTenantId()</pre>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="superProcessInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>superProcessInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;superProcessInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;superProcessInstanceId)</pre>
<div class="block">Only select historic process instances started by the given process
 instance. ProcessInstance) ids and {@link HistoricProcessInstance}
 ids match.</div>
</li>
</ul>
<a name="excludeSubprocesses-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeSubprocesses</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;excludeSubprocesses(boolean&nbsp;excludeSubprocesses)</pre>
<div class="block">Exclude sub processes from the query result;</div>
</li>
</ul>
<a name="includeProcessVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeProcessVariables</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;includeProcessVariables()</pre>
<div class="block">Include process variables in the process query result</div>
</li>
</ul>
<a name="limitProcessInstanceVariables-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>limitProcessInstanceVariables</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;limitProcessInstanceVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processInstanceVariablesLimit)</pre>
<div class="block">Limit process instance variables</div>
</li>
</ul>
<a name="withJobException--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withJobException</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;withJobException()</pre>
<div class="block">Only select process instances that failed due to an exception happening during a job execution.</div>
</li>
</ul>
<a name="processInstanceName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceName</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processInstanceName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Only select process instances with the given name.</div>
</li>
</ul>
<a name="processInstanceNameLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceNameLike</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processInstanceNameLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLike)</pre>
<div class="block">Only select process instances with a name like the given value.</div>
</li>
</ul>
<a name="processInstanceNameLikeIgnoreCase-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceNameLikeIgnoreCase</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;processInstanceNameLikeIgnoreCase(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLikeIgnoreCase)</pre>
<div class="block">Only select process instances with a name like the given value, ignoring upper/lower case.</div>
</li>
</ul>
<a name="locale-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>locale</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;locale(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale)</pre>
<div class="block">Localize historic process name and description to specified locale.</div>
</li>
</ul>
<a name="withLocalizationFallback--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>withLocalizationFallback</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;withLocalizationFallback()</pre>
<div class="block">Instruct localization to fallback to more general locales including the default locale of the JVM if the specified locale is not found.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricProcessInstanceQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricProcessInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricProcessInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
