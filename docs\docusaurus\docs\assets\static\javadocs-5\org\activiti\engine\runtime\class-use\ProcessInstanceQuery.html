<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:26 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.activiti.engine.runtime.ProcessInstanceQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.activiti.engine.runtime.ProcessInstanceQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/runtime/class-use/ProcessInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="ProcessInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.activiti.engine.runtime.ProcessInstanceQuery" class="title">Uses of Interface<br>org.activiti.engine.runtime.ProcessInstanceQuery</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine">org.activiti.engine</a></td>
<td class="colLast">
<div class="block">Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a <a href="../../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine"><code>ProcessEngineConfiguration</code></a>
    (typically based on a configuration file), from which a <a href="../../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> can be obtained.<br/><br/>
    Through the services obtained from such a <a href="../../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b><a href="../../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>: </b> Manages <a href="../../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s <br/>

    <b><a href="../../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>: </b> For starting and searching <a href="../../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s <br/>
    
    <b><a href="../../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>: </b> Exposes operations to manage human (standalone) <a href="../../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s, 
    such as claiming, completing and assigning tasks<br/>

    <b><a href="../../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>: </b> Used for managing <a href="../../../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s, 
    <a href="../../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s and the relations between them<br/>
        
    <b><a href="../../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b><a href="../../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b><a href="../../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.activiti.engine.runtime">org.activiti.engine.runtime</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="../../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a> in <a href="../../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> that return <a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">RuntimeService.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/RuntimeService.html#createProcessInstanceQuery--">createProcessInstanceQuery</a></span>()</code>
<div class="block">Creates a new <a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstanceQuery</code></a> instance, that can be used to
 query process instances.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.runtime">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a> in <a href="../../../../../org/activiti/engine/runtime/package-summary.html">org.activiti.engine.runtime</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/runtime/package-summary.html">org.activiti.engine.runtime</a> that return <a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#active--">active</a></span>()</code>
<div class="block">Only select process instances which are active, which means that 
 neither the process instance nor the corresponding process definition 
 are suspended.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#deploymentId-java.lang.String-">deploymentId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block">Select the process instances which are defined by a deployment
 with the given id.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#deploymentIdIn-java.util.List-">deploymentIdIn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;deploymentIds)</code>
<div class="block">Select the process instances which are defined by one of the given deployment ids</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#endOr--">endOr</a></span>()</code>
<div class="block">End an OR statement.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#excludeSubprocesses-boolean-">excludeSubprocesses</a></span>(boolean&nbsp;excludeSubprocesses)</code>
<div class="block">Exclude sub processes from the query result;</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#includeProcessVariables--">includeProcessVariables</a></span>()</code>
<div class="block">Include process variables in the process query result</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#involvedUser-java.lang.String-">involvedUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Select the process instances with which the user with the given id is involved.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#limitProcessInstanceVariables-java.lang.Integer-">limitProcessInstanceVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processInstanceVariablesLimit)</code>
<div class="block">Limit process instance variables</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#locale-java.lang.String-">locale</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;locale)</code>
<div class="block">Localize process name and description to specified locale.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#or--">or</a></span>()</code>
<div class="block">Begin an OR statement.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#orderByProcessDefinitionId--">orderByProcessDefinitionId</a></span>()</code>
<div class="block">Order by process definition id (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#orderByProcessDefinitionKey--">orderByProcessDefinitionKey</a></span>()</code>
<div class="block">Order by process definition key (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>
<div class="block">Order by id (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processDefinitionCategory-java.lang.String-">processDefinitionCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategory)</code>
<div class="block">Only select process instances whose process definition category is processDefinitionCategory.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Select the process instances which are defined by a process definition
 with the given id.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processDefinitionIds-java.util.Set-">processDefinitionIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionIds)</code>
<div class="block">Select the process instances which are defined by process definitions
 with the given ids.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Select the process instances which are defined by a process definition with
 the given key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processDefinitionKeys-java.util.Set-">processDefinitionKeys</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionKeys)</code>
<div class="block">Select the process instances which are defined by process definitions with
 the given keys.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processDefinitionName-java.lang.String-">processDefinitionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</code>
<div class="block">Select process instances whose process definition name is processDefinitionName</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processDefinitionVersion-java.lang.Integer-">processDefinitionVersion</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process instances with a certain process definition version.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceBusinessKey-java.lang.String-">processInstanceBusinessKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey)</code>
<div class="block">Select process instances with the given business key</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceBusinessKey-java.lang.String-java.lang.String-">processInstanceBusinessKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceBusinessKey,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Select process instance with the given business key, unique for the given process definition</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Select the process instance with the given id</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceIds-java.util.Set-">processInstanceIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processInstanceIds)</code>
<div class="block">Select process instances whose id is in the given set of ids</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceName-java.lang.String-">processInstanceName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Only select process instances with the given name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceNameLike-java.lang.String-">processInstanceNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLike)</code>
<div class="block">Only select process instances with a name like the given value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceNameLikeIgnoreCase-java.lang.String-">processInstanceNameLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameLikeIgnoreCase)</code>
<div class="block">Only select process instances with a name like the given value, ignoring upper/lower case.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceTenantId-java.lang.String-">processInstanceTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select process instances that have the given tenant id.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceTenantIdLike-java.lang.String-">processInstanceTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select process instances with a tenant id like the given one.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#processInstanceWithoutTenantId--">processInstanceWithoutTenantId</a></span>()</code>
<div class="block">Only select process instances that do not have a tenant id.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#subProcessInstanceId-java.lang.String-">subProcessInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subProcessInstanceId)</code>
<div class="block">Select the process instance that have as sub process instance the given
 process instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#superProcessInstanceId-java.lang.String-">superProcessInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;superProcessInstanceId)</code>
<div class="block">Select the process instances which are a sub process instance of the given
 super process instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#suspended--">suspended</a></span>()</code>
<div class="block">Only select process instances which are suspended, either because the 
 process instance itself is suspended or because the corresponding process 
 definition is suspended</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueEquals-java.lang.Object-">variableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which have at least one global variable with the given value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueEquals-java.lang.String-java.lang.Object-">variableValueEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a global variable with the given value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">variableValueEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a local string variable with the given value, 
 case insensitive.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueGreaterThan-java.lang.String-java.lang.Object-">variableValueGreaterThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a variable value greater than the passed value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">variableValueGreaterThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a global variable value greater than or equal to
 the passed value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueLessThan-java.lang.String-java.lang.Object-">variableValueLessThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a global variable value less than the passed value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueLessThanOrEqual-java.lang.String-java.lang.Object-">variableValueLessThanOrEqual</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a global variable value less than or equal to the passed value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueLike-java.lang.String-java.lang.String-">variableValueLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a global variable value like the given value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueLikeIgnoreCase-java.lang.String-java.lang.String-">variableValueLikeIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a global variable value like the given value (case insensitive).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueNotEquals-java.lang.String-java.lang.Object-">variableValueNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a global variable with the given name, but
 with a different value than the passed value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#variableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">variableValueNotEqualsIgnoreCase</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Only select process instances which have a local string variable which is not the given value, 
 case insensitive.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#withJobException--">withJobException</a></span>()</code>
<div class="block">Only select process instances that failed due to an exception happening during a job execution.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">ProcessInstanceQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessInstanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html#withLocalizationFallback--">withLocalizationFallback</a></span>()</code>
<div class="block">Instruct localization to fallback to more general locales including the default locale of the JVM if the specified locale is not found.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/runtime/ProcessInstanceQuery.html" title="interface in org.activiti.engine.runtime">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/runtime/class-use/ProcessInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="ProcessInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
