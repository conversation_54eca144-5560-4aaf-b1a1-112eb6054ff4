---
swagger: "2.0"
info:
  description: "# flowable / flowəb(ə)l /\r\n\r\n- a compact and highly efficient\
    \ workflow and Business Process Management (BPM) platform for developers, system\
    \ admins and business users.\r\n- a lightning fast, tried and tested BPMN 2 process\
    \ engine written in Java. It is Apache 2.0 licensed open source, with a committed\
    \ community.\r\n- can run embedded in a Java application, or as a service on a\
    \ server, a cluster, and in the cloud. It integrates perfectly with Spring. With\
    \ a rich Java and REST API, it is the ideal engine for orchestrating human or\
    \ system activities."
  version: "v1"
  title: "Flowable REST API"
  contact:
    name: "Flowable"
    url: "http://www.flowable.org/"
  license:
    name: "Apache 2.0"
    url: "http://www.apache.org/licenses/LICENSE-2.0.html"
basePath: "/flowable-rest/service"
tags:
- name: "Batch parts"
- name: "Batches"
- name: "Database tables"
- name: "Deployment"
- name: "Engine"
- name: "Event subscriptions"
- name: "Executions"
- name: "Forms"
- name: "Groups"
- name: "Historic Task Log Entries"
- name: "History"
- name: "History Process"
- name: "History Task"
- name: "Jobs"
- name: "Models"
- name: "Process Definitions"
- name: "Process Instance Identity Links"
- name: "Process Instance Variables"
- name: "Process Instances"
- name: "Query"
- name: "Runtime"
- name: "Task Attachments"
- name: "Task Comments"
- name: "Task Identity Links"
- name: "Task Variables"
- name: "Tasks"
- name: "Users"
schemes:
- "http"
- "https"
paths:
  /form/form-data:
    get:
      tags:
      - "Forms"
      summary: "Get form data"
      description: ""
      operationId: "getFormData"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "query"
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates that form data could be queried."
          schema:
            $ref: "#/definitions/FormDataResponse"
        404:
          description: "Indicates that form data could not be found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Forms"
      summary: "Submit task form data"
      description: ""
      operationId: "submitForm"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/SubmitFormRequest"
      responses:
        200:
          description: "Indicates request was successful and the form data was submitted"
          schema:
            $ref: "#/definitions/ProcessInstanceResponse"
        204:
          description: "If TaskId has been provided, Indicates request was successful\
            \ and the form data was submitted. Returns empty"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /history/historic-activity-instances:
    get:
      tags:
      - "History"
      summary: "List historic activity instances"
      description: ""
      operationId: "listHistoricActivityInstances"
      produces:
      - "application/json"
      parameters:
      - name: "activityId"
        in: "query"
        description: "An id of the activity instance."
        required: false
        type: "string"
      - name: "activityInstanceId"
        in: "query"
        description: "An id of the historic activity instance."
        required: false
        type: "string"
      - name: "activityName"
        in: "query"
        description: "The name of the historic activity instance."
        required: false
        type: "string"
      - name: "activityType"
        in: "query"
        description: "The element type of the historic activity instance."
        required: false
        type: "string"
      - name: "executionId"
        in: "query"
        description: "The execution id of the historic activity instance."
        required: false
        type: "string"
      - name: "finished"
        in: "query"
        description: "Indication if the historic activity instance is finished."
        required: false
        type: "boolean"
      - name: "taskAssignee"
        in: "query"
        description: "The assignee of the historic activity instance."
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "The process instance id of the historic activity instance."
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "The process definition id of the historic activity instance."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return instances with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return instances with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns instances without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      responses:
        200:
          description: "Indicates that historic activity instances could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricActivityInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /history/historic-detail:
    get:
      tags:
      - "History"
      summary: "Get historic detail"
      description: ""
      operationId: "listHistoricDetails"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "The id of the historic detail."
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "The process instance id of the historic detail."
        required: false
        type: "string"
      - name: "executionId"
        in: "query"
        description: "The execution id of the historic detail."
        required: false
        type: "string"
      - name: "activityInstanceId"
        in: "query"
        description: "The activity instance id of the historic detail."
        required: false
        type: "string"
      - name: "taskId"
        in: "query"
        description: "The task id of the historic detail."
        required: false
        type: "string"
      - name: "selectOnlyFormProperties"
        in: "query"
        description: "Indication to only return form properties in the result."
        required: false
        type: "boolean"
      - name: "selectOnlyVariableUpdates"
        in: "query"
        description: "Indication to only return variable updates in the result."
        required: false
        type: "boolean"
      responses:
        200:
          description: "Indicates that historic detail could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricDetailResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /history/historic-detail/{detailId}/data:
    get:
      tags:
      - "History"
      summary: "Get the binary data for a historic detail variable"
      description: "The response body contains the binary value of the variable. When\
        \ the variable is of type binary, the content-type of the response is set\
        \ to application/octet-stream, regardless of the content of the variable or\
        \ the request accept-type header. In case of serializable, application/x-java-serialized-object\
        \ is used as content-type."
      operationId: "getHistoricDetailVariableData"
      parameters:
      - name: "detailId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the historic detail instance was found and the requested\
            \ variable data is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested historic detail instance was not found\
            \ or the historic detail instance does not have a variable with the given\
            \ name or the variable does not have a binary stream available. Status\
            \ message provides additional information."
      security:
      - basicAuth: []
  /history/historic-process-instances:
    get:
      tags:
      - "History Process"
      summary: "List of historic process instances"
      description: ""
      operationId: "listHistoricProcessInstances"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "query"
        description: "An id of the historic process instance."
        required: false
        type: "string"
      - name: "processInstanceName"
        in: "query"
        description: "A name of the historic process instance."
        required: false
        type: "string"
      - name: "processInstanceNameLike"
        in: "query"
        description: "A name of the historic process instance used in a like query."
        required: false
        type: "string"
      - name: "processInstanceNameLikeIgnoreCase"
        in: "query"
        description: "A name of the historic process instance used in a like query\
          \ ignoring case."
        required: false
        type: "string"
      - name: "processDefinitionKey"
        in: "query"
        description: "The process definition key of the historic process instance."
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "The process definition id of the historic process instance."
        required: false
        type: "string"
      - name: "processDefinitionName"
        in: "query"
        description: "The process definition name of the historic process instance."
        required: false
        type: "string"
      - name: "processDefinitionCategory"
        in: "query"
        description: "The process definition category of the historic process instance."
        required: false
        type: "string"
      - name: "processDefinitionVersion"
        in: "query"
        description: "The process definition version of the historic process instance."
        required: false
        type: "string"
      - name: "deploymentId"
        in: "query"
        description: "The deployment id of the historic process instance."
        required: false
        type: "string"
      - name: "businessKey"
        in: "query"
        description: "The business key of the historic process instance."
        required: false
        type: "string"
      - name: "businessKeyLike"
        in: "query"
        description: "Only return instances with a businessKey like this key."
        required: false
        type: "string"
      - name: "involvedUser"
        in: "query"
        description: "An involved user of the historic process instance."
        required: false
        type: "string"
      - name: "finished"
        in: "query"
        description: "Indication if the historic process instance is finished."
        required: false
        type: "boolean"
      - name: "superProcessInstanceId"
        in: "query"
        description: "An optional parent process id of the historic process instance."
        required: false
        type: "string"
      - name: "excludeSubprocesses"
        in: "query"
        description: "Return only historic process instances which are not sub processes."
        required: false
        type: "boolean"
      - name: "finishedAfter"
        in: "query"
        description: "Return only historic process instances that were finished after\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "finishedBefore"
        in: "query"
        description: "Return only historic process instances that were finished before\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "startedAfter"
        in: "query"
        description: "Return only historic process instances that were started after\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "startedBefore"
        in: "query"
        description: "Return only historic process instances that were started before\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "startedBy"
        in: "query"
        description: "Return only historic process instances that were started by\
          \ this user."
        required: false
        type: "string"
      - name: "includeProcessVariables"
        in: "query"
        description: "An indication if the historic process instance variables should\
          \ be returned as well."
        required: false
        type: "boolean"
      - name: "callbackId"
        in: "query"
        description: "Only return instances with the given callbackId."
        required: false
        type: "string"
      - name: "callbackType"
        in: "query"
        description: "Only return instances with the given callbackType."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return instances with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return instances with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns instances without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored.\n"
        required: false
        type: "boolean"
      responses:
        200:
          description: "Indicates that historic process instances could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricProcessInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /history/historic-process-instances/{processInstanceId}:
    get:
      tags:
      - "History Process"
      summary: "Get a historic process instance"
      description: ""
      operationId: "getHistoricProcessInstance"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates that the historic process instances could be found."
          schema:
            $ref: "#/definitions/HistoricProcessInstanceResponse"
        404:
          description: "Indicates that the historic process instances could not be\
            \ found."
      security:
      - basicAuth: []
    delete:
      tags:
      - "History Process"
      summary: " Delete a historic process instance"
      description: ""
      operationId: "deleteHistoricProcessInstance"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates that the historic process instance was deleted."
        404:
          description: "Indicates that the historic process instance could not be\
            \ found."
      security:
      - basicAuth: []
  /history/historic-process-instances/{processInstanceId}/comments:
    get:
      tags:
      - "History Process"
      summary: "List comments on a historic process instance"
      description: ""
      operationId: "listHistoricProcessInstanceComments"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and the comments\
            \ are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/CommentResponse"
        404:
          description: "Indicates that the historic process instance could not be\
            \ found."
      security:
      - basicAuth: []
    post:
      tags:
      - "History Process"
      summary: "Create a new comment on a historic process instance"
      description: "Parameter saveProcessInstanceId is optional, if true save process\
        \ instance id of task with comment."
      operationId: "createComment"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/CommentResponse"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/CommentResponse"
        201:
          description: "Indicates the comment was created and the result is returned."
        400:
          description: "Indicates the comment is missing from the request."
        404:
          description: "Indicates that the historic process instance could not be\
            \ found."
      security:
      - basicAuth: []
  /history/historic-process-instances/{processInstanceId}/comments/{commentId}:
    get:
      tags:
      - "History Process"
      summary: "Get a comment on a historic process instance"
      description: ""
      operationId: "getComment"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "commentId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the historic process instance and comment were found\
            \ and the comment is returned."
          schema:
            $ref: "#/definitions/CommentResponse"
        404:
          description: "Indicates the requested historic process instance was not\
            \ found or the historic process instance does not have a comment with\
            \ the given ID."
      security:
      - basicAuth: []
    delete:
      tags:
      - "History Process"
      summary: "Delete a comment on a historic process instance"
      description: ""
      operationId: "deleteComment"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "commentId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the historic process instance and comment were found\
            \ and the comment is deleted. Response body is left empty intentionally."
        404:
          description: "Indicates the requested historic process instance was not\
            \ found or the historic process instance does not have a comment with\
            \ the given ID."
      security:
      - basicAuth: []
  /history/historic-process-instances/{processInstanceId}/identitylinks:
    get:
      tags:
      - "History Process"
      summary: "List identity links of a historic process instance"
      description: ""
      operationId: "listHistoricProcessInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the identity links are\
            \ returned"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/HistoricIdentityLinkResponse"
        404:
          description: "Indicates the process instance could not be found.."
      security:
      - basicAuth: []
  /history/historic-process-instances/{processInstanceId}/variables/{variableName}/data:
    get:
      tags:
      - "History Process"
      summary: "Get the binary data for a historic process instance variable"
      description: "The response body contains the binary value of the variable. When\
        \ the variable is of type binary, the content-type of the response is set\
        \ to application/octet-stream, regardless of the content of the variable or\
        \ the request accept-type header. In case of serializable, application/x-java-serialized-object\
        \ is used as content-type."
      operationId: "getHistoricProcessInstanceVariableData"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and the requested\
            \ variable data is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested process instance was not found or\
            \ the process instance does not have a variable with the given name or\
            \ the variable does not have a binary stream available. Status message\
            \ provides additional information."
      security:
      - basicAuth: []
  /history/historic-task-instances:
    get:
      tags:
      - "History Task"
      summary: "List historic task instances"
      description: ""
      operationId: "listHistoricTaskInstances"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "query"
        description: "An id of the historic task instance."
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "The process instance id of the historic task instance."
        required: false
        type: "string"
      - name: "processInstanceIdWithChildren"
        in: "query"
        description: "Selects the historic task instances for the process instance\
          \ and its children."
        required: false
        type: "string"
      - name: "processDefinitionKey"
        in: "query"
        description: "The process definition key of the historic task instance."
        required: false
        type: "string"
      - name: "processDefinitionKeyLike"
        in: "query"
        description: "The process definition key of the historic task instance, which\
          \ matches the given value."
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "The process definition id of the historic task instance."
        required: false
        type: "string"
      - name: "processDefinitionName"
        in: "query"
        description: "The process definition name of the historic task instance."
        required: false
        type: "string"
      - name: "processDefinitionNameLike"
        in: "query"
        description: "The process definition name of the historic task instance, which\
          \ matches the given value."
        required: false
        type: "string"
      - name: "processBusinessKey"
        in: "query"
        description: "The process instance business key of the historic task instance."
        required: false
        type: "string"
      - name: "processBusinessKeyLike"
        in: "query"
        description: "The process instance business key of the historic task instance\
          \ that matches the given value."
        required: false
        type: "string"
      - name: "executionId"
        in: "query"
        description: "The execution id of the historic task instance."
        required: false
        type: "string"
      - name: "taskDefinitionKey"
        in: "query"
        description: "The task definition key for tasks part of a process"
        required: false
        type: "string"
      - name: "taskDefinitionKeys"
        in: "query"
        description: "The task definition key for tasks part of a process"
        required: false
        type: "string"
      - name: "taskName"
        in: "query"
        description: "The task name of the historic task instance."
        required: false
        type: "string"
      - name: "taskNameLike"
        in: "query"
        description: "The task name with like operator for the historic task instance."
        required: false
        type: "string"
      - name: "taskDescription"
        in: "query"
        description: "The task description of the historic task instance."
        required: false
        type: "string"
      - name: "taskDescriptionLike"
        in: "query"
        description: "The task description with like operator for the historic task\
          \ instance."
        required: false
        type: "string"
      - name: "taskCategory"
        in: "query"
        description: "Select tasks with the given category. Note that this is the\
          \ task category, not the category of the process definition (namespace within\
          \ the BPMN Xml)."
        required: false
        type: "string"
      - name: "taskDeleteReason"
        in: "query"
        description: "The task delete reason of the historic task instance."
        required: false
        type: "string"
      - name: "taskDeleteReasonLike"
        in: "query"
        description: "The task delete reason with like operator for the historic task\
          \ instance."
        required: false
        type: "string"
      - name: "taskAssignee"
        in: "query"
        description: "The assignee of the historic task instance."
        required: false
        type: "string"
      - name: "taskAssigneeLike"
        in: "query"
        description: "The assignee with like operator for the historic task instance."
        required: false
        type: "string"
      - name: "taskOwner"
        in: "query"
        description: "The owner of the historic task instance."
        required: false
        type: "string"
      - name: "taskOwnerLike"
        in: "query"
        description: "The owner with like operator for the historic task instance."
        required: false
        type: "string"
      - name: "taskInvolvedUser"
        in: "query"
        description: "An involved user of the historic task instance"
        required: false
        type: "string"
      - name: "taskPriority"
        in: "query"
        description: "The priority of the historic task instance."
        required: false
        type: "string"
      - name: "finished"
        in: "query"
        description: "Indication if the historic task instance is finished."
        required: false
        type: "boolean"
      - name: "processFinished"
        in: "query"
        description: "Indication if the process instance of the historic task instance\
          \ is finished."
        required: false
        type: "boolean"
      - name: "parentTaskId"
        in: "query"
        description: "An optional parent task id of the historic task instance."
        required: false
        type: "string"
      - name: "dueDate"
        in: "query"
        description: "Return only historic task instances that have a due date equal\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueDateAfter"
        in: "query"
        description: "Return only historic task instances that have a due date after\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueDateBefore"
        in: "query"
        description: "Return only historic task instances that have a due date before\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "withoutDueDate"
        in: "query"
        description: "Return only historic task instances that have no due-date. When\
          \ false is provided as value, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "taskCompletedOn"
        in: "query"
        description: "Return only historic task instances that have been completed\
          \ on this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCompletedAfter"
        in: "query"
        description: "Return only historic task instances that have been completed\
          \ after this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCompletedBefore"
        in: "query"
        description: "Return only historic task instances that have been completed\
          \ before this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCreatedOn"
        in: "query"
        description: "Return only historic task instances that were created on this\
          \ date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCreatedBefore"
        in: "query"
        description: "Return only historic task instances that were created before\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCreatedAfter"
        in: "query"
        description: "Return only historic task instances that were created after\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "includeTaskLocalVariables"
        in: "query"
        description: "An indication if the historic task instance local variables\
          \ should be returned as well."
        required: false
        type: "boolean"
      - name: "includeProcessVariables"
        in: "query"
        description: "An indication if the historic task instance global variables\
          \ should be returned as well."
        required: false
        type: "boolean"
      - name: "scopeDefinitionId"
        in: "query"
        description: "Only return historic task instances with the given scopeDefinitionId."
        required: false
        type: "string"
      - name: "scopeId"
        in: "query"
        description: "Only return historic task instances with the given scopeId."
        required: false
        type: "string"
      - name: "scopeType"
        in: "query"
        description: "Only return historic task instances with the given scopeType."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return historic task instances with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return historic task instances with a tenantId like the\
          \ given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns historic task instances without a tenantId\
          \ set. If false, the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      responses:
        200:
          description: "Indicates that historic task instances could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricTaskInstanceResponse"
        404:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /history/historic-task-instances/{taskId}:
    get:
      tags:
      - "History Task"
      summary: "Get a single historic task instance"
      description: ""
      operationId: "getTaskInstance"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates that the historic task instances could be found."
          schema:
            $ref: "#/definitions/HistoricTaskInstanceResponse"
        404:
          description: "Indicates that the historic task instances could not be found."
      security:
      - basicAuth: []
    delete:
      tags:
      - "History Task"
      summary: "Delete a historic task instance"
      description: ""
      operationId: "deleteTaskInstance"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates that the historic task instance was deleted."
        404:
          description: "Indicates that the historic task instance could not be found."
      security:
      - basicAuth: []
  /history/historic-task-instances/{taskId}/form:
    get:
      tags:
      - "History Task"
      summary: "Get a historic task instance form"
      description: ""
      operationId: "getHistoricTaskForm"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the task form is returned"
          schema:
            type: "string"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /history/historic-task-instances/{taskId}/identitylinks:
    get:
      tags:
      - "History Task"
      summary: "List identity links of a historic task instance"
      description: ""
      operationId: "listHistoricTaskInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the identity links are\
            \ returned"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/HistoricIdentityLinkResponse"
        404:
          description: "Indicates the task instance could not be found."
      security:
      - basicAuth: []
  /history/historic-task-instances/{taskId}/variables/{variableName}/data:
    get:
      tags:
      - "History"
      summary: "Get the binary data for a historic task instance variable"
      description: "The response body contains the binary value of the variable. When\
        \ the variable is of type binary, the content-type of the response is set\
        \ to application/octet-stream, regardless of the content of the variable or\
        \ the request accept-type header. In case of serializable, application/x-java-serialized-object\
        \ is used as content-type."
      operationId: "getHistoricTaskInstanceVariableData"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the task instance was found and the requested variable\
            \ data is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested task instance was not found or the\
            \ process instance does not have a variable with the given name or the\
            \ variable does not have a binary stream available. Status message provides\
            \ additional information."
      security:
      - basicAuth: []
  /history/historic-task-log-entries:
    get:
      tags:
      - "History Task"
      summary: "List historic task log entries"
      description: ""
      operationId: "getHistoricTaskLogEntries"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "query"
        description: "An id of the historic task instance."
        required: false
        type: "string"
      - name: "type"
        in: "query"
        description: "The type of the log entry."
        required: false
        type: "string"
      - name: "userId"
        in: "query"
        description: "The user who produced the task change."
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "The process instance id of the historic task log entry."
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "The process definition id of the historic task log entry."
        required: false
        type: "string"
      - name: "scopeId"
        in: "query"
        description: "Only return historic task log entries with the given scopeId."
        required: false
        type: "string"
      - name: "scopeDefinitionId"
        in: "query"
        description: "Only return historic task log entries with the given scopeDefinitionId."
        required: false
        type: "string"
      - name: "subScopeId"
        in: "query"
        description: "Only return historic task log entries with the given subScopeId"
        required: false
        type: "string"
      - name: "scopeType"
        in: "query"
        description: "Only return historic task log entries with the given scopeType."
        required: false
        type: "string"
      - name: "from"
        in: "query"
        description: "Return task log entries starting from a date."
        required: false
        type: "string"
        format: "date-time"
      - name: "to"
        in: "query"
        description: "Return task log entries up to a date."
        required: false
        type: "string"
        format: "date-time"
      - name: "tenantId"
        in: "query"
        description: "Only return historic task log entries with the given tenantId."
        required: false
        type: "string"
      - name: "fromLogNumber"
        in: "query"
        description: "Return task log entries starting from a log number"
        required: false
        type: "string"
      - name: "toLogNumber"
        in: "query"
        description: "Return task log entries up to specific a log number"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates that historic task log entries could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricTaskLogEntryResponse"
        404:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /history/historic-variable-instances:
    get:
      tags:
      - "History"
      summary: "List of historic variable instances"
      description: ""
      operationId: "listHistoricVariableInstances"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "query"
        description: "The process instance id of the historic variable instance."
        required: false
        type: "string"
      - name: "taskId"
        in: "query"
        description: "The task id of the historic variable instance."
        required: false
        type: "string"
      - name: "excludeTaskVariables"
        in: "query"
        description: "Indication to exclude the task variables from the result."
        required: false
        type: "boolean"
      - name: "variableName"
        in: "query"
        description: "The variable name of the historic variable instance."
        required: false
        type: "string"
      - name: "variableNameLike"
        in: "query"
        description: "The variable name using the like operator for the historic variable\
          \ instance."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates that historic variable instances could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricVariableInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /history/historic-variable-instances/{varInstanceId}/data:
    get:
      tags:
      - "History"
      summary: "Get the binary data for a historic task instance variable"
      description: "The response body contains the binary value of the variable. When\
        \ the variable is of type binary, the content-type of the response is set\
        \ to application/octet-stream, regardless of the content of the variable or\
        \ the request accept-type header. In case of serializable, application/x-java-serialized-object\
        \ is used as content-type."
      operationId: "getHistoricInstanceVariableData"
      parameters:
      - name: "varInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the variable instance was found and the requested\
            \ variable data is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested variable instance was not found or\
            \ the variable instance does not have a variable with the given name or\
            \ the variable does not have a binary stream available. Status message\
            \ provides additional information."
      security:
      - basicAuth: []
  /identity/groups:
    get:
      tags:
      - "Groups"
      summary: "List groups"
      description: ""
      operationId: "listGroups"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return group with the given id"
        required: false
        type: "string"
      - name: "name"
        in: "query"
        description: "Only return groups with the given name"
        required: false
        type: "string"
      - name: "type"
        in: "query"
        description: "Only return groups with the given type"
        required: false
        type: "string"
      - name: "nameLike"
        in: "query"
        description: "Only return groups with a name like the given value. Use % as\
          \ wildcard-character."
        required: false
        type: "string"
      - name: "member"
        in: "query"
        description: "Only return groups which have a member with the given username."
        required: false
        type: "string"
      - name: "potentialStarter"
        in: "query"
        description: "Only return groups which members are potential starters for\
          \ a process-definition with the given id."
        required: false
        type: "string"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "name"
        - "type"
      responses:
        200:
          description: "Indicates the requested groups were returned."
          schema:
            $ref: "#/definitions/DataResponseGroupResponse"
      security:
      - basicAuth: []
    post:
      tags:
      - "Groups"
      summary: "Create a group"
      description: ""
      operationId: "createGroup"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/GroupRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/GroupResponse"
        201:
          description: "Indicates the group was created."
        400:
          description: "Indicates the id of the group was missing."
      security:
      - basicAuth: []
  /identity/groups/{groupId}:
    get:
      tags:
      - "Groups"
      summary: "Get a single group"
      description: ""
      operationId: "getGroup"
      produces:
      - "application/json"
      parameters:
      - name: "groupId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the group exists and is returned."
          schema:
            $ref: "#/definitions/GroupResponse"
        404:
          description: "Indicates the requested group does not exist."
      security:
      - basicAuth: []
    put:
      tags:
      - "Groups"
      summary: "Update a group"
      description: "All request values are optional. For example, you can only include\
        \ the name attribute in the request body JSON-object, only updating the name\
        \ of the group, leaving all other fields unaffected. When an attribute is\
        \ explicitly included and is set to null, the group-value will be updated\
        \ to null."
      operationId: "updateGroup"
      produces:
      - "application/json"
      parameters:
      - name: "groupId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/GroupRequest"
      responses:
        200:
          description: "Indicates the group was updated."
          schema:
            $ref: "#/definitions/GroupResponse"
        404:
          description: "Indicates the requested group was not found."
        409:
          description: "Indicates the requested group was updated simultaneously."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Groups"
      summary: "Delete a group"
      description: ""
      operationId: "deleteGroup"
      parameters:
      - name: "groupId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the group was found and  has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested group does not exist."
      security:
      - basicAuth: []
  /identity/groups/{groupId}/members:
    post:
      tags:
      - "Groups"
      summary: "Add a member to a group"
      description: ""
      operationId: "createMembership"
      produces:
      - "application/json"
      parameters:
      - name: "groupId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/MembershipRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/MembershipResponse"
        201:
          description: "Indicates the group was found and the member has been added."
        400:
          description: "Indicates the userId was not included in the request body."
        404:
          description: "Indicates the requested group was not found."
        409:
          description: "Indicates the requested user is already a member of the group."
      security:
      - basicAuth: []
  /identity/groups/{groupId}/members/{userId}:
    delete:
      tags:
      - "Groups"
      summary: "Delete a member from a group"
      description: ""
      operationId: "deleteMembership"
      parameters:
      - name: "groupId"
        in: "path"
        required: true
        type: "string"
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the group was found and the member has been deleted.\
            \ The response body is left empty intentionally."
        404:
          description: "Indicates the requested group was not found or that the user\
            \ is not a member of the group. The status description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /identity/users:
    get:
      tags:
      - "Users"
      summary: "List users"
      description: ""
      operationId: "listUsers"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return group with the given id"
        required: false
        type: "string"
      - name: "firstName"
        in: "query"
        description: "Only return users with the given firstname"
        required: false
        type: "string"
      - name: "lastName"
        in: "query"
        description: "Only return users with the given lastname"
        required: false
        type: "string"
      - name: "displayName"
        in: "query"
        description: "Only return users with the given displayName"
        required: false
        type: "string"
      - name: "email"
        in: "query"
        description: "Only return users with the given email"
        required: false
        type: "string"
      - name: "firstNameLike"
        in: "query"
        description: "Only return userswith a firstname like the given value. Use\
          \ % as wildcard-character."
        required: false
        type: "string"
      - name: "lastNameLike"
        in: "query"
        description: "Only return users with a lastname like the given value. Use\
          \ % as wildcard-character."
        required: false
        type: "string"
      - name: "displayNameLike"
        in: "query"
        description: "Only return users with a displayName like the given value. Use\
          \ % as wildcard-character."
        required: false
        type: "string"
      - name: "emailLike"
        in: "query"
        description: "Only return users with an email like the given value. Use %\
          \ as wildcard-character."
        required: false
        type: "string"
      - name: "memberOfGroup"
        in: "query"
        description: "Only return users which are a member of the given group."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return users which are a members of the given tenant."
        required: false
        type: "string"
      - name: "potentialStarter"
        in: "query"
        description: "Only return users  which members are potential starters for\
          \ a process-definition with the given id."
        required: false
        type: "string"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "firstName"
        - "lastname"
        - "email"
        - "displayName"
      responses:
        200:
          description: "Indicates the group exists and is returned."
          schema:
            $ref: "#/definitions/DataResponseUserResponse"
      security:
      - basicAuth: []
    post:
      tags:
      - "Users"
      summary: "Create a user"
      description: ""
      operationId: "createUser"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/UserRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/UserResponse"
        201:
          description: "Indicates the user was created."
        400:
          description: "Indicates the id of the user was missing."
      security:
      - basicAuth: []
  /identity/users/{userId}:
    get:
      tags:
      - "Users"
      summary: "Get a single user"
      description: ""
      operationId: "getUser"
      produces:
      - "application/json"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the user exists and is returned."
          schema:
            $ref: "#/definitions/UserResponse"
        404:
          description: "Indicates the requested user does not exist."
      security:
      - basicAuth: []
    put:
      tags:
      - "Users"
      summary: "Update a user"
      description: "All request values are optional. For example, you can only include\
        \ the firstName attribute in the request body JSON-object, only updating the\
        \ firstName of the user, leaving all other fields unaffected. When an attribute\
        \ is explicitly included and is set to null, the user-value will be updated\
        \ to null. Example: {\"firstName\" : null} will clear the firstName of the\
        \ user)."
      operationId: "updateUser"
      produces:
      - "application/json"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/UserRequest"
      responses:
        200:
          description: "Indicates the user was updated."
          schema:
            $ref: "#/definitions/UserResponse"
        404:
          description: "Indicates the requested user was not found."
        409:
          description: "Indicates the requested user was updated simultaneously."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Users"
      summary: "Delete a user"
      description: ""
      operationId: "deleteUser"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the user was found and  has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested user was not found."
      security:
      - basicAuth: []
  /identity/users/{userId}/info:
    get:
      tags:
      - "Users"
      summary: "List user’s info"
      description: ""
      operationId: "listUserInfo"
      produces:
      - "application/json"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the user was found and list of info (key and url)\
            \ is returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/UserInfoResponse"
        404:
          description: "Indicates the requested user was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Users"
      summary: "Create a new user’s info entry"
      description: ""
      operationId: "createUserInfo"
      produces:
      - "application/json"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/UserInfoRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/UserInfoResponse"
        201:
          description: "Indicates the user was found and the info has been created."
        400:
          description: "Indicates the key or value was missing from the request body.\
            \ Status description contains additional information about the error."
        404:
          description: "Indicates the requested user was not found."
        409:
          description: "Indicates there is already an info-entry with the given key\
            \ for the user, update the resource instance (PUT)."
      security:
      - basicAuth: []
  /identity/users/{userId}/info/{key}:
    get:
      tags:
      - "Users"
      summary: "Get a user’s info"
      description: ""
      operationId: "getUserInfo"
      produces:
      - "application/json"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      - name: "key"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the user was found and the user has info for the\
            \ given key."
          schema:
            $ref: "#/definitions/UserInfoResponse"
        404:
          description: "Indicates the requested user was not found or the user does\
            \ ot have info for the given key. Status description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
    put:
      tags:
      - "Users"
      summary: "Update a user’s info"
      description: ""
      operationId: "updateUserInfo"
      produces:
      - "application/json"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      - name: "key"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/UserInfoRequest"
      responses:
        200:
          description: "Indicates the user was found and the info has been updated."
          schema:
            $ref: "#/definitions/UserInfoResponse"
        400:
          description: "Indicates the value was missing from the request body."
        404:
          description: "Indicates the requested user was not found or the user does\
            \ not have info for the given key. Status description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Users"
      summary: "Delete a user’s info"
      description: ""
      operationId: "deleteUserInfo"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      - name: "key"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the user was found and the info for the given key\
            \ has been deleted. Response body is left empty intentionally."
        404:
          description: "Indicates the requested user was not found or the user does\
            \ not have info for the given key. Status description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /identity/users/{userId}/picture:
    get:
      tags:
      - "Users"
      summary: "Get a user’s picture"
      description: "The response body contains the raw picture data, representing\
        \ the user’s picture. The Content-type of the response corresponds to the\
        \ mimeType that was set when creating the picture."
      operationId: "getUserPicture"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the user was found and has a picture, which is returned\
            \ in the body."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested user was not found or the user does\
            \ not have a profile picture. Status-description contains additional information\
            \ about the error."
      security:
      - basicAuth: []
    put:
      tags:
      - "Users"
      summary: "Updating a user’s picture"
      description: "The request should be of type multipart/form-data. There should\
        \ be a single file-part included with the binary value of the picture. On\
        \ top of that, the following additional form-fields can be present:\n\nmimeType:\
        \ Optional mime-type for the uploaded picture. If omitted, the default of\
        \ image/jpeg is used as a mime-type for the picture."
      operationId: "updateUserPicture"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "userId"
        in: "path"
        required: true
        type: "string"
      - name: "file"
        in: "formData"
        description: "Picture to update"
        required: true
        type: "file"
      responses:
        204:
          description: "Indicates the user was found and the picture has been updated.\
            \ The response-body is left empty intentionally."
        404:
          description: "Indicates the requested user was not found."
      security:
      - basicAuth: []
  /management/batch-parts/{batchPartId}:
    get:
      tags:
      - "Batch parts"
      summary: "Get a single batch part"
      description: ""
      operationId: "getBatchPart"
      produces:
      - "application/json"
      parameters:
      - name: "batchPartId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the batch part exists and is returned."
          schema:
            $ref: "#/definitions/BatchPartResponse"
        404:
          description: "Indicates the requested batch part does not exist."
      security:
      - basicAuth: []
  /management/batch-parts/{batchPartId}/batch-part-document:
    get:
      tags:
      - "Batches"
      summary: "Get the batch part document"
      description: ""
      operationId: "getBatchPartDocument"
      parameters:
      - name: "batchPartId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested batch part was found and the batch\
            \ part document has been returned. The response contains the raw batch\
            \ part document and always has a Content-type of application/json."
          schema:
            type: "string"
        404:
          description: "Indicates the requested batch part was not found or the job\
            \ does not have a batch part document. Status-description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /management/batches:
    get:
      tags:
      - "Batches"
      summary: "List batches"
      description: ""
      operationId: "listBatches"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return batch with the given id"
        required: false
        type: "string"
      - name: "batchType"
        in: "query"
        description: "Only return batches for the given type"
        required: false
        type: "string"
      - name: "searchKey"
        in: "query"
        description: "Only return batches for the given search key"
        required: false
        type: "string"
      - name: "searchKey2"
        in: "query"
        description: "Only return batches for the given search key2"
        required: false
        type: "string"
      - name: "createTimeBefore"
        in: "query"
        description: "Only return batches created before the given date"
        required: false
        type: "string"
        format: "date-time"
      - name: "createTimeAfter"
        in: "query"
        description: "Only batches batches created after the given date"
        required: false
        type: "string"
        format: "date-time"
      - name: "completeTimeBefore"
        in: "query"
        description: "Only return batches completed before the given date"
        required: false
        type: "string"
        format: "date-time"
      - name: "completeTimeAfter"
        in: "query"
        description: "Only batches batches completed after the given date"
        required: false
        type: "string"
        format: "date-time"
      - name: "status"
        in: "query"
        description: "Only return batches for the given status"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return batches for the given tenant id"
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return batches like given search key"
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns batches without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      responses:
        200:
          description: "Indicates the requested batches were returned."
          schema:
            $ref: "#/definitions/DataResponseBatchResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /management/batches/{batchId}:
    get:
      tags:
      - "Batches"
      summary: "Get a single batch"
      description: ""
      operationId: "getBatch"
      produces:
      - "application/json"
      parameters:
      - name: "batchId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the batch exists and is returned."
          schema:
            $ref: "#/definitions/BatchResponse"
        404:
          description: "Indicates the requested batch does not exist."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Batches"
      summary: "Delete a batch"
      description: ""
      operationId: "deleteBatch"
      parameters:
      - name: "batchId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the batch was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested batch was not found."
      security:
      - basicAuth: []
  /management/batches/{batchId}/batch-document:
    get:
      tags:
      - "Batches"
      summary: "Get the batch document"
      description: ""
      operationId: "getBatchDocument"
      parameters:
      - name: "batchId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested batch was found and the batch document\
            \ has been returned. The response contains the raw batch document and\
            \ always has a Content-type of application/json."
          schema:
            type: "string"
        404:
          description: "Indicates the requested batch was not found or the job does\
            \ not have a batch document. Status-description contains additional information\
            \ about the error."
      security:
      - basicAuth: []
  /management/batches/{batchId}/batch-parts:
    get:
      tags:
      - "Batches"
      summary: "List batch parts"
      description: ""
      operationId: "listBatchesPart"
      produces:
      - "application/json"
      parameters:
      - name: "batchId"
        in: "path"
        required: true
        type: "string"
      - name: "status"
        in: "query"
        description: "Only return batch parts for the given status"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the requested batch parts were returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/BatchPartResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /management/deadletter-jobs:
    get:
      tags:
      - "Jobs"
      summary: "List deadletter jobs"
      description: ""
      operationId: "listDeadLetterJobs"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return job with the given id"
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "Only return jobs part of a process with the given id"
        required: false
        type: "string"
      - name: "executionId"
        in: "query"
        description: "Only return jobs part of an execution with the given id"
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "Only return jobs with the given process definition id"
        required: false
        type: "string"
      - name: "elementId"
        in: "query"
        description: "Only return jobs with the given element id"
        required: false
        type: "string"
      - name: "elementName"
        in: "query"
        description: "Only return jobs with the given element name"
        required: false
        type: "string"
      - name: "executable"
        in: "query"
        description: "If true, only return jobs which are executable. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "timersOnly"
        in: "query"
        description: "If true, only return jobs which are timers. If false, this parameter\
          \ is ignored. Cannot be used together with 'messagesOnly'."
        required: false
        type: "boolean"
      - name: "messagesOnly"
        in: "query"
        description: "If true, only return jobs which are messages. If false, this\
          \ parameter is ignored. Cannot be used together with 'timersOnly'"
        required: false
        type: "boolean"
      - name: "withException"
        in: "query"
        description: "If true, only return jobs for which an exception occurred while\
          \ executing it. If false, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "dueBefore"
        in: "query"
        description: "Only return jobs which are due to be executed before the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return jobs which are due to be executed after the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "exceptionMessage"
        in: "query"
        description: "Only return jobs with the given exception message"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return jobs with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return jobs with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns jobs without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "locked"
        in: "query"
        description: "If true, only return jobs which are locked.  If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "unlocked"
        in: "query"
        description: "If true, only return jobs which are unlocked. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "dueDate"
        - "executionId"
        - "processInstanceId"
        - "retries"
        - "tenantId"
      responses:
        200:
          description: "Indicates the requested jobs were returned."
          schema:
            $ref: "#/definitions/DataResponseJobResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter\
            \ or the both 'messagesOnly' and 'timersOnly' are used as parameters.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /management/deadletter-jobs/{jobId}:
    get:
      tags:
      - "Jobs"
      summary: "Get a single deadletter job"
      description: ""
      operationId: "getDeadletterJob"
      produces:
      - "application/json"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the suspended job exists and is returned."
          schema:
            $ref: "#/definitions/JobResponse"
        404:
          description: "Indicates the requested job does not exist."
      security:
      - basicAuth: []
    post:
      tags:
      - "Jobs"
      summary: "Move a single deadletter job"
      description: ""
      operationId: "executeDeadLetterJobAction"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestActionRequest"
      responses:
        204:
          description: "Indicates the dead letter job was moved. Response-body is\
            \ intentionally empty."
        404:
          description: "Indicates the requested job was not found."
        500:
          description: "Indicates the an exception occurred while executing the job.\
            \ The status-description contains additional detail about the error. The\
            \ full error-stacktrace can be fetched later on if needed."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Jobs"
      summary: "Delete a deadletter job"
      description: ""
      operationId: "deleteDeadLetterJob"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the job was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested job was not found."
      security:
      - basicAuth: []
  /management/deadletter-jobs/{jobId}/exception-stacktrace:
    get:
      tags:
      - "Jobs"
      summary: "Get the exception stacktrace for a deadletter job"
      description: ""
      operationId: "getDeadLetterJobStacktrace"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested job was not found and the stacktrace\
            \ has been returned. The response contains the raw stacktrace and always\
            \ has a Content-type of text/plain."
          schema:
            type: "string"
        404:
          description: "Indicates the requested job was not found or the job does\
            \ not have an exception stacktrace. Status-description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /management/engine:
    get:
      tags:
      - "Engine"
      summary: "Get engine info"
      description: ""
      operationId: "getEngineInfo"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Indicates the engine info is returned."
          schema:
            $ref: "#/definitions/ProcessEngineInfoResponse"
      security:
      - basicAuth: []
  /management/jobs:
    get:
      tags:
      - "Jobs"
      summary: "List jobs"
      description: ""
      operationId: "listJobs"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return job with the given id"
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "Only return jobs part of a process with the given id"
        required: false
        type: "string"
      - name: "executionId"
        in: "query"
        description: "Only return jobs part of an execution with the given id"
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "Only return jobs with the given process definition id"
        required: false
        type: "string"
      - name: "elementId"
        in: "query"
        description: "Only return jobs with the given element id"
        required: false
        type: "string"
      - name: "elementName"
        in: "query"
        description: "Only return jobs with the given element name"
        required: false
        type: "string"
      - name: "timersOnly"
        in: "query"
        description: "If true, only return jobs which are timers. If false, this parameter\
          \ is ignored. Cannot be used together with 'messagesOnly'."
        required: false
        type: "boolean"
      - name: "messagesOnly"
        in: "query"
        description: "If true, only return jobs which are messages. If false, this\
          \ parameter is ignored. Cannot be used together with 'timersOnly'"
        required: false
        type: "boolean"
      - name: "withException"
        in: "query"
        description: "If true, only return jobs for which an exception occurred while\
          \ executing it. If false, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "dueBefore"
        in: "query"
        description: "Only return jobs which are due to be executed before the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return jobs which are due to be executed after the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "exceptionMessage"
        in: "query"
        description: "Only return jobs with the given exception message"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return jobs with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return jobs with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns jobs without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "locked"
        in: "query"
        description: "If true, only return jobs which are locked.  If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "unlocked"
        in: "query"
        description: "If true, only return jobs which are unlocked. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "dueDate"
        - "executionId"
        - "processInstanceId"
        - "retries"
        - "tenantId"
      responses:
        200:
          description: "Indicates the requested jobs were returned."
          schema:
            $ref: "#/definitions/DataResponseJobResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter\
            \ or the both 'messagesOnly' and 'timersOnly' are used as parameters.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /management/jobs/{jobId}:
    get:
      tags:
      - "Jobs"
      summary: "Get a single job"
      description: ""
      operationId: "getJob"
      produces:
      - "application/json"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the job exists and is returned."
          schema:
            $ref: "#/definitions/JobResponse"
        404:
          description: "Indicates the requested job does not exist."
      security:
      - basicAuth: []
    post:
      tags:
      - "Jobs"
      summary: "Execute a single job"
      description: ""
      operationId: "executeJobAction"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestActionRequest"
      responses:
        204:
          description: "Indicates the job was executed. Response-body is intentionally\
            \ empty."
        404:
          description: "Indicates the requested job was not found."
        500:
          description: "Indicates the an exception occurred while executing the job.\
            \ The status-description contains additional detail about the error. The\
            \ full error-stacktrace can be fetched later on if needed."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Jobs"
      summary: "Delete a job"
      description: ""
      operationId: "deleteJob"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the job was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested job was not found.."
      security:
      - basicAuth: []
  /management/jobs/{jobId}/exception-stacktrace:
    get:
      tags:
      - "Jobs"
      summary: "Get the exception stacktrace for a job"
      description: ""
      operationId: "getJobStacktrace"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested job was not found and the stacktrace\
            \ has been returned. The response contains the raw stacktrace and always\
            \ has a Content-type of text/plain."
          schema:
            type: "string"
        404:
          description: "Indicates the requested job was not found or the job does\
            \ not have an exception stacktrace. Status-description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /management/properties:
    get:
      tags:
      - "Engine"
      summary: "List engine properties"
      description: ""
      operationId: "getProperties"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Indicates the properties are returned."
          schema:
            type: "object"
            additionalProperties:
              type: "string"
      security:
      - basicAuth: []
  /management/suspended-jobs:
    get:
      tags:
      - "Jobs"
      summary: "List suspended jobs"
      description: ""
      operationId: "listSuspendedJobs"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return job with the given id"
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "Only return jobs part of a process with the given id"
        required: false
        type: "string"
      - name: "executionId"
        in: "query"
        description: "Only return jobs part of an execution with the given id"
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "Only return jobs with the given process definition id"
        required: false
        type: "string"
      - name: "elementId"
        in: "query"
        description: "Only return jobs with the given element id"
        required: false
        type: "string"
      - name: "elementName"
        in: "query"
        description: "Only return jobs with the given element name"
        required: false
        type: "string"
      - name: "executable"
        in: "query"
        description: "If true, only return jobs which are executable. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "timersOnly"
        in: "query"
        description: "If true, only return jobs which are timers. If false, this parameter\
          \ is ignored. Cannot be used together with 'messagesOnly'."
        required: false
        type: "boolean"
      - name: "messagesOnly"
        in: "query"
        description: "If true, only return jobs which are messages. If false, this\
          \ parameter is ignored. Cannot be used together with 'timersOnly'"
        required: false
        type: "boolean"
      - name: "withException"
        in: "query"
        description: "If true, only return jobs for which an exception occurred while\
          \ executing it. If false, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "dueBefore"
        in: "query"
        description: "Only return jobs which are due to be executed before the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return jobs which are due to be executed after the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "exceptionMessage"
        in: "query"
        description: "Only return jobs with the given exception message"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return jobs with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return jobs with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns jobs without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "locked"
        in: "query"
        description: "If true, only return jobs which are locked.  If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "unlocked"
        in: "query"
        description: "If true, only return jobs which are unlocked. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "dueDate"
        - "executionId"
        - "processInstanceId"
        - "retries"
        - "tenantId"
      responses:
        200:
          description: "Indicates the requested jobs were returned."
          schema:
            $ref: "#/definitions/DataResponseJobResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter\
            \ or the both 'messagesOnly' and 'timersOnly' are used as parameters.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /management/suspended-jobs/{jobId}:
    get:
      tags:
      - "Jobs"
      summary: "Get a single suspended job"
      description: ""
      operationId: "getSuspendedJob"
      produces:
      - "application/json"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the suspended job exists and is returned."
          schema:
            $ref: "#/definitions/JobResponse"
        404:
          description: "Indicates the requested job does not exist."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Jobs"
      summary: "Delete a suspended job"
      description: ""
      operationId: "deleteSuspendedJob"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the job was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested job was not found."
      security:
      - basicAuth: []
  /management/suspended-jobs/{jobId}/exception-stacktrace:
    get:
      tags:
      - "Jobs"
      summary: "Get the exception stacktrace for a suspended job"
      description: ""
      operationId: "getSuspendedJobStacktrace"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested job was not found and the stacktrace\
            \ has been returned. The response contains the raw stacktrace and always\
            \ has a Content-type of text/plain."
          schema:
            type: "string"
        404:
          description: "Indicates the requested job was not found or the job does\
            \ not have an exception stacktrace. Status-description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /management/tables:
    get:
      tags:
      - "Database tables"
      summary: " List tables"
      description: ""
      operationId: "listTables"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Indicates the request was successful."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/TableResponse"
      security:
      - basicAuth: []
  /management/tables/{tableName}:
    get:
      tags:
      - "Database tables"
      summary: "Get a single table"
      description: ""
      operationId: "getTable"
      produces:
      - "application/json"
      parameters:
      - name: "tableName"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the table exists and the table count is returned."
          schema:
            $ref: "#/definitions/TableResponse"
        404:
          description: "Indicates the requested table does not exist."
      security:
      - basicAuth: []
  /management/tables/{tableName}/columns:
    get:
      tags:
      - "Database tables"
      summary: "Get column info for a single table"
      description: ""
      operationId: "getTableMetaData"
      produces:
      - "application/json"
      parameters:
      - name: "tableName"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the table exists and the table column info is returned."
          schema:
            $ref: "#/definitions/TableMetaData"
        404:
          description: "Indicates the requested table does not exist."
      security:
      - basicAuth: []
  /management/tables/{tableName}/data:
    get:
      tags:
      - "Database tables"
      summary: "Get row data for a single table"
      description: ""
      operationId: "getTableData"
      produces:
      - "application/json"
      parameters:
      - name: "tableName"
        in: "path"
        required: true
        type: "string"
      - name: "start"
        in: "query"
        description: "Index of the first row to fetch. Defaults to 0."
        required: false
        type: "integer"
      - name: "size"
        in: "query"
        description: "Number of rows to fetch, starting from start. Defaults to 10."
        required: false
        type: "integer"
      - name: "orderAscendingColumn"
        in: "query"
        description: "Name of the column to sort the resulting rows on, ascending."
        required: false
        type: "string"
      - name: "orderDescendingColumn"
        in: "query"
        description: "Name of the column to sort the resulting rows on, descending."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the table exists and the table row data is returned"
          schema:
            $ref: "#/definitions/DataResponseListMapStringObject"
        404:
          description: "Indicates the requested table does not exist."
      security:
      - basicAuth: []
  /management/timer-jobs:
    get:
      tags:
      - "Jobs"
      summary: "List timer jobs"
      description: ""
      operationId: "listTimerJobs"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return job with the given id"
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "Only return jobs part of a process with the given id"
        required: false
        type: "string"
      - name: "executionId"
        in: "query"
        description: "Only return jobs part of an execution with the given id"
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "Only return jobs with the given process definition id"
        required: false
        type: "string"
      - name: "elementId"
        in: "query"
        description: "Only return jobs with the given element id"
        required: false
        type: "string"
      - name: "elementName"
        in: "query"
        description: "Only return jobs with the given element name"
        required: false
        type: "string"
      - name: "executable"
        in: "query"
        description: "If true, only return jobs which are executable. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "timersOnly"
        in: "query"
        description: "If true, only return jobs which are timers. If false, this parameter\
          \ is ignored. Cannot be used together with 'messagesOnly'."
        required: false
        type: "boolean"
      - name: "messagesOnly"
        in: "query"
        description: "If true, only return jobs which are messages. If false, this\
          \ parameter is ignored. Cannot be used together with 'timersOnly'"
        required: false
        type: "boolean"
      - name: "withException"
        in: "query"
        description: "If true, only return jobs for which an exception occurred while\
          \ executing it. If false, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "dueBefore"
        in: "query"
        description: "Only return jobs which are due to be executed before the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return jobs which are due to be executed after the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "exceptionMessage"
        in: "query"
        description: "Only return jobs with the given exception message"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return jobs with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return jobs with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns jobs without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "locked"
        in: "query"
        description: "If true, only return jobs which are locked.  If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "unlocked"
        in: "query"
        description: "If true, only return jobs which are unlocked. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "dueDate"
        - "executionId"
        - "processInstanceId"
        - "retries"
        - "tenantId"
      responses:
        200:
          description: "Indicates the requested jobs were returned."
          schema:
            $ref: "#/definitions/DataResponseJobResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter\
            \ or the both 'messagesOnly' and 'timersOnly' are used as parameters.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /management/timer-jobs/{jobId}:
    get:
      tags:
      - "Jobs"
      summary: "Get a single timer job"
      description: ""
      operationId: "getTimerJob"
      produces:
      - "application/json"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the timer job exists and is returned."
          schema:
            $ref: "#/definitions/JobResponse"
        404:
          description: "Indicates the requested job does not exist."
      security:
      - basicAuth: []
    post:
      tags:
      - "Jobs"
      summary: "Move a single timer job"
      description: ""
      operationId: "executeTimerJobAction"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestActionRequest"
      responses:
        204:
          description: "Indicates the timer job was moved. Response-body is intentionally\
            \ empty."
        404:
          description: "Indicates the requested job was not found."
        500:
          description: "Indicates the an exception occurred while executing the job.\
            \ The status-description contains additional detail about the error. The\
            \ full error-stacktrace can be fetched later on if needed."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Jobs"
      summary: "Delete a timer job"
      description: ""
      operationId: "deleteTimerJob"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the job was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested job was not found."
      security:
      - basicAuth: []
  /management/timer-jobs/{jobId}/exception-stacktrace:
    get:
      tags:
      - "Jobs"
      summary: "Get the exception stacktrace for a timer job"
      description: ""
      operationId: "getTimerJobStacktrace"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested job was not found and the stacktrace\
            \ has been returned. The response contains the raw stacktrace and always\
            \ has a Content-type of text/plain."
          schema:
            type: "string"
        404:
          description: "Indicates the requested job was not found or the job does\
            \ not have an exception stacktrace. Status-description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /query/executions:
    post:
      tags:
      - "Executions"
      - "Query"
      summary: "Query executions"
      description: "The request body can contain all possible filters that can be\
        \ used in the List executions URL query. On top of these, it’s possible to\
        \ provide an array of variables and processInstanceVariables to include in\
        \ the query, with their format described here.\n\nThe general paging and sorting\
        \ query-parameters can be used for this URL."
      operationId: "queryExecutions"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ExecutionQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the executions are returned."
          schema:
            $ref: "#/definitions/DataResponseExecutionResponse"
        404:
          description: "Indicates a parameter was passed in the wrong format . The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /query/historic-activity-instances:
    post:
      tags:
      - "History"
      - "Query"
      summary: "Query for historic activity instances"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic task instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long."
      operationId: "queryActivityInstances"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricActivityInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the activities are returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricActivityInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information"
      security:
      - basicAuth: []
  /query/historic-detail:
    post:
      tags:
      - "History"
      - "Query"
      summary: "Query for historic details"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic process instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long."
      operationId: "queryHistoricDetail"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricDetailQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the historic details\
            \ are returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricDetailResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /query/historic-process-instances:
    post:
      tags:
      - "History Process"
      - "Query"
      summary: "Query for historic process instances"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic process instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long. On top of that, the query allows for filtering based on process\
        \ variables. The variables property is a JSON-array containing objects with\
        \ the format as described here."
      operationId: "queryHistoricProcessInstance"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricProcessInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the process instances\
            \ are returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricProcessInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /query/historic-task-instances:
    post:
      tags:
      - "History Task"
      - "Query"
      summary: "Query for historic task instances"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic task instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long. On top of that, the query allows for filtering based on process\
        \ variables. The taskVariables and processVariables properties are JSON-arrays\
        \ containing objects with the format as described here."
      operationId: "queryHistoricTaskInstance"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricTaskInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the tasks are returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricTaskInstanceResponse"
        404:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /query/historic-variable-instances:
    post:
      tags:
      - "History"
      - "Query"
      summary: "Query for historic variable instances"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic process instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long. On top of that, the query allows for filtering based on process\
        \ variables. The variables property is a JSON-array containing objects with\
        \ the format as described here."
      operationId: "queryVariableInstances"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricVariableInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the tasks are returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricVariableInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /query/process-instances:
    post:
      tags:
      - "Process Instances"
      - "Query"
      summary: "Query process instances"
      description: "The request body can contain all possible filters that can be\
        \ used in the List process instances URL query. On top of these, it’s possible\
        \ to provide an array of variables to include in the query, with their format\
        \ described here.\n\nThe general paging and sorting query-parameters can be\
        \ used for this URL."
      operationId: "queryProcessInstances"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ProcessInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the process-instances\
            \ are returned"
          schema:
            $ref: "#/definitions/DataResponseProcessInstanceResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format . The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /query/tasks:
    post:
      tags:
      - "Tasks"
      - "Query"
      summary: "Query for tasks"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of tasks (except for candidateGroupIn\
        \ which is only available in this POST task query REST service), but passed\
        \ in as JSON-body arguments rather than URL-parameters to allow for more advanced\
        \ querying and preventing errors with request-uri’s that are too long. On\
        \ top of that, the query allows for filtering based on task and process variables.\
        \ The taskVariables and processInstanceVariables are both JSON-arrays containing\
        \ objects with the format as described here."
      operationId: "queryTasks"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/TaskQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the tasks are returned."
          schema:
            $ref: "#/definitions/DataResponseTaskResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format or that\
            \ delegationState has an invalid value (other than pending and resolved).\
            \ The status-message contains additional information."
      security:
      - basicAuth: []
  /repository/deployments:
    get:
      tags:
      - "Deployment"
      summary: "List Deployments"
      description: ""
      operationId: "listDeployments"
      produces:
      - "application/json"
      parameters:
      - name: "name"
        in: "query"
        description: "Only return deployments with the given name."
        required: false
        type: "string"
      - name: "nameLike"
        in: "query"
        description: "Only return deployments with a name like the given name."
        required: false
        type: "string"
      - name: "category"
        in: "query"
        description: "Only return deployments with the given category."
        required: false
        type: "string"
      - name: "categoryNotEquals"
        in: "query"
        description: "Only return deployments which do not have the given category."
        required: false
        type: "string"
      - name: "parentDeploymentId"
        in: "query"
        description: "Only return deployments with the given parent deployment id."
        required: false
        type: "string"
      - name: "parentDeploymentIdLike"
        in: "query"
        description: "Only return deployments with a parent deployment id like the\
          \ given value."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return deployments with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return deployments with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns deployments without a tenantId set. If\
          \ false, the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "name"
        - "deployTime"
        - "tenantId"
      responses:
        200:
          description: "Indicates the request was successful."
          schema:
            $ref: "#/definitions/DataResponseDeploymentResponse"
      security:
      - basicAuth: []
    post:
      tags:
      - "Deployment"
      summary: "Create a new deployment"
      description: "The request body should contain data of type multipart/form-data.\
        \ There should be exactly one file in the request, any additional files will\
        \ be ignored. The deployment name is the name of the file-field passed in.\
        \ If multiple resources need to be deployed in a single deployment, compress\
        \ the resources in a zip and make sure the file-name ends with .bar or .zip.\n\
        \nAn additional parameter (form-field) can be passed in the request body with\
        \ name tenantId. The value of this field will be used as the id of the tenant\
        \ this deployment is done in."
      operationId: "uploadDeployment"
      consumes:
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentKey"
        in: "query"
        required: false
        type: "string"
      - name: "deploymentName"
        in: "query"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        required: false
        type: "string"
      - name: "file"
        in: "formData"
        required: true
        type: "file"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/DeploymentResponse"
        201:
          description: "Indicates the deployment was created."
        400:
          description: "Indicates there was no content present in the request body\
            \ or the content mime-type is not supported for deployment. The status-description\
            \ contains additional information."
      security:
      - basicAuth: []
  /repository/deployments/{deploymentId}:
    get:
      tags:
      - "Deployment"
      summary: "Get a deployment"
      description: ""
      operationId: "getDeployment"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentId"
        in: "path"
        description: "The id of the deployment to get."
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the deployment was found and returned."
          schema:
            $ref: "#/definitions/DeploymentResponse"
        404:
          description: "Indicates the requested deployment was not found."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Deployment"
      summary: "Delete a deployment"
      description: ""
      operationId: "deleteDeployment"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentId"
        in: "path"
        required: true
        type: "string"
      - name: "cascade"
        in: "query"
        required: false
        type: "boolean"
      responses:
        204:
          description: "Indicates the deployment was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested deployment was not found."
      security:
      - basicAuth: []
  /repository/deployments/{deploymentId}/resourcedata/{resourceName}:
    get:
      tags:
      - "Deployment"
      summary: "Get a deployment resource content"
      description: "The response body will contain the binary resource-content for\
        \ the requested resource. The response content-type will be the same as the\
        \ type returned in the resources mimeType property. Also, a content-disposition\
        \ header is set, allowing browsers to download the file instead of displaying\
        \ it."
      operationId: "getDeploymentResourceData"
      parameters:
      - name: "deploymentId"
        in: "path"
        required: true
        type: "string"
      - name: "resourceName"
        in: "path"
        description: "The name of the resource to get. Make sure you URL-encode the\
          \ resourceName in case it contains forward slashes. Eg: use diagrams%2Fmy-process.bpmn20.xml\
          \ instead of diagrams/my-process.bpmn20.xml."
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates both deployment and resource have been found and\
            \ the resource data has been returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested deployment was not found or there\
            \ is no resource with the given id present in the deployment. The status-description\
            \ contains additional information."
      security:
      - basicAuth: []
  /repository/deployments/{deploymentId}/resources:
    get:
      tags:
      - "Deployment"
      summary: "List resources in a deployment"
      description: "The dataUrl property in the resulting JSON for a single resource\
        \ contains the actual URL to use for retrieving the binary resource."
      operationId: "listDeploymentResources"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the deployment was found and the resource list has\
            \ been returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/DeploymentResourceResponse"
        404:
          description: "Indicates the requested deployment was not found."
      security:
      - basicAuth: []
  /repository/deployments/{deploymentId}/resources/**:
    get:
      tags:
      - "Deployment"
      summary: "Get a deployment resource"
      description: "Replace ** by ResourceId"
      operationId: "getDeploymentResource"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates both deployment and resource have been found and\
            \ the resource has been returned."
          schema:
            $ref: "#/definitions/DeploymentResourceResponse"
        404:
          description: "Indicates the requested deployment was not found or there\
            \ is no resource with the given id present in the deployment. The status-description\
            \ contains additional information."
      security:
      - basicAuth: []
  /repository/models:
    get:
      tags:
      - "Models"
      summary: "List models"
      description: ""
      operationId: "listModels"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return models with the given version."
        required: false
        type: "string"
      - name: "category"
        in: "query"
        description: "Only return models with the given category."
        required: false
        type: "string"
      - name: "categoryLike"
        in: "query"
        description: "Only return models with a category like the given name."
        required: false
        type: "string"
      - name: "categoryNotEquals"
        in: "query"
        description: "Only return models which do not have the given category."
        required: false
        type: "string"
      - name: "name"
        in: "query"
        description: "Only return models with the given name."
        required: false
        type: "string"
      - name: "nameLike"
        in: "query"
        description: "Only return models with a name like the given name."
        required: false
        type: "string"
      - name: "key"
        in: "query"
        description: "Only return models with the given key."
        required: false
        type: "string"
      - name: "deploymentId"
        in: "query"
        description: "Only return models with the given category."
        required: false
        type: "string"
      - name: "version"
        in: "query"
        description: "Only return models with the given version."
        required: false
        type: "integer"
      - name: "latestVersion"
        in: "query"
        description: "If true, only return models which are the latest version. Best\
          \ used in combination with key. If false is passed in as value, this is\
          \ ignored and all versions are returned."
        required: false
        type: "boolean"
      - name: "deployed"
        in: "query"
        description: "If true, only deployed models are returned. If false, only undeployed\
          \ models are returned (deploymentId is null)."
        required: false
        type: "boolean"
      - name: "tenantId"
        in: "query"
        description: "Only return models with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return models with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns models without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "category"
        - "createTime"
        - "key"
        - "lastUpdateTime"
        - "name"
        - "version"
        - "tenantId"
      responses:
        200:
          description: "Indicates request was successful and the models are returned"
          schema:
            $ref: "#/definitions/DataResponseModelResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
    post:
      tags:
      - "Models"
      summary: "Create a model"
      description: "All request values are optional. For example, you can only include\
        \ the name attribute in the request body JSON-object, only setting the name\
        \ of the model, leaving all other fields null."
      operationId: "createModel"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ModelRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/ModelResponse"
        201:
          description: "Indicates the model was created."
      security:
      - basicAuth: []
  /repository/models/{modelId}:
    get:
      tags:
      - "Models"
      summary: "Get a model"
      description: ""
      operationId: "getModel"
      produces:
      - "application/json"
      parameters:
      - name: "modelId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the model was found and returned."
          schema:
            $ref: "#/definitions/ModelResponse"
        404:
          description: "Indicates the requested model was not found."
      security:
      - basicAuth: []
    put:
      tags:
      - "Models"
      summary: "Update a model"
      description: "All request values are optional. For example, you can only include\
        \ the name attribute in the request body JSON-object, only updating the name\
        \ of the model, leaving all other fields unaffected. When an attribute is\
        \ explicitly included and is set to null, the model-value will be updated\
        \ to null. "
      operationId: "updateModel"
      produces:
      - "application/json"
      parameters:
      - name: "modelId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ModelRequest"
      responses:
        200:
          description: "Indicates the model was found and updated."
          schema:
            $ref: "#/definitions/ModelResponse"
        404:
          description: "Indicates the requested model was not found."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Models"
      summary: "Delete a model"
      description: ""
      operationId: "deleteModel"
      parameters:
      - name: "modelId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the model was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested model was not found."
      security:
      - basicAuth: []
  /repository/models/{modelId}/source:
    get:
      tags:
      - "Models"
      summary: "Get the editor source for a model"
      description: "Response body contains the model’s raw editor source. The response’\
        s content-type is set to application/octet-stream, regardless of the content\
        \ of the source."
      operationId: "getModelBytes"
      parameters:
      - name: "modelId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the model was found and source is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested model was not found."
      security:
      - basicAuth: []
    put:
      tags:
      - "Models"
      summary: "Set the editor source for a model"
      description: "Response body contains the model’s raw editor source. The response’\
        s content-type is set to application/octet-stream, regardless of the content\
        \ of the source."
      operationId: "setModelSource"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "modelId"
        in: "path"
        required: true
        type: "string"
      - name: "file"
        in: "formData"
        required: true
        type: "file"
      responses:
        204:
          description: "Indicates the model was found and the source has been updated."
        404:
          description: "Indicates the requested model was not found."
      security:
      - basicAuth: []
  /repository/models/{modelId}/source-extra:
    get:
      tags:
      - "Models"
      summary: "Get the extra editor source for a model"
      description: "Response body contains the model’s raw editor source. The response’\
        s content-type is set to application/octet-stream, regardless of the content\
        \ of the source."
      operationId: "getExtraEditorSource"
      parameters:
      - name: "modelId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the model was found and source is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested model was not found."
      security:
      - basicAuth: []
    put:
      tags:
      - "Models"
      summary: "Set the extra editor source for a model"
      description: "Response body contains the model’s raw editor source. The response’\
        s content-type is set to application/octet-stream, regardless of the content\
        \ of the source."
      operationId: "setExtraEditorSource"
      consumes:
      - "multipart/form-data"
      parameters:
      - name: "modelId"
        in: "path"
        required: true
        type: "string"
      - name: "file"
        in: "formData"
        required: true
        type: "file"
      responses:
        204:
          description: "Indicates the model was found and the extra source has been\
            \ updated."
        404:
          description: "Indicates the requested model was not found."
      security:
      - basicAuth: []
  /repository/process-definitions:
    get:
      tags:
      - "Process Definitions"
      summary: "List of process definitions"
      description: ""
      operationId: "listProcessDefinitions"
      produces:
      - "application/json"
      parameters:
      - name: "version"
        in: "query"
        description: "Only return process definitions with the given version."
        required: false
        type: "integer"
      - name: "name"
        in: "query"
        description: "Only return process definitions with the given name."
        required: false
        type: "string"
      - name: "nameLike"
        in: "query"
        description: "Only return process definitions with a name like the given name."
        required: false
        type: "string"
      - name: "key"
        in: "query"
        description: "Only return process definitions with the given key."
        required: false
        type: "string"
      - name: "keyLike"
        in: "query"
        description: "Only return process definitions with a name like the given key."
        required: false
        type: "string"
      - name: "resourceName"
        in: "query"
        description: "Only return process definitions with the given resource name."
        required: false
        type: "string"
      - name: "resourceNameLike"
        in: "query"
        description: "Only return process definitions with a name like the given resource\
          \ name."
        required: false
        type: "string"
      - name: "category"
        in: "query"
        description: "Only return process definitions with the given category."
        required: false
        type: "string"
      - name: "categoryLike"
        in: "query"
        description: "Only return process definitions with a category like the given\
          \ name."
        required: false
        type: "string"
      - name: "categoryNotEquals"
        in: "query"
        description: "Only return process definitions which do not have the given\
          \ category."
        required: false
        type: "string"
      - name: "deploymentId"
        in: "query"
        description: "Only return process definitions with the given category."
        required: false
        type: "string"
      - name: "startableByUser"
        in: "query"
        description: "Only return process definitions which are part of a deployment\
          \ with the given id."
        required: false
        type: "string"
      - name: "latest"
        in: "query"
        description: "Only return the latest process definition versions. Can only\
          \ be used together with key and keyLike parameters, using any other parameter\
          \ will result in a 400-response."
        required: false
        type: "boolean"
      - name: "suspended"
        in: "query"
        description: "If true, only returns process definitions which are suspended.\
          \ If false, only active process definitions (which are not suspended) are\
          \ returned."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "name"
        - "id"
        - "key"
        - "category"
        - "deploymentId"
        - "version"
      responses:
        200:
          description: "Indicates request was successful and the process-definitions\
            \ are returned"
          schema:
            $ref: "#/definitions/DataResponseProcessDefinitionResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format or that\
            \ latest is used with other parameters other than key and keyLike. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}:
    get:
      tags:
      - "Process Definitions"
      summary: "Get a process definition"
      description: ""
      operationId: "getProcessDefinition"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the process-definitions\
            \ are returned"
          schema:
            $ref: "#/definitions/ProcessDefinitionResponse"
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
    put:
      tags:
      - "Process Definitions"
      summary: "Execute actions for a process definition"
      description: "Execute actions for a process definition (Update category, Suspend\
        \ or Activate)"
      operationId: "executeProcessDefinitionAction"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: true
        schema:
          $ref: "#/definitions/ProcessDefinitionActionRequest"
      responses:
        200:
          description: "Indicates action has been executed for the specified process.\
            \ (category altered, activate or suspend)"
          schema:
            $ref: "#/definitions/ProcessDefinitionResponse"
        400:
          description: "Indicates no category was defined in the request body."
        404:
          description: "Indicates the requested process definition was not found."
        409:
          description: "Indicates the requested process definition is already suspended\
            \ or active."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/batch-migrate:
    post:
      tags:
      - "Process Definitions"
      summary: "Batch migrate all instances of process definition"
      description: ""
      operationId: "batchMigrateInstancesOfProcessDefinition"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          type: "string"
      responses:
        200:
          description: "Indicates process instances were found and batch migration\
            \ was started."
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/decision-tables:
    get:
      tags:
      - "Process Definitions"
      summary: "List decision tables for a process-definition"
      description: ""
      operationId: "listProcessDefinitionDecisionTables"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process definition was found and the decision\
            \ tables are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/DecisionTableResponse"
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/form-definitions:
    get:
      tags:
      - "Process Definitions"
      summary: "List form definitions for a process-definition"
      description: ""
      operationId: "listProcessDefinitionFormDefinitions"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process definition was found and the form definitions\
            \ are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/FormDefinitionResponse"
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/identitylinks:
    get:
      tags:
      - "Process Definitions"
      summary: "List candidate starters for a process-definition"
      description: ""
      operationId: "listProcessDefinitionIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process definition was found and the requested\
            \ identity links are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Process Definitions"
      summary: "Add a candidate starter to a process definition"
      description: "It is possible to add either a user or a group."
      operationId: "createIdentityLink"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestIdentityLink"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/RestIdentityLink"
        201:
          description: "Indicates the process definition was found and the identity\
            \ link was created."
        400:
          description: "Indicates the body does not contain the correct information."
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/identitylinks/{family}/{identityId}:
    get:
      tags:
      - "Process Definitions"
      summary: "Get a candidate starter from a process definition"
      description: ""
      operationId: "getIdentityLink"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process definition was found and the identity\
            \ link was returned."
          schema:
            $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested process definition was not found or\
            \ the process definition does not have an identity-link that matches the\
            \ url."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Process Definitions"
      summary: "Delete a candidate starter from a process definition"
      description: ""
      operationId: "deleteIdentityLink"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the process definition was found and the identity\
            \ link was removed. The response body is intentionally empty."
        404:
          description: "Indicates the requested process definition was not found or\
            \ the process definition does not have an identity-link that matches the\
            \ url."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/image:
    get:
      tags:
      - "Process Definitions"
      summary: "Get a process definition image"
      description: ""
      operationId: "getModelResource"
      produces:
      - "image/png"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the process-definitions\
            \ are returned"
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/migrate:
    post:
      tags:
      - "Process Definitions"
      summary: "Migrate all instances of process definition"
      description: ""
      operationId: "migrateInstancesOfProcessDefinition"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          type: "string"
      responses:
        200:
          description: "Indicates process instances were found and migration was executed."
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/model:
    get:
      tags:
      - "Process Definitions"
      summary: "Get a process definition BPMN model"
      description: ""
      operationId: "getBpmnModelResource"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process definition was found and the model is\
            \ returned. The response contains the full process definition model."
          schema:
            $ref: "#/definitions/BpmnModel"
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/resourcedata:
    get:
      tags:
      - "Process Definitions"
      summary: "Get a process definition resource content"
      description: ""
      operationId: "getProcessDefinitionResource"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates both process definition and resource have been found\
            \ and the resource data has been returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested process definition was not found or\
            \ there is no resource with the given id present in the process definition.\
            \ The status-description contains additional information."
      security:
      - basicAuth: []
  /repository/process-definitions/{processDefinitionId}/start-form:
    get:
      tags:
      - "Process Definitions"
      summary: "Get a process definition start form"
      description: ""
      operationId: "getProcessDefinitionStartForm"
      produces:
      - "application/json"
      parameters:
      - name: "processDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the process definition\
            \ form is returned"
          schema:
            type: "string"
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
  /runtime/event-subscriptions:
    get:
      tags:
      - "Event subscriptions"
      summary: "List of event subscriptions"
      description: ""
      operationId: "listEventSubscriptions"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return event subscriptions with the given id"
        required: false
        type: "string"
      - name: "eventType"
        in: "query"
        description: "Only return event subscriptions with the given event type"
        required: false
        type: "string"
      - name: "eventName"
        in: "query"
        description: "Only return event subscriptions with the given event name"
        required: false
        type: "string"
      - name: "activityId"
        in: "query"
        description: "Only return event subscriptions with the given activity id"
        required: false
        type: "string"
      - name: "executionId"
        in: "query"
        description: "Only return event subscriptions with the given execution id"
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "Only return event subscriptions part of a process with the given\
          \ id"
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "Only return event subscriptions with the given process definition\
          \ id"
        required: false
        type: "string"
      - name: "scopeId"
        in: "query"
        description: "Only return event subscriptions part of a scope with the given\
          \ id"
        required: false
        type: "string"
      - name: "scopeDefinitionId"
        in: "query"
        description: "Only return event subscriptions with the given scope definition\
          \ id"
        required: false
        type: "string"
      - name: "createdBefore"
        in: "query"
        description: "Only return event subscriptions which are created before the\
          \ given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "createdAfter"
        in: "query"
        description: "Only return event subscriptions which are created after the\
          \ given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "tenantId"
        in: "query"
        description: "Only return event subscriptions with the given tenant id."
        required: false
        type: "string"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "created"
        - "executionId"
        - "processInstanceId"
        - "processDefinitionId"
        - "tenantId"
      responses:
        200:
          description: "Indicates the requested event subscriptions were returned."
          schema:
            $ref: "#/definitions/DataResponseEventSubscriptionResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /runtime/event-subscriptions/{eventSubscriptionId}:
    get:
      tags:
      - "Event subscriptions"
      summary: "Get a single event subscription"
      description: ""
      operationId: "getEventSubscription"
      produces:
      - "application/json"
      parameters:
      - name: "eventSubscriptionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the event subscription exists and is returned."
          schema:
            $ref: "#/definitions/EventSubscriptionResponse"
        404:
          description: "Indicates the requested event subscription does not exist."
      security:
      - basicAuth: []
  /runtime/executions:
    get:
      tags:
      - "Executions"
      summary: "List of executions"
      description: ""
      operationId: "listExecutions"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return models with the given version."
        required: false
        type: "string"
      - name: "activityId"
        in: "query"
        description: "Only return executions with the given activity id."
        required: false
        type: "string"
      - name: "processDefinitionKey"
        in: "query"
        description: "Only return process instances with the given process definition\
          \ key."
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "Only return process instances with the given process definition\
          \ id."
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "Only return executions which are part of the process instance\
          \ with the given id."
        required: false
        type: "string"
      - name: "messageEventSubscriptionName"
        in: "query"
        description: "Only return executions which are subscribed to a message with\
          \ the given name."
        required: false
        type: "string"
      - name: "signalEventSubscriptionName"
        in: "query"
        description: "Only return executions which are subscribed to a signal with\
          \ the given name."
        required: false
        type: "string"
      - name: "parentId"
        in: "query"
        description: "Only return executions which are a direct child of the given\
          \ execution."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return process instances with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return process instances with a tenantId like the given\
          \ value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns process instances without a tenantId set.\
          \ If false, the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "processInstanceId"
        - "processDefinitionId"
        - "processDefinitionKey"
        - "tenantId"
      responses:
        200:
          description: "Indicates request was successful and the executions are returned"
          schema:
            $ref: "#/definitions/DataResponseExecutionResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format . The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
    put:
      tags:
      - "Executions"
      summary: "Signal event received"
      description: ""
      operationId: "executeExecutionAction"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ExecutionActionRequest"
      responses:
        204:
          description: "Indicates request was successful and the executions are returned"
        404:
          description: "Indicates a parameter was passed in the wrong format . The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /runtime/executions/{executionId}:
    get:
      tags:
      - "Executions"
      summary: "Get an execution"
      description: ""
      operationId: "getExecution"
      produces:
      - "application/json"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the execution was found and returned."
          schema:
            $ref: "#/definitions/ExecutionResponse"
        404:
          description: "Indicates the execution was not found."
      security:
      - basicAuth: []
    put:
      tags:
      - "Executions"
      summary: "Execute an action on an execution"
      description: ""
      operationId: "performExecutionAction"
      produces:
      - "application/json"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ExecutionActionRequest"
      responses:
        200:
          description: "Indicates the execution was found and the action is performed."
          schema:
            $ref: "#/definitions/ExecutionResponse"
        204:
          description: "Indicates the execution was found, the action was performed\
            \ and the action caused the execution to end."
        400:
          description: "Indicates an illegal action was requested, required parameters\
            \ are missing in the request body or illegal variables are passed in.\
            \ Status description contains additional information about the error."
        404:
          description: "Indicates the execution was not found."
      security:
      - basicAuth: []
  /runtime/executions/{executionId}/activities:
    get:
      tags:
      - "Executions"
      summary: "List active activities in an execution"
      description: "Returns all activities which are active in the execution and in\
        \ all child-executions (and their children, recursively), if any."
      operationId: "listExecutionActiveActivities"
      produces:
      - "application/json"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the execution was found and activities are returned."
          schema:
            type: "array"
            items:
              type: "string"
        404:
          description: "Indicates the execution was not found."
      security:
      - basicAuth: []
  /runtime/executions/{executionId}/change-state:
    post:
      tags:
      - "Executions"
      summary: "Change the state of an execution"
      description: ""
      operationId: "changeExecutionActivityState"
      produces:
      - "application/json"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ExecutionChangeActivityStateRequest"
      responses:
        200:
          description: "Indicates the execution was found and the action is performed."
        404:
          description: "Indicates the execution was not found."
      security:
      - basicAuth: []
  /runtime/executions/{executionId}/variables:
    get:
      tags:
      - "Executions"
      summary: "List variables for an execution"
      description: ""
      operationId: "listExecutionVariables"
      produces:
      - "application/json"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the execution was found and variables are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestVariable"
        404:
          description: "Indicates the requested execution was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Executions"
      summary: "Create variables on an execution"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (array\
        \ of RestVariable) or by passing a multipart/form-data Object.\nAny number\
        \ of variables can be passed into the request body array.\nNB: Swagger V2\
        \ specification does not support this use case that is why this endpoint might\
        \ be buggy/incomplete if used with other tools."
      operationId: "createExecutionVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Update a task variable"
        required: false
        schema:
          $ref: "#/definitions/ExecutionVariableCollectionResource"
      - name: "name"
        in: "formData"
        description: "Required name of the variable"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        description: "Type of variable that is updated. If omitted, reverts to raw\
          \ JSON-value type (string, boolean, integer or double)"
        required: false
        type: "string"
        x-example: "integer"
      - name: "scope"
        in: "formData"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable.."
        required: false
        type: "string"
        x-example: "local"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
        201:
          description: "Indicates the execution was found and variable is created/updated."
        400:
          description: "Indicates the request body is incomplete or contains illegal\
            \ values. The status description contains additional information about\
            \ the error."
        404:
          description: "Indicates the requested execution was not found."
        409:
          description: "Indicates the execution was found but already contains a variable\
            \ with the given name. Use the update-method instead."
      security:
      - basicAuth: []
    put:
      tags:
      - "Executions"
      summary: "Update variables on an execution"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (array\
        \ of RestVariable) or by passing a multipart/form-data Object.\nAny number\
        \ of variables can be passed into the request body array.\nNB: Swagger V2\
        \ specification does not support this use case that is why this endpoint might\
        \ be buggy/incomplete if used with other tools."
      operationId: "createOrUpdateExecutionVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Update a task variable"
        required: false
        schema:
          $ref: "#/definitions/ExecutionVariableCollectionResource"
      - name: "name"
        in: "formData"
        description: "Required name of the variable"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        description: "Type of variable that is updated. If omitted, reverts to raw\
          \ JSON-value type (string, boolean, integer or double)"
        required: false
        type: "string"
        x-example: "integer"
      - name: "scope"
        in: "formData"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable.."
        required: false
        type: "string"
        x-example: "local"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
        201:
          description: "Indicates the execution was found and variable is created/updated."
        400:
          description: "Indicates the request body is incomplete or contains illegal\
            \ values. The status description contains additional information about\
            \ the error."
        404:
          description: "Indicates the requested execution was not found."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Executions"
      summary: "Delete all variables for an execution"
      description: ""
      operationId: "deleteLocalVariables"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the execution was found and variables have been\
            \ deleted."
        404:
          description: "Indicates the requested execution was not found."
      security:
      - basicAuth: []
  /runtime/executions/{executionId}/variables/{variableName}:
    get:
      tags:
      - "Executions"
      summary: "Get a variable for an execution"
      description: ""
      operationId: "getExecutionVariable"
      produces:
      - "application/json"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates both the execution and variable were found and variable\
            \ is returned."
          schema:
            $ref: "#/definitions/RestVariable"
        400:
          description: "Indicates the request body is incomplete or contains illegal\
            \ values. The status description contains additional information about\
            \ the error."
        404:
          description: "Indicates the requested execution was not found or the execution\
            \ does not have a variable with the given name in the requested scope\
            \ (in case scope-query parameter was omitted, variable does not exist\
            \ in local and global scope). Status description contains additional information\
            \ about the error."
      security:
      - basicAuth: []
    put:
      tags:
      - "Executions"
      summary: "Update a variable on an execution"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable)\
        \ or by passing a multipart/form-data Object.\nNB: Swagger V2 specification\
        \ does not support this use case that is why this endpoint might be buggy/incomplete\
        \ if used with other tools."
      operationId: "updateExecutionVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Update a variable on an execution"
        required: false
        schema:
          $ref: "#/definitions/ExecutionVariableResource"
      - name: "file"
        in: "formData"
        required: false
        type: "file"
      - name: "name"
        in: "formData"
        required: false
        type: "string"
        x-example: "intProcVar"
      - name: "type"
        in: "formData"
        required: false
        type: "string"
        x-example: "integer"
      - name: "scope"
        in: "formData"
        required: false
        type: "string"
        x-example: "global"
      responses:
        200:
          description: "Indicates both the process instance and variable were found\
            \ and variable is updated."
          schema:
            $ref: "#/definitions/RestVariable"
        404:
          description: "Indicates the requested process instance was not found or\
            \ the process instance does not have a variable with the given name. Status\
            \ description contains additional information about the error."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Executions"
      summary: "Delete a variable for an execution"
      description: ""
      operationId: "deletedExecutionVariable"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        204:
          description: "Indicates both the execution and variable were found and variable\
            \ has been deleted."
        404:
          description: "Indicates the requested execution was not found or the execution\
            \ does not have a variable with the given name in the requested scope.\
            \ Status description contains additional information about the error."
      security:
      - basicAuth: []
  /runtime/executions/{executionId}/variables/{variableName}/data:
    get:
      tags:
      - "Executions"
      summary: "Get the binary data for an execution"
      description: ""
      operationId: "getExecutionVariableData"
      parameters:
      - name: "executionId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the execution was found and the requested variables\
            \ are returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested execution was not found or the task\
            \ does not have a variable with the given name (in the given scope). Status\
            \ message provides additional information."
      security:
      - basicAuth: []
  /runtime/process-instances:
    get:
      tags:
      - "Process Instances"
      summary: "List process instances"
      description: ""
      operationId: "listProcessInstances"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return models with the given version."
        required: false
        type: "string"
      - name: "name"
        in: "query"
        description: "Only return models with the given name."
        required: false
        type: "string"
      - name: "nameLike"
        in: "query"
        description: "Only return models like the given name."
        required: false
        type: "string"
      - name: "nameLikeIgnoreCase"
        in: "query"
        description: "Only return models like the given name ignoring case."
        required: false
        type: "string"
      - name: "processDefinitionKey"
        in: "query"
        description: "Only return process instances with the given process definition\
          \ key."
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "Only return process instances with the given process definition\
          \ id."
        required: false
        type: "string"
      - name: "processDefinitionCategory"
        in: "query"
        description: "Only return process instances with the given process definition\
          \ category."
        required: false
        type: "string"
      - name: "processDefinitionVersion"
        in: "query"
        description: "Only return process instances with the given process definition\
          \ version."
        required: false
        type: "integer"
      - name: "processDefinitionEngineVersion"
        in: "query"
        description: "Only return process instances with the given process definition\
          \ engine version."
        required: false
        type: "string"
      - name: "businessKey"
        in: "query"
        description: "Only return process instances with the given businessKey."
        required: false
        type: "string"
      - name: "businessKeyLike"
        in: "query"
        description: "Only return process instances with the businessKey like the\
          \ given key."
        required: false
        type: "string"
      - name: "startedBy"
        in: "query"
        description: "Only return process instances started by the given user."
        required: false
        type: "string"
      - name: "startedBefore"
        in: "query"
        description: "Only return process instances started before the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "startedAfter"
        in: "query"
        description: "Only return process instances started after the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "involvedUser"
        in: "query"
        description: "Only return process instances in which the given user is involved."
        required: false
        type: "string"
      - name: "suspended"
        in: "query"
        description: "If true, only return process instance which are suspended. If\
          \ false, only return process instances which are not suspended (active)."
        required: false
        type: "boolean"
      - name: "superProcessInstanceId"
        in: "query"
        description: "Only return process instances which have the given super process-instance\
          \ id (for processes that have a call-activities)."
        required: false
        type: "string"
      - name: "subProcessInstanceId"
        in: "query"
        description: "Only return process instances which have the given sub process-instance\
          \ id (for processes started as a call-activity)."
        required: false
        type: "string"
      - name: "excludeSubprocesses"
        in: "query"
        description: "Return only process instances which are not sub processes."
        required: false
        type: "boolean"
      - name: "includeProcessVariables"
        in: "query"
        description: "Indication to include process variables in the result."
        required: false
        type: "boolean"
      - name: "callbackId"
        in: "query"
        description: "Only return process instances with the given callbackId."
        required: false
        type: "string"
      - name: "callbackType"
        in: "query"
        description: "Only return process instances with the given callbackType."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return process instances with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return process instances with a tenantId like the given\
          \ value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns process instances without a tenantId set.\
          \ If false, the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "processDefinitionId"
        - "tenantId"
        - "processDefinitionKey"
      responses:
        200:
          description: "Indicates request was successful and the process-instances\
            \ are returned"
          schema:
            $ref: "#/definitions/DataResponseProcessInstanceResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format . The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
    post:
      tags:
      - "Process Instances"
      summary: "Start a process instance"
      description: "Note that also a *transientVariables* property is accepted as\
        \ part of this json, that follows the same structure as the *variables* property.\n\
        \nOnly one of *processDefinitionId*, *processDefinitionKey* or *message* can\
        \ be used in the request body. \n\nParameters *businessKey*, *variables* and\
        \ *tenantId* are optional.\n\n If tenantId is omitted, the default tenant\
        \ will be used. More information about the variable format can be found in\
        \ the REST variables section.\n\n Note that the variable-scope that is supplied\
        \ is ignored, process-variables are always local.\n\n"
      operationId: "createProcessInstance"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ProcessInstanceCreateRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/ProcessInstanceResponse"
        201:
          description: "Indicates the process instance was created."
        400:
          description: "Indicates either the process-definition was not found (based\
            \ on id or key), no process is started by sending the given message or\
            \ an invalid variable has been passed. Status description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}:
    get:
      tags:
      - "Process Instances"
      summary: "Get a process instance"
      description: ""
      operationId: "getProcessInstance"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and returned."
          schema:
            $ref: "#/definitions/ProcessInstanceResponse"
        404:
          description: "Indicates the requested process instance was not found."
      security:
      - basicAuth: []
    put:
      tags:
      - "Process Instances"
      summary: "Activate or suspend a process instance"
      description: ""
      operationId: "performProcessInstanceAction"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ProcessInstanceActionRequest"
      responses:
        200:
          description: "Indicates the process instance was found and action was executed."
          schema:
            $ref: "#/definitions/ProcessInstanceResponse"
        400:
          description: "\t\nIndicates an invalid action was supplied."
        404:
          description: "Indicates the requested process instance was not found."
        409:
          description: "Indicates the requested process instance action cannot be\
            \ executed since the process-instance is already activated/suspended."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Process Instances"
      summary: "Delete a process instance"
      description: ""
      operationId: "deleteProcessInstance"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "deleteReason"
        in: "query"
        required: false
        type: "string"
      responses:
        204:
          description: "Indicates the process instance was found and deleted. Response\
            \ body is left empty intentionally."
        404:
          description: "Indicates the requested process instance was not found."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/change-state:
    post:
      tags:
      - "Process Instances"
      summary: "Change the state a process instance"
      description: ""
      operationId: "changeActivityState"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/ExecutionChangeActivityStateRequest"
      responses:
        200:
          description: "Indicates the process instance was found and change state\
            \ activity was executed."
        404:
          description: "Indicates the requested process instance was not found."
        409:
          description: "Indicates the requested process instance action cannot be\
            \ executed since the process-instance is already activated/suspended."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/diagram:
    get:
      tags:
      - "Process Instances"
      summary: "Get diagram for a process instance"
      description: ""
      operationId: "getProcessInstanceDiagram"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and the diagram was\
            \ returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        400:
          description: "Indicates the requested process instance was not found but\
            \ the process does not contain any graphical information (BPMN:DI) and\
            \ no diagram can be created."
        404:
          description: "Indicates the requested process instance was not found."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/evaluate-conditions:
    post:
      tags:
      - "Process Instances"
      summary: "Evaluate the conditions of a process instance"
      description: ""
      operationId: "evaluateConditions"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and the evaluation\
            \ of the conditions was executed."
        404:
          description: "Indicates the requested process instance was not found."
        409:
          description: "Indicates the requested process instance action cannot be\
            \ executed since the process-instance is already activated/suspended."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/identitylinks:
    get:
      tags:
      - "Process Instance Identity Links"
      summary: "Get involved people for process instance"
      description: "Note that the groupId in Response Body will always be null, as\
        \ it’s only possible to involve users with a process-instance."
      operationId: "listProcessInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and links are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested process instance was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Process Instance Identity Links"
      summary: "Add an involved user to a process instance"
      description: "Note that the groupId in Response Body will always be null, as\
        \ it’s only possible to involve users with a process-instance."
      operationId: "createProcessInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestIdentityLink"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/RestIdentityLink"
        201:
          description: "Indicates the process instance was found and the link is created."
        400:
          description: "Indicates the requested body did not contain a userId or a\
            \ type."
        404:
          description: "Indicates the requested process instance was not found."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/identitylinks/users/{identityId}/{type}:
    get:
      tags:
      - "Process Instance Identity Links"
      summary: "Get a specific involved people from process instance"
      description: ""
      operationId: "getProcessInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      - name: "type"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and the specified\
            \ link is retrieved."
          schema:
            $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested process instance was not found or\
            \ the link to delete does not exist. The response status contains additional\
            \ information about the error."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Process Instance Identity Links"
      summary: "Remove an involved user to from process instance"
      description: ""
      operationId: "deleteProcessInstanceIdentityLinks"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      - name: "type"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the process instance was found and the link has\
            \ been deleted. Response body is left empty intentionally."
        404:
          description: "Indicates the requested process instance was not found or\
            \ the link to delete does not exist. The response status contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/inject:
    post:
      tags:
      - "Process Instances"
      summary: "Inject activity in a process instance"
      description: ""
      operationId: "injectActivityInProcessInstance"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/InjectActivityRequest"
      responses:
        200:
          description: "Indicates the process instance was updated and the activity\
            \ injection was executed."
        404:
          description: "Indicates the requested process instance was not found."
        409:
          description: "Indicates the requested process instance action cannot be\
            \ executed since the process-instance is already activated/suspended."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/migrate:
    post:
      tags:
      - "Process Instances"
      summary: "Migrate process instance"
      description: ""
      operationId: "migrateProcessInstance"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and migration was\
            \ executed."
        404:
          description: "Indicates the requested process instance was not found."
        409:
          description: "Indicates the requested process instance action cannot be\
            \ executed since the process-instance is already activated/suspended."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/variables:
    get:
      tags:
      - "Process Instance Variables"
      summary: "List variables for a process instance"
      description: "In case the variable is a binary variable or serializable, the\
        \ valueUrl points to an URL to fetch the raw value. If it’s a plain variable,\
        \ the value is present in the response. Note that only local scoped variables\
        \ are returned, as there is no global scope for process-instance variables."
      operationId: "listProcessInstanceVariables"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and variables are\
            \ returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestVariable"
        400:
          description: "Indicates the requested process instance was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Process Instance Variables"
      summary: "Create variables or new binary variable on a process instance"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable\
        \ or an array of RestVariable) or by passing a multipart/form-data Object.\n\
        Nonexistent variables are created on the process-instance and existing ones\
        \ are overridden without any error.\nAny number of variables can be passed\
        \ into the request body array.\nNote that scope is ignored, only local variables\
        \ can be set in a process instance.\nNB: Swagger V2 specification does not\
        \ support this use case that is why this endpoint might be buggy/incomplete\
        \ if used with other tools."
      operationId: "createProcessInstanceVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Create a variable on a process instance"
        required: false
        schema:
          $ref: "#/definitions/ProcessInstanceVariableCollectionResource"
      - name: "file"
        in: "formData"
        required: false
        type: "file"
      - name: "name"
        in: "formData"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        required: false
        type: "string"
        x-example: "integer"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
        201:
          description: "Indicates the process instance was found and variable is created."
        400:
          description: "Indicates the request body is incomplete or contains illegal\
            \ values. The status description contains additional information about\
            \ the error."
        404:
          description: "Indicates the requested process instance was not found."
        409:
          description: "Indicates the process instance was found but already contains\
            \ a variable with the given name (only thrown when POST method is used).\
            \ Use the update-method instead."
      security:
      - basicAuth: []
    put:
      tags:
      - "Process Instance Variables"
      summary: "Update a multiple/single (non)binary variable on a process instance"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable\
        \ or an array of RestVariable) or by passing a multipart/form-data Object.\n\
        Nonexistent variables are created on the process-instance and existing ones\
        \ are overridden without any error.\nAny number of variables can be passed\
        \ into the request body array.\nNote that scope is ignored, only local variables\
        \ can be set in a process instance.\nNB: Swagger V2 specification does not\
        \ support this use case that is why this endpoint might be buggy/incomplete\
        \ if used with other tools."
      operationId: "createOrUpdateProcessVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Create a variable on a process instance"
        required: false
        schema:
          $ref: "#/definitions/ProcessInstanceVariableCollectionResource"
      - name: "file"
        in: "formData"
        required: false
        type: "file"
      - name: "name"
        in: "formData"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        required: false
        type: "string"
        x-example: "integer"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
        201:
          description: "Indicates the process instance was found and variable is created."
        400:
          description: "Indicates the request body is incomplete or contains illegal\
            \ values. The status description contains additional information about\
            \ the error."
        404:
          description: "Indicates the requested process instance was not found."
        415:
          description: "Indicates the serializable data contains an object for which\
            \ no class is present in the JVM running the Flowable engine and therefore\
            \ cannot be deserialized."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Process Instance Variables"
      summary: "Delete all variables"
      description: ""
      operationId: "deleteLocalProcessVariable"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates variables were found and have been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested process instance was not found."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/variables/{variableName}:
    get:
      tags:
      - "Process Instance Variables"
      summary: "Get a variable for a process instance"
      description: ""
      operationId: "getProcessInstanceVariable"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates both the process instance and variable were found\
            \ and variable is returned."
          schema:
            $ref: "#/definitions/RestVariable"
        404:
          description: "Indicates the requested process instance was not found or\
            \ the process instance does not have a variable with the given name. Status\
            \ description contains additional information about the error."
      security:
      - basicAuth: []
    put:
      tags:
      - "Process Instance Variables"
      summary: "Update a single variable on a process instance"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable)\
        \ or by passing a multipart/form-data Object.\nNonexistent variables are created\
        \ on the process-instance and existing ones are overridden without any error.\n\
        Note that scope is ignored, only local variables can be set in a process instance.\n\
        NB: Swagger V2 specification does not support this use case that is why this\
        \ endpoint might be buggy/incomplete if used with other tools."
      operationId: "updateProcessInstanceVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Create a variable on a process instance"
        required: false
        schema:
          $ref: "#/definitions/ProcessInstanceVariableResource"
      - name: "file"
        in: "formData"
        required: false
        type: "file"
      - name: "name"
        in: "formData"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        required: false
        type: "string"
        x-example: "integer"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/RestVariable"
        201:
          description: "Indicates both the process instance and variable were found\
            \ and variable is updated."
        404:
          description: "Indicates the requested process instance was not found or\
            \ the process instance does not have a variable with the given name. Status\
            \ description contains additional information about the error."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Process Instance Variables"
      summary: "Delete a variable"
      description: ""
      operationId: "deleteProcessInstanceVariable"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        204:
          description: "Indicates the variable was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested variable was not found."
      security:
      - basicAuth: []
  /runtime/process-instances/{processInstanceId}/variables/{variableName}/data:
    get:
      tags:
      - "Process Instance Variables"
      summary: "Get the binary data for a variable"
      description: ""
      operationId: "getProcessInstanceVariableData"
      parameters:
      - name: "processInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the process instance was found and the requested\
            \ variables are returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name (in the given scope). Status\
            \ message provides additional information."
      security:
      - basicAuth: []
  /runtime/signals:
    post:
      tags:
      - "Runtime"
      summary: "Signal event received"
      description: ""
      operationId: "signalEventReceived"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/SignalEventReceivedRequest"
      responses:
        202:
          description: "Indicated signal processing is queued as a job, ready to be\
            \ executed."
        204:
          description: "Indicated signal has been processed and no errors occurred."
        400:
          description: "Signal not processed. The signal name is missing or variables\
            \ are used together with async, which is not allowed. Response body contains\
            \ additional information about the error."
      security:
      - basicAuth: []
  /runtime/tasks:
    get:
      tags:
      - "Tasks"
      summary: "List of tasks"
      description: ""
      operationId: "listTasks"
      produces:
      - "application/json"
      parameters:
      - name: "name"
        in: "query"
        description: "Only return models with the given version."
        required: false
        type: "string"
      - name: "nameLike"
        in: "query"
        description: "Only return tasks with a name like the given name."
        required: false
        type: "string"
      - name: "description"
        in: "query"
        description: "Only return tasks with the given description."
        required: false
        type: "string"
      - name: "priority"
        in: "query"
        description: "Only return tasks with the given priority."
        required: false
        type: "string"
      - name: "minimumPriority"
        in: "query"
        description: "Only return tasks with a priority greater than the given value."
        required: false
        type: "string"
      - name: "maximumPriority"
        in: "query"
        description: "Only return tasks with a priority lower than the given value."
        required: false
        type: "string"
      - name: "assignee"
        in: "query"
        description: "Only return tasks assigned to the given user."
        required: false
        type: "string"
      - name: "assigneeLike"
        in: "query"
        description: "Only return tasks assigned with an assignee like the given value."
        required: false
        type: "string"
      - name: "owner"
        in: "query"
        description: "Only return tasks owned by the given user."
        required: false
        type: "string"
      - name: "ownerLike"
        in: "query"
        description: "Only return tasks assigned with an owner like the given value."
        required: false
        type: "string"
      - name: "unassigned"
        in: "query"
        description: "Only return tasks that are not assigned to anyone. If false\
          \ is passed, the value is ignored."
        required: false
        type: "string"
      - name: "delegationState"
        in: "query"
        description: "Only return tasks that have the given delegation state. Possible\
          \ values are pending and resolved."
        required: false
        type: "string"
      - name: "candidateUser"
        in: "query"
        description: "Only return tasks that can be claimed by the given user. This\
          \ includes both tasks where the user is an explicit candidate for and task\
          \ that are claimable by a group that the user is a member of."
        required: false
        type: "string"
      - name: "candidateGroup"
        in: "query"
        description: "Only return tasks that can be claimed by a user in the given\
          \ group."
        required: false
        type: "string"
      - name: "candidateGroups"
        in: "query"
        description: "Only return tasks that can be claimed by a user in the given\
          \ groups. Values split by comma."
        required: false
        type: "string"
      - name: "involvedUser"
        in: "query"
        description: "Only return tasks in which the given user is involved."
        required: false
        type: "string"
      - name: "taskDefinitionKey"
        in: "query"
        description: "Only return tasks with the given task definition id."
        required: false
        type: "string"
      - name: "taskDefinitionKeyLike"
        in: "query"
        description: "Only return tasks with a given task definition id like the given\
          \ value."
        required: false
        type: "string"
      - name: "taskDefinitionKeys"
        in: "query"
        description: "Only return tasks with the given task definition ids."
        required: false
        type: "string"
      - name: "processInstanceId"
        in: "query"
        description: "Only return tasks which are part of the process instance with\
          \ the given id."
        required: false
        type: "string"
      - name: "processInstanceIdWithChildren"
        in: "query"
        description: "Only return tasks which are part of the process instance and\
          \ its children with the given id."
        required: false
        type: "string"
      - name: "processInstanceBusinessKey"
        in: "query"
        description: "Only return tasks which are part of the process instance with\
          \ the given business key."
        required: false
        type: "string"
      - name: "processInstanceBusinessKeyLike"
        in: "query"
        description: "Only return tasks which are part of the process instance which\
          \ has a business key like the given value."
        required: false
        type: "string"
      - name: "processDefinitionId"
        in: "query"
        description: "Only return tasks which are part of a process instance which\
          \ has a process definition with the given id."
        required: false
        type: "string"
      - name: "processDefinitionKey"
        in: "query"
        description: "Only return tasks which are part of a process instance which\
          \ has a process definition with the given key."
        required: false
        type: "string"
      - name: "processDefinitionKeyLike"
        in: "query"
        description: "Only return tasks which are part of a process instance which\
          \ has a process definition with a key like the given value."
        required: false
        type: "string"
      - name: "processDefinitionName"
        in: "query"
        description: "Only return tasks which are part of a process instance which\
          \ has a process definition with the given name."
        required: false
        type: "string"
      - name: "processDefinitionNameLike"
        in: "query"
        description: "Only return tasks which are part of a process instance which\
          \ has a process definition with a name like the given value."
        required: false
        type: "string"
      - name: "executionId"
        in: "query"
        description: "Only return tasks which are part of the execution with the given\
          \ id."
        required: false
        type: "string"
      - name: "createdOn"
        in: "query"
        description: "Only return tasks which are created on the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "createdBefore"
        in: "query"
        description: "Only return tasks which are created before the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "createdAfter"
        in: "query"
        description: "Only return tasks which are created after the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueOn"
        in: "query"
        description: "Only return tasks which are due on the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueBefore"
        in: "query"
        description: "Only return tasks which are due before the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return tasks which are due after the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "withoutDueDate"
        in: "query"
        description: "Only return tasks which do not have a due date. The property\
          \ is ignored if the value is false."
        required: false
        type: "boolean"
      - name: "excludeSubTasks"
        in: "query"
        description: "Only return tasks that are not a subtask of another task."
        required: false
        type: "boolean"
      - name: "active"
        in: "query"
        description: "If true, only return tasks that are not suspended (either part\
          \ of a process that is not suspended or not part of a process at all). If\
          \ false, only tasks that are part of suspended process instances are returned."
        required: false
        type: "boolean"
      - name: "includeTaskLocalVariables"
        in: "query"
        description: "Indication to include task local variables in the result."
        required: false
        type: "boolean"
      - name: "includeProcessVariables"
        in: "query"
        description: "Indication to include process variables in the result."
        required: false
        type: "boolean"
      - name: "scopeDefinitionId"
        in: "query"
        description: "Only return tasks with the given scopeDefinitionId."
        required: false
        type: "string"
      - name: "scopeId"
        in: "query"
        description: "Only return tasks with the given scopeId."
        required: false
        type: "string"
      - name: "scopeType"
        in: "query"
        description: "Only return tasks with the given scopeType."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return tasks with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return tasks with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns tasks without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "candidateOrAssigned"
        in: "query"
        description: "Select tasks that has been claimed or assigned to user or waiting\
          \ to claim by user (candidate user or groups)."
        required: false
        type: "string"
      - name: "category"
        in: "query"
        description: "Select tasks with the given category. Note that this is the\
          \ task category, not the category of the process definition (namespace within\
          \ the BPMN Xml).\n"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the tasks are returned"
          schema:
            $ref: "#/definitions/DataResponseTaskResponse"
        404:
          description: "Indicates a parameter was passed in the wrong format or that\
            \ delegationState has an invalid value (other than pending and resolved).\
            \ The status-message contains additional information."
      security:
      - basicAuth: []
    post:
      tags:
      - "Tasks"
      summary: "Create Task"
      description: ""
      operationId: "createTask"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/TaskRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/TaskResponse"
        201:
          description: "Indicates request was successful and the tasks are returned"
        400:
          description: "Indicates a parameter was passed in the wrong format or that\
            \ delegationState has an invalid value (other than pending and resolved).\
            \ The status-message contains additional information."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}:
    get:
      tags:
      - "Tasks"
      summary: "Get a task"
      description: ""
      operationId: "getTask"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and returned."
          schema:
            $ref: "#/definitions/TaskResponse"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Tasks"
      summary: "Tasks actions"
      description: ""
      operationId: "executeTaskAction"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/TaskActionRequest"
      responses:
        200:
          description: "Indicates the action was executed."
        400:
          description: "When the body contains an invalid value or when the assignee\
            \ is missing when the action requires it."
        404:
          description: "Indicates the requested task was not found."
        409:
          description: "Indicates the action cannot be performed due to a conflict.\
            \ Either the task was updates simultaneously or the task was claimed by\
            \ another user, in case of the claim action."
      security:
      - basicAuth: []
    put:
      tags:
      - "Tasks"
      summary: "Update a task"
      description: "All request values are optional. For example, you can only include\
        \ the assignee attribute in the request body JSON-object, only updating the\
        \ assignee of the task, leaving all other fields unaffected. When an attribute\
        \ is explicitly included and is set to null, the task-value will be updated\
        \ to null. Example: {\"dueDate\" : null} will clear the duedate of the task)."
      operationId: "updateTask"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/TaskRequest"
      responses:
        200:
          description: "Indicates the task was updated."
          schema:
            $ref: "#/definitions/TaskResponse"
        404:
          description: "Indicates the requested task was not found."
        409:
          description: "Indicates the requested task was updated simultaneously."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Tasks"
      summary: "Delete a task"
      description: ""
      operationId: "deleteTask"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "cascadeHistory"
        in: "query"
        description: "Whether or not to delete the HistoricTask instance when deleting\
          \ the task (if applicable). If not provided, this value defaults to false."
        required: false
        type: "string"
      - name: "deleteReason"
        in: "query"
        description: "Reason why the task is deleted. This value is ignored when cascadeHistory\
          \ is true."
        required: false
        type: "string"
      responses:
        204:
          description: "Indicates the task was found and has been deleted. Response-body\
            \ is intentionally empty."
        403:
          description: "Indicates the requested task cannot be deleted because it’\
            s part of a workflow."
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/attachments:
    get:
      tags:
      - "Task Attachments"
      summary: "List attachments on a task"
      description: ""
      operationId: "listTaskAttachments"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the attachments are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/AttachmentResponse"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Task Attachments"
      summary: "Create a new attachment on a task, containing a link to an external\
        \ resource or an attached file"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (AttachmentRequest)\
        \ to link an external resource or by passing a multipart/form-data Object\
        \ to attach a file.\nNB: Swagger V2 specification does not support this use\
        \ case that is why this endpoint might be buggy/incomplete if used with other\
        \ tools."
      operationId: "createAttachment"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "create an attachment containing a link to an external resource"
        required: false
        schema:
          $ref: "#/definitions/TaskAttachmentCollectionResource"
      - name: "file"
        in: "formData"
        description: "Attachment file"
        required: false
        type: "file"
      - name: "name"
        in: "formData"
        description: "Required name of the variable"
        required: false
        type: "string"
        x-example: "Simple attachment"
      - name: "description"
        in: "formData"
        description: "Description of the attachment, optional"
        required: false
        type: "string"
        x-example: "Simple attachment description"
      - name: "type"
        in: "formData"
        description: "Type of attachment, optional. Supports any arbitrary string\
          \ or a valid HTTP content-type."
        required: false
        type: "string"
        x-example: "simpleType"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/AttachmentResponse"
        201:
          description: "Indicates the attachment was created and the result is returned."
        400:
          description: "Indicates the attachment name is missing from the request."
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/attachments/{attachmentId}:
    get:
      tags:
      - "Task Attachments"
      summary: "Get an attachment on a task"
      description: ""
      operationId: "getAttachment"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "attachmentId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task and attachment were found and the attachment\
            \ is returned."
          schema:
            $ref: "#/definitions/AttachmentResponse"
        404:
          description: "Indicates the requested task was not found or the tasks does\
            \ not have a attachment with the given ID."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Task Attachments"
      summary: "Delete an attachment on a task"
      description: ""
      operationId: "deleteAttachment"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "attachmentId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the task and attachment were found and the attachment\
            \ is deleted. Response body is left empty intentionally."
        404:
          description: "Indicates the requested task was not found or the tasks does\
            \ not have a attachment with the given ID."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/attachments/{attachmentId}/content:
    get:
      tags:
      - "Task Attachments"
      summary: "Get the content for an attachment"
      description: "The response body contains the binary content. By default, the\
        \ content-type of the response is set to application/octet-stream unless the\
        \ attachment type contains a valid Content-type."
      operationId: "getAttachmentContent"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "attachmentId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task and attachment was found and the requested\
            \ content is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have an attachment with the given id or the attachment does not\
            \ have a binary stream available. Status message provides additional information."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/comments:
    get:
      tags:
      - "Task Comments"
      summary: "List comments on a task"
      description: ""
      operationId: "listTaskComments"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the comments are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/CommentResponse"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Task Comments"
      summary: "Create a new comment on a task"
      description: ""
      operationId: "createTaskComments"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/CommentRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/CommentResponse"
        201:
          description: "Indicates the comment was created and the result is returned."
        400:
          description: "Indicates the comment is missing from the request."
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/comments/{commentId}:
    get:
      tags:
      - "Task Comments"
      summary: " Get a comment on a task"
      description: ""
      operationId: "getTaskComment"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "commentId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task and comment were found and the comment\
            \ is returned."
          schema:
            $ref: "#/definitions/CommentResponse"
        404:
          description: "Indicates the requested task was not found or the tasks does\
            \ not have a comment with the given ID."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Task Comments"
      summary: "Delete a comment on a task"
      description: ""
      operationId: "deleteTaskComment"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "commentId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the task and comment were found and the comment\
            \ is deleted. Response body is left empty intentionally."
        404:
          description: "Indicates the requested task was not found or the tasks does\
            \ not have a comment with the given ID."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/events:
    get:
      tags:
      - "Tasks"
      summary: "List events for a task"
      description: ""
      operationId: "listTaskEvents"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the events are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/EventResponse"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/events/{eventId}:
    get:
      tags:
      - "Tasks"
      summary: "Get an event on a task"
      description: ""
      operationId: "getEvent"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "eventId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task and event were found and the event is returned."
          schema:
            $ref: "#/definitions/EventResponse"
        404:
          description: "Indicates the requested task was not found or the tasks does\
            \ not have an event with the given ID."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Tasks"
      summary: "Delete an event on a task"
      description: ""
      operationId: "deleteEvent"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "eventId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the task was found and the events are returned."
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have the requested event."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/form:
    get:
      tags:
      - "Tasks"
      summary: "Get a task form"
      description: ""
      operationId: "getTaskForm"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the task form is returned"
          schema:
            type: "string"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/identitylinks:
    get:
      tags:
      - "Task Identity Links"
      summary: "List identity links for a task"
      description: ""
      operationId: "listTasksInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested identity links\
            \ are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Task Identity Links"
      summary: "Create an identity link on a task"
      description: "It is possible to add either a user or a group."
      operationId: "createTaskInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestIdentityLink"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/RestIdentityLink"
        201:
          description: "Indicates the task was found and the identity link was created."
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have the requested identityLink. The status contains additional\
            \ information about this error."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/identitylinks/{family}:
    get:
      tags:
      - "Task Identity Links"
      summary: "List identity links for a task for either groups or users"
      description: "Returns only identity links targeting either users or groups.\
        \ Response body and status-codes are exactly the same as when getting the\
        \ full list of identity links for a task."
      operationId: "listIdentityLinksForFamily"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested identity links\
            \ are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/identitylinks/{family}/{identityId}/{type}:
    get:
      tags:
      - "Task Identity Links"
      summary: "Get a single identity link on a task"
      description: ""
      operationId: "getTaskInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      - name: "type"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task and identity link was found and returned."
          schema:
            $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have the requested identityLink. The status contains additional\
            \ information about this error."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Task Identity Links"
      summary: "Delete an identity link on a task"
      description: ""
      operationId: "deleteTaskInstanceIdentityLinks"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      - name: "type"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the task and identity link were found and the link\
            \ has been deleted. Response-body is intentionally empty."
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have the requested identityLink. The status contains additional\
            \ information about this error."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/subtasks:
    get:
      tags:
      - "Tasks"
      summary: "List of sub tasks for a task"
      description: ""
      operationId: "listTaskSubtasks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the  sub tasks are returned"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/TaskResponse"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/variables:
    get:
      tags:
      - "Task Variables"
      summary: "List variables for a task"
      description: ""
      operationId: "listTaskVariables"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested variables are\
            \ returned"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestVariable"
        404:
          description: "Indicates the requested task was not found.."
      security:
      - basicAuth: []
    post:
      tags:
      - "Tasks"
      - "Task Variables"
      summary: "Create new variables on a task"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable\
        \ or an Array of RestVariable) or by passing a multipart/form-data Object.\n\
        It is possible to create simple (non-binary) variable or list of variables\
        \ or new binary variable \nAny number of variables can be passed into the\
        \ request body array.\nNB: Swagger V2 specification does not support this\
        \ use case that is why this endpoint might be buggy/incomplete if used with\
        \ other tools."
      operationId: "createTaskVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Create a variable on a task"
        required: false
        schema:
          $ref: "#/definitions/TaskVariableCollectionResource"
      - name: "name"
        in: "formData"
        description: "Required name of the variable"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        description: "Type of variable that is created. If omitted, reverts to raw\
          \ JSON-value type (string, boolean, integer or double)"
        required: false
        type: "string"
        x-example: "integer"
      - name: "scope"
        in: "formData"
        description: "Scope of variable that is created. If omitted, local is assumed."
        required: false
        type: "string"
        x-example: "local"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
        201:
          description: "Indicates the variables were created and the result is returned."
        400:
          description: "Indicates the name of a variable to create was missing or\
            \ that an attempt is done to create a variable on a standalone task (without\
            \ a process associated) with scope global or an empty array of variables\
            \ was included in the request or request did not contain an array of variables.\
            \ Status message provides additional information."
        404:
          description: "Indicates the requested task was not found."
        409:
          description: "Indicates the task already has a variable with the given name.\
            \ Use the PUT method to update the task variable instead."
        415:
          description: "Indicates the serializable data contains an object for which\
            \ no class is present in the JVM running the Flowable engine and therefore\
            \ cannot be deserialized."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Tasks"
      summary: "Delete all local variables on a task"
      description: ""
      operationId: "deleteAllLocalTaskVariables"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates all local task variables have been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/variables/{variableName}:
    get:
      tags:
      - "Task Variables"
      summary: "Get a variable from a task"
      description: ""
      operationId: "getTaskInstanceVariable"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested variables are\
            \ returned."
          schema:
            $ref: "#/definitions/RestVariable"
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name (in the given scope). Status\
            \ message provides additional information."
      security:
      - basicAuth: []
    put:
      tags:
      - "Task Variables"
      summary: "Update an existing variable on a task"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable)\
        \ or by passing a multipart/form-data Object.\nIt is possible to update simple\
        \ (non-binary) variable or  binary variable \nAny number of variables can\
        \ be passed into the request body array.\nNB: Swagger V2 specification does\
        \ not support this use case that is why this endpoint might be buggy/incomplete\
        \ if used with other tools."
      operationId: "updateTaskInstanceVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Update a task variable"
        required: false
        schema:
          $ref: "#/definitions/TaskVariableResource"
      - name: "name"
        in: "formData"
        description: "Required name of the variable"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        description: "Type of variable that is updated. If omitted, reverts to raw\
          \ JSON-value type (string, boolean, integer or double)"
        required: false
        type: "string"
        x-example: "integer"
      - name: "scope"
        in: "formData"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable.."
        required: false
        type: "string"
        x-example: "local"
      responses:
        200:
          description: "Indicates the variables was updated and the result is returned."
          schema:
            $ref: "#/definitions/RestVariable"
        400:
          description: "Indicates the name of a variable to update was missing or\
            \ that an attempt is done to update a variable on a standalone task (without\
            \ a process associated) with scope global. Status message provides additional\
            \ information."
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name in the given scope. Status message\
            \ contains additional information about the error."
        415:
          description: "Indicates the serializable data contains an object for which\
            \ no class is present in the JVM running the Flowable engine and therefore\
            \ cannot be deserialized."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Task Variables"
      summary: "Delete a variable on a task"
      description: ""
      operationId: "deleteTaskInstanceVariable"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable."
        required: false
        type: "string"
      responses:
        204:
          description: "Indicates the task variable was found and has been deleted.\
            \ Response-body is intentionally empty."
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name. Status message contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /runtime/tasks/{taskId}/variables/{variableName}/data:
    get:
      tags:
      - "Task Variables"
      summary: "Get the binary data for a variable"
      description: "The response body contains the binary value of the variable. When\
        \ the variable is of type binary, the content-type of the response is set\
        \ to application/octet-stream, regardless of the content of the variable or\
        \ the request accept-type header. In case of serializable, application/x-java-serialized-object\
        \ is used as content-type."
      operationId: "getTaskVariableData"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested variables are\
            \ returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name (in the given scope). Status\
            \ message provides additional information."
      security:
      - basicAuth: []
securityDefinitions:
  basicAuth:
    type: "basic"
definitions:
  Artifact:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
  AttachmentResponse:
    type: "object"
    properties:
      id:
        type: "string"
      url:
        type: "string"
      name:
        type: "string"
      userId:
        type: "string"
      description:
        type: "string"
      type:
        type: "string"
        description: "Can be any arbitrary value. When a valid formatted media-type\
          \ (e.g. application/xml, text/plain) is included, the binary content HTTP\
          \ response content-type will be set the given value."
      taskUrl:
        type: "string"
      processInstanceUrl:
        type: "string"
      externalUrl:
        type: "string"
        description: "contentUrl:In case the attachment is a link to an external resource,\
          \ the externalUrl contains the URL to the external content. If the attachment\
          \ content is present in the Flowable engine, the contentUrl will contain\
          \ an URL where the binary content can be streamed from."
      contentUrl:
        type: "string"
      time:
        type: "string"
        format: "date-time"
  BaseElement:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
  BatchPartResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "8"
      url:
        type: "string"
        example: "http://localhost:8182/management/batch-parts/8"
      batchId:
        type: "string"
        example: "4"
      batchUrl:
        type: "string"
        example: "http://localhost:8182/management/batch/4"
      batchType:
        type: "string"
        example: "processMigration"
      searchKey:
        type: "string"
        example: "1:22:MP"
      searchKey2:
        type: "string"
        example: "1:24:MP"
      scopeId:
        type: "string"
        example: "1"
      subScopeId:
        type: "string"
        example: "2"
      scopeType:
        type: "string"
        example: "bpmn"
      createTime:
        type: "string"
        format: "date-time"
        example: "2020-06-03T22:05:05.474+0000"
      completeTime:
        type: "string"
        format: "date-time"
        example: "2020-06-03T22:05:05.474+0000"
      status:
        type: "string"
        example: "completed"
      tenantId:
        type: "string"
        example: "null"
  BatchResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "8"
      url:
        type: "string"
        example: "http://localhost:8182/management/batches/8"
      batchType:
        type: "string"
        example: "processMigration"
      searchKey:
        type: "string"
        example: "1:22:MP"
      searchKey2:
        type: "string"
        example: "1:24:MP"
      createTime:
        type: "string"
        format: "date-time"
        example: "2020-06-03T22:05:05.474+0000"
      completeTime:
        type: "string"
        format: "date-time"
        example: "2020-06-03T22:05:05.474+0000"
      status:
        type: "string"
        example: "completed"
      tenantId:
        type: "string"
        example: "null"
  BpmnModel:
    type: "object"
    properties:
      definitionsAttributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      processes:
        type: "array"
        items:
          $ref: "#/definitions/Process"
      locationMap:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/GraphicInfo"
      labelLocationMap:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/GraphicInfo"
      flowLocationMap:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/GraphicInfo"
      signals:
        type: "array"
        items:
          $ref: "#/definitions/Signal"
      pools:
        type: "array"
        items:
          $ref: "#/definitions/Pool"
      imports:
        type: "array"
        items:
          $ref: "#/definitions/Import"
      interfaces:
        type: "array"
        items:
          $ref: "#/definitions/Interface"
      globalArtifacts:
        type: "array"
        items:
          $ref: "#/definitions/Artifact"
      resources:
        type: "array"
        items:
          $ref: "#/definitions/Resource"
      targetNamespace:
        type: "string"
      sourceSystemId:
        type: "string"
      userTaskFormTypes:
        type: "array"
        items:
          type: "string"
      startEventFormTypes:
        type: "array"
        items:
          type: "string"
      errors:
        type: "object"
        additionalProperties:
          type: "string"
      messages:
        type: "array"
        items:
          $ref: "#/definitions/Message"
      mainProcess:
        $ref: "#/definitions/Process"
      messageFlows:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/MessageFlow"
      escalations:
        type: "array"
        items:
          $ref: "#/definitions/Escalation"
      itemDefinitions:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/ItemDefinition"
      dataStores:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/DataStore"
      namespaces:
        type: "object"
        additionalProperties:
          type: "string"
  CommentRequest:
    type: "object"
    properties:
      id:
        type: "string"
      url:
        type: "string"
      author:
        type: "string"
      message:
        type: "string"
      type:
        type: "string"
      saveProcessInstanceId:
        type: "boolean"
  CommentResponse:
    type: "object"
    properties:
      id:
        type: "string"
      author:
        type: "string"
      message:
        type: "string"
      time:
        type: "string"
        format: "date-time"
      taskId:
        type: "string"
      taskUrl:
        type: "string"
      processInstanceId:
        type: "string"
      processInstanceUrl:
        type: "string"
  DataResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          type: "object"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseBatchResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/BatchResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseDeploymentResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/DeploymentResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseEventSubscriptionResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/EventSubscriptionResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseExecutionResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/ExecutionResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseGroupResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/GroupResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricActivityInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricActivityInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricDetailResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricDetailResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricProcessInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricProcessInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricTaskInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricTaskInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricTaskLogEntryResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricTaskLogEntryResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricVariableInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricVariableInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseJobResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/JobResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseListMapStringObject:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          type: "array"
          items:
            type: "object"
            additionalProperties:
              type: "object"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseModelResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/ModelResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseProcessDefinitionResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/ProcessDefinitionResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseProcessInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/ProcessInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseTaskResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/TaskResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseUserResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/UserResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataSpec:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      itemSubjectRef:
        type: "string"
      collection:
        type: "boolean"
  DataStore:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      dataState:
        type: "string"
      itemSubjectRef:
        type: "string"
  DecisionTableResponse:
    type: "object"
    properties:
      id:
        type: "string"
      category:
        type: "string"
      name:
        type: "string"
      key:
        type: "string"
      description:
        type: "string"
      version:
        type: "integer"
        format: "int32"
      resourceName:
        type: "string"
      deploymentId:
        type: "string"
      tenantId:
        type: "string"
      url:
        type: "string"
  DeploymentResourceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "diagrams/my-process.bpmn20.xml"
      url:
        type: "string"
        example: "http://localhost:8081/flowable-rest/service/repository/deployments/10/resources/diagrams%2Fmy-process.bpmn20.xml"
        description: "For a single resource contains the actual URL to use for retrieving\
          \ the binary resource"
      contentUrl:
        type: "string"
        example: "http://localhost:8081/flowable-rest/service/repository/deployments/10/resourcedata/diagrams%2Fmy-process.bpmn20.xml"
      mediaType:
        type: "string"
        example: "text/xml"
        description: "Contains the media-type the resource has. This is resolved using\
          \ a (pluggable) MediaTypeResolver and contains, by default, a limited number\
          \ of mime-type mappings."
      type:
        type: "string"
        example: "processDefinition"
        description: "Type of resource"
        enum:
        - "resource"
        - "processDefinition"
        - "processImage"
  DeploymentResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "10"
      name:
        type: "string"
        example: "flowable-examples.bar"
      deploymentTime:
        type: "string"
        format: "date-time"
        example: "2010-10-13T14:54:26.750+02:00"
      category:
        type: "string"
        example: "examples"
      parentDeploymentId:
        type: "string"
        example: "12"
      url:
        type: "string"
        example: "http://localhost:8081/flowable-rest/service/repository/deployments/10"
      tenantId:
        type: "string"
  Escalation:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      escalationCode:
        type: "string"
  EventListener:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      events:
        type: "string"
      implementationType:
        type: "string"
      implementation:
        type: "string"
      entityType:
        type: "string"
      onTransaction:
        type: "string"
  EventResponse:
    type: "object"
    properties:
      url:
        type: "string"
      id:
        type: "string"
      action:
        type: "string"
      userId:
        type: "string"
      time:
        type: "string"
        format: "date-time"
      taskUrl:
        type: "string"
      processInstanceUrl:
        type: "string"
      message:
        type: "array"
        items:
          type: "string"
  EventSubscriptionResponse:
    type: "object"
    properties:
      id:
        type: "string"
      url:
        type: "string"
      eventType:
        type: "string"
      eventName:
        type: "string"
      activityId:
        type: "string"
      executionId:
        type: "string"
      executionUrl:
        type: "string"
      processInstanceId:
        type: "string"
      processInstanceUrl:
        type: "string"
      processDefinitionId:
        type: "string"
      processDefinitionUrl:
        type: "string"
      scopeId:
        type: "string"
      scopeType:
        type: "string"
      subScopeId:
        type: "string"
      scopeDefinitionId:
        type: "string"
      created:
        type: "string"
        format: "date-time"
      configuration:
        type: "string"
      tenantId:
        type: "string"
  ExecutionActionRequest:
    type: "object"
    required:
    - "action"
    properties:
      action:
        type: "string"
        example: "signalEventReceived"
        description: "Action to perform: Either signal, trigger, signalEventReceived\
          \ or messageEventReceived"
      signalName:
        type: "string"
        example: "My Signal"
        description: "Name of the signal"
      messageName:
        type: "string"
        example: "My Signal"
        description: "Message of the signal"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      transientVariables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
  ExecutionChangeActivityStateRequest:
    type: "object"
    properties:
      cancelActivityIds:
        type: "array"
        description: "activityIds to be canceled"
        items:
          type: "string"
      startActivityIds:
        type: "array"
        description: "activityIds to be started"
        items:
          type: "string"
  ExecutionQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      id:
        type: "string"
      activityId:
        type: "string"
      parentId:
        type: "string"
      processInstanceId:
        type: "string"
      processBusinessKey:
        type: "string"
      processDefinitionId:
        type: "string"
      processDefinitionKey:
        type: "string"
      signalEventSubscriptionName:
        type: "string"
      messageEventSubscriptionName:
        type: "string"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      processInstanceVariables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      tenantId:
        type: "string"
      tenantIdLike:
        type: "string"
      withoutTenantId:
        type: "boolean"
  ExecutionResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "5"
      url:
        type: "string"
        example: "http://localhost:8182/runtime/executions/5"
      parentId:
        type: "string"
        example: "null"
      parentUrl:
        type: "string"
        example: "null"
      superExecutionId:
        type: "string"
        example: "null"
      superExecutionUrl:
        type: "string"
        example: "null"
      processInstanceId:
        type: "string"
        example: "5"
      processInstanceUrl:
        type: "string"
        example: "http://localhost:8182/runtime/process-instances/5"
      suspended:
        type: "boolean"
      activityId:
        type: "string"
        example: "null"
      tenantId:
        type: "string"
        example: "null"
  ExecutionVariableCollectionResource:
    type: "object"
  ExecutionVariableResource:
    type: "object"
  ExtensionAttribute:
    type: "object"
    properties:
      name:
        type: "string"
      value:
        type: "string"
      namespacePrefix:
        type: "string"
      namespace:
        type: "string"
  ExtensionElement:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      namespacePrefix:
        type: "string"
      namespace:
        type: "string"
      elementText:
        type: "string"
      childElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
  FieldExtension:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      fieldName:
        type: "string"
      stringValue:
        type: "string"
      expression:
        type: "string"
  FlowElement:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      documentation:
        type: "string"
      executionListeners:
        type: "array"
        items:
          $ref: "#/definitions/FlowableListener"
  FlowableListener:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      event:
        type: "string"
      implementationType:
        type: "string"
      implementation:
        type: "string"
      fieldExtensions:
        type: "array"
        items:
          $ref: "#/definitions/FieldExtension"
      onTransaction:
        type: "string"
      customPropertiesResolverImplementationType:
        type: "string"
      customPropertiesResolverImplementation:
        type: "string"
  FormDataResponse:
    type: "object"
    properties:
      formKey:
        type: "string"
        example: "null"
      deploymentId:
        type: "string"
        example: "2"
      processDefinitionId:
        type: "string"
        example: "3"
      processDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/repository/process-definition/3"
      taskId:
        type: "string"
        example: "6"
      taskUrl:
        type: "string"
        example: "http://localhost:8182/runtime/task/6"
      formProperties:
        type: "array"
        items:
          $ref: "#/definitions/RestFormProperty"
  FormDefinitionResponse:
    type: "object"
    properties:
      id:
        type: "string"
      url:
        type: "string"
      category:
        type: "string"
      name:
        type: "string"
      key:
        type: "string"
      description:
        type: "string"
      version:
        type: "integer"
        format: "int32"
      resourceName:
        type: "string"
      deploymentId:
        type: "string"
      tenantId:
        type: "string"
  GraphicInfo:
    type: "object"
    properties:
      x:
        type: "number"
        format: "double"
      y:
        type: "number"
        format: "double"
      height:
        type: "number"
        format: "double"
      width:
        type: "number"
        format: "double"
      element:
        $ref: "#/definitions/BaseElement"
      expanded:
        type: "boolean"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
  GroupRequest:
    type: "object"
    properties:
      id:
        type: "string"
        example: "testgroup"
      url:
        type: "string"
        example: "http://localhost:8182/identity/groups/testgroup"
      name:
        type: "string"
        example: "Test group"
      type:
        type: "string"
        example: "Test type"
      nameChanged:
        type: "boolean"
      typeChanged:
        type: "boolean"
  GroupResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "testgroup"
      url:
        type: "string"
        example: "http://localhost:8182/identity/groups/testgroup"
      name:
        type: "string"
        example: "Test group"
      type:
        type: "string"
        example: "Test type"
  HistoricActivityInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      activityId:
        type: "string"
      activityInstanceId:
        type: "string"
      activityName:
        type: "string"
      activityType:
        type: "string"
      executionId:
        type: "string"
      finished:
        type: "boolean"
      taskAssignee:
        type: "string"
      processInstanceId:
        type: "string"
      processDefinitionId:
        type: "string"
      tenantId:
        type: "string"
      tenantIdLike:
        type: "string"
      withoutTenantId:
        type: "boolean"
  HistoricActivityInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "5"
      activityId:
        type: "string"
        example: "4"
      activityName:
        type: "string"
        example: "My user task"
      activityType:
        type: "string"
        example: "userTask"
      processDefinitionId:
        type: "string"
        example: "oneTaskProcess%3A1%3A4"
      processDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/repository/process-definitions/oneTaskProcess%3A1%3A4"
      processInstanceId:
        type: "string"
        example: "3"
      processInstanceUrl:
        type: "string"
        example: "http://localhost:8182/history/historic-process-instances/3"
      executionId:
        type: "string"
        example: "4"
      taskId:
        type: "string"
        example: "4"
      calledProcessInstanceId:
        type: "string"
        example: "null"
      assignee:
        type: "string"
        example: "fozzie"
      startTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      endTime:
        type: "string"
        format: "date-time"
        example: "2013-04-18T14:06:32.715+0000"
      durationInMillis:
        type: "integer"
        format: "int64"
        example: 86400056
      tenantId:
        type: "string"
        example: "null"
  HistoricDetailQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      id:
        type: "string"
      processInstanceId:
        type: "string"
      executionId:
        type: "string"
      activityInstanceId:
        type: "string"
      taskId:
        type: "string"
      selectOnlyFormProperties:
        type: "boolean"
      selectOnlyVariableUpdates:
        type: "boolean"
  HistoricDetailResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "26"
      processInstanceId:
        type: "string"
        example: "5"
      processInstanceUrl:
        type: "string"
        example: "http://localhost:8182/history/historic-process-instances/5"
      executionId:
        type: "string"
        example: "6"
      activityInstanceId:
        type: "string"
        example: "10"
      taskId:
        type: "string"
        example: "6"
      taskUrl:
        type: "string"
        example: "http://localhost:8182/history/historic-task-instances/6"
      time:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      detailType:
        type: "string"
        example: "variableUpdate"
      revision:
        type: "integer"
        format: "int32"
        example: 2
      variable:
        $ref: "#/definitions/RestVariable"
      propertyId:
        type: "string"
        example: "null"
      propertyValue:
        type: "string"
        example: "null"
  HistoricIdentityLinkResponse:
    type: "object"
    properties:
      type:
        type: "string"
        example: "participant"
      userId:
        type: "string"
        example: "kermit"
      groupId:
        type: "string"
        example: "sales"
      taskId:
        type: "string"
        example: "null"
      taskUrl:
        type: "string"
        example: "null"
      processInstanceId:
        type: "string"
        example: "5"
      processInstanceUrl:
        type: "string"
        example: "http://localhost:8182/history/historic-process-instances/5"
  HistoricProcessInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      processInstanceId:
        type: "string"
      processInstanceIds:
        type: "array"
        items:
          type: "string"
      processInstanceName:
        type: "string"
      processInstanceNameLike:
        type: "string"
      processInstanceNameLikeIgnoreCase:
        type: "string"
      processBusinessKey:
        type: "string"
      processBusinessKeyLike:
        type: "string"
      processDefinitionId:
        type: "string"
      processDefinitionKey:
        type: "string"
      processDefinitionKeyIn:
        type: "array"
        items:
          type: "string"
      processDefinitionKeyNotIn:
        type: "array"
        items:
          type: "string"
      processDefinitionName:
        type: "string"
      processDefinitionVersion:
        type: "integer"
        format: "int32"
      processDefinitionCategory:
        type: "string"
      deploymentId:
        type: "string"
      deploymentIdIn:
        type: "array"
        items:
          type: "string"
      superProcessInstanceId:
        type: "string"
      excludeSubprocesses:
        type: "boolean"
      finished:
        type: "boolean"
      involvedUser:
        type: "string"
      finishedAfter:
        type: "string"
        format: "date-time"
      finishedBefore:
        type: "string"
        format: "date-time"
      startedAfter:
        type: "string"
        format: "date-time"
      startedBefore:
        type: "string"
        format: "date-time"
      startedBy:
        type: "string"
      includeProcessVariables:
        type: "boolean"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      callbackId:
        type: "string"
      callbackType:
        type: "string"
      tenantId:
        type: "string"
      tenantIdLike:
        type: "string"
      withoutTenantId:
        type: "boolean"
  HistoricProcessInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "5"
      url:
        type: "string"
        example: "http://localhost:8182/history/historic-process-instances/5"
      name:
        type: "string"
        example: "myProcessInstanceName"
      businessKey:
        type: "string"
        example: "myKey"
      processDefinitionId:
        type: "string"
        example: "oneTaskProcess%3A1%3A4"
      processDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/repository/process-definitions/oneTaskProcess%3A1%3A4"
      processDefinitionName:
        type: "string"
        example: "A process definition name"
      processDefinitionDescription:
        type: "string"
        example: "A process definition description"
      startTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      endTime:
        type: "string"
        format: "date-time"
        example: "2013-04-18T14:06:32.715+0000"
      durationInMillis:
        type: "integer"
        format: "int64"
        example: 86400056
      startUserId:
        type: "string"
        example: "kermit"
      startActivityId:
        type: "string"
        example: "startEvent"
      endActivityId:
        type: "string"
        example: "endEvent"
      deleteReason:
        type: "string"
        example: "null"
      superProcessInstanceId:
        type: "string"
        example: "3"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      callbackId:
        type: "string"
        example: "3"
      callbackType:
        type: "string"
        example: "cmmn"
      referenceId:
        type: "string"
        example: "123"
      referenceType:
        type: "string"
        example: "event-to-bpmn-2.0-process"
      tenantId:
        type: "string"
        example: "someTenantId"
  HistoricTaskInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      taskId:
        type: "string"
      processInstanceId:
        type: "string"
      processInstanceIdWithChildren:
        type: "string"
      processBusinessKey:
        type: "string"
      processBusinessKeyLike:
        type: "string"
      processDefinitionId:
        type: "string"
      processDefinitionKey:
        type: "string"
      processDefinitionKeyLike:
        type: "string"
      processDefinitionName:
        type: "string"
      processDefinitionNameLike:
        type: "string"
      executionId:
        type: "string"
      taskName:
        type: "string"
      taskNameLike:
        type: "string"
      taskDescription:
        type: "string"
      taskDescriptionLike:
        type: "string"
      taskDefinitionKey:
        type: "string"
      taskDefinitionKeyLike:
        type: "string"
      taskDefinitionKeys:
        type: "array"
        items:
          type: "string"
      taskCategory:
        type: "string"
      taskDeleteReason:
        type: "string"
      taskDeleteReasonLike:
        type: "string"
      taskAssignee:
        type: "string"
      taskAssigneeLike:
        type: "string"
      taskOwner:
        type: "string"
      taskOwnerLike:
        type: "string"
      taskInvolvedUser:
        type: "string"
      taskPriority:
        type: "integer"
        format: "int32"
      taskMinPriority:
        type: "integer"
        format: "int32"
      taskMaxPriority:
        type: "integer"
        format: "int32"
      finished:
        type: "boolean"
      processFinished:
        type: "boolean"
      parentTaskId:
        type: "string"
      dueDate:
        type: "string"
        format: "date-time"
      dueDateAfter:
        type: "string"
        format: "date-time"
      dueDateBefore:
        type: "string"
        format: "date-time"
      withoutDueDate:
        type: "boolean"
      taskCreatedOn:
        type: "string"
        format: "date-time"
      taskCreatedBefore:
        type: "string"
        format: "date-time"
      taskCreatedAfter:
        type: "string"
        format: "date-time"
      taskCompletedOn:
        type: "string"
        format: "date-time"
      taskCompletedBefore:
        type: "string"
        format: "date-time"
      taskCompletedAfter:
        type: "string"
        format: "date-time"
      includeTaskLocalVariables:
        type: "boolean"
      includeProcessVariables:
        type: "boolean"
      taskVariables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      processVariables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      scopeDefinitionId:
        type: "string"
      scopeId:
        type: "string"
      scopeType:
        type: "string"
      tenantId:
        type: "string"
      tenantIdLike:
        type: "string"
      withoutTenantId:
        type: "boolean"
      withoutDeleteReason:
        type: "boolean"
      taskCandidateGroup:
        type: "string"
  HistoricTaskInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
      processDefinitionId:
        type: "string"
      processDefinitionUrl:
        type: "string"
      processInstanceId:
        type: "string"
      processInstanceUrl:
        type: "string"
      executionId:
        type: "string"
      name:
        type: "string"
      description:
        type: "string"
      deleteReason:
        type: "string"
      owner:
        type: "string"
      assignee:
        type: "string"
      startTime:
        type: "string"
        format: "date-time"
      endTime:
        type: "string"
        format: "date-time"
      durationInMillis:
        type: "integer"
        format: "int64"
      workTimeInMillis:
        type: "integer"
        format: "int64"
      claimTime:
        type: "string"
        format: "date-time"
      taskDefinitionKey:
        type: "string"
      formKey:
        type: "string"
      priority:
        type: "integer"
        format: "int32"
      dueDate:
        type: "string"
        format: "date-time"
      parentTaskId:
        type: "string"
      url:
        type: "string"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      scopeDefinitionId:
        type: "string"
      scopeId:
        type: "string"
      subScopeId:
        type: "string"
      scopeType:
        type: "string"
      propagatedStageInstanceId:
        type: "string"
      tenantId:
        type: "string"
      category:
        type: "string"
  HistoricTaskLogEntryResponse:
    type: "object"
    properties:
      logNumber:
        type: "integer"
        format: "int64"
      type:
        type: "string"
      taskId:
        type: "string"
      timeStamp:
        type: "string"
        format: "date-time"
      userId:
        type: "string"
      data:
        type: "string"
      executionId:
        type: "string"
      processInstanceId:
        type: "string"
      processDefinitionId:
        type: "string"
      scopeId:
        type: "string"
      scopeDefinitionId:
        type: "string"
      subScopeId:
        type: "string"
      scopeType:
        type: "string"
      tenantId:
        type: "string"
  HistoricVariableInstanceQueryRequest:
    type: "object"
    properties:
      excludeTaskVariables:
        type: "boolean"
      taskId:
        type: "string"
      executionId:
        type: "string"
      processInstanceId:
        type: "string"
      variableName:
        type: "string"
      variableNameLike:
        type: "string"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
  HistoricVariableInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "14"
      processInstanceId:
        type: "string"
        example: "5"
      processInstanceUrl:
        type: "string"
        example: "http://localhost:8182/history/historic-process-instances/5"
      taskId:
        type: "string"
        example: "6"
      variable:
        $ref: "#/definitions/RestVariable"
  IOSpecification:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      dataInputs:
        type: "array"
        items:
          $ref: "#/definitions/DataSpec"
      dataOutputs:
        type: "array"
        items:
          $ref: "#/definitions/DataSpec"
      dataInputRefs:
        type: "array"
        items:
          type: "string"
      dataOutputRefs:
        type: "array"
        items:
          type: "string"
  Import:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      importType:
        type: "string"
      location:
        type: "string"
      namespace:
        type: "string"
  InjectActivityRequest:
    type: "object"
    properties:
      injectionType:
        type: "string"
      id:
        type: "string"
      name:
        type: "string"
      assignee:
        type: "string"
      taskId:
        type: "string"
      processDefinitionId:
        type: "string"
      joinParallelActivitiesOnComplete:
        type: "boolean"
  Interface:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      implementationRef:
        type: "string"
      operations:
        type: "array"
        items:
          $ref: "#/definitions/Operation"
  ItemDefinition:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      structureRef:
        type: "string"
      itemKind:
        type: "string"
  JobResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "8"
      url:
        type: "string"
        example: "http://localhost:8182/management/jobs/8"
      processInstanceId:
        type: "string"
        example: "5"
      processInstanceUrl:
        type: "string"
        example: "http://localhost:8182/runtime/process-instances/5"
      processDefinitionId:
        type: "string"
        example: "timerProcess:1:4"
      processDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/repository/process-definitions/timerProcess%3A1%3A4"
      executionId:
        type: "string"
        example: "7"
      executionUrl:
        type: "string"
        example: "http://localhost:8182/runtime/executions/7"
      elementId:
        type: "string"
        example: "timer"
      elementName:
        type: "string"
        example: "Timer task"
      retries:
        type: "integer"
        format: "int32"
        example: 3
      exceptionMessage:
        type: "string"
        example: "null"
      dueDate:
        type: "string"
        format: "date-time"
        example: "2013-06-04T22:05:05.474+0000"
      createTime:
        type: "string"
        format: "date-time"
        example: "2013-06-03T22:05:05.474+0000"
      tenantId:
        type: "string"
        example: "null"
  Lane:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      parentProcess:
        $ref: "#/definitions/Process"
      flowReferences:
        type: "array"
        items:
          type: "string"
  MembershipRequest:
    type: "object"
    properties:
      userId:
        type: "string"
        example: "kermit"
  MembershipResponse:
    type: "object"
    properties:
      userId:
        type: "string"
        example: "kermit"
      url:
        type: "string"
        example: "http://localhost:8182/identity/groups/sales/members/kermit"
      groupId:
        type: "string"
        example: "sales"
  Message:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      itemRef:
        type: "string"
  MessageFlow:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      sourceRef:
        type: "string"
      targetRef:
        type: "string"
      messageRef:
        type: "string"
  ModelRequest:
    type: "object"
    properties:
      name:
        type: "string"
        example: "Model name"
      key:
        type: "string"
        example: "Model key"
      category:
        type: "string"
        example: "Model category"
      version:
        type: "integer"
        format: "int32"
        example: 2
      metaInfo:
        type: "string"
        example: "Model metainfo"
      deploymentId:
        type: "string"
        example: "7"
      tenantId:
        type: "string"
        example: "null"
  ModelResponse:
    type: "object"
    properties:
      name:
        type: "string"
        example: "Model name"
      key:
        type: "string"
        example: "Model key"
      category:
        type: "string"
        example: "Model category"
      version:
        type: "integer"
        format: "int32"
        example: 2
      metaInfo:
        type: "string"
        example: "Model metainfo"
      deploymentId:
        type: "string"
        example: "7"
      tenantId:
        type: "string"
        example: "null"
      id:
        type: "string"
        example: "5"
      url:
        type: "string"
        example: "http://localhost:8182/repository/models/5"
      createTime:
        type: "string"
        format: "date-time"
        example: "2013-06-12T12:31:19.861+0000"
      lastUpdateTime:
        type: "string"
        format: "date-time"
        example: "2013-06-12T12:31:19.861+0000"
      deploymentUrl:
        type: "string"
        example: "http://localhost:8182/repository/deployments/2"
      sourceUrl:
        type: "string"
      sourceExtraUrl:
        type: "string"
  Operation:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      implementationRef:
        type: "string"
      inMessageRef:
        type: "string"
      outMessageRef:
        type: "string"
      errorMessageRef:
        type: "array"
        items:
          type: "string"
  Pool:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      processRef:
        type: "string"
      executable:
        type: "boolean"
  Process:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      executable:
        type: "boolean"
      documentation:
        type: "string"
      ioSpecification:
        $ref: "#/definitions/IOSpecification"
      executionListeners:
        type: "array"
        items:
          $ref: "#/definitions/FlowableListener"
      lanes:
        type: "array"
        items:
          $ref: "#/definitions/Lane"
      dataObjects:
        type: "array"
        items:
          $ref: "#/definitions/ValuedDataObject"
      candidateStarterUsers:
        type: "array"
        items:
          type: "string"
      candidateStarterGroups:
        type: "array"
        items:
          type: "string"
      eventListeners:
        type: "array"
        items:
          $ref: "#/definitions/EventListener"
      flowElementMap:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/FlowElement"
      initialFlowElement:
        $ref: "#/definitions/FlowElement"
      enableEagerExecutionTreeFetching:
        type: "boolean"
      artifacts:
        type: "array"
        items:
          $ref: "#/definitions/Artifact"
      flowElements:
        type: "array"
        items:
          $ref: "#/definitions/FlowElement"
  ProcessDefinitionActionRequest:
    type: "object"
    required:
    - "action"
    properties:
      action:
        type: "string"
        example: "activate"
        description: "Action to perform: Either activate or suspend"
      includeProcessInstances:
        type: "boolean"
        description: "Whether or not to suspend/activate running process-instances\
          \ for this process-definition. If omitted, the process-instances are left\
          \ in the state they are"
      date:
        type: "string"
        format: "date-time"
        description: "Date (ISO-8601) when the suspension/activation should be executed.\
          \ If omitted, the suspend/activation is effective immediately."
      category:
        type: "string"
  ProcessDefinitionResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "oneTaskProcess:1:4"
      url:
        type: "string"
        example: "http://localhost:8182/repository/process-definitions/oneTaskProcess%3A1%3A4"
      key:
        type: "string"
        example: "oneTaskProcess"
      version:
        type: "integer"
        format: "int32"
        example: 1
      name:
        type: "string"
        example: "The One Task Process"
      description:
        type: "string"
        example: "This is a process for testing purposes"
      tenantId:
        type: "string"
        example: "null"
      deploymentId:
        type: "string"
        example: "2"
      deploymentUrl:
        type: "string"
        example: "http://localhost:8081/repository/deployments/2"
      resource:
        type: "string"
        example: "http://localhost:8182/repository/deployments/2/resources/testProcess.xml"
        description: "Contains the actual deployed BPMN 2.0 xml."
      diagramResource:
        type: "string"
        example: "http://localhost:8182/repository/deployments/2/resources/testProcess.png"
        description: "Contains a graphical representation of the process, null when\
          \ no diagram is available."
      category:
        type: "string"
        example: "Examples"
      graphicalNotationDefined:
        type: "boolean"
        description: "Indicates the process definition contains graphical information\
          \ (BPMN DI)."
      suspended:
        type: "boolean"
      startFormDefined:
        type: "boolean"
  ProcessEngineInfoResponse:
    type: "object"
    properties:
      name:
        type: "string"
      resourceUrl:
        type: "string"
      exception:
        type: "string"
      version:
        type: "string"
  ProcessInstanceActionRequest:
    type: "object"
    required:
    - "action"
    properties:
      action:
        type: "string"
        example: "activate"
        description: "Action to perform: Either activate or suspend"
  ProcessInstanceCreateRequest:
    type: "object"
    properties:
      processDefinitionId:
        type: "string"
        example: "oneTaskProcess:1:158"
      processDefinitionKey:
        type: "string"
        example: "oneTaskProcess"
      message:
        type: "string"
        example: "newOrderMessage"
      name:
        type: "string"
        example: "myProcessInstanceName"
      businessKey:
        type: "string"
        example: "myBusinessKey"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      transientVariables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      startFormVariables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      outcome:
        type: "string"
      tenantId:
        type: "string"
        example: "tenant1"
      overrideDefinitionTenantId:
        type: "string"
        example: "overrideTenant1"
      returnVariables:
        type: "boolean"
    description: "Only one of processDefinitionId, processDefinitionKey or message\
      \ can be used in the request body"
  ProcessInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      processInstanceId:
        type: "string"
      processInstanceIds:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
      processInstanceName:
        type: "string"
      processInstanceNameLike:
        type: "string"
      processInstanceNameLikeIgnoreCase:
        type: "string"
      processBusinessKey:
        type: "string"
      processBusinessKeyLike:
        type: "string"
      processDefinitionId:
        type: "string"
      processDefinitionIds:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
      processDefinitionKey:
        type: "string"
      processDefinitionKeys:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
      processDefinitionName:
        type: "string"
      processDefinitionCategory:
        type: "string"
      processDefinitionVersion:
        type: "integer"
        format: "int32"
      processDefinitionEngineVersion:
        type: "string"
      deploymentId:
        type: "string"
      deploymentIdIn:
        type: "array"
        items:
          type: "string"
      superProcessInstanceId:
        type: "string"
      subProcessInstanceId:
        type: "string"
      excludeSubprocesses:
        type: "boolean"
      involvedUser:
        type: "string"
      startedBy:
        type: "string"
      startedBefore:
        type: "string"
        format: "date-time"
      startedAfter:
        type: "string"
        format: "date-time"
      suspended:
        type: "boolean"
      includeProcessVariables:
        type: "boolean"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      callbackId:
        type: "string"
      callbackType:
        type: "string"
      tenantId:
        type: "string"
      tenantIdLike:
        type: "string"
      withoutTenantId:
        type: "boolean"
  ProcessInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "187"
      url:
        type: "string"
        example: "http://localhost:8182/repository/process-definitions/processOne%3A1%3A4"
      name:
        type: "string"
        example: "myProcessInstanceName"
      businessKey:
        type: "string"
        example: "myBusinessKey"
      suspended:
        type: "boolean"
      ended:
        type: "boolean"
      processDefinitionId:
        type: "string"
        example: "oneTaskProcess:1:158"
      processDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/repository/process-definitions/processOne%3A1%3A4"
      processDefinitionName:
        type: "string"
        example: "A process definition name"
      processDefinitionDescription:
        type: "string"
        example: "A process definition description"
      activityId:
        type: "string"
        example: "processTask"
      startUserId:
        type: "string"
        example: "johnDoe"
      startTime:
        type: "string"
        example: "2018-04-17T10:17:43.902+0000"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      callbackId:
        type: "string"
        example: "3"
      callbackType:
        type: "string"
        example: "cmmn"
      referenceId:
        type: "string"
        example: "123"
      referenceType:
        type: "string"
        example: "event-to-bpmn-2.0-process"
      tenantId:
        type: "string"
        example: "someTenantId"
      completed:
        type: "boolean"
  ProcessInstanceVariableCollectionResource:
    type: "object"
  ProcessInstanceVariableResource:
    type: "object"
  QueryVariable:
    type: "object"
    properties:
      name:
        type: "string"
      operation:
        type: "string"
        enum:
        - "equals"
        - "notEquals"
        - "equalsIgnoreCase"
        - "notEqualsIgnoreCase"
        - "like"
        - "likeIgnoreCase"
        - "greaterThan"
        - "greaterThanOrEquals"
        - "lessThan"
        - "lessThanOrEquals"
      value:
        type: "object"
      type:
        type: "string"
  Resource:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
  RestActionRequest:
    type: "object"
    properties:
      action:
        type: "string"
  RestEnumFormProperty:
    type: "object"
    properties:
      id:
        type: "string"
        example: "normal"
      name:
        type: "string"
        example: "Normal bed"
  RestFormProperty:
    type: "object"
    properties:
      id:
        type: "string"
        example: "room"
      name:
        type: "string"
        example: "Room"
      type:
        type: "string"
        example: "string"
      value:
        type: "string"
      readable:
        type: "boolean"
      writable:
        type: "boolean"
      required:
        type: "boolean"
      datePattern:
        type: "string"
      enumValues:
        type: "array"
        items:
          $ref: "#/definitions/RestEnumFormProperty"
  RestIdentityLink:
    type: "object"
    properties:
      url:
        type: "string"
      user:
        type: "string"
        example: "kermit"
      group:
        type: "string"
        example: "sales"
      type:
        type: "string"
        example: "candidate"
  RestVariable:
    type: "object"
    properties:
      name:
        type: "string"
        example: "myVariable"
        description: "Name of the variable"
      type:
        type: "string"
        example: "string"
        description: "Type of the variable."
      value:
        type: "object"
        example: "test"
        description: "Value of the variable."
      valueUrl:
        type: "string"
        example: "http://...."
      scope:
        type: "string"
  Signal:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      scope:
        type: "string"
  SignalEventReceivedRequest:
    type: "object"
    properties:
      signalName:
        type: "string"
        description: "Name of the signal"
      variables:
        type: "array"
        description: "Array of variables (in the general variables format) to use\
          \ as payload to pass along with the signal. Cannot be used in case async\
          \ is set to true, this will result in an error."
        items:
          $ref: "#/definitions/RestVariable"
      tenantId:
        type: "string"
        description: "ID of the tenant that the signal event should be processed in"
      async:
        type: "boolean"
        description: "If true, handling of the signal will happen asynchronously.\
          \ Return code will be 202 - Accepted to indicate the request is accepted\
          \ but not yet executed. If false,\n                    handling the signal\
          \ will be done immediately and result (200 - OK) will only return after\
          \ this completed successfully. Defaults to false if omitted."
  SubmitFormRequest:
    type: "object"
    properties:
      action:
        type: "string"
      processDefinitionId:
        type: "string"
      taskId:
        type: "string"
      businessKey:
        type: "string"
      properties:
        type: "array"
        items:
          $ref: "#/definitions/RestFormProperty"
  TableMetaData:
    type: "object"
    properties:
      tableName:
        type: "string"
      columnNames:
        type: "array"
        items:
          type: "string"
      columnTypes:
        type: "array"
        items:
          type: "string"
  TableResponse:
    type: "object"
    properties:
      name:
        type: "string"
        example: "ACT_RU_VARIABLE"
      url:
        type: "string"
        example: "http://localhost:8080/flowable-rest/service/management/tables/ACT_RU_VARIABLE"
      count:
        type: "integer"
        format: "int64"
        example: 4528
  TaskActionRequest:
    type: "object"
    required:
    - "action"
    properties:
      action:
        type: "string"
        example: "complete"
        description: "Action to perform: Either complete, claim, delegate or resolve"
      assignee:
        type: "string"
        example: "userWhoClaims/userToDelegateTo"
        description: "If action is claim or delegate, you can use this parameter to\
          \ set the assignee associated "
      formDefinitionId:
        type: "string"
        example: "12345"
        description: "Required when completing a task with a form"
      outcome:
        type: "string"
        example: "accepted/rejected"
        description: "Optional outcome value when completing a task with a form"
      variables:
        type: "array"
        description: "If action is complete, you can use this parameter to set variables "
        items:
          $ref: "#/definitions/RestVariable"
      transientVariables:
        type: "array"
        description: "If action is complete, you can use this parameter to set transient\
          \ variables "
        items:
          $ref: "#/definitions/RestVariable"
  TaskAttachmentCollectionResource:
    type: "object"
  TaskQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      name:
        type: "string"
      nameLike:
        type: "string"
      description:
        type: "string"
      descriptionLike:
        type: "string"
      priority:
        type: "integer"
        format: "int32"
      minimumPriority:
        type: "integer"
        format: "int32"
      maximumPriority:
        type: "integer"
        format: "int32"
      assignee:
        type: "string"
      assigneeLike:
        type: "string"
      owner:
        type: "string"
      ownerLike:
        type: "string"
      unassigned:
        type: "boolean"
      delegationState:
        type: "string"
      candidateUser:
        type: "string"
      candidateGroup:
        type: "string"
      candidateGroupIn:
        type: "array"
        items:
          type: "string"
      involvedUser:
        type: "string"
      processInstanceId:
        type: "string"
      processInstanceIdWithChildren:
        type: "string"
      processInstanceBusinessKey:
        type: "string"
      processInstanceBusinessKeyLike:
        type: "string"
      processDefinitionId:
        type: "string"
      processDefinitionKey:
        type: "string"
      processDefinitionName:
        type: "string"
      processDefinitionKeyLike:
        type: "string"
      processDefinitionNameLike:
        type: "string"
      executionId:
        type: "string"
      createdOn:
        type: "string"
        format: "date-time"
      createdBefore:
        type: "string"
        format: "date-time"
      createdAfter:
        type: "string"
        format: "date-time"
      excludeSubTasks:
        type: "boolean"
      taskDefinitionKey:
        type: "string"
      taskDefinitionKeyLike:
        type: "string"
      taskDefinitionKeys:
        type: "array"
        items:
          type: "string"
      dueDate:
        type: "string"
        format: "date-time"
      dueBefore:
        type: "string"
        format: "date-time"
      dueAfter:
        type: "string"
        format: "date-time"
      withoutDueDate:
        type: "boolean"
      active:
        type: "boolean"
      includeTaskLocalVariables:
        type: "boolean"
      includeProcessVariables:
        type: "boolean"
      scopeDefinitionId:
        type: "string"
      scopeId:
        type: "string"
      scopeType:
        type: "string"
      tenantId:
        type: "string"
      tenantIdLike:
        type: "string"
      withoutTenantId:
        type: "boolean"
      candidateOrAssigned:
        type: "string"
      category:
        type: "string"
      taskVariables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      processInstanceVariables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
  TaskRequest:
    type: "object"
    properties:
      owner:
        type: "string"
      assignee:
        type: "string"
      delegationState:
        type: "string"
      name:
        type: "string"
      description:
        type: "string"
      dueDate:
        type: "string"
        format: "date-time"
      priority:
        type: "integer"
        format: "int32"
      parentTaskId:
        type: "string"
      category:
        type: "string"
      tenantId:
        type: "string"
      formKey:
        type: "string"
      ownerSet:
        type: "boolean"
      assigneeSet:
        type: "boolean"
      delegationStateSet:
        type: "boolean"
      nameSet:
        type: "boolean"
      descriptionSet:
        type: "boolean"
      duedateSet:
        type: "boolean"
      prioritySet:
        type: "boolean"
      parentTaskIdSet:
        type: "boolean"
      categorySet:
        type: "boolean"
      tenantIdSet:
        type: "boolean"
      formKeySet:
        type: "boolean"
  TaskResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "8"
      url:
        type: "string"
        example: "http://localhost:8182/runtime/tasks/8"
      owner:
        type: "string"
        example: "owner"
      assignee:
        type: "string"
        example: "kermit"
      delegationState:
        type: "string"
        example: "pending"
        description: "Delegation-state of the task, can be null, \"pending\" or \"\
          resolved\"."
      name:
        type: "string"
        example: "My task"
      description:
        type: "string"
        example: "Task description"
      createTime:
        type: "string"
        format: "date-time"
        example: "2018-04-17T10:17:43.902+0000"
      dueDate:
        type: "string"
        format: "date-time"
        example: "2018-04-17T10:17:43.902+0000"
      priority:
        type: "integer"
        format: "int32"
        example: 50
      suspended:
        type: "boolean"
      claimTime:
        type: "string"
        example: "2018-04-17T10:17:43.902+0000"
      taskDefinitionKey:
        type: "string"
        example: "theTask"
      scopeDefinitionId:
        type: "string"
        example: "12"
      scopeId:
        type: "string"
        example: "14"
      subScopeId:
        type: "string"
        example: "15"
      scopeType:
        type: "string"
        example: "cmmn"
      propagatedStageInstanceId:
        type: "string"
        example: "16"
      tenantId:
        type: "string"
        example: "someTenantId"
      category:
        type: "string"
        example: "ExampleCategory"
      formKey:
        type: "string"
      parentTaskId:
        type: "string"
        example: "null"
      parentTaskUrl:
        type: "string"
        example: "null"
      executionId:
        type: "string"
        example: "5"
      executionUrl:
        type: "string"
        example: "http://localhost:8182/runtime/executions/5"
      processInstanceId:
        type: "string"
        example: "5"
      processInstanceUrl:
        type: "string"
        example: "http://localhost:8182/runtime/process-instances/5"
      processDefinitionId:
        type: "string"
        example: "oneTaskProcess%3A1%3A4"
      processDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/runtime/process-definitions/oneTaskProcess%3A1%3A4"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
  TaskVariableCollectionResource:
    type: "object"
  TaskVariableResource:
    type: "object"
  UserInfoRequest:
    type: "object"
    properties:
      key:
        type: "string"
        example: "jobTitle"
      value:
        type: "string"
        example: "Muppet"
  UserInfoResponse:
    type: "object"
    properties:
      key:
        type: "string"
        example: "jobTitle"
      value:
        type: "string"
        example: "Muppet"
      url:
        type: "string"
        example: "http://localhost:8080/flowable-rest/service/identity/users/kermit/info/jobTitle"
  UserRequest:
    type: "object"
    properties:
      id:
        type: "string"
        example: "testuser"
      firstName:
        type: "string"
        example: "Fred"
      lastName:
        type: "string"
        example: "Smith"
      displayName:
        type: "string"
        example: "Fred Smith"
      url:
        type: "string"
        example: "http://localhost:8182/identity/users/testuser"
      email:
        type: "string"
        example: "<EMAIL>"
      tenantId:
        type: "string"
        example: "companyTenantId"
      pictureUrl:
        type: "string"
        example: "http://localhost:8182/identity/users/testuser/picture"
      password:
        type: "string"
  UserResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "testuser"
      firstName:
        type: "string"
        example: "Fred"
      lastName:
        type: "string"
        example: "Smith"
      displayName:
        type: "string"
        example: "Fred Smith"
      url:
        type: "string"
        example: "http://localhost:8182/identity/users/testuser"
      email:
        type: "string"
        example: "<EMAIL>"
      tenantId:
        type: "string"
        example: "companyTenantId"
      pictureUrl:
        type: "string"
        example: "http://localhost:8182/identity/users/testuser/picture"
      password:
        type: "string"
  ValuedDataObject:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      documentation:
        type: "string"
      executionListeners:
        type: "array"
        items:
          $ref: "#/definitions/FlowableListener"
      itemSubjectRef:
        $ref: "#/definitions/ItemDefinition"
      value:
        type: "object"
      type:
        type: "string"
