# OSCAL Workflow Generation Patterns

## Overview

This document provides comprehensive patterns and examples for automatically generating Flowable BPMN workflows from OSCAL (Open Security Controls Assessment Language) documents. The AI-driven workflow generation transforms static OSCAL assessment plans into executable, intelligent workflows that automate compliance processes while maintaining full audit trails.

## OSCAL to Workflow Mapping Principles

### Core Mapping Rules

#### 1. Assessment Activities → Workflow Tasks
Each OSCAL assessment activity becomes a corresponding workflow task with appropriate type and configuration.

```json
{
  "oscal_activity": {
    "uuid": "activity-001-examine-logs",
    "title": "Examine System Logs",
    "description": "Review system logs for security events",
    "type": "examine",
    "subjects": [
      {"subject-uuid-ref": "component-web-server"}
    ],
    "responsible-roles": [
      {"role-id": "security-analyst"}
    ]
  },
  "generated_task": {
    "id": "examine-logs-activity-001",
    "name": "Examine System Logs",
    "type": "userTask",
    "assignee": "security-analyst",
    "documentation": "Review system logs for security events",
    "formKey": "log-examination-form"
  }
}
```

#### 2. Task Dependencies → Sequence Flows
OSCAL task dependencies are converted to BPMN sequence flows with appropriate conditions.

```json
{
  "oscal_dependencies": [
    {
      "task-uuid-ref": "activity-000-preparation",
      "dependency-type": "prerequisite"
    }
  ],
  "generated_flows": [
    {
      "id": "flow-prep-to-examine",
      "sourceRef": "preparation-activity-000",
      "targetRef": "examine-logs-activity-001"
    }
  ]
}
```

#### 3. Timing Constraints → Timer Events
OSCAL timing requirements become timer boundary events or timer start events.

```json
{
  "oscal_timing": {
    "within-date-range": {
      "start": "2024-01-01T00:00:00Z",
      "end": "2024-01-31T23:59:59Z"
    }
  },
  "generated_timer": {
    "id": "timer-deadline-activity-001",
    "type": "boundaryEvent",
    "attachedToRef": "examine-logs-activity-001",
    "timerEventDefinition": {
      "timeDate": "2024-01-31T23:59:59Z"
    }
  }
}
```

## Workflow Generation Engine

### AI-Powered Generator Implementation

#### OSCALWorkflowGenerator.py
```python
from typing import Dict, List, Any, Optional
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
import uuid

class OSCALWorkflowGenerator:
    """
    AI-powered generator for creating Flowable workflows from OSCAL documents
    """
    
    def __init__(self, workflow_agent, template_repository):
        self.workflow_agent = workflow_agent
        self.template_repository = template_repository
        self.activity_type_mappings = self._load_activity_mappings()
        self.role_mappings = self._load_role_mappings()
    
    def generate_assessment_workflow(self, assessment_plan: Dict[str, Any]) -> str:
        """
        Generate complete BPMN workflow from OSCAL assessment plan
        """
        # Extract workflow metadata
        workflow_metadata = self._extract_workflow_metadata(assessment_plan)
        
        # Analyze assessment activities using AI
        activity_analysis = self.workflow_agent.analyze_assessment_activities(
            assessment_plan.get('assessment-activities', {})
        )
        
        # Generate workflow structure
        workflow_structure = self._generate_workflow_structure(
            assessment_plan, 
            activity_analysis
        )
        
        # Create BPMN XML
        bpmn_xml = self._create_bpmn_xml(workflow_structure, workflow_metadata)
        
        # Optimize workflow using AI
        optimized_bpmn = self.workflow_agent.optimize_workflow(bpmn_xml)
        
        return optimized_bpmn
    
    def _extract_workflow_metadata(self, assessment_plan: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract metadata for workflow generation
        """
        return {
            'process_id': f"assessment-{assessment_plan.get('uuid', str(uuid.uuid4()))}",
            'process_name': assessment_plan.get('title', 'OSCAL Assessment Workflow'),
            'description': assessment_plan.get('description', ''),
            'version': 1,
            'assessment_plan_uuid': assessment_plan.get('uuid'),
            'objectives': assessment_plan.get('objectives', []),
            'scope': assessment_plan.get('assessment-scope', {}),
            'schedule': assessment_plan.get('schedule', {})
        }
    
    def _generate_workflow_structure(self, assessment_plan: Dict[str, Any], 
                                   activity_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate workflow structure from OSCAL assessment plan
        """
        activities = assessment_plan.get('assessment-activities', {}).get('activities', [])
        
        # Create workflow elements
        workflow_elements = []
        
        # Start event
        start_event = self._create_start_event(assessment_plan)
        workflow_elements.append(start_event)
        
        # Process activities in dependency order
        ordered_activities = self._order_activities_by_dependencies(activities)
        
        for activity in ordered_activities:
            # Determine activity type and create appropriate elements
            activity_type = self._determine_activity_type(activity, activity_analysis)
            
            if activity_type == 'automated':
                elements = self._create_automated_activity_elements(activity)
            elif activity_type == 'manual':
                elements = self._create_manual_activity_elements(activity)
            elif activity_type == 'approval':
                elements = self._create_approval_activity_elements(activity)
            elif activity_type == 'parallel':
                elements = self._create_parallel_activity_elements(activity)
            else:
                elements = self._create_default_activity_elements(activity)
            
            workflow_elements.extend(elements)
        
        # End event
        end_event = self._create_end_event(assessment_plan)
        workflow_elements.append(end_event)
        
        # Generate sequence flows
        sequence_flows = self._generate_sequence_flows(workflow_elements, activities)
        workflow_elements.extend(sequence_flows)
        
        return {
            'elements': workflow_elements,
            'metadata': self._extract_workflow_metadata(assessment_plan)
        }
    
    def _create_automated_activity_elements(self, activity: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Create workflow elements for automated assessment activities
        """
        elements = []
        activity_id = activity.get('uuid')
        
        # Preparation service task
        prep_task = {
            'id': f'prep-{activity_id}',
            'name': f"Prepare {activity.get('title', 'Activity')}",
            'type': 'serviceTask',
            'implementation': {
                'type': 'class',
                'value': 'com.grcos.workflow.PrepareAssessmentTask'
            },
            'extensionElements': {
                'fields': [
                    {'name': 'activityUuid', 'value': activity_id},
                    {'name': 'activityType', 'value': 'automated'},
                    {'name': 'subjects', 'value': str(activity.get('subjects', []))},
                    {'name': 'methods', 'value': str(activity.get('methods', []))}
                ]
            }
        }
        elements.append(prep_task)
        
        # Execution service task
        exec_task = {
            'id': f'exec-{activity_id}',
            'name': activity.get('title', 'Execute Activity'),
            'type': 'serviceTask',
            'implementation': {
                'type': 'class',
                'value': 'com.grcos.workflow.ExecuteAutomatedAssessmentTask'
            },
            'extensionElements': {
                'fields': [
                    {'name': 'activityUuid', 'value': activity_id},
                    {'name': 'automationLevel', 'value': 'full'},
                    {'name': 'tools', 'value': self._get_automation_tools(activity)},
                    {'name': 'expectedResults', 'value': str(activity.get('expected-results', []))}
                ]
            }
        }
        elements.append(exec_task)
        
        # Results processing task
        process_task = {
            'id': f'process-{activity_id}',
            'name': f"Process Results - {activity.get('title', 'Activity')}",
            'type': 'serviceTask',
            'implementation': {
                'type': 'class',
                'value': 'com.grcos.workflow.ProcessAssessmentResultsTask'
            },
            'extensionElements': {
                'fields': [
                    {'name': 'activityUuid', 'value': activity_id},
                    {'name': 'generateFindings', 'value': 'true'},
                    {'name': 'updateOscalResults', 'value': 'true'},
                    {'name': 'notifyStakeholders', 'value': 'true'}
                ]
            }
        }
        elements.append(process_task)
        
        return elements
    
    def _create_manual_activity_elements(self, activity: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Create workflow elements for manual assessment activities
        """
        elements = []
        activity_id = activity.get('uuid')
        
        # Manual task with form
        manual_task = {
            'id': f'manual-{activity_id}',
            'name': activity.get('title', 'Manual Activity'),
            'type': 'userTask',
            'documentation': activity.get('description', ''),
            'assignee': self._get_primary_assignee(activity),
            'candidateGroups': self._get_candidate_groups(activity),
            'formKey': f'assessment-activity-form-{activity_id}',
            'extensionElements': {
                'formProperties': self._generate_form_properties(activity)
            }
        }
        elements.append(manual_task)
        
        # Add timer boundary event if timing constraints exist
        timing = activity.get('timing', {})
        if timing:
            timer_event = self._create_timer_boundary_event(activity_id, timing)
            elements.append(timer_event)
        
        return elements
    
    def _create_approval_activity_elements(self, activity: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Create workflow elements for approval activities
        """
        elements = []
        activity_id = activity.get('uuid')
        
        # Review task
        review_task = {
            'id': f'review-{activity_id}',
            'name': f"Review {activity.get('title', 'Activity')}",
            'type': 'userTask',
            'candidateGroups': 'reviewers,security-officers',
            'formKey': f'review-form-{activity_id}',
            'extensionElements': {
                'formProperties': [
                    {
                        'id': 'reviewDecision',
                        'name': 'Review Decision',
                        'type': 'enum',
                        'required': 'true',
                        'values': [
                            {'id': 'approved', 'name': 'Approved'},
                            {'id': 'rejected', 'name': 'Rejected'},
                            {'id': 'needs-revision', 'name': 'Needs Revision'}
                        ]
                    },
                    {
                        'id': 'reviewComments',
                        'name': 'Review Comments',
                        'type': 'string',
                        'required': 'false'
                    }
                ]
            }
        }
        elements.append(review_task)
        
        # Decision gateway
        decision_gateway = {
            'id': f'decision-{activity_id}',
            'name': 'Review Decision',
            'type': 'exclusiveGateway'
        }
        elements.append(decision_gateway)
        
        # Approval notification task
        approval_task = {
            'id': f'approve-{activity_id}',
            'name': 'Process Approval',
            'type': 'serviceTask',
            'implementation': {
                'type': 'class',
                'value': 'com.grcos.workflow.ProcessApprovalTask'
            }
        }
        elements.append(approval_task)
        
        return elements
    
    def _create_bpmn_xml(self, workflow_structure: Dict[str, Any], 
                        metadata: Dict[str, Any]) -> str:
        """
        Create BPMN XML from workflow structure
        """
        # Create root definitions element
        definitions = ET.Element('definitions')
        definitions.set('xmlns', 'http://www.omg.org/spec/BPMN/20100524/MODEL')
        definitions.set('xmlns:flowable', 'http://flowable.org/bpmn')
        definitions.set('targetNamespace', 'http://grcos.com/workflows')
        definitions.set('id', f"definitions-{metadata['process_id']}")
        
        # Create process element
        process = ET.SubElement(definitions, 'process')
        process.set('id', metadata['process_id'])
        process.set('name', metadata['process_name'])
        process.set('isExecutable', 'true')
        
        # Add documentation
        if metadata.get('description'):
            documentation = ET.SubElement(process, 'documentation')
            documentation.text = metadata['description']
        
        # Add workflow elements
        for element in workflow_structure['elements']:
            self._add_element_to_process(process, element)
        
        # Format and return XML
        self._indent_xml(definitions)
        return ET.tostring(definitions, encoding='unicode')
    
    def _add_element_to_process(self, process: ET.Element, element: Dict[str, Any]):
        """
        Add workflow element to BPMN process
        """
        element_type = element['type']
        
        if element_type == 'startEvent':
            self._add_start_event(process, element)
        elif element_type == 'endEvent':
            self._add_end_event(process, element)
        elif element_type == 'userTask':
            self._add_user_task(process, element)
        elif element_type == 'serviceTask':
            self._add_service_task(process, element)
        elif element_type == 'exclusiveGateway':
            self._add_exclusive_gateway(process, element)
        elif element_type == 'parallelGateway':
            self._add_parallel_gateway(process, element)
        elif element_type == 'sequenceFlow':
            self._add_sequence_flow(process, element)
        elif element_type == 'boundaryEvent':
            self._add_boundary_event(process, element)
    
    def _add_user_task(self, process: ET.Element, element: Dict[str, Any]):
        """
        Add user task to BPMN process
        """
        user_task = ET.SubElement(process, 'userTask')
        user_task.set('id', element['id'])
        user_task.set('name', element['name'])
        
        if element.get('assignee'):
            user_task.set('flowable:assignee', element['assignee'])
        
        if element.get('candidateGroups'):
            user_task.set('flowable:candidateGroups', element['candidateGroups'])
        
        if element.get('formKey'):
            user_task.set('flowable:formKey', element['formKey'])
        
        # Add documentation
        if element.get('documentation'):
            documentation = ET.SubElement(user_task, 'documentation')
            documentation.text = element['documentation']
        
        # Add extension elements (form properties, etc.)
        if element.get('extensionElements'):
            self._add_extension_elements(user_task, element['extensionElements'])
    
    def _add_service_task(self, process: ET.Element, element: Dict[str, Any]):
        """
        Add service task to BPMN process
        """
        service_task = ET.SubElement(process, 'serviceTask')
        service_task.set('id', element['id'])
        service_task.set('name', element['name'])
        
        implementation = element.get('implementation', {})
        if implementation.get('type') == 'class':
            service_task.set('flowable:class', implementation['value'])
        elif implementation.get('type') == 'expression':
            service_task.set('flowable:expression', implementation['value'])
        
        # Add extension elements (fields, etc.)
        if element.get('extensionElements'):
            self._add_extension_elements(service_task, element['extensionElements'])
```

This detailed documentation provides comprehensive patterns for OSCAL workflow generation. Would you like me to continue with more specific implementation guides for other aspects like custom service tasks, AI agent integration, or module-specific workflows?
