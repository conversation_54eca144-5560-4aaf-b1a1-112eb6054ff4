/* your custom css */

.disclaimer {
  color: rgba(255, 255, 255, 0.4);
  text-align: left;
    font-size: 8px;

}

.normal-link:hover {
   text-decoration: underline;
}

@media only screen and (min-device-width: 360px) and (max-device-width: 736px) {
}

@media only screen and (min-width: 1024px) {
}

@media only screen and (max-width: 1023px) {
}

@media only screen and (min-width: 1400px) {
}

@media only screen and (min-width: 1500px) {
}