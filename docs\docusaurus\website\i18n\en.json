{"_comment": "This file is auto-generated by write-translations.js", "localized-strings": {"next": "Next", "previous": "Previous", "tagline": "Reference and User Guides", "docs": {"bpmn/ch02-GettingStarted": {"title": "Getting Started"}, "bpmn/ch03-Configuration": {"title": "Configuration"}, "bpmn/ch04-API": {"title": "The Flowable API"}, "bpmn/ch05-Spring": {"title": "Spring integration"}, "bpmn/ch05a-Spring-Boot": {"title": "Spring Boot"}, "bpmn/ch06-Deployment": {"title": "Deployment"}, "bpmn/ch07a-BPMN-Introduction": {"title": "BPMN 2.0 Introduction"}, "bpmn/ch07b-BPMN-Constructs": {"title": "BPMN 2.0 Constructs"}, "bpmn/ch08-ProcessInstanceMigration": {"title": "Process Instance Migration"}, "bpmn/ch09-JPA": {"title": "JPA"}, "bpmn/ch10-History": {"title": "History"}, "bpmn/ch11-IDM": {"title": "Identity management"}, "bpmn/ch12-Design": {"title": "Flowable Design"}, "bpmn/ch13-Applications": {"title": "Flowable applications"}, "bpmn/ch14-REST": {"title": "REST API"}, "bpmn/ch16-Cdi": {"title": "CDI integration"}, "bpmn/ch16-Ldap": {"title": "LDAP integration"}, "bpmn/ch18-Advanced": {"title": "Advanced"}, "bpmn/ch18-tooling": {"title": "<PERSON><PERSON>"}, "cmmn/ch02-Configuration": {"title": "Configuration"}, "cmmn/ch03-API": {"title": "The Flowable CMMN API"}, "cmmn/ch04-Spring": {"title": "Spring integration"}, "cmmn/ch05-Deployment": {"title": "Deployment"}, "cmmn/ch06-cmmn": {"title": "CMMN 1.1"}, "cmmn/ch07-architecture": {"title": "Architecture"}, "cmmn/ch08-REST": {"title": "REST API"}, "disclaimer": {"title": "Disclaimer"}, "dmn/ch02-Configuration": {"title": "Configuration"}, "dmn/ch03-API": {"title": "The Flowable DMN API"}, "dmn/ch04-Spring": {"title": "Spring integration"}, "dmn/ch05-Deployment": {"title": "Deployment"}, "dmn/ch06-DMN-Introduction": {"title": "DMN 1.1 Introduction"}, "dmn/ch07-REST": {"title": "REST API"}, "eventregistry/ch02-Configuration": {"title": "Configuration"}, "eventregistry/ch03-API": {"title": "The Flowable Event Registry API"}, "eventregistry/ch04-Spring": {"title": "Spring integration"}, "eventregistry/ch05-Deployment": {"title": "Deployment"}, "eventregistry/ch06-EventRegistry-Introduction": {"title": "Event Registry Introduction"}, "eventregistry/ch07-REST": {"title": "REST API"}, "header-rest": {"title": "General Flowable REST principles"}, "oss-introduction": {"title": "Introducing Flowable Open Source", "sidebar_label": "Open Source Details"}}, "links": {"Guides": "Guides", "Javadocs": "Javadocs", "Open source home": "Open source home"}, "categories": {"Flowable Open Source": "Flowable Open Source", "BPMN User Guide": "BPMN User Guide", "CMMN User Guide": "CMMN User Guide", "Event Registry User Guide": "Event Registry User Guide", "DMN User Guide": "DMN User Guide", "Forms User Guide": "Forms User Guide", "Applications Guide": "Applications Guide", "Disclaimer": "Disclaimer"}}, "pages-strings": {"Help Translate|recruit community translators for your project": "Help Translate", "Edit this Doc|recruitment message asking to edit the doc source": "Edit", "Translate this Doc|recruitment message asking to translate the docs": "Translate"}}