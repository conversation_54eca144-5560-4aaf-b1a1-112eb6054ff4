# GRCOS Assets Module OPA Integration

## Overview

The Assets Module OPA integration transforms static asset inventory management into dynamic, policy-driven asset governance. This integration enables real-time configuration compliance validation, automated drift detection, and intelligent asset lifecycle management across IT, OT, and IoT environments through OSCAL-derived OPA policies.

## Asset Policy Framework

### Configuration Management Policies
```rego
# Asset Configuration Baseline Policy
package grcos.assets.configuration.baseline

import future.keywords.if
import future.keywords.in
import future.keywords.every

# OSCAL Control: CM-2 (Baseline Configuration)
# Enforce baseline configuration compliance for all assets

default allow_configuration_change := false

# Allow configuration changes only if they comply with baseline
allow_configuration_change if {
    asset_exists
    baseline_compliant
    change_authorized
    security_impact_acceptable
}

# Verify asset exists in inventory
asset_exists if {
    input.asset.id in data.asset_inventory
    data.asset_inventory[input.asset.id].status == "active"
}

# Check baseline compliance
baseline_compliant if {
    asset_type := data.asset_inventory[input.asset.id].type
    baseline_config := data.baseline_configurations[asset_type]
    
    every setting in input.configuration_changes {
        setting.parameter in baseline_config.allowed_parameters
        validate_parameter_value(setting, baseline_config)
    }
}

# Validate individual parameter values
validate_parameter_value(setting, baseline) if {
    param_config := baseline.allowed_parameters[setting.parameter]
    
    # Check value type
    type_name(setting.value) == param_config.type
    
    # Check value constraints
    validate_value_constraints(setting.value, param_config.constraints)
}

# Change authorization validation
change_authorized if {
    input.change_request.approved_by in data.authorized_approvers[input.asset.classification]
    input.change_request.approval_timestamp > (time.now_ns() - data.approval_validity_ns)
    input.change_request.change_window_valid == true
}

# Security impact assessment
security_impact_acceptable if {
    impact_score := calculate_security_impact
    impact_score <= data.max_allowed_impact[input.asset.classification]
}

calculate_security_impact := score {
    base_score := data.change_impact_scores[input.change_request.type]
    asset_criticality := data.asset_inventory[input.asset.id].criticality_score
    environment_factor := data.environment_risk_factors[input.asset.environment]
    
    score := base_score * asset_criticality * environment_factor
}

# Configuration drift detection
configuration_drift[drift] {
    asset := data.asset_inventory[asset_id]
    current_config := input.current_configurations[asset_id]
    expected_config := data.baseline_configurations[asset.type]
    
    some param in expected_config.required_parameters
    current_config[param] != expected_config.required_parameters[param]
    
    drift := {
        "asset_id": asset_id,
        "parameter": param,
        "expected": expected_config.required_parameters[param],
        "actual": current_config[param],
        "severity": expected_config.parameter_criticality[param]
    }
}

# Violations and alerts
violations[violation] {
    not baseline_compliant
    violation := {
        "type": "baseline_violation",
        "message": "Configuration change violates baseline requirements",
        "severity": "high",
        "asset_id": input.asset.id,
        "remediation": "Review configuration against approved baseline"
    }
}
```

### Asset Access Control Policies
```rego
# Asset Access Control Policy
package grcos.assets.access_control

import future.keywords.if
import future.keywords.in

# OSCAL Control: AC-3 (Access Enforcement)
# Control access to asset management operations

default allow_asset_access := false

# Allow asset access based on comprehensive checks
allow_asset_access if {
    user_authenticated
    user_authorized_for_asset
    operation_permitted
    context_appropriate
}

# User authentication verification
user_authenticated if {
    input.user.authenticated == true
    input.user.session_valid == true
    input.user.mfa_verified == true
}

# Asset-specific authorization
user_authorized_for_asset if {
    asset := data.asset_inventory[input.asset.id]
    user_roles := data.users[input.user.id].roles
    
    # Check role-based access
    required_role := asset.access_requirements.minimum_role
    required_role in user_roles
    
    # Check clearance level
    user_clearance := data.users[input.user.id].clearance_level
    user_clearance >= asset.access_requirements.minimum_clearance
    
    # Check department authorization
    user_department := data.users[input.user.id].department
    user_department in asset.access_requirements.authorized_departments
}

# Operation permission validation
operation_permitted if {
    operation := input.operation.type
    asset_type := data.asset_inventory[input.asset.id].type
    user_permissions := data.users[input.user.id].permissions
    
    required_permission := data.operation_permissions[asset_type][operation]
    required_permission in user_permissions
}

# Context validation
context_appropriate if {
    # Time-based access control
    time_authorized
    
    # Location-based access control
    location_authorized
    
    # Network-based access control
    network_authorized
}

time_authorized if {
    current_time := time.now_ns()
    asset := data.asset_inventory[input.asset.id]
    
    some time_window in asset.access_requirements.allowed_time_windows
    current_time >= time_window.start
    current_time <= time_window.end
}

location_authorized if {
    user_location := input.context.user_location
    asset := data.asset_inventory[input.asset.id]
    user_location in asset.access_requirements.allowed_locations
}

network_authorized if {
    source_network := input.context.source_network
    asset := data.asset_inventory[input.asset.id]
    source_network in asset.access_requirements.allowed_networks
}
```

## Asset Lifecycle Management

### Asset Registration Policies
```rego
# Asset Registration Policy
package grcos.assets.lifecycle.registration

import future.keywords.if
import future.keywords.in

# OSCAL Control: CM-8 (Information System Component Inventory)
# Govern asset registration and inventory management

default allow_asset_registration := false

# Allow asset registration with proper validation
allow_asset_registration if {
    asset_data_valid
    registration_authorized
    security_requirements_met
    compliance_validated
}

# Asset data validation
asset_data_valid if {
    # Required fields present
    required_fields_present
    
    # Data format validation
    data_format_valid
    
    # Asset uniqueness
    asset_unique
}

required_fields_present if {
    required_fields := data.asset_registration_requirements.required_fields
    
    every field in required_fields {
        field in object.keys(input.asset)
        input.asset[field] != ""
    }
}

data_format_valid if {
    # Validate asset ID format
    regex.match(data.asset_id_pattern, input.asset.id)
    
    # Validate asset type
    input.asset.type in data.supported_asset_types
    
    # Validate classification
    input.asset.classification in data.valid_classifications
}

asset_unique if {
    not input.asset.id in data.existing_asset_ids
}

# Registration authorization
registration_authorized if {
    registrar := input.registrar
    asset_type := input.asset.type
    
    # Check registrar permissions
    registrar.id in data.authorized_registrars[asset_type]
    
    # Verify approval if required
    approval_requirements_met
}

approval_requirements_met if {
    asset_classification := input.asset.classification
    approval_required := data.approval_requirements[asset_classification]
    
    not approval_required
} else if {
    input.approval.provided == true
    input.approval.approver in data.authorized_approvers
    input.approval.timestamp > (time.now_ns() - data.approval_validity_ns)
}

# Security requirements validation
security_requirements_met if {
    # Security scan completed
    input.security_scan.completed == true
    input.security_scan.passed == true
    
    # Vulnerability assessment
    vulnerability_assessment_acceptable
    
    # Configuration security
    configuration_secure
}

vulnerability_assessment_acceptable if {
    vuln_score := input.security_scan.vulnerability_score
    max_allowed := data.max_vulnerability_scores[input.asset.classification]
    vuln_score <= max_allowed
}

configuration_secure if {
    security_config := input.asset.security_configuration
    required_config := data.security_requirements[input.asset.type]
    
    every requirement in required_config {
        security_config[requirement.setting] == requirement.required_value
    }
}
```

## Cross-Environment Asset Policies

### IT Environment Asset Policies
```rego
# IT Environment Asset Management
package grcos.assets.it.management

import future.keywords.if

# IT-specific asset management policies
allow_it_asset_operation if {
    standard_asset_checks
    it_specific_requirements
}

standard_asset_checks if {
    user_authenticated
    operation_authorized
    asset_accessible
}

it_specific_requirements if {
    # Network security requirements
    network_security_compliant
    
    # Software licensing compliance
    licensing_compliant
    
    # Patch management compliance
    patch_management_compliant
}

network_security_compliant if {
    asset := data.asset_inventory[input.asset.id]
    
    # Firewall configuration
    asset.network_config.firewall_enabled == true
    
    # Network segmentation
    asset.network_config.vlan in data.authorized_vlans[asset.classification]
    
    # Intrusion detection
    asset.security_config.ids_enabled == true
}

licensing_compliant if {
    asset := data.asset_inventory[input.asset.id]
    
    every software in asset.installed_software {
        license := data.software_licenses[software.name]
        license.valid == true
        license.expires > time.now_ns()
        software.version in license.covered_versions
    }
}
```

### OT Environment Asset Policies
```rego
# OT Environment Asset Management
package grcos.assets.ot.management

import future.keywords.if

# OT-specific asset management with safety focus
allow_ot_asset_operation if {
    standard_asset_checks
    ot_safety_requirements
    operational_continuity_maintained
}

ot_safety_requirements if {
    # Safety system integrity
    safety_systems_operational
    
    # Maintenance window compliance
    maintenance_window_authorized
    
    # Safety procedures followed
    safety_procedures_compliant
}

safety_systems_operational if {
    asset := data.asset_inventory[input.asset.id]
    
    every safety_system in asset.connected_safety_systems {
        system_status := data.safety_system_status[safety_system.id]
        system_status.operational == true
        system_status.last_check > (time.now_ns() - data.safety_check_interval_ns)
    }
}

maintenance_window_authorized if {
    current_time := time.now_ns()
    maintenance_windows := data.authorized_maintenance_windows
    
    some window in maintenance_windows
    current_time >= window.start
    current_time <= window.end
    window.asset_types[_] == data.asset_inventory[input.asset.id].type
}

operational_continuity_maintained if {
    # Backup systems available
    backup_systems_ready
    
    # Production impact minimal
    production_impact_acceptable
}

backup_systems_ready if {
    asset := data.asset_inventory[input.asset.id]
    backup_systems := data.backup_systems[input.asset.id]
    
    every backup in backup_systems {
        backup_status := data.system_status[backup.id]
        backup_status.operational == true
        backup_status.ready_for_failover == true
    }
}
```

### IoT Environment Asset Policies
```rego
# IoT Environment Asset Management
package grcos.assets.iot.management

import future.keywords.if

# IoT-specific asset management with resource constraints
allow_iot_asset_operation if {
    standard_asset_checks
    iot_resource_requirements
    iot_security_requirements
}

iot_resource_requirements if {
    # Battery level sufficient
    battery_level_adequate
    
    # Connectivity stable
    connectivity_stable
    
    # Processing capacity available
    processing_capacity_available
}

battery_level_adequate if {
    device := data.asset_inventory[input.asset.id]
    device.battery_level > data.minimum_battery_levels[device.type]
}

connectivity_stable if {
    device := data.asset_inventory[input.asset.id]
    device.connectivity.signal_strength > data.minimum_signal_strength
    device.connectivity.last_heartbeat > (time.now_ns() - data.heartbeat_timeout_ns)
}

iot_security_requirements if {
    # Device authentication
    device_authenticated
    
    # Firmware up to date
    firmware_current
    
    # Encryption enabled
    encryption_enabled
}

device_authenticated if {
    device := data.asset_inventory[input.asset.id]
    device.authentication.certificate_valid == true
    device.authentication.certificate_expires > time.now_ns()
}

firmware_current if {
    device := data.asset_inventory[input.asset.id]
    current_version := device.firmware.version
    latest_version := data.latest_firmware_versions[device.model]
    current_version == latest_version
}
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Assets Module Team
