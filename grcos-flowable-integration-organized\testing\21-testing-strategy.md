# GRCOS Flowable Integration Testing Strategy

## Overview

This document outlines the comprehensive testing strategy for the GRCOS Flowable integration, covering unit tests, integration tests, end-to-end tests, performance tests, and compliance validation tests. The strategy ensures reliability, performance, and compliance of the integrated system.

## Testing Architecture

### Testing Pyramid

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        GRCOS Testing Strategy Pyramid                           │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        E2E & Compliance Tests                               │ │
│  │                              (Slow, Few)                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │ Full System │  │ Compliance  │  │ Performance │  │ Security    │       │ │
│  │  │    Tests    │  │ Validation  │  │    Tests    │  │   Tests     │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        Integration Tests                                    │ │
│  │                           (Medium Speed)                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │  Workflow   │  │    OSCAL    │  │   CrewAI    │  │ Blockchain  │       │ │
│  │  │Integration  │  │Integration  │  │Integration  │  │Integration  │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                          Unit Tests                                         │ │
│  │                        (Fast, Many)                                        │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │  Service    │  │   Agent     │  │   OSCAL     │  │  Workflow   │       │ │
│  │  │   Tests     │  │   Tests     │  │   Tests     │  │   Tests     │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Unit Testing

### Test Configuration

#### TestConfiguration.java
```java
@TestConfiguration
@EnableConfigurationProperties
public class GRCOSTestConfiguration {
    
    @Bean
    @Primary
    public DataSource testDataSource() {
        return new EmbeddedDatabaseBuilder()
            .setType(EmbeddedDatabaseType.H2)
            .addScript("classpath:db/test-schema.sql")
            .addScript("classpath:db/test-data.sql")
            .build();
    }
    
    @Bean
    @Primary
    public ProcessEngineConfiguration testProcessEngineConfiguration(DataSource dataSource) {
        SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
        config.setDataSource(dataSource);
        config.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_CREATE_DROP);
        config.setAsyncExecutorActivate(false);
        config.setHistory(ProcessEngineConfiguration.HISTORY_FULL);
        return config;
    }
    
    @Bean
    @Primary
    @MockBean
    public CrewAIClient mockCrewAIClient() {
        return Mockito.mock(CrewAIClient.class);
    }
    
    @Bean
    @Primary
    @MockBean
    public BlockchainAuditService mockBlockchainAuditService() {
        return Mockito.mock(BlockchainAuditService.class);
    }
    
    @Bean
    @Primary
    @MockBean
    public OSCALService mockOSCALService() {
        return Mockito.mock(OSCALService.class);
    }
}
```

### Service Layer Tests

#### AssessmentServiceTest.java
```java
@ExtendWith(MockitoExtension.class)
class AssessmentServiceTest {
    
    @Mock
    private AssessmentRepository assessmentRepository;
    
    @Mock
    private OSCALService oscalService;
    
    @Mock
    private WorkflowOrchestrationAgent orchestrationAgent;
    
    @Mock
    private FlowableRuntimeService runtimeService;
    
    @InjectMocks
    private AssessmentService assessmentService;
    
    @Test
    @DisplayName("Should create assessment from OSCAL assessment plan")
    void shouldCreateAssessmentFromOSCALPlan() {
        // Given
        String assessmentPlanUuid = "test-plan-uuid";
        OSCALAssessmentPlan mockPlan = createMockAssessmentPlan(assessmentPlanUuid);
        Assessment expectedAssessment = createExpectedAssessment();
        
        when(oscalService.getAssessmentPlan(assessmentPlanUuid)).thenReturn(mockPlan);
        when(assessmentRepository.save(any(Assessment.class))).thenReturn(expectedAssessment);
        
        // When
        Assessment result = assessmentService.createAssessmentFromOSCAL(assessmentPlanUuid);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOscalPlanUuid()).isEqualTo(assessmentPlanUuid);
        assertThat(result.getStatus()).isEqualTo(AssessmentStatus.CREATED);
        
        verify(oscalService).getAssessmentPlan(assessmentPlanUuid);
        verify(assessmentRepository).save(any(Assessment.class));
    }
    
    @Test
    @DisplayName("Should start assessment workflow")
    void shouldStartAssessmentWorkflow() {
        // Given
        Long assessmentId = 1L;
        Assessment mockAssessment = createMockAssessment(assessmentId);
        ProcessInstance mockProcessInstance = createMockProcessInstance();
        
        when(assessmentRepository.findById(assessmentId)).thenReturn(Optional.of(mockAssessment));
        when(runtimeService.startProcessInstanceByKey(
            eq("assessment-execution"), 
            eq(assessmentId.toString()), 
            any(Map.class)
        )).thenReturn(mockProcessInstance);
        
        // When
        String processInstanceId = assessmentService.startAssessment(assessmentId);
        
        // Then
        assertThat(processInstanceId).isEqualTo(mockProcessInstance.getId());
        verify(runtimeService).startProcessInstanceByKey(
            eq("assessment-execution"),
            eq(assessmentId.toString()),
            any(Map.class)
        );
    }
    
    @Test
    @DisplayName("Should handle assessment execution failure")
    void shouldHandleAssessmentExecutionFailure() {
        // Given
        Long assessmentId = 1L;
        Assessment mockAssessment = createMockAssessment(assessmentId);
        
        when(assessmentRepository.findById(assessmentId)).thenReturn(Optional.of(mockAssessment));
        when(runtimeService.startProcessInstanceByKey(any(), any(), any()))
            .thenThrow(new FlowableException("Process start failed"));
        
        // When & Then
        assertThatThrownBy(() -> assessmentService.startAssessment(assessmentId))
            .isInstanceOf(AssessmentExecutionException.class)
            .hasMessageContaining("Failed to start assessment workflow");
    }
    
    @Test
    @DisplayName("Should calculate assessment progress correctly")
    void shouldCalculateAssessmentProgressCorrectly() {
        // Given
        Long assessmentId = 1L;
        List<AssessmentTask> tasks = createMockAssessmentTasks();
        
        when(assessmentRepository.findTasksByAssessmentId(assessmentId)).thenReturn(tasks);
        
        // When
        AssessmentProgress progress = assessmentService.calculateProgress(assessmentId);
        
        // Then
        assertThat(progress.getTotalTasks()).isEqualTo(5);
        assertThat(progress.getCompletedTasks()).isEqualTo(3);
        assertThat(progress.getProgressPercentage()).isEqualTo(60.0);
        assertThat(progress.getEstimatedCompletion()).isNotNull();
    }
    
    private OSCALAssessmentPlan createMockAssessmentPlan(String uuid) {
        return OSCALAssessmentPlan.builder()
            .uuid(uuid)
            .metadata(OSCALMetadata.builder()
                .title("Test Assessment Plan")
                .build())
            .tasks(List.of(
                OSCALTask.builder()
                    .uuid("task-1")
                    .title("Test Task 1")
                    .build()
            ))
            .build();
    }
    
    private Assessment createExpectedAssessment() {
        return Assessment.builder()
            .id(1L)
            .oscalPlanUuid("test-plan-uuid")
            .status(AssessmentStatus.CREATED)
            .createdAt(Instant.now())
            .build();
    }
    
    private List<AssessmentTask> createMockAssessmentTasks() {
        return List.of(
            AssessmentTask.builder().id(1L).status(TaskStatus.COMPLETED).build(),
            AssessmentTask.builder().id(2L).status(TaskStatus.COMPLETED).build(),
            AssessmentTask.builder().id(3L).status(TaskStatus.COMPLETED).build(),
            AssessmentTask.builder().id(4L).status(TaskStatus.IN_PROGRESS).build(),
            AssessmentTask.builder().id(5L).status(TaskStatus.PENDING).build()
        );
    }
}
```

### Agent Tests

#### WorkflowOrchestratorAgentTest.java
```java
@ExtendWith(MockitoExtension.class)
class WorkflowOrchestratorAgentTest {
    
    @Mock
    private OpenAiChatModel chatModel;
    
    @Mock
    private ContentRetriever contentRetriever;
    
    @Mock
    private AgentKnowledgeBase knowledgeBase;
    
    @Mock
    private FlowableRuntimeService runtimeService;
    
    @Mock
    private WorkflowAnalyticsService analyticsService;
    
    @InjectMocks
    private WorkflowOrchestratorAgent orchestratorAgent;
    
    @Test
    @DisplayName("Should optimize workflow successfully")
    void shouldOptimizeWorkflowSuccessfully() {
        // Given
        String processInstanceId = "test-process-123";
        WorkflowPerformanceMetrics mockMetrics = createMockPerformanceMetrics();
        ChatResponse mockResponse = createMockOptimizationResponse();
        
        when(analyticsService.getProcessMetrics(processInstanceId)).thenReturn(mockMetrics);
        when(contentRetriever.retrieve(any(Query.class))).thenReturn(Collections.emptyList());
        when(chatModel.chat(any(ChatRequest.class))).thenReturn(mockResponse);
        
        // When
        WorkflowOptimizationResult result = orchestratorAgent.optimizeWorkflow(processInstanceId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getRecommendations()).isNotEmpty();
        assertThat(result.getConfidence()).isGreaterThan(0.0);
        
        verify(analyticsService).getProcessMetrics(processInstanceId);
        verify(chatModel).chat(any(ChatRequest.class));
        verify(knowledgeBase).storeInteraction(any(AgentInteraction.class));
    }
    
    @Test
    @DisplayName("Should route task to optimal assignee")
    void shouldRouteTaskToOptimalAssignee() {
        // Given
        String taskId = "test-task-456";
        Task mockTask = createMockTask(taskId);
        List<User> availableUsers = createMockUsers();
        ChatResponse mockResponse = createMockRoutingResponse();
        
        when(taskService.createTaskQuery().taskId(taskId).singleResult()).thenReturn(mockTask);
        when(identityService.getActiveUsers()).thenReturn(availableUsers);
        when(chatModel.chat(any(ChatRequest.class))).thenReturn(mockResponse);
        
        // When
        TaskRoutingDecision decision = orchestratorAgent.routeTask(taskId);
        
        // Then
        assertThat(decision).isNotNull();
        assertThat(decision.getRecommendedAssignee()).isNotNull();
        assertThat(decision.getConfidence()).isGreaterThan(0.5);
        assertThat(decision.getReasoning()).isNotBlank();
    }
    
    @Test
    @DisplayName("Should predict workflow outcome with high confidence")
    void shouldPredictWorkflowOutcomeWithHighConfidence() {
        // Given
        String processInstanceId = "test-process-789";
        WorkflowState mockState = createMockWorkflowState();
        List<HistoricalPattern> mockPatterns = createMockHistoricalPatterns();
        ChatResponse mockResponse = createMockPredictionResponse();
        
        when(analyticsService.getCurrentProcessState(processInstanceId)).thenReturn(mockState);
        when(analyticsService.getHistoricalPatterns(processInstanceId)).thenReturn(mockPatterns);
        when(chatModel.chat(any(ChatRequest.class))).thenReturn(mockResponse);
        
        // When
        WorkflowPrediction prediction = orchestratorAgent.predictWorkflowOutcome(processInstanceId);
        
        // Then
        assertThat(prediction).isNotNull();
        assertThat(prediction.getPredictedOutcome()).isNotNull();
        assertThat(prediction.getConfidence()).isGreaterThan(0.7);
        assertThat(prediction.getRiskFactors()).isNotNull();
    }
    
    private ChatResponse createMockOptimizationResponse() {
        return ChatResponse.builder()
            .content(TextContent.from("""
                {
                    "recommendations": [
                        {
                            "type": "PARALLEL_EXECUTION",
                            "description": "Execute tasks T1 and T2 in parallel",
                            "expectedImprovement": 0.3
                        }
                    ],
                    "expectedImprovement": 0.25,
                    "confidence": 0.85,
                    "reasoning": "Analysis shows bottleneck in sequential task execution"
                }
                """))
            .build();
    }
}
```

## Integration Testing

### Workflow Integration Tests

#### AssessmentWorkflowIntegrationTest.java
```java
@SpringBootTest
@Testcontainers
@DirtiesContext
class AssessmentWorkflowIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:14")
            .withDatabaseName("grcos_test")
            .withUsername("test")
            .withPassword("test");
    
    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7-alpine")
            .withExposedPorts(6379);
    
    @Autowired
    private ProcessEngine processEngine;
    
    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private HistoryService historyService;
    
    @Autowired
    private AssessmentService assessmentService;
    
    @Autowired
    private OSCALService oscalService;
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.redis.host", redis::getHost);
        registry.add("spring.redis.port", redis::getFirstMappedPort);
    }
    
    @Test
    @DisplayName("Should execute complete assessment workflow")
    void shouldExecuteCompleteAssessmentWorkflow() {
        // Given
        String assessmentPlanContent = loadTestResource("oscal/test-assessment-plan.json");
        OSCALAssessmentPlan plan = oscalService.parseOSCALAssessmentPlan(assessmentPlanContent);
        Assessment assessment = assessmentService.createAssessmentFromOSCAL(plan.getUuid());
        
        // When - Start assessment workflow
        String processInstanceId = assessmentService.startAssessment(assessment.getId());
        
        // Then - Verify process started
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
            .processInstanceId(processInstanceId)
            .singleResult();
        
        assertThat(processInstance).isNotNull();
        assertThat(processInstance.isEnded()).isFalse();
        
        // When - Complete first task
        Task firstTask = taskService.createTaskQuery()
            .processInstanceId(processInstanceId)
            .singleResult();
        
        assertThat(firstTask).isNotNull();
        assertThat(firstTask.getName()).isEqualTo("Plan Assessment");
        
        Map<String, Object> taskVariables = Map.of(
            "assessmentScope", "full",
            "assessmentMethod", "automated",
            "planningComplete", true
        );
        
        taskService.complete(firstTask.getId(), taskVariables);
        
        // Then - Verify next task created
        Task secondTask = taskService.createTaskQuery()
            .processInstanceId(processInstanceId)
            .singleResult();
        
        assertThat(secondTask).isNotNull();
        assertThat(secondTask.getName()).isEqualTo("Execute Assessment");
        
        // When - Complete assessment execution
        taskService.complete(secondTask.getId(), Map.of(
            "assessmentResults", createMockAssessmentResults(),
            "executionComplete", true
        ));
        
        // Then - Verify process completion
        HistoricProcessInstance historicProcess = historyService
            .createHistoricProcessInstanceQuery()
            .processInstanceId(processInstanceId)
            .singleResult();
        
        assertThat(historicProcess.getEndTime()).isNotNull();
        
        // Verify assessment status updated
        Assessment completedAssessment = assessmentService.getAssessment(assessment.getId());
        assertThat(completedAssessment.getStatus()).isEqualTo(AssessmentStatus.COMPLETED);
    }
    
    @Test
    @DisplayName("Should handle assessment workflow failure gracefully")
    void shouldHandleAssessmentWorkflowFailureGracefully() {
        // Given
        Assessment assessment = createTestAssessment();
        String processInstanceId = assessmentService.startAssessment(assessment.getId());
        
        // When - Simulate task failure
        Task task = taskService.createTaskQuery()
            .processInstanceId(processInstanceId)
            .singleResult();
        
        try {
            taskService.complete(task.getId(), Map.of(
                "simulateFailure", true,
                "errorMessage", "Simulated assessment failure"
            ));
        } catch (Exception e) {
            // Expected failure
        }
        
        // Then - Verify error handling
        List<Job> deadLetterJobs = processEngine.getManagementService()
            .createDeadLetterJobQuery()
            .processInstanceId(processInstanceId)
            .list();
        
        assertThat(deadLetterJobs).isNotEmpty();
        
        // Verify assessment status
        Assessment failedAssessment = assessmentService.getAssessment(assessment.getId());
        assertThat(failedAssessment.getStatus()).isEqualTo(AssessmentStatus.FAILED);
    }
    
    private Map<String, Object> createMockAssessmentResults() {
        return Map.of(
            "controlsAssessed", 25,
            "findingsCount", 3,
            "complianceScore", 0.85,
            "assessmentDate", Instant.now().toString()
        );
    }
    
    private String loadTestResource(String path) {
        try {
            return Files.readString(Paths.get("src/test/resources/" + path));
        } catch (IOException e) {
            throw new RuntimeException("Failed to load test resource: " + path, e);
        }
    }
}
```

## Performance Testing

### Load Testing Configuration

#### PerformanceTestConfiguration.java
```java
@TestConfiguration
public class PerformanceTestConfiguration {
    
    @Bean
    @Profile("performance")
    public LoadTestExecutor loadTestExecutor() {
        return LoadTestExecutor.builder()
            .maxConcurrentUsers(100)
            .rampUpDuration(Duration.ofMinutes(5))
            .testDuration(Duration.ofMinutes(30))
            .build();
    }
    
    @Bean
    @Profile("performance")
    public PerformanceMetricsCollector metricsCollector() {
        return new PerformanceMetricsCollector();
    }
}
```

### Workflow Performance Tests

#### WorkflowPerformanceTest.java
```java
@SpringBootTest
@ActiveProfiles("performance")
@Testcontainers
class WorkflowPerformanceTest {
    
    @Autowired
    private LoadTestExecutor loadTestExecutor;
    
    @Autowired
    private PerformanceMetricsCollector metricsCollector;
    
    @Autowired
    private AssessmentService assessmentService;
    
    @Test
    @DisplayName("Should handle concurrent assessment workflows")
    void shouldHandleConcurrentAssessmentWorkflows() {
        // Given
        int concurrentWorkflows = 50;
        List<Assessment> assessments = createTestAssessments(concurrentWorkflows);
        
        // When
        LoadTestResult result = loadTestExecutor.execute(
            "Concurrent Assessment Workflows",
            concurrentWorkflows,
            () -> {
                Assessment assessment = assessments.get(
                    ThreadLocalRandom.current().nextInt(assessments.size())
                );
                return assessmentService.startAssessment(assessment.getId());
            }
        );
        
        // Then
        assertThat(result.getSuccessRate()).isGreaterThan(0.95);
        assertThat(result.getAverageResponseTime()).isLessThan(Duration.ofSeconds(5));
        assertThat(result.getMaxResponseTime()).isLessThan(Duration.ofSeconds(30));
        
        // Verify system stability
        PerformanceMetrics metrics = metricsCollector.getMetrics();
        assertThat(metrics.getCpuUsage()).isLessThan(0.8);
        assertThat(metrics.getMemoryUsage()).isLessThan(0.8);
        assertThat(metrics.getDatabaseConnectionPoolUsage()).isLessThan(0.9);
    }
    
    @Test
    @DisplayName("Should maintain performance under sustained load")
    void shouldMaintainPerformanceUnderSustainedLoad() {
        // Given
        Duration testDuration = Duration.ofMinutes(10);
        int targetThroughput = 10; // workflows per second
        
        // When
        SustainedLoadTestResult result = loadTestExecutor.executeSustainedLoad(
            "Sustained Assessment Load",
            targetThroughput,
            testDuration,
            this::executeAssessmentWorkflow
        );
        
        // Then
        assertThat(result.getAchievedThroughput()).isGreaterThan(targetThroughput * 0.9);
        assertThat(result.getErrorRate()).isLessThan(0.01);
        
        // Verify no performance degradation over time
        List<PerformanceSnapshot> snapshots = result.getPerformanceSnapshots();
        double initialResponseTime = snapshots.get(0).getAverageResponseTime();
        double finalResponseTime = snapshots.get(snapshots.size() - 1).getAverageResponseTime();
        
        assertThat(finalResponseTime).isLessThan(initialResponseTime * 1.2); // Max 20% degradation
    }
    
    private String executeAssessmentWorkflow() {
        Assessment assessment = createTestAssessment();
        return assessmentService.startAssessment(assessment.getId());
    }
}
```

## Compliance Testing

### OSCAL Compliance Tests

#### OSCALComplianceTest.java
```java
@SpringBootTest
class OSCALComplianceTest {
    
    @Autowired
    private OSCALService oscalService;
    
    @Autowired
    private OSCALValidator oscalValidator;
    
    @Test
    @DisplayName("Should validate OSCAL documents against official schemas")
    void shouldValidateOSCALDocumentsAgainstOfficialSchemas() {
        // Test all OSCAL document types
        Map<OSCALDocumentType, String> testDocuments = Map.of(
            OSCALDocumentType.CATALOG, "oscal/nist-sp-800-53-rev5-catalog.json",
            OSCALDocumentType.PROFILE, "oscal/nist-sp-800-53-rev5-high-profile.json",
            OSCALDocumentType.SYSTEM_SECURITY_PLAN, "oscal/example-ssp.json",
            OSCALDocumentType.ASSESSMENT_PLAN, "oscal/example-assessment-plan.json"
        );
        
        for (Map.Entry<OSCALDocumentType, String> entry : testDocuments.entrySet()) {
            String documentContent = loadTestResource(entry.getValue());
            
            ValidationResult result = oscalValidator.validateDocument(
                documentContent, entry.getKey()
            );
            
            assertThat(result.isValid())
                .as("OSCAL %s document should be valid", entry.getKey())
                .isTrue();
            
            if (!result.isValid()) {
                result.getErrors().forEach(error -> 
                    System.err.println("Validation error: " + error.getMessage())
                );
            }
        }
    }
    
    @Test
    @DisplayName("Should generate compliant OSCAL assessment results")
    void shouldGenerateCompliantOSCALAssessmentResults() {
        // Given
        String assessmentPlanContent = loadTestResource("oscal/test-assessment-plan.json");
        OSCALAssessmentPlan plan = oscalService.parseOSCALAssessmentPlan(assessmentPlanContent);
        
        List<AssessmentResult> mockResults = createMockAssessmentResults();
        
        // When
        OSCALAssessmentResults results = oscalService.createAssessmentResults(
            plan.getUuid(), mockResults
        );
        
        // Then
        assertThat(results).isNotNull();
        assertThat(results.getUuid()).isNotNull();
        assertThat(results.getResults()).isNotEmpty();
        
        // Validate generated results against OSCAL schema
        String resultsJson = oscalService.serializeToJson(results);
        ValidationResult validation = oscalValidator.validateDocument(
            resultsJson, OSCALDocumentType.ASSESSMENT_RESULTS
        );
        
        assertThat(validation.isValid()).isTrue();
    }
}
```

This comprehensive testing strategy ensures the reliability, performance, and compliance of the GRCOS Flowable integration through multiple layers of automated testing.
