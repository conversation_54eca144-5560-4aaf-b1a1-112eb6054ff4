<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProcessEngines (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProcessEngines (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessEngines.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/ProcessEngines.html" target="_top">Frames</a></li>
<li><a href="ProcessEngines.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Class ProcessEngines" class="title">Class ProcessEngines</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.ProcessEngines</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">ProcessEngines</span>
extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Helper for initializing and closing process engines in server environments.
 <br>
 All created <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>s will be registered with this class.
 <br>
 The activiti-webapp-init webapp will
 call the <a href="../../../org/activiti/engine/ProcessEngines.html#init--"><code>init()</code></a> method when the webapp is deployed and it will call the 
 <a href="../../../org/activiti/engine/ProcessEngines.html#destroy--"><code>destroy()</code></a> method when the webapp is destroyed, using a context-listener 
 (<code>org.activiti.impl.servlet.listener.ProcessEnginesServletContextListener</code>).  That way, 
 all applications can just use the <a href="../../../org/activiti/engine/ProcessEngines.html#getProcessEngines--"><code>getProcessEngines()</code></a> to 
 obtain pre-initialized and cached process engines. <br>
 <br>
 Please note that there is <b>no lazy initialization</b> of process engines, so make sure the 
 context-listener is configured or <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>s are already created so they were registered
 on this class.<br>
 <br>
 The <a href="../../../org/activiti/engine/ProcessEngines.html#init--"><code>init()</code></a> method will try to build one <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> for 
 each activiti.cfg.xml file found on the classpath.  If you have more then one,
 make sure you specify different process.engine.name values.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#isInitialized">isInitialized</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#NAME_DEFAULT">NAME_DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#processEngineInfos">processEngineInfos</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#processEngineInfosByName">processEngineInfosByName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#processEngineInfosByResourceUrl">processEngineInfosByResourceUrl</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#processEngines">processEngines</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#ProcessEngines--">ProcessEngines</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#destroy--">destroy</a></span>()</code>
<div class="block">closes all process engines.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#getDefaultProcessEngine--">getDefaultProcessEngine</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#getProcessEngine-java.lang.String-">getProcessEngine</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processEngineName)</code>
<div class="block">obtain a process engine by name.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#getProcessEngineInfo-java.lang.String-">getProcessEngineInfo</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processEngineName)</code>
<div class="block">Get initialization results.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#getProcessEngineInfos--">getProcessEngineInfos</a></span>()</code>
<div class="block">Get initialization results.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#getProcessEngines--">getProcessEngines</a></span>()</code>
<div class="block">provides access to process engine to application clients in a 
 managed server environment.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#init--">init</a></span>()</code>
<div class="block">Initializes all process engines that can be found on the classpath for 
 resources <code>activiti.cfg.xml</code> (plain Activiti style configuration)
 and for resources <code>activiti-context.xml</code> (Spring style configuration).</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#initProcessEngineFromSpringResource-java.net.URL-">initProcessEngineFromSpringResource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/net/URL.html?is-external=true" title="class or interface in java.net">URL</a>&nbsp;resource)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#isInitialized--">isInitialized</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#registerProcessEngine-org.activiti.engine.ProcessEngine-">registerProcessEngine</a></span>(<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;processEngine)</code>
<div class="block">Registers the given process engine.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#retry-java.lang.String-">retry</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceUrl)</code>
<div class="block">retries to initialize a process engine that previously failed.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#setInitialized-boolean-">setInitialized</a></span>(boolean&nbsp;isInitialized)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ProcessEngines.html#unregister-org.activiti.engine.ProcessEngine-">unregister</a></span>(<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;processEngine)</code>
<div class="block">Unregisters the given process engine.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NAME_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME_DEFAULT</h4>
<pre>public static final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> NAME_DEFAULT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.activiti.engine.ProcessEngines.NAME_DEFAULT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="isInitialized">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInitialized</h4>
<pre>protected static&nbsp;boolean isInitialized</pre>
</li>
</ul>
<a name="processEngines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processEngines</h4>
<pre>protected static&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&gt; processEngines</pre>
</li>
</ul>
<a name="processEngineInfosByName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processEngineInfosByName</h4>
<pre>protected static&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&gt; processEngineInfosByName</pre>
</li>
</ul>
<a name="processEngineInfosByResourceUrl">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processEngineInfosByResourceUrl</h4>
<pre>protected static&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&gt; processEngineInfosByResourceUrl</pre>
</li>
</ul>
<a name="processEngineInfos">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>processEngineInfos</h4>
<pre>protected static&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&gt; processEngineInfos</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ProcessEngines--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ProcessEngines</h4>
<pre>public&nbsp;ProcessEngines()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public static&nbsp;void&nbsp;init()</pre>
<div class="block">Initializes all process engines that can be found on the classpath for 
 resources <code>activiti.cfg.xml</code> (plain Activiti style configuration)
 and for resources <code>activiti-context.xml</code> (Spring style configuration).</div>
</li>
</ul>
<a name="initProcessEngineFromSpringResource-java.net.URL-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initProcessEngineFromSpringResource</h4>
<pre>protected static&nbsp;void&nbsp;initProcessEngineFromSpringResource(<a href="http://docs.oracle.com/javase/6/docs/api/java/net/URL.html?is-external=true" title="class or interface in java.net">URL</a>&nbsp;resource)</pre>
</li>
</ul>
<a name="registerProcessEngine-org.activiti.engine.ProcessEngine-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>registerProcessEngine</h4>
<pre>public static&nbsp;void&nbsp;registerProcessEngine(<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;processEngine)</pre>
<div class="block">Registers the given process engine. No <a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine"><code>ProcessEngineInfo</code></a> will be 
 available for this process engine. An engine that is registered will be closed
 when the <a href="../../../org/activiti/engine/ProcessEngines.html#destroy--"><code>destroy()</code></a> is called.</div>
</li>
</ul>
<a name="unregister-org.activiti.engine.ProcessEngine-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unregister</h4>
<pre>public static&nbsp;void&nbsp;unregister(<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;processEngine)</pre>
<div class="block">Unregisters the given process engine.</div>
</li>
</ul>
<a name="getProcessEngineInfos--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessEngineInfos</h4>
<pre>public static&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&gt;&nbsp;getProcessEngineInfos()</pre>
<div class="block">Get initialization results.</div>
</li>
</ul>
<a name="getProcessEngineInfo-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessEngineInfo</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&nbsp;getProcessEngineInfo(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processEngineName)</pre>
<div class="block">Get initialization results. Only info will we available for process engines
 which were added in the <a href="../../../org/activiti/engine/ProcessEngines.html#init--"><code>init()</code></a>. No <a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine"><code>ProcessEngineInfo</code></a>
 is available for engines which were registered programatically.</div>
</li>
</ul>
<a name="getDefaultProcessEngine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultProcessEngine</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;getDefaultProcessEngine()</pre>
</li>
</ul>
<a name="getProcessEngine-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessEngine</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&nbsp;getProcessEngine(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processEngineName)</pre>
<div class="block">obtain a process engine by name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processEngineName</code> - is the name of the process engine or null for the default process engine.</dd>
</dl>
</li>
</ul>
<a name="retry-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>retry</h4>
<pre>public static&nbsp;<a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a>&nbsp;retry(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceUrl)</pre>
<div class="block">retries to initialize a process engine that previously failed.</div>
</li>
</ul>
<a name="getProcessEngines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessEngines</h4>
<pre>public static&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a>&gt;&nbsp;getProcessEngines()</pre>
<div class="block">provides access to process engine to application clients in a 
 managed server environment.</div>
</li>
</ul>
<a name="destroy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>destroy</h4>
<pre>public static&nbsp;void&nbsp;destroy()</pre>
<div class="block">closes all process engines.  This method should be called when the server shuts down.</div>
</li>
</ul>
<a name="isInitialized--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInitialized</h4>
<pre>public static&nbsp;boolean&nbsp;isInitialized()</pre>
</li>
</ul>
<a name="setInitialized-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setInitialized</h4>
<pre>public static&nbsp;void&nbsp;setInitialized(boolean&nbsp;isInitialized)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessEngines.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/ProcessEngines.html" target="_top">Frames</a></li>
<li><a href="ProcessEngines.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
