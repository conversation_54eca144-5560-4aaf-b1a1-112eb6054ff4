<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RepositoryService (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RepositoryService (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":38,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6,"i44":6,"i45":6,"i46":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RepositoryService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/ProcessEngines.html" title="class in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/RepositoryService.html" target="_top">Frames</a></li>
<li><a href="RepositoryService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Interface RepositoryService" class="title">Interface RepositoryService</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">RepositoryService</span></pre>
<div class="block">Service providing access to the repository of process definitions and deployments.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Falko Menge, Tijs Rademakers, Joram Barrez, Henry Yan</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionById-java.lang.String-">activateProcessDefinitionById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Activates the process definition with the given id.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionById-java.lang.String-boolean-java.util.Date-">activateProcessDefinitionById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                             boolean&nbsp;activateProcessInstances,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;activationDate)</code>
<div class="block">Activates the process definition with the given id.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionByKey-java.lang.String-">activateProcessDefinitionByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Activates the process definition with the given key (=id in the bpmn20.xml file).</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-">activateProcessDefinitionByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                              boolean&nbsp;activateProcessInstances,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;activationDate)</code>
<div class="block">Activates the process definition with the given key (=id in the bpmn20.xml file).</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-java.lang.String-">activateProcessDefinitionByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                              boolean&nbsp;activateProcessInstances,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;activationDate,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-"><code>activateProcessDefinitionByKey(String, boolean, Date)</code></a>, but only applicable for the given tenant identifier.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionByKey-java.lang.String-java.lang.String-">activateProcessDefinitionByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionByKey-java.lang.String-"><code>activateProcessDefinitionByKey(String)</code></a>, but only applicable for the given tenant identifier.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#addCandidateStarterGroup-java.lang.String-java.lang.String-">addCandidateStarterGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Authorizes a candidate group for a process definition.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#addCandidateStarterUser-java.lang.String-java.lang.String-">addCandidateStarterUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Authorizes a candidate user for a process definition.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#addModelEditorSource-java.lang.String-byte:A-">addModelEditorSource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId,
                    byte[]&nbsp;bytes)</code>
<div class="block">Saves the model editor source for a model</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#addModelEditorSourceExtra-java.lang.String-byte:A-">addModelEditorSourceExtra</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId,
                         byte[]&nbsp;bytes)</code>
<div class="block">Saves the model editor source extra for a model</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#changeDeploymentTenantId-java.lang.String-java.lang.String-">changeDeploymentTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;newTenantId)</code>
<div class="block">EXPERIMENTAL FEATURE!
 
 Changes the tenant identifier of a deployment to match the given tenant identifier.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#createDeployment--">createDeployment</a></span>()</code>
<div class="block">Starts creating a new deployment</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#createDeploymentQuery--">createDeploymentQuery</a></span>()</code>
<div class="block">Query deployment.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#createModelQuery--">createModelQuery</a></span>()</code>
<div class="block">Query models.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/NativeDeploymentQuery.html" title="interface in org.activiti.engine.repository">NativeDeploymentQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#createNativeDeploymentQuery--">createNativeDeploymentQuery</a></span>()</code>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for deployment.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/NativeModelQuery.html" title="interface in org.activiti.engine.repository">NativeModelQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#createNativeModelQuery--">createNativeModelQuery</a></span>()</code>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for process definitions.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/NativeProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">NativeProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#createNativeProcessDefinitionQuery--">createNativeProcessDefinitionQuery</a></span>()</code>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for process definitions.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#createProcessDefinitionQuery--">createProcessDefinitionQuery</a></span>()</code>
<div class="block">Query process definitions.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#deleteCandidateStarterGroup-java.lang.String-java.lang.String-">deleteCandidateStarterGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Removes the authorization of a candidate group for a process definition.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#deleteCandidateStarterUser-java.lang.String-java.lang.String-">deleteCandidateStarterUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Removes the authorization of a candidate user for a process definition.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#deleteDeployment-java.lang.String-">deleteDeployment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block">Deletes the given deployment.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#deleteDeployment-java.lang.String-boolean-">deleteDeployment</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId,
                boolean&nbsp;cascade)</code>
<div class="block">Deletes the given deployment and cascade deletion to process instances, 
 history process instances and jobs.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#deleteDeploymentCascade-java.lang.String-">deleteDeploymentCascade</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">use <a href="../../../org/activiti/engine/RepositoryService.html#deleteDeployment-java.lang.String-boolean-"><code>deleteDeployment(String, boolean)</code></a>.  This methods may be deleted from 5.3.</span></div>
</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#deleteModel-java.lang.String-">deleteModel</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>org.activiti.bpmn.model.BpmnModel</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getBpmnModel-java.lang.String-">getBpmnModel</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Returns the <code>BpmnModel</code> corresponding with the process definition with
 the provided process definition id.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getDeploymentResourceNames-java.lang.String-">getDeploymentResourceNames</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block">Retrieves a list of deployment resources for the given deployment, 
 ordered alphabetically.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task">IdentityLink</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getIdentityLinksForProcessDefinition-java.lang.String-">getIdentityLinksForProcessDefinition</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a>s associated with the given process definition.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getModel-java.lang.String-">getModel</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</code>
<div class="block">Returns the <a href="../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository"><code>Model</code></a></div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getModelEditorSource-java.lang.String-">getModelEditorSource</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</code>
<div class="block">Returns the model editor source as a byte array</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getModelEditorSourceExtra-java.lang.String-">getModelEditorSourceExtra</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</code>
<div class="block">Returns the model editor source extra as a byte array</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository">ProcessDefinition</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getProcessDefinition-java.lang.String-">getProcessDefinition</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Returns the <a href="../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><code>ProcessDefinition</code></a> including all BPMN information like additional 
 Properties (e.g.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getProcessDiagram-java.lang.String-">getProcessDiagram</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Gives access to a deployed process diagram, e.g., a PNG image, through a
 stream of bytes.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/DiagramLayout.html" title="class in org.activiti.engine.repository">DiagramLayout</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getProcessDiagramLayout-java.lang.String-">getProcessDiagramLayout</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Provides positions and dimensions of elements in a process diagram as
 provided by <a href="../../../org/activiti/engine/RepositoryService.html#getProcessDiagram-java.lang.String-"><code>getProcessDiagram(String)</code></a>.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getProcessModel-java.lang.String-">getProcessModel</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Gives access to a deployed process model, e.g., a BPMN 2.0 XML file,
 through a stream of bytes.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#getResourceAsStream-java.lang.String-java.lang.String-">getResourceAsStream</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName)</code>
<div class="block">Gives access to a deployment resource through a stream of bytes.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#isProcessDefinitionSuspended-java.lang.String-">isProcessDefinitionSuspended</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Checks if the process definition is suspended.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#newModel--">newModel</a></span>()</code>
<div class="block">Creates a new model.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#saveModel-org.activiti.engine.repository.Model-">saveModel</a></span>(<a href="../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository">Model</a>&nbsp;model)</code>
<div class="block">Saves the model.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#setDeploymentCategory-java.lang.String-java.lang.String-">setDeploymentCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</code>
<div class="block">Sets the category of the deployment.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#setProcessDefinitionCategory-java.lang.String-java.lang.String-">setProcessDefinitionCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</code>
<div class="block">Sets the category of the process definition.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionById-java.lang.String-">suspendProcessDefinitionById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Suspends the process definition with the given id.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionById-java.lang.String-boolean-java.util.Date-">suspendProcessDefinitionById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                            boolean&nbsp;suspendProcessInstances,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;suspensionDate)</code>
<div class="block">Suspends the process definition with the given id.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionByKey-java.lang.String-">suspendProcessDefinitionByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Suspends the <strong>all</strong> process definitions with the given key (= id in the bpmn20.xml file).</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-">suspendProcessDefinitionByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                             boolean&nbsp;suspendProcessInstances,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;suspensionDate)</code>
<div class="block">Suspends the <strong>all</strong> process definitions with the given key (= id in the bpmn20.xml file).</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-java.lang.String-">suspendProcessDefinitionByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                             boolean&nbsp;suspendProcessInstances,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;suspensionDate,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-"><code>suspendProcessDefinitionByKey(String, boolean, Date)</code></a>, but only applicable for the given tenant identifier.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionByKey-java.lang.String-java.lang.String-">suspendProcessDefinitionByKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Similar to <a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionByKey-java.lang.String-"><code>suspendProcessDefinitionByKey(String)</code></a>, but only applicable for the given tenant identifier.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.validation.ValidationError&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/RepositoryService.html#validateProcess-org.activiti.bpmn.model.BpmnModel-">validateProcess</a></span>(org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</code>
<div class="block">Validates the given process definition against the rules for executing a process definition
 on the Activiti engine.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createDeployment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDeployment</h4>
<pre><a href="../../../org/activiti/engine/repository/DeploymentBuilder.html" title="interface in org.activiti.engine.repository">DeploymentBuilder</a>&nbsp;createDeployment()</pre>
<div class="block">Starts creating a new deployment</div>
</li>
</ul>
<a name="deleteDeployment-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteDeployment</h4>
<pre>void&nbsp;deleteDeployment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</pre>
<div class="block">Deletes the given deployment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>deploymentId</code> - id of the deployment, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="deleteDeploymentCascade-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteDeploymentCascade</h4>
<pre>void&nbsp;deleteDeploymentCascade(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">use <a href="../../../org/activiti/engine/RepositoryService.html#deleteDeployment-java.lang.String-boolean-"><code>deleteDeployment(String, boolean)</code></a>.  This methods may be deleted from 5.3.</span></div>
<div class="block">Deletes the given deployment and cascade deletion to process instances, 
 history process instances and jobs.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>deploymentId</code> - id of the deployment, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="deleteDeployment-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteDeployment</h4>
<pre>void&nbsp;deleteDeployment(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId,
                      boolean&nbsp;cascade)</pre>
<div class="block">Deletes the given deployment and cascade deletion to process instances, 
 history process instances and jobs.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>deploymentId</code> - id of the deployment, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="setDeploymentCategory-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeploymentCategory</h4>
<pre>void&nbsp;setDeploymentCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</pre>
<div class="block">Sets the category of the deployment.
 Deployments can be queried by category: see <a href="../../../org/activiti/engine/repository/DeploymentQuery.html#deploymentCategory-java.lang.String-"><code>DeploymentQuery.deploymentCategory(String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no deployment with the provided id can be found.</dd>
</dl>
</li>
</ul>
<a name="getDeploymentResourceNames-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeploymentResourceNames</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getDeploymentResourceNames(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</pre>
<div class="block">Retrieves a list of deployment resources for the given deployment, 
 ordered alphabetically.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>deploymentId</code> - id of the deployment, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="getResourceAsStream-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceAsStream</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;getResourceAsStream(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName)</pre>
<div class="block">Gives access to a deployment resource through a stream of bytes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>deploymentId</code> - id of the deployment, cannot be null.</dd>
<dd><code>resourceName</code> - name of the resource, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the resource doesn't exist in the given deployment or when no deployment exists
 for the given deploymentId.</dd>
</dl>
</li>
</ul>
<a name="changeDeploymentTenantId-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeDeploymentTenantId</h4>
<pre>void&nbsp;changeDeploymentTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;newTenantId)</pre>
<div class="block">EXPERIMENTAL FEATURE!
 
 Changes the tenant identifier of a deployment to match the given tenant identifier.
 This change will cascade to any related entity:
 - process definitions related to the deployment
 - process instances related to those process definitions
 - executions related to those process instances
 - tasks related to those process instances
 - jobs related to the process definitions and process instances
 
 This method can be used in the case that there was no tenant identifier set
 on the deployment or those entities before.
 
 This method can be used to remove a tenant identifier from the 
 deployment and related entities (simply pass null).
 
 Important: no optimistic locking will be done while executing the tenant identifier change!
 
 This is an experimental feature, mainly because it WILL NOT work
 properly in a clustered environment without special care:
 suppose some process instance is in flight. The process definition is in the
 process definition cache. When a task or job is created when continuing the process
 instance, the process definition cache will be consulted to get the process definition
 and from it the tenant identifier. Since it's cached, it will not be the new tenant identifier.
 This method does clear the cache for this engineinstance , but it will not be cleared
 on other nodes in a cluster (unless using a shared process definition cache).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>deploymentId</code> - The id of the deployment of which the tenant identifier will be changed.</dd>
<dd><code>newTenantId</code> - The new tenant identifier.</dd>
</dl>
</li>
</ul>
<a name="createProcessDefinitionQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProcessDefinitionQuery</h4>
<pre><a href="../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;createProcessDefinitionQuery()</pre>
<div class="block">Query process definitions.</div>
</li>
</ul>
<a name="createNativeProcessDefinitionQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeProcessDefinitionQuery</h4>
<pre><a href="../../../org/activiti/engine/repository/NativeProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">NativeProcessDefinitionQuery</a>&nbsp;createNativeProcessDefinitionQuery()</pre>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for process definitions.</div>
</li>
</ul>
<a name="createDeploymentQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDeploymentQuery</h4>
<pre><a href="../../../org/activiti/engine/repository/DeploymentQuery.html" title="interface in org.activiti.engine.repository">DeploymentQuery</a>&nbsp;createDeploymentQuery()</pre>
<div class="block">Query deployment.</div>
</li>
</ul>
<a name="createNativeDeploymentQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeDeploymentQuery</h4>
<pre><a href="../../../org/activiti/engine/repository/NativeDeploymentQuery.html" title="interface in org.activiti.engine.repository">NativeDeploymentQuery</a>&nbsp;createNativeDeploymentQuery()</pre>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for deployment.</div>
</li>
</ul>
<a name="suspendProcessDefinitionById-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suspendProcessDefinitionById</h4>
<pre>void&nbsp;suspendProcessDefinitionById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Suspends the process definition with the given id. 
 
 If a process definition is in state suspended, it will not be possible to start new process instances
 based on the process definition.
 
 <strong>Note: all the process instances of the process definition will still be active 
 (ie. not suspended)!</strong></div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processDefinition can be found</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the process definition is already in state suspended.</dd>
</dl>
</li>
</ul>
<a name="suspendProcessDefinitionById-java.lang.String-boolean-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suspendProcessDefinitionById</h4>
<pre>void&nbsp;suspendProcessDefinitionById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                  boolean&nbsp;suspendProcessInstances,
                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;suspensionDate)</pre>
<div class="block">Suspends the process definition with the given id. 
 
 If a process definition is in state suspended, it will not be possible to start new process instances
 based on the process definition.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>suspendProcessInstances</code> - If true, all the process instances of the provided process definition
                                will be suspended too.</dd>
<dd><code>suspensionDate</code> - The date on which the process definition will be suspended. If null, the 
                       process definition is suspended immediately. 
                       Note: The job executor needs to be active to use this!</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processDefinition can be found.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the process definition is already in state suspended.</dd>
</dl>
</li>
</ul>
<a name="suspendProcessDefinitionByKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suspendProcessDefinitionByKey</h4>
<pre>void&nbsp;suspendProcessDefinitionByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</pre>
<div class="block">Suspends the <strong>all</strong> process definitions with the given key (= id in the bpmn20.xml file).
 
 If a process definition is in state suspended, it will not be possible to start new process instances
 based on the process definition.
 
 <strong>Note: all the process instances of the process definition will still be active 
 (ie. not suspended)!</strong></div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processDefinition can be found</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the process definition is already in state suspended.</dd>
</dl>
</li>
</ul>
<a name="suspendProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suspendProcessDefinitionByKey</h4>
<pre>void&nbsp;suspendProcessDefinitionByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                   boolean&nbsp;suspendProcessInstances,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;suspensionDate)</pre>
<div class="block">Suspends the <strong>all</strong> process definitions with the given key (= id in the bpmn20.xml file).
 
 If a process definition is in state suspended, it will not be possible to start new process instances
 based on the process definition.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>suspendProcessInstances</code> - If true, all the process instances of the provided process definition
                                will be suspended too.</dd>
<dd><code>suspensionDate</code> - The date on which the process definition will be suspended. If null, the 
                       process definition is suspended immediately. 
                       Note: The job executor needs to be active to use this!</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processDefinition can be found</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the process definition is already in state suspended.</dd>
</dl>
</li>
</ul>
<a name="suspendProcessDefinitionByKey-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suspendProcessDefinitionByKey</h4>
<pre>void&nbsp;suspendProcessDefinitionByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionByKey-java.lang.String-"><code>suspendProcessDefinitionByKey(String)</code></a>, but only applicable for the given tenant identifier.</div>
</li>
</ul>
<a name="suspendProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suspendProcessDefinitionByKey</h4>
<pre>void&nbsp;suspendProcessDefinitionByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                   boolean&nbsp;suspendProcessInstances,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;suspensionDate,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RepositoryService.html#suspendProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-"><code>suspendProcessDefinitionByKey(String, boolean, Date)</code></a>, but only applicable for the given tenant identifier.</div>
</li>
</ul>
<a name="activateProcessDefinitionById-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activateProcessDefinitionById</h4>
<pre>void&nbsp;activateProcessDefinitionById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Activates the process definition with the given id.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processDefinition can be found or if the process definition is already in state active.</dd>
</dl>
</li>
</ul>
<a name="activateProcessDefinitionById-java.lang.String-boolean-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activateProcessDefinitionById</h4>
<pre>void&nbsp;activateProcessDefinitionById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                   boolean&nbsp;activateProcessInstances,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;activationDate)</pre>
<div class="block">Activates the process definition with the given id.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>activationDate</code> - The date on which the process definition will be activated. If null, the
                       process definition is activated immediately. 
                       Note: The job executor needs to be active to use this!</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processDefinition can be found.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the process definition is already in state active.</dd>
</dl>
</li>
</ul>
<a name="activateProcessDefinitionByKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activateProcessDefinitionByKey</h4>
<pre>void&nbsp;activateProcessDefinitionByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</pre>
<div class="block">Activates the process definition with the given key (=id in the bpmn20.xml file).</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processDefinition can be found.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the process definition is already in state active.</dd>
</dl>
</li>
</ul>
<a name="activateProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activateProcessDefinitionByKey</h4>
<pre>void&nbsp;activateProcessDefinitionByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                    boolean&nbsp;activateProcessInstances,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;activationDate)</pre>
<div class="block">Activates the process definition with the given key (=id in the bpmn20.xml file).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>activationDate</code> - The date on which the process definition will be activated. If null, the
                       process definition is activated immediately. 
                       Note: The job executor needs to be active to use this!</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no such processDefinition can be found.</dd>
<dd><code><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></code> - if the process definition is already in state active.</dd>
</dl>
</li>
</ul>
<a name="activateProcessDefinitionByKey-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activateProcessDefinitionByKey</h4>
<pre>void&nbsp;activateProcessDefinitionByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionByKey-java.lang.String-"><code>activateProcessDefinitionByKey(String)</code></a>, but only applicable for the given tenant identifier.</div>
</li>
</ul>
<a name="activateProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activateProcessDefinitionByKey</h4>
<pre>void&nbsp;activateProcessDefinitionByKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey,
                                    boolean&nbsp;activateProcessInstances,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;activationDate,
                                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Similar to <a href="../../../org/activiti/engine/RepositoryService.html#activateProcessDefinitionByKey-java.lang.String-boolean-java.util.Date-"><code>activateProcessDefinitionByKey(String, boolean, Date)</code></a>, but only applicable for the given tenant identifier.</div>
</li>
</ul>
<a name="setProcessDefinitionCategory-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProcessDefinitionCategory</h4>
<pre>void&nbsp;setProcessDefinitionCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</pre>
<div class="block">Sets the category of the process definition.
 Process definitions can be queried by category: see <a href="../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionCategory-java.lang.String-"><code>ProcessDefinitionQuery.processDefinitionCategory(String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - if no process defintion with the provided id can be found.</dd>
</dl>
</li>
</ul>
<a name="getProcessModel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessModel</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;getProcessModel(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Gives access to a deployed process model, e.g., a BPMN 2.0 XML file,
 through a stream of bytes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - id of a <a href="../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><code>ProcessDefinition</code></a>, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the process model doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="getProcessDiagram-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDiagram</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;getProcessDiagram(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Gives access to a deployed process diagram, e.g., a PNG image, through a
 stream of bytes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - id of a <a href="../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><code>ProcessDefinition</code></a>, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>null when the diagram resource name of a <a href="../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><code>ProcessDefinition</code></a> is null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the process diagram doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="getProcessDefinition-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDefinition</h4>
<pre><a href="../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository">ProcessDefinition</a>&nbsp;getProcessDefinition(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Returns the <a href="../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><code>ProcessDefinition</code></a> including all BPMN information like additional 
 Properties (e.g. documentation).</div>
</li>
</ul>
<a name="getBpmnModel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBpmnModel</h4>
<pre>org.activiti.bpmn.model.BpmnModel&nbsp;getBpmnModel(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Returns the <code>BpmnModel</code> corresponding with the process definition with
 the provided process definition id. The <code>BpmnModel</code> is a pojo versions
 of the BPMN 2.0 xml and can be used to introspect the process definition
 using regular Java.</div>
</li>
</ul>
<a name="isProcessDefinitionSuspended-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isProcessDefinitionSuspended</h4>
<pre>boolean&nbsp;isProcessDefinitionSuspended(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Checks if the process definition is suspended.</div>
</li>
</ul>
<a name="getProcessDiagramLayout-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDiagramLayout</h4>
<pre><a href="../../../org/activiti/engine/repository/DiagramLayout.html" title="class in org.activiti.engine.repository">DiagramLayout</a>&nbsp;getProcessDiagramLayout(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Provides positions and dimensions of elements in a process diagram as
 provided by <a href="../../../org/activiti/engine/RepositoryService.html#getProcessDiagram-java.lang.String-"><code>getProcessDiagram(String)</code></a>.

 This method requires a process model and a diagram image to be deployed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - id of a <a href="../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><code>ProcessDefinition</code></a>, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Map with process element ids as keys and positions and dimensions as values.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the process model or diagram doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="newModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newModel</h4>
<pre><a href="../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository">Model</a>&nbsp;newModel()</pre>
<div class="block">Creates a new model. The model is transient and must be saved using 
 <a href="../../../org/activiti/engine/RepositoryService.html#saveModel-org.activiti.engine.repository.Model-"><code>saveModel(Model)</code></a>.</div>
</li>
</ul>
<a name="saveModel-org.activiti.engine.repository.Model-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveModel</h4>
<pre>void&nbsp;saveModel(<a href="../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository">Model</a>&nbsp;model)</pre>
<div class="block">Saves the model. If the model already existed, the model is updated
 otherwise a new model is created.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - model to save, cannot be null.</dd>
</dl>
</li>
</ul>
<a name="deleteModel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteModel</h4>
<pre>void&nbsp;deleteModel(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelId</code> - id of model to delete, cannot be null. When an id is passed
 for an unexisting model, this operation is ignored.</dd>
</dl>
</li>
</ul>
<a name="addModelEditorSource-java.lang.String-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addModelEditorSource</h4>
<pre>void&nbsp;addModelEditorSource(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId,
                          byte[]&nbsp;bytes)</pre>
<div class="block">Saves the model editor source for a model</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelId</code> - id of model to delete, cannot be null. When an id is passed
 for an unexisting model, this operation is ignored.</dd>
</dl>
</li>
</ul>
<a name="addModelEditorSourceExtra-java.lang.String-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addModelEditorSourceExtra</h4>
<pre>void&nbsp;addModelEditorSourceExtra(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId,
                               byte[]&nbsp;bytes)</pre>
<div class="block">Saves the model editor source extra for a model</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelId</code> - id of model to delete, cannot be null. When an id is passed
 for an unexisting model, this operation is ignored.</dd>
</dl>
</li>
</ul>
<a name="createModelQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createModelQuery</h4>
<pre><a href="../../../org/activiti/engine/repository/ModelQuery.html" title="interface in org.activiti.engine.repository">ModelQuery</a>&nbsp;createModelQuery()</pre>
<div class="block">Query models.</div>
</li>
</ul>
<a name="createNativeModelQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeModelQuery</h4>
<pre><a href="../../../org/activiti/engine/repository/NativeModelQuery.html" title="interface in org.activiti.engine.repository">NativeModelQuery</a>&nbsp;createNativeModelQuery()</pre>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for process definitions.</div>
</li>
</ul>
<a name="getModel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModel</h4>
<pre><a href="../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository">Model</a>&nbsp;getModel(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</pre>
<div class="block">Returns the <a href="../../../org/activiti/engine/repository/Model.html" title="interface in org.activiti.engine.repository"><code>Model</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelId</code> - id of model</dd>
</dl>
</li>
</ul>
<a name="getModelEditorSource-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelEditorSource</h4>
<pre>byte[]&nbsp;getModelEditorSource(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</pre>
<div class="block">Returns the model editor source as a byte array</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelId</code> - id of model</dd>
</dl>
</li>
</ul>
<a name="getModelEditorSourceExtra-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelEditorSourceExtra</h4>
<pre>byte[]&nbsp;getModelEditorSourceExtra(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;modelId)</pre>
<div class="block">Returns the model editor source extra as a byte array</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelId</code> - id of model</dd>
</dl>
</li>
</ul>
<a name="addCandidateStarterUser-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCandidateStarterUser</h4>
<pre>void&nbsp;addCandidateStarterUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Authorizes a candidate user for a process definition.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - id of the process definition, cannot be null.</dd>
<dd><code>userId</code> - id of the user involve, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the process definition or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="addCandidateStarterGroup-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCandidateStarterGroup</h4>
<pre>void&nbsp;addCandidateStarterGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                              <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Authorizes a candidate group for a process definition.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - id of the process definition, cannot be null.</dd>
<dd><code>groupId</code> - id of the group involve, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the process definition or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteCandidateStarterUser-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteCandidateStarterUser</h4>
<pre>void&nbsp;deleteCandidateStarterUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Removes the authorization of a candidate user for a process definition.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - id of the process definition, cannot be null.</dd>
<dd><code>userId</code> - id of the user involve, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the process definition or user doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="deleteCandidateStarterGroup-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteCandidateStarterGroup</h4>
<pre>void&nbsp;deleteCandidateStarterGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Removes the authorization of a candidate group for a process definition.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - id of the process definition, cannot be null.</dd>
<dd><code>groupId</code> - id of the group involve, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when the process definition or group doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="getIdentityLinksForProcessDefinition-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIdentityLinksForProcessDefinition</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task">IdentityLink</a>&gt;&nbsp;getIdentityLinksForProcessDefinition(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a>s associated with the given process definition.
 Such an <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a> informs how a certain identity (eg. group or user)
 is authorized for a certain process definition</div>
</li>
</ul>
<a name="validateProcess-org.activiti.bpmn.model.BpmnModel-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>validateProcess</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.validation.ValidationError&gt;&nbsp;validateProcess(org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</pre>
<div class="block">Validates the given process definition against the rules for executing a process definition
 on the Activiti engine.
 
 To create such a <code>BpmnModel</code> from a String, following code may be used:
 
 XMLInputFactory xif = XMLInputFactory.newInstance();
 InputStreamReader in = new InputStreamReader(new ByteArrayInputStream(myProcess.getBytes()), "UTF-8"); // Change to other streams for eg from classpath
 XMLStreamReader xtr = xif.createXMLStreamReader(in);
 bpmnModel = new BpmnXMLConverter().convertToBpmnModel(xtr);</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RepositoryService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/ProcessEngines.html" title="class in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/RepositoryService.html" target="_top">Frames</a></li>
<li><a href="RepositoryService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
