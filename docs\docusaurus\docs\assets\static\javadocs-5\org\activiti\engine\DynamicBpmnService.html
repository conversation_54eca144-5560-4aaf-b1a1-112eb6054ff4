<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DynamicBpmnService (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DynamicBpmnService (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DynamicBpmnService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/DynamicBpmnService.html" target="_top">Frames</a></li>
<li><a href="DynamicBpmnService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Interface DynamicBpmnService" class="title">Interface DynamicBpmnService</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">DynamicBpmnService</span></pre>
<div class="block">Service providing access to the repository of process definitions and deployments.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tijs Rademakers</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeLocalizationDescription-java.lang.String-java.lang.String-java.lang.String-">changeLocalizationDescription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeLocalizationDescription-java.lang.String-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeLocalizationDescription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
                             com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeLocalizationName-java.lang.String-java.lang.String-java.lang.String-">changeLocalizationName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeLocalizationName-java.lang.String-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeLocalizationName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
                      com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeScriptTaskScript-java.lang.String-java.lang.String-">changeScriptTaskScript</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;script)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeScriptTaskScript-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeScriptTaskScript</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;script,
                      com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeSequenceFlowCondition-java.lang.String-java.lang.String-">changeSequenceFlowCondition</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;condition)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeSequenceFlowCondition-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeSequenceFlowCondition</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;condition,
                           com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeServiceTaskClassName-java.lang.String-java.lang.String-">changeServiceTaskClassName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;className)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeServiceTaskClassName-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeServiceTaskClassName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;className,
                          com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeServiceTaskDelegateExpression-java.lang.String-java.lang.String-">changeServiceTaskDelegateExpression</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeServiceTaskDelegateExpression-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeServiceTaskDelegateExpression</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
                                   com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeServiceTaskExpression-java.lang.String-java.lang.String-">changeServiceTaskExpression</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeServiceTaskExpression-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeServiceTaskExpression</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
                           com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskAssignee-java.lang.String-java.lang.String-">changeUserTaskAssignee</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskAssignee-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskAssignee</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee,
                      com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCandidateGroup-java.lang.String-java.lang.String-boolean-">changeUserTaskCandidateGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateGroup,
                            boolean&nbsp;overwriteOtherChangedEntries)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCandidateGroup-java.lang.String-java.lang.String-boolean-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskCandidateGroup</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateGroup,
                            boolean&nbsp;overwriteOtherChangedEntries,
                            com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCandidateGroups-java.lang.String-java.util.List-">changeUserTaskCandidateGroups</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateGroups)</code>
<div class="block">Creates a new processDefinitionInfo with <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> for the given BPMN element.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCandidateGroups-java.lang.String-java.util.List-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskCandidateGroups</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateGroups,
                             com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>
<div class="block">Updates a processDefinitionInfo's <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> with the new list.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCandidateUser-java.lang.String-java.lang.String-boolean-">changeUserTaskCandidateUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateUser,
                           boolean&nbsp;overwriteOtherChangedEntries)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCandidateUser-java.lang.String-java.lang.String-boolean-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskCandidateUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateUser,
                           boolean&nbsp;overwriteOtherChangedEntries,
                           com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCandidateUsers-java.lang.String-java.util.List-">changeUserTaskCandidateUsers</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateUsers)</code>
<div class="block">Creates a new processDefinitionInfo with <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> for the given BPMN element.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCandidateUsers-java.lang.String-java.util.List-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskCandidateUsers</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateUsers,
                            com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>
<div class="block">Updates a processDefinitionInfo's <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> with the new list.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCategory-java.lang.String-java.lang.String-">changeUserTaskCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskCategory-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category,
                      com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskDescription-java.lang.String-java.lang.String-">changeUserTaskDescription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskDescription-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskDescription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
                         com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskDueDate-java.lang.String-java.lang.String-">changeUserTaskDueDate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dueDate)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskDueDate-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskDueDate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dueDate,
                     com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskFormKey-java.lang.String-java.lang.String-">changeUserTaskFormKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;formKey)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskFormKey-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskFormKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;formKey,
                     com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskName-java.lang.String-java.lang.String-">changeUserTaskName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskName-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                  com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskOwner-java.lang.String-java.lang.String-">changeUserTaskOwner</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskOwner-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskOwner</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner,
                   com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskPriority-java.lang.String-java.lang.String-">changeUserTaskPriority</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;priority)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#changeUserTaskPriority-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">changeUserTaskPriority</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;priority,
                      com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#getBpmnElementProperties-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">getBpmnElementProperties</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                        com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html" title="class in org.activiti.engine.dynamic">DynamicProcessDefinitionSummary</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#getDynamicProcessDefinitionSummary-java.lang.String-">getDynamicProcessDefinitionSummary</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Gives a summary between the <code>BpmnModel</code> and <a href="../../../org/activiti/engine/DynamicBpmnService.html#getProcessDefinitionInfo-java.lang.String-"><code>getProcessDefinitionInfo(String)</code></a></div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#getLocalizationElementProperties-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">getLocalizationElementProperties</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.node.ObjectNode</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#getProcessDefinitionInfo-java.lang.String-">getProcessDefinitionInfo</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#resetProperty-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">resetProperty</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;elementId,
             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;property,
             com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>
<div class="block">
  Clears the field from the infoNode.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/DynamicBpmnService.html#saveProcessDefinitionInfo-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">saveProcessDefinitionInfo</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                         com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getProcessDefinitionInfo-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessDefinitionInfo</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;getProcessDefinitionInfo(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
</li>
</ul>
<a name="saveProcessDefinitionInfo-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveProcessDefinitionInfo</h4>
<pre>void&nbsp;saveProcessDefinitionInfo(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId,
                               com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeServiceTaskClassName-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeServiceTaskClassName</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeServiceTaskClassName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;className)</pre>
</li>
</ul>
<a name="changeServiceTaskClassName-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeServiceTaskClassName</h4>
<pre>void&nbsp;changeServiceTaskClassName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;className,
                                com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeServiceTaskExpression-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeServiceTaskExpression</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeServiceTaskExpression(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)</pre>
</li>
</ul>
<a name="changeServiceTaskExpression-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeServiceTaskExpression</h4>
<pre>void&nbsp;changeServiceTaskExpression(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
                                 com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeServiceTaskDelegateExpression-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeServiceTaskDelegateExpression</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeServiceTaskDelegateExpression(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)</pre>
</li>
</ul>
<a name="changeServiceTaskDelegateExpression-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeServiceTaskDelegateExpression</h4>
<pre>void&nbsp;changeServiceTaskDelegateExpression(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
                                         com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeScriptTaskScript-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeScriptTaskScript</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeScriptTaskScript(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;script)</pre>
</li>
</ul>
<a name="changeScriptTaskScript-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeScriptTaskScript</h4>
<pre>void&nbsp;changeScriptTaskScript(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;script,
                            com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskName-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskName</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
</li>
</ul>
<a name="changeUserTaskName-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskName</h4>
<pre>void&nbsp;changeUserTaskName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                        com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskDescription-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskDescription</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskDescription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</pre>
</li>
</ul>
<a name="changeUserTaskDescription-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskDescription</h4>
<pre>void&nbsp;changeUserTaskDescription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
                               com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskDueDate-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskDueDate</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskDueDate(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dueDate)</pre>
</li>
</ul>
<a name="changeUserTaskDueDate-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskDueDate</h4>
<pre>void&nbsp;changeUserTaskDueDate(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dueDate,
                           com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskPriority-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskPriority</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskPriority(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;priority)</pre>
</li>
</ul>
<a name="changeUserTaskPriority-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskPriority</h4>
<pre>void&nbsp;changeUserTaskPriority(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;priority,
                            com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskCategory-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCategory</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</pre>
</li>
</ul>
<a name="changeUserTaskCategory-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCategory</h4>
<pre>void&nbsp;changeUserTaskCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category,
                            com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskFormKey-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskFormKey</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskFormKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;formKey)</pre>
</li>
</ul>
<a name="changeUserTaskFormKey-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskFormKey</h4>
<pre>void&nbsp;changeUserTaskFormKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;formKey,
                           com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskAssignee-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskAssignee</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskAssignee(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee)</pre>
</li>
</ul>
<a name="changeUserTaskAssignee-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskAssignee</h4>
<pre>void&nbsp;changeUserTaskAssignee(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee,
                            com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskOwner-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskOwner</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskOwner(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner)</pre>
</li>
</ul>
<a name="changeUserTaskOwner-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskOwner</h4>
<pre>void&nbsp;changeUserTaskOwner(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner,
                         com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskCandidateUser-java.lang.String-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCandidateUser</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskCandidateUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateUser,
                                                                           boolean&nbsp;overwriteOtherChangedEntries)</pre>
</li>
</ul>
<a name="changeUserTaskCandidateUser-java.lang.String-java.lang.String-boolean-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCandidateUser</h4>
<pre>void&nbsp;changeUserTaskCandidateUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateUser,
                                 boolean&nbsp;overwriteOtherChangedEntries,
                                 com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskCandidateGroup-java.lang.String-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCandidateGroup</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskCandidateGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateGroup,
                                                                            boolean&nbsp;overwriteOtherChangedEntries)</pre>
</li>
</ul>
<a name="changeUserTaskCandidateGroup-java.lang.String-java.lang.String-boolean-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCandidateGroup</h4>
<pre>void&nbsp;changeUserTaskCandidateGroup(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;candidateGroup,
                                  boolean&nbsp;overwriteOtherChangedEntries,
                                  com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeUserTaskCandidateUsers-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCandidateUsers</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskCandidateUsers(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                            <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateUsers)</pre>
<div class="block">Creates a new processDefinitionInfo with <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> for the given BPMN element.

 <p color="red">
     Don't forget to call <a href="../../../org/activiti/engine/DynamicBpmnService.html#saveProcessDefinitionInfo-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-"><code>saveProcessDefinitionInfo(String, ObjectNode)</code></a>
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the bpmn element id (ex. sid-3392FDEE-DD6F-484E-97FE-55F30BFEA77E)</dd>
<dd><code>candidateUsers</code> - the candidate users.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a new processDefinitionNode with the candidate users for the given bpmn element.</dd>
</dl>
</li>
</ul>
<a name="changeUserTaskCandidateUsers-java.lang.String-java.util.List-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCandidateUsers</h4>
<pre>void&nbsp;changeUserTaskCandidateUsers(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                  <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateUsers,
                                  com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
<div class="block">Updates a processDefinitionInfo's <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> with the new list.
 Previous values for the BPMN Element with <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> as key are ignored.

 <p color="red">
     Don't forget to call <a href="../../../org/activiti/engine/DynamicBpmnService.html#saveProcessDefinitionInfo-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-"><code>saveProcessDefinitionInfo(String, ObjectNode)</code></a>
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the bpmn element id (ex. sid-3392FDEE-DD6F-484E-97FE-55F30BFEA77E)</dd>
<dd><code>candidateUsers</code> - the candidate users.</dd>
<dd><code>infoNode</code> - the current processDefinitionInfo. This object will be modified.</dd>
</dl>
</li>
</ul>
<a name="changeUserTaskCandidateGroups-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCandidateGroups</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeUserTaskCandidateGroups(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateGroups)</pre>
<div class="block">Creates a new processDefinitionInfo with <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> for the given BPMN element.

 <p color="red">
     Don't forget to call <a href="../../../org/activiti/engine/DynamicBpmnService.html#saveProcessDefinitionInfo-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-"><code>saveProcessDefinitionInfo(String, ObjectNode)</code></a>
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the bpmn element id (ex. sid-3392FDEE-DD6F-484E-97FE-55F30BFEA77E)</dd>
<dd><code>candidateGroups</code> - the candidate groups.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a new processDefinitionNode with the candidate users for the given bpmn element.</dd>
</dl>
</li>
</ul>
<a name="changeUserTaskCandidateGroups-java.lang.String-java.util.List-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeUserTaskCandidateGroups</h4>
<pre>void&nbsp;changeUserTaskCandidateGroups(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;candidateGroups,
                                   com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
<div class="block">Updates a processDefinitionInfo's <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> with the new list.
 Previous values for the BPMN Element with <a href="../../../org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS"><code>DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS</code></a> as key are ignored.

 <p color="red">
     Don't forget to call <a href="../../../org/activiti/engine/DynamicBpmnService.html#saveProcessDefinitionInfo-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-"><code>saveProcessDefinitionInfo(String, ObjectNode)</code></a>
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the bpmn element id (ex. sid-3392FDEE-DD6F-484E-97FE-55F30BFEA77E)</dd>
<dd><code>candidateGroups</code> - the candidate groups.</dd>
<dd><code>infoNode</code> - the current processDefinitionInfo. This object will be modified.</dd>
</dl>
</li>
</ul>
<a name="changeSequenceFlowCondition-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeSequenceFlowCondition</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeSequenceFlowCondition(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;condition)</pre>
</li>
</ul>
<a name="changeSequenceFlowCondition-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeSequenceFlowCondition</h4>
<pre>void&nbsp;changeSequenceFlowCondition(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;condition,
                                 com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="getBpmnElementProperties-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBpmnElementProperties</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;getBpmnElementProperties(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                        com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeLocalizationName-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeLocalizationName</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeLocalizationName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                      <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
</li>
</ul>
<a name="changeLocalizationName-java.lang.String-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeLocalizationName</h4>
<pre>void&nbsp;changeLocalizationName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                            <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
                            com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="changeLocalizationDescription-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeLocalizationDescription</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;changeLocalizationDescription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
</li>
</ul>
<a name="changeLocalizationDescription-java.lang.String-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeLocalizationDescription</h4>
<pre>void&nbsp;changeLocalizationDescription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
                                   com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="getLocalizationElementProperties-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalizationElementProperties</h4>
<pre>com.fasterxml.jackson.databind.node.ObjectNode&nbsp;getLocalizationElementProperties(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language,
                                                                                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                                                                                com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
</li>
</ul>
<a name="resetProperty-java.lang.String-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetProperty</h4>
<pre>void&nbsp;resetProperty(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;elementId,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;property,
                   com.fasterxml.jackson.databind.node.ObjectNode&nbsp;infoNode)</pre>
<div class="block"><p>
  Clears the field from the infoNode. So the engine uses the <code>BpmnModel</code> value
  On next instance.
 </p>

 <p color="red">
     Don't forget to save the modified infoNode by calling <a href="../../../org/activiti/engine/DynamicBpmnService.html#saveProcessDefinitionInfo-java.lang.String-com.fasterxml.jackson.databind.node.ObjectNode-"><code>saveProcessDefinitionInfo(String, ObjectNode)</code></a>
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementId</code> - the flow elements id.</dd>
<dd><code>property</code> - <a href="../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine"><code>DynamicBpmnConstants</code></a> property</dd>
<dd><code>infoNode</code> - to modify</dd>
</dl>
</li>
</ul>
<a name="getDynamicProcessDefinitionSummary-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDynamicProcessDefinitionSummary</h4>
<pre><a href="../../../org/activiti/engine/dynamic/DynamicProcessDefinitionSummary.html" title="class in org.activiti.engine.dynamic">DynamicProcessDefinitionSummary</a>&nbsp;getDynamicProcessDefinitionSummary(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Gives a summary between the <code>BpmnModel</code> and <a href="../../../org/activiti/engine/DynamicBpmnService.html#getProcessDefinitionInfo-java.lang.String-"><code>getProcessDefinitionInfo(String)</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>processDefinitionId</code> - the process definition id (key:version:sequence)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>DynamicProcessDefinitionSummary if the processdefinition exists</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/IllegalStateException.html?is-external=true" title="class or interface in java.lang">IllegalStateException</a></code> - if there is no processDefinition found for the provided processDefinitionId.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DynamicBpmnService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/DynamicBpmnService.html" target="_top">Frames</a></li>
<li><a href="DynamicBpmnService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
