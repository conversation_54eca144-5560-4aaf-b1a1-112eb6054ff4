GEM
  remote: https://rubygems.org/
  specs:
    activesupport (5.0.1)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (~> 0.7)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
    addressable (2.5.2)
      public_suffix (>= 2.0.2, < 4.0)
    autoprefixer-rails (6.6.1)
      execjs
    backports (3.6.8)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    compass-import-once (1.0.5)
      sass (>= 3.2, < 3.5)
    concurrent-ruby (1.0.4)
    contracts (0.13.0)
    dotenv (2.2.0)
    erubis (2.7.0)
    execjs (2.7.0)
    fast_blank (1.0.0)
    fastimage (2.0.1)
      addressable (~> 2)
    ffi (1.9.18)
    haml (4.0.7)
      tilt
    hamster (3.0.0)
      concurrent-ruby (~> 1.0)
    hashie (3.5.1)
    i18n (0.7.0)
    kramdown (1.15.0)
    listen (3.0.8)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    memoist (0.15.0)
    middleman (4.2.1)
      coffee-script (~> 2.2)
      compass-import-once (= 1.0.5)
      haml (>= 4.0.5)
      kramdown (~> 1.2)
      middleman-cli (= 4.2.1)
      middleman-core (= 4.2.1)
      sass (>= 3.4.0, < 4.0)
    middleman-autoprefixer (2.7.1)
      autoprefixer-rails (>= 6.5.2, < 7.0.0)
      middleman-core (>= 3.3.3)
    middleman-cli (4.2.1)
      thor (>= 0.17.0, < 2.0)
    middleman-core (4.2.1)
      activesupport (>= 4.2, < 5.1)
      addressable (~> 2.3)
      backports (~> 3.6)
      bundler (~> 1.1)
      contracts (~> 0.13.0)
      dotenv
      erubis
      execjs (~> 2.0)
      fast_blank
      fastimage (~> 2.0)
      hamster (~> 3.0)
      hashie (~> 3.4)
      i18n (~> 0.7.0)
      listen (~> 3.0.0)
      memoist (~> 0.14)
      padrino-helpers (~> 0.13.0)
      parallel
      rack (>= 1.4.5, < 3)
      sass (>= 3.4)
      servolux
      tilt (~> 2.0)
      uglifier (~> 3.0)
    middleman-sprockets (4.1.0)
      middleman-core (~> 4.0)
      sprockets (>= 3.0)
    middleman-syntax (3.0.0)
      middleman-core (>= 3.2)
      rouge (~> 2.0)
    mini_portile2 (2.1.0)
    minitest (5.10.1)
    nokogiri (*******)
      mini_portile2 (~> 2.1.0)
    padrino-helpers (********)
      i18n (~> 0.6, >= 0.6.7)
      padrino-support (= ********)
      tilt (>= 1.4.1, < 3)
    padrino-support (********)
      activesupport (>= 3.1)
    parallel (1.10.0)
    public_suffix (3.0.0)
    rack (2.0.1)
    rb-fsevent (0.10.2)
    rb-inotify (0.9.10)
      ffi (>= 0.5.0, < 2)
    redcarpet (3.4.0)
    rouge (2.0.7)
    sass (3.4.23)
    servolux (0.12.0)
    sprockets (3.7.1)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    thor (0.19.4)
    thread_safe (0.3.6)
    tilt (2.0.6)
    tzinfo (1.2.2)
      thread_safe (~> 0.1)
    uglifier (3.0.4)
      execjs (>= 0.3.0, < 3)

PLATFORMS
  ruby

DEPENDENCIES
  middleman (~> 4.2.1)
  middleman-autoprefixer (~> 2.7.0)
  middleman-sprockets (~> 4.1.0)
  middleman-syntax (~> 3.0.0)
  nokogiri (~> 1.6.8)
  redcarpet (~> 3.4.0)
  rouge (~> 2.0.5)

RUBY VERSION
   ruby 2.4.2p198

BUNDLED WITH
   1.15.4
