<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GroupQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GroupQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GroupQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/identity/NativeGroupQuery.html" title="interface in org.activiti.engine.identity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/identity/GroupQuery.html" target="_top">Frames</a></li>
<li><a href="GroupQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.identity</div>
<h2 title="Interface GroupQuery" class="title">Interface GroupQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>,<a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity">Group</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">GroupQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>,<a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity">Group</a>&gt;</pre>
<div class="block">Allows to programmatically query for <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/identity/GroupQuery.html#groupId-java.lang.String-">groupId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s with the given id.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/identity/GroupQuery.html#groupMember-java.lang.String-">groupMember</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupMemberUserId)</code>
<div class="block">Only selects <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s where the given user is a member of.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/identity/GroupQuery.html#groupName-java.lang.String-">groupName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupName)</code>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s with the given name.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/identity/GroupQuery.html#groupNameLike-java.lang.String-">groupNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupNameLike)</code>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s where the name matches the given parameter.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/identity/GroupQuery.html#groupType-java.lang.String-">groupType</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupType)</code>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s which have the given type.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/identity/GroupQuery.html#orderByGroupId--">orderByGroupId</a></span>()</code>
<div class="block">Order by group id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/identity/GroupQuery.html#orderByGroupName--">orderByGroupName</a></span>()</code>
<div class="block">Order by group name (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/identity/GroupQuery.html#orderByGroupType--">orderByGroupType</a></span>()</code>
<div class="block">Order by group type (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/identity/GroupQuery.html#potentialStarter-java.lang.String-">potentialStarter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;procDefId)</code>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>S that are potential starter for the given process definition.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="groupId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupId</h4>
<pre><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;groupId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</pre>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s with the given id.</div>
</li>
</ul>
<a name="groupName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupName</h4>
<pre><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;groupName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupName)</pre>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s with the given name.</div>
</li>
</ul>
<a name="groupNameLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupNameLike</h4>
<pre><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;groupNameLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupNameLike)</pre>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s where the name matches the given parameter.
  The syntax to use is that of SQL, eg. %activiti%.</div>
</li>
</ul>
<a name="groupType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupType</h4>
<pre><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;groupType(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupType)</pre>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s which have the given type.</div>
</li>
</ul>
<a name="groupMember-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupMember</h4>
<pre><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;groupMember(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupMemberUserId)</pre>
<div class="block">Only selects <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s where the given user is a member of.</div>
</li>
</ul>
<a name="potentialStarter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>potentialStarter</h4>
<pre><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;potentialStarter(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;procDefId)</pre>
<div class="block">Only select <a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>S that are potential starter for the given process definition.</div>
</li>
</ul>
<a name="orderByGroupId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByGroupId</h4>
<pre><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;orderByGroupId()</pre>
<div class="block">Order by group id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByGroupName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByGroupName</h4>
<pre><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;orderByGroupName()</pre>
<div class="block">Order by group name (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByGroupType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByGroupType</h4>
<pre><a href="../../../../org/activiti/engine/identity/GroupQuery.html" title="interface in org.activiti.engine.identity">GroupQuery</a>&nbsp;orderByGroupType()</pre>
<div class="block">Order by group type (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GroupQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/identity/NativeGroupQuery.html" title="interface in org.activiti.engine.identity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/identity/GroupQuery.html" target="_top">Frames</a></li>
<li><a href="GroupQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
