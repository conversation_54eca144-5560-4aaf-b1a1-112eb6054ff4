<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitiEventType (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitiEventType (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEventType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiExceptionEvent.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/ActivitiEventType.html" target="_top">Frames</a></li>
<li><a href="ActivitiEventType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate.event</div>
<h2 title="Enum ActivitiEventType" class="title">Enum ActivitiEventType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">java.lang.Enum</a>&lt;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.delegate.event.ActivitiEventType</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="http://docs.oracle.com/javase/6/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public enum <span class="typeNameLabel">ActivitiEventType</span>
extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&gt;</pre>
<div class="block">Enumeration containing all possible types of <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEvent</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Frederik Heremans</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_CANCELLED">ACTIVITY_CANCELLED</a></span></code>
<div class="block">An activity has been cancelled because of boundary event.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_COMPENSATE">ACTIVITY_COMPENSATE</a></span></code>
<div class="block">An activity is about to be executed as a compensation for another activity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_COMPLETED">ACTIVITY_COMPLETED</a></span></code>
<div class="block">An activity has been completed successfully.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_ERROR_RECEIVED">ACTIVITY_ERROR_RECEIVED</a></span></code>
<div class="block">An activity has received an error event.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_MESSAGE_RECEIVED">ACTIVITY_MESSAGE_RECEIVED</a></span></code>
<div class="block">An activity has received a message event.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_SIGNALED">ACTIVITY_SIGNALED</a></span></code>
<div class="block">An activity has received a signal.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_STARTED">ACTIVITY_STARTED</a></span></code>
<div class="block">An activity is starting to execute.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#CUSTOM">CUSTOM</a></span></code>
<div class="block">An event type to be used by custom events.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENGINE_CLOSED">ENGINE_CLOSED</a></span></code>
<div class="block">The process-engine that dispatched this event has been closed and cannot be used anymore.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENGINE_CREATED">ENGINE_CREATED</a></span></code>
<div class="block">The process-engine that dispatched this event has been created and is ready for use.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_ACTIVATED">ENTITY_ACTIVATED</a></span></code>
<div class="block">Existing entity has been activated.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_CREATED">ENTITY_CREATED</a></span></code>
<div class="block">New entity is created.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_DELETED">ENTITY_DELETED</a></span></code>
<div class="block">Existing entity is deleted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_INITIALIZED">ENTITY_INITIALIZED</a></span></code>
<div class="block">New entity has been created and all child-entities that are created as a result of the creation of this
 particular entity are also created and initialized.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_SUSPENDED">ENTITY_SUSPENDED</a></span></code>
<div class="block">Existing entity has been suspended.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_UPDATED">ENTITY_UPDATED</a></span></code>
<div class="block">Existing entity us updated.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#HISTORIC_ACTIVITY_INSTANCE_CREATED">HISTORIC_ACTIVITY_INSTANCE_CREATED</a></span></code>
<div class="block">A event dispatched when a <a href="../../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a> is created.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#HISTORIC_ACTIVITY_INSTANCE_ENDED">HISTORIC_ACTIVITY_INSTANCE_ENDED</a></span></code>
<div class="block">A event dispatched when a <a href="../../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a> is marked as ended.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#HISTORIC_PROCESS_INSTANCE_CREATED">HISTORIC_PROCESS_INSTANCE_CREATED</a></span></code>
<div class="block">A event dispatched when a <a href="../../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a> is created.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#HISTORIC_PROCESS_INSTANCE_ENDED">HISTORIC_PROCESS_INSTANCE_ENDED</a></span></code>
<div class="block">A event dispatched when a <a href="../../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a> is marked as ended.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#JOB_CANCELED">JOB_CANCELED</a></span></code>
<div class="block">Timer has been cancelled (e.g.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#JOB_EXECUTION_FAILURE">JOB_EXECUTION_FAILURE</a></span></code>
<div class="block">A job has been executed, but failed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#JOB_EXECUTION_SUCCESS">JOB_EXECUTION_SUCCESS</a></span></code>
<div class="block">A job has been successfully executed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#JOB_RETRIES_DECREMENTED">JOB_RETRIES_DECREMENTED</a></span></code>
<div class="block">The retry-count on a job has been decremented.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#MEMBERSHIP_CREATED">MEMBERSHIP_CREATED</a></span></code>
<div class="block">A new membership has been created.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#MEMBERSHIP_DELETED">MEMBERSHIP_DELETED</a></span></code>
<div class="block">A single membership has been deleted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#MEMBERSHIPS_DELETED">MEMBERSHIPS_DELETED</a></span></code>
<div class="block">All memberships in the related group have been deleted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#PROCESS_CANCELLED">PROCESS_CANCELLED</a></span></code>
<div class="block">A process has been cancelled.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#PROCESS_COMPLETED">PROCESS_COMPLETED</a></span></code>
<div class="block">A process has been completed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#PROCESS_COMPLETED_WITH_ERROR_END_EVENT">PROCESS_COMPLETED_WITH_ERROR_END_EVENT</a></span></code>
<div class="block">A process has been completed with an error end event.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#PROCESS_STARTED">PROCESS_STARTED</a></span></code>
<div class="block">A process instance has been started.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#SEQUENCEFLOW_TAKEN">SEQUENCEFLOW_TAKEN</a></span></code>
<div class="block">Indicates the engine has taken (ie.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#TASK_ASSIGNED">TASK_ASSIGNED</a></span></code>
<div class="block">A task as been assigned.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#TASK_COMPLETED">TASK_COMPLETED</a></span></code>
<div class="block">A task has been completed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#TASK_CREATED">TASK_CREATED</a></span></code>
<div class="block">A task has been created.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#TIMER_FIRED">TIMER_FIRED</a></span></code>
<div class="block">Timer has been fired successfully.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#UNCAUGHT_BPMN_ERROR">UNCAUGHT_BPMN_ERROR</a></span></code>
<div class="block">When a BPMN Error was thrown, but was not caught within in the process.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#VARIABLE_CREATED">VARIABLE_CREATED</a></span></code>
<div class="block">A new variable has been created.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#VARIABLE_DELETED">VARIABLE_DELETED</a></span></code>
<div class="block">An existing variable has been deleted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#VARIABLE_UPDATED">VARIABLE_UPDATED</a></span></code>
<div class="block">An existing variable has been updated.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#EMPTY_ARRAY">EMPTY_ARRAY</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#getTypesFromString-java.lang.String-">getTypesFromString</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;string)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#valueOf-java.lang.String-">valueOf</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#compareTo-E-" title="class or interface in java.lang">compareTo</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#getDeclaringClass--" title="class or interface in java.lang">getDeclaringClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#name--" title="class or interface in java.lang">name</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#ordinal--" title="class or interface in java.lang">ordinal</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Enum.html?is-external=true#valueOf-java.lang.Class-java.lang.String-" title="class or interface in java.lang">valueOf</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="ENTITY_CREATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTITY_CREATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ENTITY_CREATED</pre>
<div class="block">New entity is created.</div>
</li>
</ul>
<a name="ENTITY_INITIALIZED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTITY_INITIALIZED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ENTITY_INITIALIZED</pre>
<div class="block">New entity has been created and all child-entities that are created as a result of the creation of this
 particular entity are also created and initialized.</div>
</li>
</ul>
<a name="ENTITY_UPDATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTITY_UPDATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ENTITY_UPDATED</pre>
<div class="block">Existing entity us updated.</div>
</li>
</ul>
<a name="ENTITY_DELETED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTITY_DELETED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ENTITY_DELETED</pre>
<div class="block">Existing entity is deleted.</div>
</li>
</ul>
<a name="ENTITY_SUSPENDED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTITY_SUSPENDED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ENTITY_SUSPENDED</pre>
<div class="block">Existing entity has been suspended.</div>
</li>
</ul>
<a name="ENTITY_ACTIVATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTITY_ACTIVATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ENTITY_ACTIVATED</pre>
<div class="block">Existing entity has been activated.</div>
</li>
</ul>
<a name="TIMER_FIRED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMER_FIRED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> TIMER_FIRED</pre>
<div class="block">Timer has been fired successfully.</div>
</li>
</ul>
<a name="JOB_CANCELED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JOB_CANCELED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> JOB_CANCELED</pre>
<div class="block">Timer has been cancelled (e.g. user task on which it was bounded has been completed earlier than expected)</div>
</li>
</ul>
<a name="JOB_EXECUTION_SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JOB_EXECUTION_SUCCESS</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> JOB_EXECUTION_SUCCESS</pre>
<div class="block">A job has been successfully executed.</div>
</li>
</ul>
<a name="JOB_EXECUTION_FAILURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JOB_EXECUTION_FAILURE</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> JOB_EXECUTION_FAILURE</pre>
<div class="block">A job has been executed, but failed. Event should be an instance of a <a href="../../../../../org/activiti/engine/delegate/event/ActivitiExceptionEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiExceptionEvent</code></a>.</div>
</li>
</ul>
<a name="JOB_RETRIES_DECREMENTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JOB_RETRIES_DECREMENTED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> JOB_RETRIES_DECREMENTED</pre>
<div class="block">The retry-count on a job has been decremented.</div>
</li>
</ul>
<a name="CUSTOM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CUSTOM</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> CUSTOM</pre>
<div class="block">An event type to be used by custom events. These types of events are never thrown by the engine itself,
 only be an external API call to dispatch an event.</div>
</li>
</ul>
<a name="ENGINE_CREATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENGINE_CREATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ENGINE_CREATED</pre>
<div class="block">The process-engine that dispatched this event has been created and is ready for use.</div>
</li>
</ul>
<a name="ENGINE_CLOSED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENGINE_CLOSED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ENGINE_CLOSED</pre>
<div class="block">The process-engine that dispatched this event has been closed and cannot be used anymore.</div>
</li>
</ul>
<a name="ACTIVITY_STARTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_STARTED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ACTIVITY_STARTED</pre>
<div class="block">An activity is starting to execute. This event is dispatch right before an activity is executed.</div>
</li>
</ul>
<a name="ACTIVITY_COMPLETED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_COMPLETED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ACTIVITY_COMPLETED</pre>
<div class="block">An activity has been completed successfully.</div>
</li>
</ul>
<a name="ACTIVITY_CANCELLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_CANCELLED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ACTIVITY_CANCELLED</pre>
<div class="block">An activity has been cancelled because of boundary event.</div>
</li>
</ul>
<a name="ACTIVITY_SIGNALED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_SIGNALED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ACTIVITY_SIGNALED</pre>
<div class="block">An activity has received a signal. Dispatched after the activity has responded to the signal.</div>
</li>
</ul>
<a name="ACTIVITY_COMPENSATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_COMPENSATE</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ACTIVITY_COMPENSATE</pre>
<div class="block">An activity is about to be executed as a compensation for another activity. The event targets the
 activity that is about to be executed for compensation.</div>
</li>
</ul>
<a name="ACTIVITY_MESSAGE_RECEIVED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_MESSAGE_RECEIVED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ACTIVITY_MESSAGE_RECEIVED</pre>
<div class="block">An activity has received a message event. Dispatched before the actual message has been received by
 the activity. This event will be either followed by a <code>#ACTIVITY_SIGNALLED</code> event or <code>#ACTIVITY_COMPLETE</code>
 for the involved activity, if the message was delivered successfully.</div>
</li>
</ul>
<a name="ACTIVITY_ERROR_RECEIVED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_ERROR_RECEIVED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> ACTIVITY_ERROR_RECEIVED</pre>
<div class="block">An activity has received an error event. Dispatched before the actual error has been received by
 the activity. This event will be either followed by a <code>#ACTIVITY_SIGNALLED</code> event or <code>#ACTIVITY_COMPLETE</code>
 for the involved activity, if the error was delivered successfully.</div>
</li>
</ul>
<a name="HISTORIC_ACTIVITY_INSTANCE_CREATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HISTORIC_ACTIVITY_INSTANCE_CREATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> HISTORIC_ACTIVITY_INSTANCE_CREATED</pre>
<div class="block">A event dispatched when a <a href="../../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a> is created. 
 This is a specialized version of the <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_CREATED"><code>ENTITY_CREATED</code></a> and <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_INITIALIZED"><code>ENTITY_INITIALIZED</code></a> event,
 with the same use case as the <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_STARTED"><code>ACTIVITY_STARTED</code></a>, but containing
 slightly different data.
 
 Note this will be an <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a>, where the entity is the <a href="../../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a>.
  
 Note that history (minimum level ACTIVITY) must be enabled to receive this event.</div>
</li>
</ul>
<a name="HISTORIC_ACTIVITY_INSTANCE_ENDED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HISTORIC_ACTIVITY_INSTANCE_ENDED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> HISTORIC_ACTIVITY_INSTANCE_ENDED</pre>
<div class="block">A event dispatched when a <a href="../../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a> is marked as ended. 
 his is a specialized version of the <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_UPDATED"><code>ENTITY_UPDATED</code></a> event,
 with the same use case as the <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_COMPLETED"><code>ACTIVITY_COMPLETED</code></a>, but containing
 slightly different data (e.g. the end time, the duration, etc.). 
  
 Note that history (minimum level ACTIVITY) must be enabled to receive this event.</div>
</li>
</ul>
<a name="SEQUENCEFLOW_TAKEN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SEQUENCEFLOW_TAKEN</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> SEQUENCEFLOW_TAKEN</pre>
<div class="block">Indicates the engine has taken (ie. followed) a sequenceflow from a source activity to a target activity.</div>
</li>
</ul>
<a name="UNCAUGHT_BPMN_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNCAUGHT_BPMN_ERROR</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> UNCAUGHT_BPMN_ERROR</pre>
<div class="block">When a BPMN Error was thrown, but was not caught within in the process.</div>
</li>
</ul>
<a name="VARIABLE_CREATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VARIABLE_CREATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> VARIABLE_CREATED</pre>
<div class="block">A new variable has been created.</div>
</li>
</ul>
<a name="VARIABLE_UPDATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VARIABLE_UPDATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> VARIABLE_UPDATED</pre>
<div class="block">An existing variable has been updated.</div>
</li>
</ul>
<a name="VARIABLE_DELETED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VARIABLE_DELETED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> VARIABLE_DELETED</pre>
<div class="block">An existing variable has been deleted.</div>
</li>
</ul>
<a name="TASK_CREATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_CREATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> TASK_CREATED</pre>
<div class="block">A task has been created. This is thrown when task is fully initialized (before TaskListener.EVENTNAME_CREATE).</div>
</li>
</ul>
<a name="TASK_ASSIGNED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_ASSIGNED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> TASK_ASSIGNED</pre>
<div class="block">A task as been assigned. This is thrown alongside with an <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_UPDATED"><code>ENTITY_UPDATED</code></a> event.</div>
</li>
</ul>
<a name="TASK_COMPLETED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_COMPLETED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> TASK_COMPLETED</pre>
<div class="block">A task has been completed. Dispatched before the task entity is deleted (<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_DELETED"><code>ENTITY_DELETED</code></a>).
 If the task is part of a process, this event is dispatched before the process moves on, as a result of
 the task completion. In that case, a <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ACTIVITY_COMPLETED"><code>ACTIVITY_COMPLETED</code></a> will be dispatched after an event of this type
 for the activity corresponding to the task.</div>
</li>
</ul>
<a name="PROCESS_STARTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROCESS_STARTED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> PROCESS_STARTED</pre>
<div class="block">A process instance has been started. Dispatched when starting a process instance previously created. The event
 PROCESS_STARTED is dispatched after the associated event ENTITY_INITIALIZED.</div>
</li>
</ul>
<a name="PROCESS_COMPLETED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROCESS_COMPLETED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> PROCESS_COMPLETED</pre>
<div class="block">A process has been completed. Dispatched after the last activity is ACTIVITY_COMPLETED. Process is completed
 when it reaches state in which process instance does not have any transition to take.</div>
</li>
</ul>
<a name="PROCESS_COMPLETED_WITH_ERROR_END_EVENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROCESS_COMPLETED_WITH_ERROR_END_EVENT</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> PROCESS_COMPLETED_WITH_ERROR_END_EVENT</pre>
<div class="block">A process has been completed with an error end event.</div>
</li>
</ul>
<a name="PROCESS_CANCELLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROCESS_CANCELLED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> PROCESS_CANCELLED</pre>
<div class="block">A process has been cancelled. Dispatched when process instance is deleted by</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>org.activiti.engine.impl.RuntimeServiceImpl#deleteProcessInstance(java.lang.String, java.lang.String), before
 DB delete.</code></dd>
</dl>
</li>
</ul>
<a name="HISTORIC_PROCESS_INSTANCE_CREATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HISTORIC_PROCESS_INSTANCE_CREATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> HISTORIC_PROCESS_INSTANCE_CREATED</pre>
<div class="block">A event dispatched when a <a href="../../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a> is created. 
 This is a specialized version of the <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_CREATED"><code>ENTITY_CREATED</code></a> and <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_INITIALIZED"><code>ENTITY_INITIALIZED</code></a> event,
 with the same use case as the <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#PROCESS_STARTED"><code>PROCESS_STARTED</code></a>, but containing
 slightly different data (e.g. the start time, the start user id, etc.). 
 
 Note this will be an <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEntityEvent.html" title="interface in org.activiti.engine.delegate.event"><code>ActivitiEntityEvent</code></a>, where the entity is the <a href="../../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a>.
  
 Note that history (minimum level ACTIVITY) must be enabled to receive this event.</div>
</li>
</ul>
<a name="HISTORIC_PROCESS_INSTANCE_ENDED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HISTORIC_PROCESS_INSTANCE_ENDED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> HISTORIC_PROCESS_INSTANCE_ENDED</pre>
<div class="block">A event dispatched when a <a href="../../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a> is marked as ended. 
 his is a specialized version of the <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#ENTITY_UPDATED"><code>ENTITY_UPDATED</code></a> event,
 with the same use case as the <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#PROCESS_COMPLETED"><code>PROCESS_COMPLETED</code></a>, but containing
 slightly different data (e.g. the end time, the duration, etc.). 
  
 Note that history (minimum level ACTIVITY) must be enabled to receive this event.</div>
</li>
</ul>
<a name="MEMBERSHIP_CREATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MEMBERSHIP_CREATED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> MEMBERSHIP_CREATED</pre>
<div class="block">A new membership has been created.</div>
</li>
</ul>
<a name="MEMBERSHIP_DELETED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MEMBERSHIP_DELETED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> MEMBERSHIP_DELETED</pre>
<div class="block">A single membership has been deleted.</div>
</li>
</ul>
<a name="MEMBERSHIPS_DELETED">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MEMBERSHIPS_DELETED</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a> MEMBERSHIPS_DELETED</pre>
<div class="block">All memberships in the related group have been deleted. No individual <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html#MEMBERSHIP_DELETED"><code>MEMBERSHIP_DELETED</code></a> events will
 be dispatched due to possible performance reasons. The event is dispatched before the memberships are deleted,
 so they can still be accessed in the dispatch method of the listener.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="EMPTY_ARRAY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EMPTY_ARRAY</h4>
<pre>public static final&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>[] EMPTY_ARRAY</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (ActivitiEventType c : ActivitiEventType.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>&nbsp;valueOf(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/IllegalArgumentException.html?is-external=true" title="class or interface in java.lang">IllegalArgumentException</a></code> - if this enum type has no constant with the specified name</dd>
<dd><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the argument is null</dd>
</dl>
</li>
</ul>
<a name="getTypesFromString-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTypesFromString</h4>
<pre>public static&nbsp;<a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event">ActivitiEventType</a>[]&nbsp;getTypesFromString(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;string)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>string</code> - the string containing a comma-separated list of event-type names</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a list of <a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventType.html" title="enum in org.activiti.engine.delegate.event"><code>ActivitiEventType</code></a> based on the given list.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></code> - when one of the given string is not a valid type name</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitiEventType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiEventListener.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../org/activiti/engine/delegate/event/ActivitiExceptionEvent.html" title="interface in org.activiti.engine.delegate.event"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/event/ActivitiEventType.html" target="_top">Frames</a></li>
<li><a href="ActivitiEventType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
