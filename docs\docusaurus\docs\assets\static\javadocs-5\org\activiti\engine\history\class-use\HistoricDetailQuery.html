<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:26 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.activiti.engine.history.HistoricDetailQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.activiti.engine.history.HistoricDetailQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/history/class-use/HistoricDetailQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricDetailQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.activiti.engine.history.HistoricDetailQuery" class="title">Uses of Interface<br>org.activiti.engine.history.HistoricDetailQuery</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine">org.activiti.engine</a></td>
<td class="colLast">
<div class="block">Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a <a href="../../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine"><code>ProcessEngineConfiguration</code></a>
    (typically based on a configuration file), from which a <a href="../../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> can be obtained.<br/><br/>
    Through the services obtained from such a <a href="../../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b><a href="../../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>: </b> Manages <a href="../../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s <br/>

    <b><a href="../../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>: </b> For starting and searching <a href="../../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s <br/>
    
    <b><a href="../../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>: </b> Exposes operations to manage human (standalone) <a href="../../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s, 
    such as claiming, completing and assigning tasks<br/>

    <b><a href="../../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>: </b> Used for managing <a href="../../../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s, 
    <a href="../../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s and the relations between them<br/>
        
    <b><a href="../../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b><a href="../../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b><a href="../../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.activiti.engine.history">org.activiti.engine.history</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="../../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a> in <a href="../../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> that return <a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoryService.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/HistoryService.html#createHistoricDetailQuery--">createHistoricDetailQuery</a></span>()</code>
<div class="block">Creates a new programmatic query to search for <a href="../../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><code>HistoricDetail</code></a>s.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.history">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a> in <a href="../../../../../org/activiti/engine/history/package-summary.html">org.activiti.engine.history</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/history/package-summary.html">org.activiti.engine.history</a> that return <a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#activityInstanceId-java.lang.String-">activityInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityInstanceId)</code>
<div class="block">Only select historic variable updates associated to the given <a href="../../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>activity instance</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#excludeTaskDetails--">excludeTaskDetails</a></span>()</code>
<div class="block">Exclude all task-related <a href="../../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><code>HistoricDetail</code></a>s, so only items which have no 
 task-id set will be selected.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#executionId-java.lang.String-">executionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Only select historic variable updates with the given execution.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#formProperties--">formProperties</a></span>()</code>
<div class="block">Only select <a href="../../../../../org/activiti/engine/history/HistoricFormProperty.html" title="interface in org.activiti.engine.history"><code>HistoricFormProperty</code></a>s.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#id-java.lang.String-">id</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</code>
<div class="block">Only select historic info with the given id.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByFormPropertyId--">orderByFormPropertyId</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByTime--">orderByTime</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByVariableName--">orderByVariableName</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByVariableRevision--">orderByVariableRevision</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByVariableType--">orderByVariableType</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Only select historic variable updates with the given process instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#taskId-java.lang.String-">taskId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Only select historic variable updates associated to the given <a href="../../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>historic task instance</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">HistoricDetailQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html#variableUpdates--">variableUpdates</a></span>()</code>
<div class="block">Only select <a href="../../../../../org/activiti/engine/history/HistoricVariableUpdate.html" title="interface in org.activiti.engine.history"><code>HistoricVariableUpdate</code></a>s.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/history/class-use/HistoricDetailQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricDetailQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
