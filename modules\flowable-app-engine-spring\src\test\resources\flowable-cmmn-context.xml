<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans   http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- This bean is needed in context xml in order for the jdbc properties to work -->
    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer" />

    <bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource">
        <property name="jdbcUrl" value="${jdbc.url:jdbc:h2:mem:flowable;DB_CLOSE_DELAY=1000}" />
        <property name="driverClassName" value="${jdbc.driver:org.h2.Driver}" />
        <property name="username" value="${jdbc.username:sa}" />
        <property name="password" value="${jdbc.password:}" />
    </bean>

    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <bean id="cmmnEngineConfiguration" class="org.flowable.cmmn.spring.SpringCmmnEngineConfiguration">
        <property name="dataSource" ref="dataSource"/>
        <property name="transactionManager" ref="transactionManager"/>
        <property name="databaseSchemaUpdate" value="true"/>
        <property name="idmEngineConfigurator" ref="idmEngineConfigurator" />

    </bean>

    <bean id="idmEngineConfigurator" class="org.flowable.idm.spring.configurator.SpringIdmEngineConfigurator" />

    <bean id="cmmnEngine" class="org.flowable.cmmn.spring.CmmnEngineFactoryBean">
        <property name="cmmnEngineConfiguration" ref="cmmnEngineConfiguration"/>
    </bean>

    <bean id="cmmnRepositoryService" factory-bean="cmmnEngine" factory-method="getCmmnRepositoryService"/>
    <bean id="cmmnRuntimeService" factory-bean="cmmnEngine" factory-method="getCmmnRuntimeService"/>

</beans>