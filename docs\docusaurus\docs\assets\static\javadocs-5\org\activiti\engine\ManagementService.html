<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ManagementService (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ManagementService (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ManagementService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/JobNotFoundException.html" title="class in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/ManagementService.html" target="_top">Frames</a></li>
<li><a href="ManagementService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Interface ManagementService" class="title">Interface ManagementService</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ManagementService</span></pre>
<div class="block">Service for admin and maintenance operations on the process engine.
 
 These operations will typically not be used in a workflow driven application,
 but are used in for example the operational console.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez, Falko Menge</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#createJobQuery--">createJobQuery</a></span>()</code>
<div class="block">Returns a new JobQuery implementation, that can be used
 to dynamically query the jobs.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/management/TablePageQuery.html" title="interface in org.activiti.engine.management">TablePageQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#createTablePageQuery--">createTablePageQuery</a></span>()</code>
<div class="block">Creates a <a href="../../../org/activiti/engine/management/TablePageQuery.html" title="interface in org.activiti.engine.management"><code>TablePageQuery</code></a> that can be used to fetch <a href="../../../org/activiti/engine/management/TablePage.html" title="class in org.activiti.engine.management"><code>TablePage</code></a>
 containing specific sections of table row data.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#databaseSchemaUpgrade-java.sql.Connection-java.lang.String-java.lang.String-">databaseSchemaUpgrade</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/sql/Connection.html?is-external=true" title="class or interface in java.sql">Connection</a>&nbsp;connection,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;catalog,
                     <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;schema)</code>
<div class="block">programmatic schema update on a given connection returning feedback about what happened</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#deleteEventLogEntry-long-">deleteEventLogEntry</a></span>(long&nbsp;logNr)</code>
<div class="block">Delete a EventLogEntry.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#deleteJob-java.lang.String-">deleteJob</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId)</code>
<div class="block">Delete the job with the provided id.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#executeCommand-org.activiti.engine.impl.interceptor.Command-">executeCommand</a></span>(org.activiti.engine.impl.interceptor.Command&lt;T&gt;&nbsp;command)</code>
<div class="block">Executes a given command with the default <code>CommandConfig</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#executeCommand-org.activiti.engine.impl.interceptor.CommandConfig-org.activiti.engine.impl.interceptor.Command-">executeCommand</a></span>(org.activiti.engine.impl.interceptor.CommandConfig&nbsp;config,
              org.activiti.engine.impl.interceptor.Command&lt;T&gt;&nbsp;command)</code>
<div class="block">Executes a given command with the specified <code>CommandConfig</code>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>&lt;MapperType,ResultType&gt;<br>ResultType</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#executeCustomSql-org.activiti.engine.impl.cmd.CustomSqlExecution-">executeCustomSql</a></span>(org.activiti.engine.impl.cmd.CustomSqlExecution&lt;MapperType,ResultType&gt;&nbsp;customSqlExecution)</code>
<div class="block">[EXPERIMENTAL]
 
 Executes the sql contained in the <code>CustomSqlExecution</code> parameter.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#executeJob-java.lang.String-">executeJob</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId)</code>
<div class="block">Forced synchronous execution of a job (eg.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/event/EventLogEntry.html" title="interface in org.activiti.engine.event">EventLogEntry</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#getEventLogEntries-java.lang.Long-java.lang.Long-">getEventLogEntries</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;startLogNr,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;pageSize)</code>
<div class="block">[EXPERIMENTAL]
 
 Returns a list of event log entries, describing everything the engine has processed.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/event/EventLogEntry.html" title="interface in org.activiti.engine.event">EventLogEntry</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#getEventLogEntriesByProcessInstanceId-java.lang.String-">getEventLogEntriesByProcessInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">[EXPERIMENTAL]
 
 Returns a list of event log entries for a specific process instance id.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#getJobExceptionStacktrace-java.lang.String-">getJobExceptionStacktrace</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId)</code>
<div class="block">Returns the full stacktrace of the exception that occurs when the job
 with the given id was last executed.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#getProperties--">getProperties</a></span>()</code>
<div class="block">get the list of properties.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#getTableCount--">getTableCount</a></span>()</code>
<div class="block">Get the mapping containing {table name, row count} entries of the
 Activiti database schema.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/management/TableMetaData.html" title="class in org.activiti.engine.management">TableMetaData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#getTableMetaData-java.lang.String-">getTableMetaData</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tableName)</code>
<div class="block">Gets the metadata (column names, column types, etc.) of a certain table.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#getTableName-java.lang.Class-">getTableName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;&nbsp;activitiEntityClass)</code>
<div class="block">Gets the table name (including any configured prefix) for an Activiti entity like Task, Execution or the like.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/ManagementService.html#setJobRetries-java.lang.String-int-">setJobRetries</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId,
             int&nbsp;retries)</code>
<div class="block">Sets the number of retries that a job has left.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTableCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTableCount</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&gt;&nbsp;getTableCount()</pre>
<div class="block">Get the mapping containing {table name, row count} entries of the
 Activiti database schema.</div>
</li>
</ul>
<a name="getTableName-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTableName</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTableName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;&nbsp;activitiEntityClass)</pre>
<div class="block">Gets the table name (including any configured prefix) for an Activiti entity like Task, Execution or the like.</div>
</li>
</ul>
<a name="getTableMetaData-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTableMetaData</h4>
<pre><a href="../../../org/activiti/engine/management/TableMetaData.html" title="class in org.activiti.engine.management">TableMetaData</a>&nbsp;getTableMetaData(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tableName)</pre>
<div class="block">Gets the metadata (column names, column types, etc.) of a certain table. 
 Returns null when no table exists with the given name.</div>
</li>
</ul>
<a name="createTablePageQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTablePageQuery</h4>
<pre><a href="../../../org/activiti/engine/management/TablePageQuery.html" title="interface in org.activiti.engine.management">TablePageQuery</a>&nbsp;createTablePageQuery()</pre>
<div class="block">Creates a <a href="../../../org/activiti/engine/management/TablePageQuery.html" title="interface in org.activiti.engine.management"><code>TablePageQuery</code></a> that can be used to fetch <a href="../../../org/activiti/engine/management/TablePage.html" title="class in org.activiti.engine.management"><code>TablePage</code></a>
 containing specific sections of table row data.</div>
</li>
</ul>
<a name="createJobQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createJobQuery</h4>
<pre><a href="../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a>&nbsp;createJobQuery()</pre>
<div class="block">Returns a new JobQuery implementation, that can be used
 to dynamically query the jobs.</div>
</li>
</ul>
<a name="executeJob-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeJob</h4>
<pre>void&nbsp;executeJob(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId)</pre>
<div class="block">Forced synchronous execution of a job (eg. for administation or testing)
 The job will be executed, even if the process definition and/or the process instance
 is in suspended state.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>jobId</code> - id of the job to execute, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when there is no job with the given id.</dd>
</dl>
</li>
</ul>
<a name="deleteJob-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteJob</h4>
<pre>void&nbsp;deleteJob(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId)</pre>
<div class="block">Delete the job with the provided id.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>jobId</code> - id of the job to execute, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when there is no job with the given id.</dd>
</dl>
</li>
</ul>
<a name="setJobRetries-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJobRetries</h4>
<pre>void&nbsp;setJobRetries(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId,
                   int&nbsp;retries)</pre>
<div class="block">Sets the number of retries that a job has left.

 Whenever the JobExecutor fails to execute a job, this value is decremented. 
 When it hits zero, the job is supposed to be dead and not retried again.
 In that case, this method can be used to increase the number of retries.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>jobId</code> - id of the job to modify, cannot be null.</dd>
<dd><code>retries</code> - number of retries.</dd>
</dl>
</li>
</ul>
<a name="getJobExceptionStacktrace-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJobExceptionStacktrace</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getJobExceptionStacktrace(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId)</pre>
<div class="block">Returns the full stacktrace of the exception that occurs when the job
 with the given id was last executed. Returns null when the job has no
 exception stacktrace.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>jobId</code> - id of the job, cannot be null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></code> - when no job exists with the given id.</dd>
</dl>
</li>
</ul>
<a name="getProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProperties</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getProperties()</pre>
<div class="block">get the list of properties.</div>
</li>
</ul>
<a name="databaseSchemaUpgrade-java.sql.Connection-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>databaseSchemaUpgrade</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;databaseSchemaUpgrade(<a href="http://docs.oracle.com/javase/6/docs/api/java/sql/Connection.html?is-external=true" title="class or interface in java.sql">Connection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;catalog,
                             <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;schema)</pre>
<div class="block">programmatic schema update on a given connection returning feedback about what happened</div>
</li>
</ul>
<a name="executeCommand-org.activiti.engine.impl.interceptor.Command-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeCommand</h4>
<pre>&lt;T&gt;&nbsp;T&nbsp;executeCommand(org.activiti.engine.impl.interceptor.Command&lt;T&gt;&nbsp;command)</pre>
<div class="block">Executes a given command with the default <code>CommandConfig</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>command</code> - the command, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the result of command execution</dd>
</dl>
</li>
</ul>
<a name="executeCommand-org.activiti.engine.impl.interceptor.CommandConfig-org.activiti.engine.impl.interceptor.Command-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeCommand</h4>
<pre>&lt;T&gt;&nbsp;T&nbsp;executeCommand(org.activiti.engine.impl.interceptor.CommandConfig&nbsp;config,
                     org.activiti.engine.impl.interceptor.Command&lt;T&gt;&nbsp;command)</pre>
<div class="block">Executes a given command with the specified <code>CommandConfig</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>config</code> - the command execution configuration, cannot be null.</dd>
<dd><code>command</code> - the command, cannot be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the result of command execution</dd>
</dl>
</li>
</ul>
<a name="executeCustomSql-org.activiti.engine.impl.cmd.CustomSqlExecution-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeCustomSql</h4>
<pre>&lt;MapperType,ResultType&gt;&nbsp;ResultType&nbsp;executeCustomSql(org.activiti.engine.impl.cmd.CustomSqlExecution&lt;MapperType,ResultType&gt;&nbsp;customSqlExecution)</pre>
<div class="block">[EXPERIMENTAL]
 
 Executes the sql contained in the <code>CustomSqlExecution</code> parameter.</div>
</li>
</ul>
<a name="getEventLogEntries-java.lang.Long-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEventLogEntries</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/event/EventLogEntry.html" title="interface in org.activiti.engine.event">EventLogEntry</a>&gt;&nbsp;getEventLogEntries(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;startLogNr,
                                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;pageSize)</pre>
<div class="block">[EXPERIMENTAL]
 
 Returns a list of event log entries, describing everything the engine has processed.
 Note that the event logging must specifically must be enabled in the process engine configuration.
 
 Passing null as arguments will effectively fetch ALL event log entries. 
 Be careful, as this list might be huge!</div>
</li>
</ul>
<a name="getEventLogEntriesByProcessInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEventLogEntriesByProcessInstanceId</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/event/EventLogEntry.html" title="interface in org.activiti.engine.event">EventLogEntry</a>&gt;&nbsp;getEventLogEntriesByProcessInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">[EXPERIMENTAL]
 
 Returns a list of event log entries for a specific process instance id.
 Note that the event logging must specifically must be enabled in the process engine configuration.
 
 Passing null as arguments will effectively fetch ALL event log entries. 
 Be careful, as this list might be huge!</div>
</li>
</ul>
<a name="deleteEventLogEntry-long-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>deleteEventLogEntry</h4>
<pre>void&nbsp;deleteEventLogEntry(long&nbsp;logNr)</pre>
<div class="block">Delete a EventLogEntry.
 Typically only used in testing, as deleting log entries defeats the whole purpose of keeping a log.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ManagementService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/JobNotFoundException.html" title="class in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/ManagementService.html" target="_top">Frames</a></li>
<li><a href="ManagementService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
