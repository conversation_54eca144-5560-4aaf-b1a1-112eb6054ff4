<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TaskQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TaskQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":38,"i3":6,"i4":6,"i5":6,"i6":6,"i7":38};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TaskQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/task/TaskInfoQueryWrapper.html" title="class in org.activiti.engine.task"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/task/TaskQuery.html" target="_top">Frames</a></li>
<li><a href="TaskQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.task</div>
<h2 title="Interface TaskQuery" class="title">Interface TaskQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>,<a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a>&gt;, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task">TaskInfoQuery</a>&lt;<a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>,<a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">TaskQuery</span>
extends <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task">TaskInfoQuery</a>&lt;<a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>,<a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a>&gt;</pre>
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s;</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez, Falko Menge, Tijs Rademakers</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskQuery.html#active--">active</a></span>()</code>
<div class="block">Only selects tasks which are active (ie.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskQuery.html#excludeSubtasks--">excludeSubtasks</a></span>()</code>
<div class="block">Only select tasks that have no parent (i.e.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskQuery.html#orderByDueDate--">orderByDueDate</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use orderByTaskDueDate() instead</span></div>
</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskQuery.html#suspended--">suspended</a></span>()</code>
<div class="block">Only selects tasks which are suspended, because its process instance was suspended.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskQuery.html#taskCandidateOrAssigned-java.lang.String-">taskCandidateOrAssigned</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userIdForCandidateAndAssignee)</code>
<div class="block">Select tasks that has been claimed or assigned to user or waiting to claim by user (candidate user or groups).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskQuery.html#taskDelegationState-org.activiti.engine.task.DelegationState-">taskDelegationState</a></span>(<a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a>&nbsp;delegationState)</code>
<div class="block">Only select tasks with the given <a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskQuery.html#taskUnassigned--">taskUnassigned</a></span>()</code>
<div class="block">Only select tasks which don't have an assignee.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/TaskQuery.html#taskUnnassigned--">taskUnnassigned</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.task.TaskInfoQuery">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.task.<a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task">TaskInfoQuery</a></h3>
<code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#deploymentId-java.lang.String-">deploymentId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#deploymentIdIn-java.util.List-">deploymentIdIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#dueAfter-java.util.Date-">dueAfter</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#dueBefore-java.util.Date-">dueBefore</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#dueDate-java.util.Date-">dueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#endOr--">endOr</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#executionId-java.lang.String-">executionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#includeProcessVariables--">includeProcessVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#includeTaskLocalVariables--">includeTaskLocalVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#limitTaskVariables-java.lang.Integer-">limitTaskVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#locale-java.lang.String-">locale</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#or--">or</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByDueDateNullsFirst--">orderByDueDateNullsFirst</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByDueDateNullsLast--">orderByDueDateNullsLast</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByExecutionId--">orderByExecutionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByProcessDefinitionId--">orderByProcessDefinitionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskAssignee--">orderByTaskAssignee</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskCreateTime--">orderByTaskCreateTime</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskDefinitionKey--">orderByTaskDefinitionKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskDescription--">orderByTaskDescription</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskDueDate--">orderByTaskDueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskId--">orderByTaskId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskName--">orderByTaskName</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskOwner--">orderByTaskOwner</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskPriority--">orderByTaskPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTenantId--">orderByTenantId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processCategoryIn-java.util.List-">processCategoryIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processCategoryNotIn-java.util.List-">processCategoryNotIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyIn-java.util.List-">processDefinitionKeyIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyLike-java.lang.String-">processDefinitionKeyLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyLikeIgnoreCase-java.lang.String-">processDefinitionKeyLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionName-java.lang.String-">processDefinitionName</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionNameLike-java.lang.String-">processDefinitionNameLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKey-java.lang.String-">processInstanceBusinessKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKeyLike-java.lang.String-">processInstanceBusinessKeyLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKeyLikeIgnoreCase-java.lang.String-">processInstanceBusinessKeyLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceId-java.lang.String-">processInstanceId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceIdIn-java.util.List-">processInstanceIdIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueEquals-java.lang.Object-">processVariableValueEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueEquals-java.lang.String-java.lang.Object-">processVariableValueEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">processVariableValueEqualsIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueGreaterThan-java.lang.String-java.lang.Object-">processVariableValueGreaterThan</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">processVariableValueGreaterThanOrEqual</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLessThan-java.lang.String-java.lang.Object-">processVariableValueLessThan</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLessThanOrEqual-java.lang.String-java.lang.Object-">processVariableValueLessThanOrEqual</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLike-java.lang.String-java.lang.String-">processVariableValueLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">processVariableValueLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueNotEquals-java.lang.String-java.lang.Object-">processVariableValueNotEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">processVariableValueNotEqualsIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssignee-java.lang.String-">taskAssignee</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeIds-java.util.List-">taskAssigneeIds</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeLike-java.lang.String-">taskAssigneeLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeLikeIgnoreCase-java.lang.String-">taskAssigneeLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateGroup-java.lang.String-">taskCandidateGroup</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateGroupIn-java.util.List-">taskCandidateGroupIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateUser-java.lang.String-">taskCandidateUser</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCategory-java.lang.String-">taskCategory</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCreatedAfter-java.util.Date-">taskCreatedAfter</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCreatedBefore-java.util.Date-">taskCreatedBefore</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCreatedOn-java.util.Date-">taskCreatedOn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDefinitionKey-java.lang.String-">taskDefinitionKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDefinitionKeyLike-java.lang.String-">taskDefinitionKeyLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescription-java.lang.String-">taskDescription</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescriptionLike-java.lang.String-">taskDescriptionLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescriptionLikeIgnoreCase-java.lang.String-">taskDescriptionLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDueAfter-java.util.Date-">taskDueAfter</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDueBefore-java.util.Date-">taskDueBefore</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDueDate-java.util.Date-">taskDueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskId-java.lang.String-">taskId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskInvolvedUser-java.lang.String-">taskInvolvedUser</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskMaxPriority-java.lang.Integer-">taskMaxPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskMinPriority-java.lang.Integer-">taskMinPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskName-java.lang.String-">taskName</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameIn-java.util.List-">taskNameIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameInIgnoreCase-java.util.List-">taskNameInIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameLike-java.lang.String-">taskNameLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameLikeIgnoreCase-java.lang.String-">taskNameLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwner-java.lang.String-">taskOwner</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwnerLike-java.lang.String-">taskOwnerLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwnerLikeIgnoreCase-java.lang.String-">taskOwnerLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskPriority-java.lang.Integer-">taskPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskTenantId-java.lang.String-">taskTenantId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskTenantIdLike-java.lang.String-">taskTenantIdLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueEquals-java.lang.Object-">taskVariableValueEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueEquals-java.lang.String-java.lang.Object-">taskVariableValueEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">taskVariableValueEqualsIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueGreaterThan-java.lang.String-java.lang.Object-">taskVariableValueGreaterThan</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">taskVariableValueGreaterThanOrEqual</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLessThan-java.lang.String-java.lang.Object-">taskVariableValueLessThan</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLessThanOrEqual-java.lang.String-java.lang.Object-">taskVariableValueLessThanOrEqual</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLike-java.lang.String-java.lang.String-">taskVariableValueLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">taskVariableValueLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueNotEquals-java.lang.String-java.lang.Object-">taskVariableValueNotEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">taskVariableValueNotEqualsIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskWithoutTenantId--">taskWithoutTenantId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#withLocalizationFallback--">withLocalizationFallback</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#withoutDueDate--">withoutDueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#withoutTaskDueDate--">withoutTaskDueDate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="taskUnassigned--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskUnassigned</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>&nbsp;taskUnassigned()</pre>
<div class="block">Only select tasks which don't have an assignee.</div>
</li>
</ul>
<a name="taskUnnassigned--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskUnnassigned</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
<a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>&nbsp;taskUnnassigned()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>#taskUnassigned}</code></dd>
</dl>
</li>
</ul>
<a name="taskDelegationState-org.activiti.engine.task.DelegationState-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDelegationState</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>&nbsp;taskDelegationState(<a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a>&nbsp;delegationState)</pre>
<div class="block">Only select tasks with the given <a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a>.</div>
</li>
</ul>
<a name="taskCandidateOrAssigned-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCandidateOrAssigned</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>&nbsp;taskCandidateOrAssigned(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userIdForCandidateAndAssignee)</pre>
<div class="block">Select tasks that has been claimed or assigned to user or waiting to claim by user (candidate user or groups).
  You can invoke <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateGroupIn-java.util.List-"><code>TaskInfoQuery.taskCandidateGroupIn(List)</code></a> to include tasks that can be claimed by a user in the given groups
  while set property <strong>dbIdentityUsed</strong> to <strong>false</strong> in process engine configuration
  or using custom session factory of GroupIdentityManager.</div>
</li>
</ul>
<a name="excludeSubtasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeSubtasks</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>&nbsp;excludeSubtasks()</pre>
<div class="block">Only select tasks that have no parent (i.e. do not select subtasks).</div>
</li>
</ul>
<a name="suspended--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suspended</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>&nbsp;suspended()</pre>
<div class="block">Only selects tasks which are suspended, because its process instance was suspended.</div>
</li>
</ul>
<a name="active--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>active</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>&nbsp;active()</pre>
<div class="block">Only selects tasks which are active (ie. not suspended)</div>
</li>
</ul>
<a name="orderByDueDate--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByDueDate</h4>
<pre><a href="../../../../org/activiti/engine/task/TaskQuery.html" title="interface in org.activiti.engine.task">TaskQuery</a>&nbsp;orderByDueDate()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use orderByTaskDueDate() instead</span></div>
<div class="block">Order by due date (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).
 This will use the default handling of null values of the used database.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TaskQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/task/TaskInfoQueryWrapper.html" title="class in org.activiti.engine.task"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/task/TaskQuery.html" target="_top">Frames</a></li>
<li><a href="TaskQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
