<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HistoryService (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HistoryService (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoryService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/HistoryService.html" target="_top">Frames</a></li>
<li><a href="HistoryService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine</div>
<h2 title="Interface HistoryService" class="title">Interface HistoryService</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">HistoryService</span></pre>
<div class="block">Service exposing information about ongoing and past process instances.  This is different
 from the runtime information in the sense that this runtime information only contains 
 the actual runtime state at any given moment and it is optimized for runtime 
 process execution performance.  The history information is optimized for easy 
 querying and remains permanent in the persistent storage.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Christian Stettler, Tom Baeyens, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createHistoricActivityInstanceQuery--">createHistoricActivityInstanceQuery</a></span>()</code>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a>s.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createHistoricDetailQuery--">createHistoricDetailQuery</a></span>()</code>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><code>HistoricDetail</code></a>s.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createHistoricProcessInstanceQuery--">createHistoricProcessInstanceQuery</a></span>()</code>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a>s.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createHistoricTaskInstanceQuery--">createHistoricTaskInstanceQuery</a></span>()</code>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstance</code></a>s.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createHistoricVariableInstanceQuery--">createHistoricVariableInstanceQuery</a></span>()</code>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><code>HistoricVariableInstance</code></a>s.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/NativeHistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricActivityInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createNativeHistoricActivityInstanceQuery--">createNativeHistoricActivityInstanceQuery</a></span>()</code>
<div class="block">creates a native query to search for <a href="../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a>s via SQL</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/NativeHistoricDetailQuery.html" title="interface in org.activiti.engine.history">NativeHistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createNativeHistoricDetailQuery--">createNativeHistoricDetailQuery</a></span>()</code>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for process definitions.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/NativeHistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricProcessInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createNativeHistoricProcessInstanceQuery--">createNativeHistoricProcessInstanceQuery</a></span>()</code>
<div class="block">creates a native query to search for <a href="../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a>s via SQL</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/NativeHistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createNativeHistoricTaskInstanceQuery--">createNativeHistoricTaskInstanceQuery</a></span>()</code>
<div class="block">creates a native query to search for <a href="../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstance</code></a>s via SQL</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/NativeHistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricVariableInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createNativeHistoricVariableInstanceQuery--">createNativeHistoricVariableInstanceQuery</a></span>()</code>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for process definitions.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#createProcessInstanceHistoryLogQuery-java.lang.String-">createProcessInstanceHistoryLogQuery</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Allows to retrieve the <a href="../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> for one process instance.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#deleteHistoricProcessInstance-java.lang.String-">deleteHistoricProcessInstance</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Deletes historic process instance.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#deleteHistoricTaskInstance-java.lang.String-">deleteHistoricTaskInstance</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Deletes historic task instance.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history">HistoricIdentityLink</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#getHistoricIdentityLinksForProcessInstance-java.lang.String-">getHistoricIdentityLinksForProcessInstance</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history"><code>HistoricIdentityLink</code></a>s associated with the given process instance.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history">HistoricIdentityLink</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/activiti/engine/HistoryService.html#getHistoricIdentityLinksForTask-java.lang.String-">getHistoricIdentityLinksForTask</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history"><code>HistoricIdentityLink</code></a>s associated with the given task.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createHistoricProcessInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHistoricProcessInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a>&nbsp;createHistoricProcessInstanceQuery()</pre>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a>s.</div>
</li>
</ul>
<a name="createHistoricActivityInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHistoricActivityInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a>&nbsp;createHistoricActivityInstanceQuery()</pre>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a>s.</div>
</li>
</ul>
<a name="createHistoricTaskInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHistoricTaskInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;createHistoricTaskInstanceQuery()</pre>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstance</code></a>s.</div>
</li>
</ul>
<a name="createHistoricDetailQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHistoricDetailQuery</h4>
<pre><a href="../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;createHistoricDetailQuery()</pre>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><code>HistoricDetail</code></a>s.</div>
</li>
</ul>
<a name="createNativeHistoricDetailQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeHistoricDetailQuery</h4>
<pre><a href="../../../org/activiti/engine/history/NativeHistoricDetailQuery.html" title="interface in org.activiti.engine.history">NativeHistoricDetailQuery</a>&nbsp;createNativeHistoricDetailQuery()</pre>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for process definitions.</div>
</li>
</ul>
<a name="createHistoricVariableInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHistoricVariableInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a>&nbsp;createHistoricVariableInstanceQuery()</pre>
<div class="block">Creates a new programmatic query to search for <a href="../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><code>HistoricVariableInstance</code></a>s.</div>
</li>
</ul>
<a name="createNativeHistoricVariableInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeHistoricVariableInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/history/NativeHistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricVariableInstanceQuery</a>&nbsp;createNativeHistoricVariableInstanceQuery()</pre>
<div class="block">Returns a new <a href="../../../org/activiti/engine/query/NativeQuery.html" title="interface in org.activiti.engine.query"><code>NativeQuery</code></a> for process definitions.</div>
</li>
</ul>
<a name="deleteHistoricTaskInstance-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteHistoricTaskInstance</h4>
<pre>void&nbsp;deleteHistoricTaskInstance(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">Deletes historic task instance.  This might be useful for tasks that are 
 <a href="../../../org/activiti/engine/TaskService.html#newTask--"><code>dynamically created</code></a> and then <a href="../../../org/activiti/engine/TaskService.html#complete-java.lang.String-"><code>completed</code></a>. 
 If the historic task instance doesn't exist, no exception is thrown and the 
 method returns normal.</div>
</li>
</ul>
<a name="deleteHistoricProcessInstance-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteHistoricProcessInstance</h4>
<pre>void&nbsp;deleteHistoricProcessInstance(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Deletes historic process instance. All historic activities, historic task and
 historic details (variable updates, form properties) are deleted as well.</div>
</li>
</ul>
<a name="createNativeHistoricProcessInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeHistoricProcessInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/history/NativeHistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricProcessInstanceQuery</a>&nbsp;createNativeHistoricProcessInstanceQuery()</pre>
<div class="block">creates a native query to search for <a href="../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a>s via SQL</div>
</li>
</ul>
<a name="createNativeHistoricTaskInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeHistoricTaskInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/history/NativeHistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricTaskInstanceQuery</a>&nbsp;createNativeHistoricTaskInstanceQuery()</pre>
<div class="block">creates a native query to search for <a href="../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstance</code></a>s via SQL</div>
</li>
</ul>
<a name="createNativeHistoricActivityInstanceQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNativeHistoricActivityInstanceQuery</h4>
<pre><a href="../../../org/activiti/engine/history/NativeHistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricActivityInstanceQuery</a>&nbsp;createNativeHistoricActivityInstanceQuery()</pre>
<div class="block">creates a native query to search for <a href="../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a>s via SQL</div>
</li>
</ul>
<a name="getHistoricIdentityLinksForTask-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHistoricIdentityLinksForTask</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history">HistoricIdentityLink</a>&gt;&nbsp;getHistoricIdentityLinksForTask(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history"><code>HistoricIdentityLink</code></a>s associated with the given task.
 Such an <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a> informs how a certain identity (eg. group or user)
 is associated with a certain task (eg. as candidate, assignee, etc.), even if the
 task is completed as opposed to <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a>s which only exist for active
 tasks.</div>
</li>
</ul>
<a name="getHistoricIdentityLinksForProcessInstance-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHistoricIdentityLinksForProcessInstance</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history">HistoricIdentityLink</a>&gt;&nbsp;getHistoricIdentityLinksForProcessInstance(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Retrieves the <a href="../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history"><code>HistoricIdentityLink</code></a>s associated with the given process instance.
 Such an <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a> informs how a certain identity (eg. group or user)
 is associated with a certain process instance, even if the instance is completed as 
 opposed to <a href="../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a>s which only exist for active instances.</div>
</li>
</ul>
<a name="createProcessInstanceHistoryLogQuery-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>createProcessInstanceHistoryLogQuery</h4>
<pre><a href="../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a>&nbsp;createProcessInstanceHistoryLogQuery(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Allows to retrieve the <a href="../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> for one process instance.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoryService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/HistoryService.html" target="_top">Frames</a></li>
<li><a href="HistoryService.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
