<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine.task (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../org/activiti/engine/task/package-summary.html" target="classFrame">org.activiti.engine.task</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="Attachment.html" title="interface in org.activiti.engine.task" target="classFrame"><span class="interfaceName">Attachment</span></a></li>
<li><a href="Comment.html" title="interface in org.activiti.engine.task" target="classFrame"><span class="interfaceName">Comment</span></a></li>
<li><a href="Event.html" title="interface in org.activiti.engine.task" target="classFrame"><span class="interfaceName">Event</span></a></li>
<li><a href="IdentityLink.html" title="interface in org.activiti.engine.task" target="classFrame"><span class="interfaceName">IdentityLink</span></a></li>
<li><a href="NativeTaskQuery.html" title="interface in org.activiti.engine.task" target="classFrame"><span class="interfaceName">NativeTaskQuery</span></a></li>
<li><a href="Task.html" title="interface in org.activiti.engine.task" target="classFrame"><span class="interfaceName">Task</span></a></li>
<li><a href="TaskInfo.html" title="interface in org.activiti.engine.task" target="classFrame"><span class="interfaceName">TaskInfo</span></a></li>
<li><a href="TaskInfoQuery.html" title="interface in org.activiti.engine.task" target="classFrame"><span class="interfaceName">TaskInfoQuery</span></a></li>
<li><a href="TaskQuery.html" title="interface in org.activiti.engine.task" target="classFrame"><span class="interfaceName">TaskQuery</span></a></li>
</ul>
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="IdentityLinkType.html" title="class in org.activiti.engine.task" target="classFrame">IdentityLinkType</a></li>
<li><a href="TaskInfoQueryWrapper.html" title="class in org.activiti.engine.task" target="classFrame">TaskInfoQueryWrapper</a></li>
</ul>
<h2 title="Enums">Enums</h2>
<ul title="Enums">
<li><a href="DelegationState.html" title="enum in org.activiti.engine.task" target="classFrame">DelegationState</a></li>
</ul>
</div>
</body>
</html>
