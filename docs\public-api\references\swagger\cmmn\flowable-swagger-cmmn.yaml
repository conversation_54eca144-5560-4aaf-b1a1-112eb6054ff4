---
swagger: "2.0"
info:
  description: "# flowable / flowəb(ə)l /\r\n\r\n- a compact and highly efficient\
    \ workflow and Business Process Management (BPM) platform for developers, system\
    \ admins and business users.\r\n- a lightning fast, tried and tested BPMN 2 process\
    \ engine written in Java. It is Apache 2.0 licensed open source, with a committed\
    \ community.\r\n- can run embedded in a Java application, or as a service on a\
    \ server, a cluster, and in the cloud. It integrates perfectly with Spring. With\
    \ a rich Java and REST API, it is the ideal engine for orchestrating human or\
    \ system activities."
  version: "v1"
  title: "Flowable CMMN REST API"
  contact:
    name: "Flowable"
    url: "http://www.flowable.org/"
  license:
    name: "Apache 2.0"
    url: "http://www.apache.org/licenses/LICENSE-2.0.html"
basePath: "/flowable-rest/cmmn-api"
tags:
- name: "CMMN Deployment"
- name: "Case Definitions"
- name: "Case Instance Identity Links"
- name: "Case Instance Variables"
- name: "Case Instances"
- name: "Cmmn Deployment"
- name: "Cmmn Engine"
- name: "CmmnDeployment"
- name: "Deployment"
- name: "History"
- name: "History Case"
- name: "History Milestone"
- name: "History PlanItem"
- name: "History Process"
- name: "History Task"
- name: "Jobs"
- name: "Plan Item Instances"
- name: "Process Definitions"
- name: "Query"
- name: "Task Identity Links"
- name: "Task Variables"
- name: "Tasks"
schemes:
- "http"
- "https"
paths:
  /cmmn-history/historic-case-instances:
    get:
      tags:
      - "History Case"
      summary: "List of historic case instances"
      description: ""
      operationId: "listHistoricCaseInstances"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "query"
        description: "An id of the historic case instance."
        required: false
        type: "string"
      - name: "caseDefinitionKey"
        in: "query"
        description: "The process definition key of the historic case instance."
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "The process definition id of the historic case instance."
        required: false
        type: "string"
      - name: "businessKey"
        in: "query"
        description: "The business key of the historic case instance."
        required: false
        type: "string"
      - name: "involvedUser"
        in: "query"
        description: "An involved user of the historic case instance."
        required: false
        type: "string"
      - name: "finished"
        in: "query"
        description: "Indication if the historic case instance is finished."
        required: false
        type: "boolean"
      - name: "finishedAfter"
        in: "query"
        description: "Return only historic case instances that were finished after\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "finishedBefore"
        in: "query"
        description: "Return only historic case instances that were finished before\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "startedAfter"
        in: "query"
        description: "Return only historic case instances that were started after\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "startedBefore"
        in: "query"
        description: "Return only historic case instances that were started before\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "startedBy"
        in: "query"
        description: "Return only historic case instances that were started by this\
          \ user."
        required: false
        type: "string"
      - name: "includeCaseVariables"
        in: "query"
        description: "An indication if the historic case instance variables should\
          \ be returned as well."
        required: false
        type: "boolean"
      - name: "tenantId"
        in: "query"
        description: "Only return instances with the given tenantId."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns instances without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored.\n"
        required: false
        type: "boolean"
      responses:
        200:
          description: "Indicates that historic case instances could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricCaseInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-history/historic-case-instances/{caseInstanceId}:
    get:
      tags:
      - "History Case"
      summary: "Get a historic case instance"
      description: ""
      operationId: "getHistoricCaseInstance"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates that the historic process instances could be found."
          schema:
            $ref: "#/definitions/HistoricCaseInstanceResponse"
        404:
          description: "Indicates that the historic process instances could not be\
            \ found."
      security:
      - basicAuth: []
    delete:
      tags:
      - "History Case"
      summary: " Delete a historic case instance"
      description: ""
      operationId: "deleteHistoricCaseInstance"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates that the historic process instance was deleted."
        404:
          description: "Indicates that the historic process instance could not be\
            \ found."
      security:
      - basicAuth: []
  /cmmn-history/historic-case-instances/{caseInstanceId}/identitylinks:
    get:
      tags:
      - "History Case"
      summary: "List identity links of a historic case instance"
      description: ""
      operationId: "listHistoricCaseInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the identity links are\
            \ returned"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/HistoricIdentityLinkResponse"
        404:
          description: "Indicates the process instance could not be found.."
      security:
      - basicAuth: []
  /cmmn-history/historic-case-instances/{caseInstanceId}/stage-overview:
    get:
      tags:
      - "History Case"
      operationId: "getStageOverview"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/StageResponse"
      security:
      - basicAuth: []
  /cmmn-history/historic-case-instances/{caseInstanceId}/variables/{variableName}/data:
    get:
      tags:
      - "History Process"
      summary: "Get the binary data for a historic case instance variable"
      description: "The response body contains the binary value of the variable. When\
        \ the variable is of type binary, the content-type of the response is set\
        \ to application/octet-stream, regardless of the content of the variable or\
        \ the request accept-type header. In case of serializable, application/x-java-serialized-object\
        \ is used as content-type."
      operationId: "getHistoricCaseInstanceVariableData"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case instance was found and the requested variable\
            \ data is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested case instance was not found or the\
            \ process instance does not have a variable with the given name or the\
            \ variable does not have a binary stream available. Status message provides\
            \ additional information."
      security:
      - basicAuth: []
  /cmmn-history/historic-milestone-instances:
    get:
      tags:
      - "History Milestone"
      summary: "List of historic milestone instances"
      description: ""
      operationId: "listHistoricMilestoneInstances"
      produces:
      - "application/json"
      parameters:
      - name: "milestoneId"
        in: "query"
        description: "An id of the historic milestone instance."
        required: false
        type: "string"
      - name: "milestoneName"
        in: "query"
        description: "The name of the historic milestone instance"
        required: false
        type: "string"
      - name: "caseInstanceId"
        in: "query"
        description: "The id of the case instance containing the milestone."
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "The id of the definition of the case where the milestone is\
          \ defined."
        required: false
        type: "string"
      - name: "reachedBefore"
        in: "query"
        description: "Return only historic milestone instances that were reached before\
          \ this date."
        required: false
        type: "string"
      - name: "reachedAfter"
        in: "query"
        description: "Return only historic milestone instances that were reached after\
          \ this date."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates that historic milestone instances could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricMilestoneInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-history/historic-milestone-instances/{milestoneInstanceId}:
    get:
      tags:
      - "History Milestone"
      summary: "Get a historic milestone instance by id"
      description: ""
      operationId: "getHistoricMilestoneInstanceById"
      produces:
      - "application/json"
      parameters:
      - name: "milestoneInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates that the historic milestone instances could be found."
          schema:
            $ref: "#/definitions/HistoricMilestoneInstanceResponse"
        404:
          description: "Indicates that the historic milestone instances could not\
            \ be found."
      security:
      - basicAuth: []
  /cmmn-history/historic-planitem-instances:
    get:
      tags:
      - "History PlanItem"
      summary: "List of historic plan item instances"
      description: ""
      operationId: "listHistoricPlanItemInstances"
      produces:
      - "application/json"
      parameters:
      - name: "planItemInstanceId"
        in: "query"
        description: "The id of the historic planItem instance."
        required: false
        type: "string"
      - name: "planItemInstanceName"
        in: "query"
        description: "The name of the historic planItem instance."
        required: false
        type: "string"
      - name: "planItemInstanceState"
        in: "query"
        description: "The state of the historic planItem instance."
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "The id of the definition of the case were the historic planItem\
          \ instance is defined."
        required: false
        type: "string"
      - name: "caseInstanceId"
        in: "query"
        description: "The id of the case instance were the historic planItem instance\
          \ existed."
        required: false
        type: "string"
      - name: "stageInstanceId"
        in: "query"
        description: "The id of the stage were the historic planItem instance was\
          \ contained."
        required: false
        type: "string"
      - name: "elementId"
        in: "query"
        description: "The id of the planItem model of the historic planItem instance."
        required: false
        type: "string"
      - name: "planItemDefinitionId"
        in: "query"
        description: "The id of the planItem model definition of the historic planItem\
          \ instance."
        required: false
        type: "string"
      - name: "planItemDefinitionType"
        in: "query"
        description: "The type of planItem of the historic planItem instance."
        required: false
        type: "string"
      - name: "createdBefore"
        in: "query"
        description: "Return only historic planItem instances that were created before\
          \ this date."
        required: false
        type: "date-time"
      - name: "createdAfter"
        in: "query"
        description: "Return only historic planItem instances that were created after\
          \ this date."
        required: false
        type: "date-time"
      - name: "lastAvailableBefore"
        in: "query"
        description: "Return only historic planItem instances that were last in available\
          \ before this date."
        required: false
        type: "date-time"
      - name: "lastAvailableAfter"
        in: "query"
        description: "Return only historic planItem instances that were last in available\
          \ state after this date."
        required: false
        type: "date-time"
      - name: "lastEnabledBefore"
        in: "query"
        description: "Return only historic planItem instances that were last in enabled\
          \ state before this date."
        required: false
        type: "date-time"
      - name: "lastEnabledAfter"
        in: "query"
        description: "Return only historic planItem instances that were last in enabled\
          \ state after this date."
        required: false
        type: "date-time"
      - name: "lastDisabledBefore"
        in: "query"
        description: "Return only historic planItem instances that were last in disable\
          \ state before this date."
        required: false
        type: "date-time"
      - name: "lastDisabledAfter"
        in: "query"
        description: "Return only historic planItem instances that were last in disabled\
          \ state after this date."
        required: false
        type: "date-time"
      - name: "lastStartedBefore"
        in: "query"
        description: "Return only historic planItem instances that were last in active\
          \ state before this date."
        required: false
        type: "date-time"
      - name: "lastStartedAfter"
        in: "query"
        description: "Return only historic planItem instances that were last in active\
          \ state after this date."
        required: false
        type: "date-time"
      - name: "lastSuspendedBefore"
        in: "query"
        description: "Return only historic planItem instances that were last in suspended\
          \ state before this date."
        required: false
        type: "date-time"
      - name: "lastSuspendedAfter"
        in: "query"
        description: "Return only historic planItem instances that were last in suspended\
          \ state after this date."
        required: false
        type: "date-time"
      - name: "completedBefore"
        in: "query"
        description: "Return only historic planItem instances that were completed\
          \ before this date."
        required: false
        type: "date-time"
      - name: "completedAfter"
        in: "query"
        description: "Return only historic planItem instances that were completed\
          \ after this date."
        required: false
        type: "date-time"
      - name: "terminatedBefore"
        in: "query"
        description: "Return only historic planItem instances that were terminated\
          \ before this date."
        required: false
        type: "date-time"
      - name: "terminatedAfter"
        in: "query"
        description: "Return only historic planItem instances that were terminated\
          \ after this date."
        required: false
        type: "date-time"
      - name: "occurredBefore"
        in: "query"
        description: "Return only historic planItem instances that occurred before\
          \ this date."
        required: false
        type: "date-time"
      - name: "occurredAfter"
        in: "query"
        description: "Return only historic planItem instances that occurred after\
          \ after this date."
        required: false
        type: "date-time"
      - name: "exitBefore"
        in: "query"
        description: "Return only historic planItem instances that exit before this\
          \ date."
        required: false
        type: "date-time"
      - name: "exitAfter"
        in: "query"
        description: "Return only historic planItem instances that exit after this\
          \ date."
        required: false
        type: "date-time"
        format: "date-time"
      - name: "endedBefore"
        in: "query"
        description: "Return only historic planItem instances that ended before this\
          \ date."
        required: false
        type: "date-time"
        format: "date-time"
      - name: "endedAfter"
        in: "query"
        description: "Return only historic planItem instances that ended after this\
          \ date."
        required: false
        type: "date-time"
        format: "date-time"
      - name: "startUserId"
        in: "query"
        description: "Return only historic planItem instances that were started by\
          \ this user."
        required: false
        type: "string"
        format: "date-time"
      - name: "referenceId"
        in: "query"
        description: "The id of process that was referenced by this historic planItem\
          \ instance."
        required: false
        type: "string"
      - name: "referenceType"
        in: "query"
        description: "The type of reference to the process referenced by this historic\
          \ planItem instance."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return instances with the given tenantId."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns instances without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored.\n"
        required: false
        type: "boolean"
      responses:
        200:
          description: "Indicates that historic planItem instances could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricPlanItemInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-history/historic-planitem-instances/{planItemInstanceId}:
    get:
      tags:
      - "History PlanItem"
      summary: "Get a historic plan item instance"
      description: ""
      operationId: "getHistoricPlanItemInstance"
      produces:
      - "application/json"
      parameters:
      - name: "planItemInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates that the historic plan item instances could be found."
          schema:
            $ref: "#/definitions/HistoricPlanItemInstanceResponse"
        404:
          description: "Indicates that the historic plan item instances could not\
            \ be found."
      security:
      - basicAuth: []
  /cmmn-history/historic-task-instances:
    get:
      tags:
      - "History Task"
      summary: "List historic task instances"
      description: ""
      operationId: "listHistoricTaskInstances"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "query"
        description: "An id of the historic task instance."
        required: false
        type: "string"
      - name: "caseInstanceId"
        in: "query"
        description: "The case instance id of the historic task instance."
        required: false
        type: "string"
      - name: "caseInstanceIdWithChildren"
        in: "query"
        description: "Selects the historic task instance of a case instance and its\
          \ children."
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "The case definition id of the historic task instance."
        required: false
        type: "string"
      - name: "taskDefinitionKey"
        in: "query"
        description: "The task definition key for tasks part of a process"
        required: false
        type: "string"
      - name: "taskName"
        in: "query"
        description: "The task name of the historic task instance."
        required: false
        type: "string"
      - name: "taskNameLike"
        in: "query"
        description: "The task name with like operator for the historic task instance."
        required: false
        type: "string"
      - name: "taskDescription"
        in: "query"
        description: "The task description of the historic task instance."
        required: false
        type: "string"
      - name: "taskDescriptionLike"
        in: "query"
        description: "The task description with like operator for the historic task\
          \ instance."
        required: false
        type: "string"
      - name: "taskCategory"
        in: "query"
        description: "Select tasks with the given category. Note that this is the\
          \ task category, not the category of the process definition (namespace within\
          \ the BPMN Xml)."
        required: false
        type: "string"
      - name: "taskDeleteReason"
        in: "query"
        description: "The task delete reason of the historic task instance."
        required: false
        type: "string"
      - name: "taskDeleteReasonLike"
        in: "query"
        description: "The task delete reason with like operator for the historic task\
          \ instance."
        required: false
        type: "string"
      - name: "taskAssignee"
        in: "query"
        description: "The assignee of the historic task instance."
        required: false
        type: "string"
      - name: "taskAssigneeLike"
        in: "query"
        description: "The assignee with like operator for the historic task instance."
        required: false
        type: "string"
      - name: "taskOwner"
        in: "query"
        description: "The owner of the historic task instance."
        required: false
        type: "string"
      - name: "taskOwnerLike"
        in: "query"
        description: "The owner with like operator for the historic task instance."
        required: false
        type: "string"
      - name: "taskInvolvedUser"
        in: "query"
        description: "An involved user of the historic task instance"
        required: false
        type: "string"
      - name: "taskPriority"
        in: "query"
        description: "The priority of the historic task instance."
        required: false
        type: "string"
      - name: "finished"
        in: "query"
        description: "Indication if the historic task instance is finished."
        required: false
        type: "boolean"
      - name: "processFinished"
        in: "query"
        description: "Indication if the process instance of the historic task instance\
          \ is finished."
        required: false
        type: "boolean"
      - name: "parentTaskId"
        in: "query"
        description: "An optional parent task id of the historic task instance."
        required: false
        type: "string"
      - name: "dueDate"
        in: "query"
        description: "Return only historic task instances that have a due date equal\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueDateAfter"
        in: "query"
        description: "Return only historic task instances that have a due date after\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueDateBefore"
        in: "query"
        description: "Return only historic task instances that have a due date before\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "withoutDueDate"
        in: "query"
        description: "Return only historic task instances that have no due-date. When\
          \ false is provided as value, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "taskCompletedOn"
        in: "query"
        description: "Return only historic task instances that have been completed\
          \ on this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCompletedAfter"
        in: "query"
        description: "Return only historic task instances that have been completed\
          \ after this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCompletedBefore"
        in: "query"
        description: "Return only historic task instances that have been completed\
          \ before this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCreatedOn"
        in: "query"
        description: "Return only historic task instances that were created on this\
          \ date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCreatedBefore"
        in: "query"
        description: "Return only historic task instances that were created before\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "taskCreatedAfter"
        in: "query"
        description: "Return only historic task instances that were created after\
          \ this date."
        required: false
        type: "string"
        format: "date-time"
      - name: "includeTaskLocalVariables"
        in: "query"
        description: "An indication if the historic task instance local variables\
          \ should be returned as well."
        required: false
        type: "boolean"
      - name: "tenantId"
        in: "query"
        description: "Only return historic task instances with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return historic task instances with a tenantId like the\
          \ given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns historic task instances without a tenantId\
          \ set. If false, the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      responses:
        200:
          description: "Indicates that historic task instances could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricTaskInstanceResponse"
        404:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-history/historic-task-instances/{taskId}:
    get:
      tags:
      - "History Task"
      summary: "Get a single historic task instance"
      description: ""
      operationId: "getTaskInstance"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates that the historic task instances could be found."
          schema:
            $ref: "#/definitions/HistoricTaskInstanceResponse"
        404:
          description: "Indicates that the historic task instances could not be found."
      security:
      - basicAuth: []
    delete:
      tags:
      - "History Task"
      summary: "Delete a historic task instance"
      description: ""
      operationId: "deleteTaskInstance"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates that the historic task instance was deleted."
        404:
          description: "Indicates that the historic task instance could not be found."
      security:
      - basicAuth: []
  /cmmn-history/historic-task-instances/{taskId}/form:
    get:
      tags:
      - "History Task"
      summary: "Get a historic task instance form"
      description: ""
      operationId: "getTaskForm"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the task form is returned"
          schema:
            type: "string"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /cmmn-history/historic-task-instances/{taskId}/identitylinks:
    get:
      tags:
      - "History Task"
      summary: "List identity links of a historic task instance"
      description: ""
      operationId: "listHistoricTaskInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the identity links are\
            \ returned"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/HistoricIdentityLinkResponse"
        404:
          description: "Indicates the task instance could not be found."
      security:
      - basicAuth: []
  /cmmn-history/historic-task-instances/{taskId}/variables/{variableName}/data:
    get:
      tags:
      - "History"
      summary: "Get the binary data for a historic task instance variable"
      description: "The response body contains the binary value of the variable. When\
        \ the variable is of type binary, the content-type of the response is set\
        \ to application/octet-stream, regardless of the content of the variable or\
        \ the request accept-type header. In case of serializable, application/x-java-serialized-object\
        \ is used as content-type."
      operationId: "getHistoricTaskInstanceVariableData"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the task instance was found and the requested variable\
            \ data is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested task instance was not found or the\
            \ process instance does not have a variable with the given name or the\
            \ variable does not  have a binary stream available. Status message provides\
            \ additional information."
      security:
      - basicAuth: []
  /cmmn-history/historic-variable-instances:
    get:
      tags:
      - "History"
      summary: "List of historic variable instances"
      description: ""
      operationId: "listHistoricVariableInstances"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "query"
        description: "The case instance id of the historic variable instance."
        required: false
        type: "string"
      - name: "taskId"
        in: "query"
        description: "The task id of the historic variable instance."
        required: false
        type: "string"
      - name: "excludeTaskVariables"
        in: "query"
        description: "Indication to exclude the task variables from the result."
        required: false
        type: "boolean"
      - name: "variableName"
        in: "query"
        description: "The variable name of the historic variable instance."
        required: false
        type: "string"
      - name: "variableNameLike"
        in: "query"
        description: "The variable name using the like operator for the historic variable\
          \ instance."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates that historic variable instances could be queried."
          schema:
            $ref: "#/definitions/DataResponseHistoricVariableInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-history/historic-variable-instances/{varInstanceId}/data:
    get:
      tags:
      - "History"
      summary: "Get the binary data for a historic task instance variable"
      description: "The response body contains the binary value of the variable. When\
        \ the variable is of type binary, the content-type of the response is set\
        \ to application/octet-stream, regardless of the content of the variable or\
        \ the request accept-type header. In case of serializable, application/x-java-serialized-object\
        \ is used as content-type."
      operationId: "getHistoricInstanceVariableData"
      parameters:
      - name: "varInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the variable instance was found and the requested\
            \ variable data is returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested variable instance was not found or\
            \ the variable instance does not have a variable with the given name or\
            \ the variable does not have a binary stream available. Status message\
            \ provides additional information."
      security:
      - basicAuth: []
  /cmmn-management/deadletter-jobs:
    get:
      tags:
      - "Jobs"
      summary: "List deadletter jobs"
      description: ""
      operationId: "listDeadLetterJobs"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return job with the given id"
        required: false
        type: "string"
      - name: "caseInstanceId"
        in: "query"
        description: "Only return jobs part of a case with the given id"
        required: false
        type: "string"
      - name: "planItemInstanceId"
        in: "query"
        description: "Only return jobs part of a plan item instance with the given\
          \ id"
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "Only return jobs with the given process definition id"
        required: false
        type: "string"
      - name: "timersOnly"
        in: "query"
        description: "If true, only return jobs which are timers. If false, this parameter\
          \ is ignored. Cannot be used together with 'messagesOnly'."
        required: false
        type: "boolean"
      - name: "messagesOnly"
        in: "query"
        description: "If true, only return jobs which are messages. If false, this\
          \ parameter is ignored. Cannot be used together with 'timersOnly'"
        required: false
        type: "boolean"
      - name: "withException"
        in: "query"
        description: "If true, only return jobs for which an exception occurred while\
          \ executing it. If false, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "dueBefore"
        in: "query"
        description: "Only return jobs which are due to be executed before the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return jobs which are due to be executed after the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "exceptionMessage"
        in: "query"
        description: "Only return jobs with the given exception message"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return jobs with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return jobs with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns jobs without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "locked"
        in: "query"
        description: "If true, only return jobs which are locked.  If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "unlocked"
        in: "query"
        description: "If true, only return jobs which are unlocked. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "dueDate"
        - "executionId"
        - "processInstanceId"
        - "retries"
        - "tenantId"
      responses:
        200:
          description: "Indicates the requested jobs were returned."
          schema:
            $ref: "#/definitions/DataResponseJobResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter\
            \ or the both 'messagesOnly' and 'timersOnly' are used as parameters.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /cmmn-management/deadletter-jobs/{jobId}:
    get:
      tags:
      - "Jobs"
      summary: "Get a single deadletter job"
      description: ""
      operationId: "getDeadletterJob"
      produces:
      - "application/json"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the suspended job exists and is returned."
          schema:
            $ref: "#/definitions/JobResponse"
        404:
          description: "Indicates the requested job does not exist."
      security:
      - basicAuth: []
    post:
      tags:
      - "Jobs"
      summary: "Move a single deadletter job"
      description: ""
      operationId: "executeDeadLetterJobAction"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestActionRequest"
      responses:
        204:
          description: "Indicates the dead letter job was moved. Response-body is\
            \ intentionally empty."
        404:
          description: "Indicates the requested job was not found."
        500:
          description: "Indicates the an exception occurred while executing the job.\
            \ The status-description contains additional detail about the error. The\
            \ full error-stacktrace can be fetched later on if needed."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Jobs"
      summary: "Delete a deadletter job"
      description: ""
      operationId: "deleteDeadLetterJob"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the job was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested job was not found."
      security:
      - basicAuth: []
  /cmmn-management/deadletter-jobs/{jobId}/exception-stacktrace:
    get:
      tags:
      - "Jobs"
      summary: "Get the exception stacktrace for a deadletter job"
      description: ""
      operationId: "getDeadLetterJobStacktrace"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested job was not found and the stacktrace\
            \ has been returned. The response contains the raw stacktrace and always\
            \ has a Content-type of text/plain."
          schema:
            type: "string"
        404:
          description: "Indicates the requested job was not found or the job does\
            \ not have an exception stacktrace. Status-description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /cmmn-management/engine:
    get:
      tags:
      - "Cmmn Engine"
      summary: "Get engine info"
      description: ""
      operationId: "getEngineInfo"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "Indicates the engine info is returned."
          schema:
            $ref: "#/definitions/CmmnEngineInfoResponse"
      security:
      - basicAuth: []
  /cmmn-management/jobs:
    get:
      tags:
      - "Jobs"
      summary: "List jobs"
      description: ""
      operationId: "listJobs"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return job with the given id"
        required: false
        type: "string"
      - name: "caseInstanceId"
        in: "query"
        description: "Only return jobs part of a case with the given id"
        required: false
        type: "string"
      - name: "planItemInstanceId"
        in: "query"
        description: "Only return jobs part of a plan item instance with the given\
          \ id"
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "Only return jobs with the given process definition id"
        required: false
        type: "string"
      - name: "timersOnly"
        in: "query"
        description: "If true, only return jobs which are timers. If false, this parameter\
          \ is ignored. Cannot be used together with 'messagesOnly'."
        required: false
        type: "boolean"
      - name: "messagesOnly"
        in: "query"
        description: "If true, only return jobs which are messages. If false, this\
          \ parameter is ignored. Cannot be used together with 'timersOnly'"
        required: false
        type: "boolean"
      - name: "withException"
        in: "query"
        description: "If true, only return jobs for which an exception occurred while\
          \ executing it. If false, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "dueBefore"
        in: "query"
        description: "Only return jobs which are due to be executed before the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return jobs which are due to be executed after the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "exceptionMessage"
        in: "query"
        description: "Only return jobs with the given exception message"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return jobs with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return jobs with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns jobs without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "locked"
        in: "query"
        description: "If true, only return jobs which are locked.  If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "unlocked"
        in: "query"
        description: "If true, only return jobs which are unlocked. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "dueDate"
        - "executionId"
        - "processInstanceId"
        - "retries"
        - "tenantId"
      responses:
        200:
          description: "Indicates the requested jobs were returned."
          schema:
            $ref: "#/definitions/DataResponseJobResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter\
            \ or the both 'messagesOnly' and 'timersOnly' are used as parameters.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /cmmn-management/jobs/{jobId}:
    get:
      tags:
      - "Jobs"
      summary: "Get a single job"
      description: ""
      operationId: "getJob"
      produces:
      - "application/json"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the job exists and is returned."
          schema:
            $ref: "#/definitions/JobResponse"
        404:
          description: "Indicates the requested job does not exist."
      security:
      - basicAuth: []
    post:
      tags:
      - "Jobs"
      summary: "Execute a single job"
      description: ""
      operationId: "executeJobAction"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestActionRequest"
      responses:
        204:
          description: "Indicates the job was executed. Response-body is intentionally\
            \ empty."
        404:
          description: "Indicates the requested job was not found."
        500:
          description: "Indicates the an exception occurred while executing the job.\
            \ The status-description contains additional detail about the error. The\
            \ full error-stacktrace can be fetched later on if needed."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Jobs"
      summary: "Delete a job"
      description: ""
      operationId: "deleteJob"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the job was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested job was not found.."
      security:
      - basicAuth: []
  /cmmn-management/jobs/{jobId}/exception-stacktrace:
    get:
      tags:
      - "Jobs"
      summary: "Get the exception stacktrace for a job"
      description: ""
      operationId: "getJobStacktrace"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested job was not found and the stacktrace\
            \ has been returned. The response contains the raw stacktrace and always\
            \ has a Content-type of text/plain."
          schema:
            type: "string"
        404:
          description: "Indicates the requested job was not found or the job does\
            \ not have an exception stacktrace. Status-description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /cmmn-management/suspended-jobs:
    get:
      tags:
      - "Jobs"
      summary: "List suspended jobs"
      description: ""
      operationId: "listSuspendedJobs"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return job with the given id"
        required: false
        type: "string"
      - name: "caseInstanceId"
        in: "query"
        description: "Only return jobs part of a case with the given id"
        required: false
        type: "string"
      - name: "planItemInstanceId"
        in: "query"
        description: "Only return jobs part of a plan item instance with the given\
          \ id"
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "Only return jobs with the given process definition id"
        required: false
        type: "string"
      - name: "timersOnly"
        in: "query"
        description: "If true, only return jobs which are timers. If false, this parameter\
          \ is ignored. Cannot be used together with 'messagesOnly'."
        required: false
        type: "boolean"
      - name: "messagesOnly"
        in: "query"
        description: "If true, only return jobs which are messages. If false, this\
          \ parameter is ignored. Cannot be used together with 'timersOnly'"
        required: false
        type: "boolean"
      - name: "withException"
        in: "query"
        description: "If true, only return jobs for which an exception occurred while\
          \ executing it. If false, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "dueBefore"
        in: "query"
        description: "Only return jobs which are due to be executed before the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return jobs which are due to be executed after the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "exceptionMessage"
        in: "query"
        description: "Only return jobs with the given exception message"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return jobs with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return jobs with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns jobs without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "locked"
        in: "query"
        description: "If true, only return jobs which are locked.  If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "unlocked"
        in: "query"
        description: "If true, only return jobs which are unlocked. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "dueDate"
        - "executionId"
        - "processInstanceId"
        - "retries"
        - "tenantId"
      responses:
        200:
          description: "Indicates the requested jobs were returned."
          schema:
            $ref: "#/definitions/DataResponseJobResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter\
            \ or the both 'messagesOnly' and 'timersOnly' are used as parameters.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /cmmn-management/suspended-jobs/{jobId}:
    get:
      tags:
      - "Jobs"
      summary: "Get a single suspended job"
      description: ""
      operationId: "getSuspendedJob"
      produces:
      - "application/json"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the suspended job exists and is returned."
          schema:
            $ref: "#/definitions/JobResponse"
        404:
          description: "Indicates the requested job does not exist."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Jobs"
      summary: "Delete a suspended job"
      description: ""
      operationId: "deleteSuspendedJob"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the job was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested job was not found."
      security:
      - basicAuth: []
  /cmmn-management/suspended-jobs/{jobId}/exception-stacktrace:
    get:
      tags:
      - "Jobs"
      summary: "Get the exception stacktrace for a suspended job"
      description: ""
      operationId: "getSuspendedJobStacktrace"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested job was not found and the stacktrace\
            \ has been returned. The response contains the raw stacktrace and always\
            \ has a Content-type of text/plain."
          schema:
            type: "string"
        404:
          description: "Indicates the requested job was not found or the job does\
            \ not have an exception stacktrace. Status-description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /cmmn-management/timer-jobs:
    get:
      tags:
      - "Jobs"
      summary: "List timer jobs"
      description: ""
      operationId: "listTimerJobs"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return job with the given id"
        required: false
        type: "string"
      - name: "caseInstanceId"
        in: "query"
        description: "Only return jobs part of a case with the given id"
        required: false
        type: "string"
      - name: "planItemInstanceId"
        in: "query"
        description: "Only return jobs part of a plan item instance with the given\
          \ id"
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "Only return jobs with the given process definition id"
        required: false
        type: "string"
      - name: "timersOnly"
        in: "query"
        description: "If true, only return jobs which are timers. If false, this parameter\
          \ is ignored. Cannot be used together with 'messagesOnly'."
        required: false
        type: "boolean"
      - name: "messagesOnly"
        in: "query"
        description: "If true, only return jobs which are messages. If false, this\
          \ parameter is ignored. Cannot be used together with 'timersOnly'"
        required: false
        type: "boolean"
      - name: "withException"
        in: "query"
        description: "If true, only return jobs for which an exception occurred while\
          \ executing it. If false, this parameter is ignored."
        required: false
        type: "boolean"
      - name: "dueBefore"
        in: "query"
        description: "Only return jobs which are due to be executed before the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return jobs which are due to be executed after the given\
          \ date. Jobs without duedate are never returned using this parameter."
        required: false
        type: "string"
        format: "date-time"
      - name: "exceptionMessage"
        in: "query"
        description: "Only return jobs with the given exception message"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return jobs with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return jobs with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns jobs without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "locked"
        in: "query"
        description: "If true, only return jobs which are locked.  If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "unlocked"
        in: "query"
        description: "If true, only return jobs which are unlocked. If false, this\
          \ parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "dueDate"
        - "executionId"
        - "processInstanceId"
        - "retries"
        - "tenantId"
      responses:
        200:
          description: "Indicates the requested jobs were returned."
          schema:
            $ref: "#/definitions/DataResponseJobResponse"
        400:
          description: "Indicates an illegal value has been used in a url query parameter\
            \ or the both 'messagesOnly' and 'timersOnly' are used as parameters.\
            \ Status description contains additional details about the error."
      security:
      - basicAuth: []
  /cmmn-management/timer-jobs/{jobId}:
    get:
      tags:
      - "Jobs"
      summary: "Get a single timer job"
      description: ""
      operationId: "getTimerJob"
      produces:
      - "application/json"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the timer job exists and is returned."
          schema:
            $ref: "#/definitions/JobResponse"
        404:
          description: "Indicates the requested job does not exist."
      security:
      - basicAuth: []
    post:
      tags:
      - "Jobs"
      summary: "Move a single timer job"
      description: ""
      operationId: "executeTimerJobAction"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestActionRequest"
      responses:
        204:
          description: "Indicates the timer job was moved. Response-body is intentionally\
            \ empty."
        404:
          description: "Indicates the requested job was not found."
        500:
          description: "Indicates the an exception occurred while executing the job.\
            \ The status-description contains additional detail about the error. The\
            \ full error-stacktrace can be fetched later on if needed."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Jobs"
      summary: "Delete a timer job"
      description: ""
      operationId: "deleteTimerJob"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the job was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested job was not found."
      security:
      - basicAuth: []
  /cmmn-management/timer-jobs/{jobId}/exception-stacktrace:
    get:
      tags:
      - "Jobs"
      summary: "Get the exception stacktrace for a timer job"
      description: ""
      operationId: "getTimerJobStacktrace"
      parameters:
      - name: "jobId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the requested job was not found and the stacktrace\
            \ has been returned. The response contains the raw stacktrace and always\
            \ has a Content-type of text/plain."
          schema:
            type: "string"
        404:
          description: "Indicates the requested job was not found or the job does\
            \ not have an exception stacktrace. Status-description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /cmmn-query/case-instances:
    post:
      tags:
      - "Case Instances"
      - "Query"
      summary: "Query case instances"
      description: "The request body can contain all possible filters that can be\
        \ used in the List case instances URL query. On top of these, it’s possible\
        \ to provide an array of variables to include in the query, with their format\
        \ described here.\n\nThe general paging and sorting query-parameters can be\
        \ used for this URL."
      operationId: "queryCaseInstances"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/CaseInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the case instances are\
            \ returned"
          schema:
            $ref: "#/definitions/DataResponseCaseInstanceResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format . The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-query/historic-case-instances:
    post:
      tags:
      - "History Case"
      - "Query"
      summary: "Query for historic case instances"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic case instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long. On top of that, the query allows for filtering based on process\
        \ variables. The variables property is a JSON-array containing objects with\
        \ the format as described here."
      operationId: "queryHistoricCaseInstance"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricCaseInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the case instances are\
            \ returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricCaseInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-query/historic-milestone-instances:
    post:
      tags:
      - "History Milestone"
      - "Query"
      summary: "Query for historic milestone instances"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic milestone instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long."
      operationId: "queryHistoricMilestoneInstance"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricMilestoneInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the milestone instances\
            \ are returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricMilestoneInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-query/historic-planitem-instances:
    post:
      tags:
      - "History PlanItem"
      - "Query"
      summary: "Query for historic plan item instances"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic plan item instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long."
      operationId: "queryHistoricPlanItemInstance"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricPlanItemInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the plan item instances\
            \ are returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricPlanItemInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-query/historic-task-instances:
    post:
      tags:
      - "History Task"
      - "Query"
      summary: "Query for historic task instances"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic task instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long. On top of that, the query allows for filtering based on process\
        \ variables. The taskVariables and processVariables properties are JSON-arrays\
        \ containing objects with the format as described here."
      operationId: "queryHistoricTaskInstance"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricTaskInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the tasks are returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricTaskInstanceResponse"
        404:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-query/historic-variable-instances:
    post:
      tags:
      - "History"
      - "Query"
      summary: "Query for historic variable instances"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of historic process instances,\
        \ but passed in as JSON-body arguments rather than URL-parameters to allow\
        \ for more advanced querying and preventing errors with request-uri’s that\
        \ are too long. On top of that, the query allows for filtering based on process\
        \ variables. The variables property is a JSON-array containing objects with\
        \ the format as described here."
      operationId: "queryVariableInstances"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/HistoricVariableInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the tasks are returned"
          schema:
            $ref: "#/definitions/DataResponseHistoricVariableInstanceResponse"
        400:
          description: "Indicates an parameter was passed in the wrong format. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-query/plan-item-instances:
    post:
      tags:
      - "Plan Item Instances"
      - "Query"
      summary: "Query plan item instances"
      description: "The request body can contain all possible filters that can be\
        \ used in the List plan item instances URL query. On top of these, it’s possible\
        \ to provide an array of variables and caseInstanceVariables to include in\
        \ the query, with their format described here.\n\nThe general paging and sorting\
        \ query-parameters can be used for this URL."
      operationId: "queryPlanItemInstances"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/PlanItemInstanceQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the plan item instances\
            \ are returned."
          schema:
            $ref: "#/definitions/DataResponsePlanItemInstanceResponse"
        404:
          description: "Indicates a parameter was passed in the wrong format. The\
            \ status message contains additional information."
      security:
      - basicAuth: []
  /cmmn-query/tasks:
    post:
      tags:
      - "Tasks"
      - "Query"
      summary: "Query for tasks"
      description: "All supported JSON parameter fields allowed are exactly the same\
        \ as the parameters found for getting a collection of tasks (except for candidateGroupIn\
        \ which is only available in this POST task query REST service), but passed\
        \ in as JSON-body arguments rather than URL-parameters to allow for more advanced\
        \ querying and preventing errors with request-uri’s that are too long. On\
        \ top of that, the query allows for filtering based on task and process variables.\
        \ The taskVariables and processInstanceVariables are both JSON-arrays containing\
        \ objects with the format as described here."
      operationId: "queryTasks"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/TaskQueryRequest"
      responses:
        200:
          description: "Indicates request was successful and the tasks are returned."
          schema:
            $ref: "#/definitions/DataResponseTaskResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format or that\
            \ delegationState has an invalid value (other than pending and resolved).\
            \ The status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions:
    get:
      tags:
      - "Case Definitions"
      summary: "List of case definitions"
      description: ""
      operationId: "listCaseDefinitions"
      produces:
      - "application/json"
      parameters:
      - name: "version"
        in: "query"
        description: "Only return case definitions with the given version."
        required: false
        type: "integer"
      - name: "name"
        in: "query"
        description: "Only return case definitions with the given name."
        required: false
        type: "string"
      - name: "nameLike"
        in: "query"
        description: "Only return case definitions with a name like the given name."
        required: false
        type: "string"
      - name: "key"
        in: "query"
        description: "Only return case definitions with the given key."
        required: false
        type: "string"
      - name: "keyLike"
        in: "query"
        description: "Only return case definitions with a name like the given key."
        required: false
        type: "string"
      - name: "resourceName"
        in: "query"
        description: "Only return case definitions with the given resource name."
        required: false
        type: "string"
      - name: "resourceNameLike"
        in: "query"
        description: "Only return case definitions with a name like the given resource\
          \ name."
        required: false
        type: "string"
      - name: "category"
        in: "query"
        description: "Only return case definitions with the given category."
        required: false
        type: "string"
      - name: "categoryLike"
        in: "query"
        description: "Only return case definitions with a category like the given\
          \ name."
        required: false
        type: "string"
      - name: "categoryNotEquals"
        in: "query"
        description: "Only return case definitions which do not have the given category."
        required: false
        type: "string"
      - name: "deploymentId"
        in: "query"
        description: "Only return case definitions with the given category."
        required: false
        type: "string"
      - name: "startableByUser"
        in: "query"
        description: "Only return case definitions which are part of a deployment\
          \ with the given id."
        required: false
        type: "string"
      - name: "latest"
        in: "query"
        description: "Only return the latest case definition versions. Can only be\
          \ used together with key and keyLike parameters, using any other parameter\
          \ will result in a 400-response."
        required: false
        type: "boolean"
      - name: "suspended"
        in: "query"
        description: "If true, only returns case definitions which are suspended.\
          \ If false, only active process definitions (which are not suspended) are\
          \ returned."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "name"
        - "id"
        - "key"
        - "category"
        - "deploymentId"
        - "version"
      responses:
        200:
          description: "Indicates request was successful and the case definitions\
            \ are returned"
          schema:
            $ref: "#/definitions/DataResponseCaseDefinitionResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format or that\
            \ latest is used with other parameters other than key and keyLike. The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions/{caseDefinitionId}:
    get:
      tags:
      - "Case Definitions"
      summary: "Get a case definition"
      description: ""
      operationId: "getCaseDefinition"
      produces:
      - "application/json"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the case definitions\
            \ are returned"
          schema:
            $ref: "#/definitions/CaseDefinitionResponse"
        404:
          description: "Indicates the requested case definition was not found."
      security:
      - basicAuth: []
    put:
      tags:
      - "Case Definitions"
      summary: "Execute actions for a case definition"
      description: "Execute actions for a case definition (Update category)"
      operationId: "executeCaseDefinitionAction"
      produces:
      - "application/json"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: true
        schema:
          $ref: "#/definitions/CaseDefinitionActionRequest"
      responses:
        200:
          description: "Indicates action has been executed for the specified process.\
            \ (category altered)"
          schema:
            $ref: "#/definitions/CaseDefinitionResponse"
        400:
          description: "Indicates no category was defined in the request body."
        404:
          description: "Indicates the requested case definition was not found."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions/{caseDefinitionId}/decision-tables:
    get:
      tags:
      - "Case Definitions"
      summary: "List decision tables for a case definition"
      description: ""
      operationId: "listCaseDefinitionDecisionTables"
      produces:
      - "application/json"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case definition was found and the decision tables\
            \ are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/DmnDecisionTable"
        404:
          description: "Indicates the requested case definition was not found."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions/{caseDefinitionId}/form-definitions:
    get:
      tags:
      - "Process Definitions"
      summary: "List form definitions for a case definition"
      description: ""
      operationId: "listCaseDefinitionFormDefinitions"
      produces:
      - "application/json"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case definition was found and the form definitions\
            \ are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/FormDefinition"
        404:
          description: "Indicates the requested case definition was not found."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions/{caseDefinitionId}/identitylinks:
    get:
      tags:
      - "Case Definitions"
      summary: "List candidate starters for a case definition"
      description: ""
      operationId: "listCaseDefinitionIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case definition was found and the requested\
            \ identity links are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested case definition was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Case Definitions"
      summary: "Add a candidate starter to a case definition"
      description: "It is possible to add either a user or a group."
      operationId: "createIdentityLink"
      produces:
      - "application/json"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestIdentityLink"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/RestIdentityLink"
        201:
          description: "Indicates the case definition was found and the identity link\
            \ was created."
        400:
          description: "Indicates the body does not contain the correct information."
        404:
          description: "Indicates the requested case definition was not found."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions/{caseDefinitionId}/identitylinks/{family}/{identityId}:
    get:
      tags:
      - "Case Definitions"
      summary: "Get a candidate starter from a case definition"
      description: ""
      operationId: "getIdentityLink"
      produces:
      - "application/json"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case definition was found and the identity link\
            \ was returned."
          schema:
            $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested case definition was not found or the\
            \ case definition does not have an identity-link that matches the url."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Case Definitions"
      summary: "Delete a candidate starter from a case definition"
      description: ""
      operationId: "deleteIdentityLink"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the case definition was found and the identity link\
            \ was removed. The response body is intentionally empty."
        404:
          description: "Indicates the requested case definition was not found or the\
            \ case definition does not have an identity-link that matches the url."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions/{caseDefinitionId}/image:
    get:
      tags:
      - "Case Definitions"
      summary: "Get a case definition image"
      description: ""
      operationId: "getImageResource"
      produces:
      - "image/png"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the case definitions\
            \ are returned"
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested case definition was not found."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions/{caseDefinitionId}/model:
    get:
      tags:
      - "Case Definitions"
      summary: "Get a case definition CMMN model"
      description: ""
      operationId: "getCmmnModelResource"
      produces:
      - "application/json"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the process definition was found and the model is\
            \ returned. The response contains the full process definition model."
          schema:
            $ref: "#/definitions/CmmnModel"
        404:
          description: "Indicates the requested process definition was not found."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions/{caseDefinitionId}/resourcedata:
    get:
      tags:
      - "Case Definitions"
      summary: "Get a case definition resource content"
      description: ""
      operationId: "getProcessDefinitionResource"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates both case definition and resource have been found\
            \ and the resource data has been returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested case definition was not found or there\
            \ is no resource with the given id present in the case definition. The\
            \ status-description contains additional information."
      security:
      - basicAuth: []
  /cmmn-repository/case-definitions/{caseDefinitionId}/start-form:
    get:
      tags:
      - "Case Definitions"
      summary: "Get a case definition start form"
      description: ""
      operationId: "getProcessDefinitionStartForm"
      produces:
      - "application/json"
      parameters:
      - name: "caseDefinitionId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the case definition form\
            \ is returned"
          schema:
            type: "string"
        404:
          description: "Indicates the requested case definition was not found."
      security:
      - basicAuth: []
  /cmmn-repository/deployments:
    get:
      tags:
      - "Deployment"
      summary: "List Deployments"
      description: ""
      operationId: "listDeployments"
      produces:
      - "application/json"
      parameters:
      - name: "name"
        in: "query"
        description: "Only return deployments with the given name."
        required: false
        type: "string"
      - name: "nameLike"
        in: "query"
        description: "Only return deployments with a name like the given name."
        required: false
        type: "string"
      - name: "category"
        in: "query"
        description: "Only return deployments with the given category."
        required: false
        type: "string"
      - name: "categoryNotEquals"
        in: "query"
        description: "Only return deployments which do not have the given category."
        required: false
        type: "string"
      - name: "parentDeploymentId"
        in: "query"
        description: "Only return deployments with the given parent deployment id."
        required: false
        type: "string"
      - name: "parentDeploymentIdLike"
        in: "query"
        description: "Only return deployments with a parent deployment id like the\
          \ given value."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return deployments with a tenantId like the given value."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return deployments with the given tenantId."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns deployments without a tenantId set. If\
          \ false, the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "name"
        - "deployTime"
        - "tenantId"
      responses:
        200:
          description: "Indicates the request was successful."
          schema:
            $ref: "#/definitions/DataResponseCmmnDeploymentResponse"
      security:
      - basicAuth: []
    post:
      tags:
      - "Deployment"
      summary: "Create a new deployment"
      description: "The request body should contain data of type multipart/form-data.\
        \ There should be exactly one file in the request, any additional files will\
        \ be ignored. The deployment name is the name of the file-field passed in.\
        \ If multiple resources need to be deployed in a single deployment, compress\
        \ the resources in a zip and make sure the file-name ends with .bar or .zip.\n\
        \nAn additional parameter (form-field) can be passed in the request body with\
        \ name tenantId. The value of this field will be used as the id of the tenant\
        \ this deployment is done in."
      operationId: "uploadDeployment"
      consumes:
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentKey"
        in: "query"
        required: false
        type: "string"
      - name: "deploymentName"
        in: "query"
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        required: false
        type: "string"
      - name: "file"
        in: "formData"
        required: true
        type: "file"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/CmmnDeploymentResponse"
        201:
          description: "Indicates the deployment was created."
        400:
          description: "Indicates there was no content present in the request body\
            \ or the content mime-type is not supported for deployment. The status-description\
            \ contains additional information."
      security:
      - basicAuth: []
  /cmmn-repository/deployments/{deploymentId}:
    get:
      tags:
      - "Deployment"
      summary: "Get a deployment"
      description: ""
      operationId: "getDeployment"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentId"
        in: "path"
        description: "The id of the deployment to get."
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the deployment was found and returned."
          schema:
            $ref: "#/definitions/CmmnDeploymentResponse"
        404:
          description: "Indicates the requested deployment was not found."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Deployment"
      summary: "Delete a deployment"
      description: ""
      operationId: "deleteDeployment"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentId"
        in: "path"
        required: true
        type: "string"
      - name: "cascade"
        in: "query"
        required: false
        type: "boolean"
      responses:
        204:
          description: "Indicates the deployment was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested deployment was not found."
      security:
      - basicAuth: []
  /cmmn-repository/deployments/{deploymentId}/resourcedata/{resourceName}:
    get:
      tags:
      - "Deployment"
      summary: "Get a deployment resource content"
      description: "The response body will contain the binary resource-content for\
        \ the requested resource. The response content-type will be the same as the\
        \ type returned in the resources mimeType property. Also, a content-disposition\
        \ header is set, allowing browsers to download the file instead of displaying\
        \ it."
      operationId: "getDeploymentResourceData"
      parameters:
      - name: "deploymentId"
        in: "path"
        required: true
        type: "string"
      - name: "resourceName"
        in: "path"
        description: "The name of the resource to get. Make sure you URL-encode the\
          \ resourceName in case it contains forward slashes. Eg: use diagrams%2Fmy-process.bpmn20.xml\
          \ instead of diagrams/my-process.bpmn20.xml."
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates both deployment and resource have been found and\
            \ the resource data has been returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested deployment was not found or there\
            \ is no resource with the given id present in the deployment. The status-description\
            \ contains additional information."
      security:
      - basicAuth: []
  /cmmn-repository/deployments/{deploymentId}/resources:
    get:
      tags:
      - "Deployment"
      summary: "List resources in a deployment"
      description: "The dataUrl property in the resulting JSON for a single resource\
        \ contains the actual URL to use for retrieving the binary resource."
      operationId: "listDeploymentResources"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the deployment was found and the resource list has\
            \ been returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/DeploymentResourceResponse"
        404:
          description: "Indicates the requested deployment was not found."
      security:
      - basicAuth: []
  /cmmn-repository/deployments/{deploymentId}/resources/**:
    get:
      tags:
      - "Deployment"
      summary: "Get a deployment resource"
      description: "Replace ** by ResourceId"
      operationId: "getDeploymentResource"
      produces:
      - "application/json"
      parameters:
      - name: "deploymentId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates both deployment and resource have been found and\
            \ the resource has been returned."
          schema:
            $ref: "#/definitions/DeploymentResourceResponse"
        404:
          description: "Indicates the requested deployment was not found or there\
            \ is no resource with the given id present in the deployment. The status-description\
            \ contains additional information."
      security:
      - basicAuth: []
  /cmmn-runtime/case-instances:
    get:
      tags:
      - "Case Instances"
      summary: "List case instances"
      description: ""
      operationId: "listCaseInstances"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return models with the given version."
        required: false
        type: "string"
      - name: "caseDefinitionKey"
        in: "query"
        description: "Only return case instances with the given case definition key."
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "Only return case instances with the given case definition id."
        required: false
        type: "string"
      - name: "businessKey"
        in: "query"
        description: "Only return case instances with the given businessKey."
        required: false
        type: "string"
      - name: "superCaseInstanceId"
        in: "query"
        description: "Only return case instances which have the given super case instance\
          \ id (for cases that have a case tasks)."
        required: false
        type: "string"
      - name: "includeCaseVariables"
        in: "query"
        description: "Indication to include case variables in the result."
        required: false
        type: "boolean"
      - name: "tenantId"
        in: "query"
        description: "Only return case instances with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return case instances with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns case instances without a tenantId set.\
          \ If false, the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "id"
        - "caseDefinitionId"
        - "tenantId"
        - "caseDefinitionKey"
      responses:
        200:
          description: "Indicates request was successful and the case instances are\
            \ returned"
          schema:
            $ref: "#/definitions/DataResponseCaseInstanceResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format . The\
            \ status message contains additional information."
      security:
      - basicAuth: []
    post:
      tags:
      - "Case Instances"
      summary: "Start a case instance"
      description: "Note that also a *transientVariables* property is accepted as\
        \ part of this json, that follows the same structure as the *variables* property.\n\
        \nOnly one of *caseDefinitionId* or *caseDefinitionKey* an be used in the\
        \ request body. \n\nParameters *businessKey*, *variables* and *tenantId* are\
        \ optional.\n\n If tenantId is omitted, the default tenant will be used. More\
        \ information about the variable format can be found in the REST variables\
        \ section.\n\n Note that the variable-scope that is supplied is ignored, process-variables\
        \ are always local.\n\n"
      operationId: "createCaseInstance"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/CaseInstanceCreateRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/CaseInstanceResponse"
        201:
          description: "Indicates the case instance was created."
        400:
          description: "Indicates either the case definition was not found (based\
            \ on id or key), no process is started by sending the given message or\
            \ an invalid variable has been passed. Status description contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /cmmn-runtime/case-instances/{caseInstanceId}:
    get:
      tags:
      - "Case Instances"
      summary: "Get a case instance"
      description: ""
      operationId: "getCaseInstance"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case instance was found and returned."
          schema:
            $ref: "#/definitions/CaseInstanceResponse"
        404:
          description: "Indicates the requested case instance was not found."
      security:
      - basicAuth: []
    put:
      tags:
      - "Plan Item Instances"
      summary: "Execute an action on a case instance"
      description: ""
      operationId: "performCaseInstanceAction"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestActionRequest"
      responses:
        200:
          description: "Indicates the case instance was found and the action is performed."
          schema:
            $ref: "#/definitions/CaseInstanceResponse"
        204:
          description: "Indicates the case was found, the action was performed and\
            \ the action caused the case instance to end."
        400:
          description: "Indicates an illegal action was requested, required parameters\
            \ are missing in the request body or illegal variables are passed in.\
            \ Status description contains additional information about the error."
        404:
          description: "Indicates the case instance was not found."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Case Instances"
      summary: "Delete a case instance"
      description: ""
      operationId: "deleteCaseInstance"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "deleteReason"
        in: "query"
        required: false
        type: "string"
      responses:
        204:
          description: "Indicates the case instance was found and deleted. Response\
            \ body is left empty intentionally."
        404:
          description: "Indicates the requested case instance was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/case-instances/{caseInstanceId}/diagram:
    get:
      tags:
      - "Case Instances"
      summary: "Get diagram for a case instance"
      description: ""
      operationId: "getCaseInstanceDiagram"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case instance was found and the diagram was\
            \ returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        400:
          description: "Indicates the requested case instance was not found but the\
            \ process does not contain any graphical information (CMMN DI) and no\
            \ diagram can be created."
        404:
          description: "Indicates the requested case instance was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/case-instances/{caseInstanceId}/identitylinks:
    get:
      tags:
      - "Case Instance Identity Links"
      summary: "Get involved people for case instance"
      description: "Note that the groupId in Response Body will always be null, as\
        \ it’s only possible to involve users with a case instance."
      operationId: "listCaseInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case instance was found and links are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested case instance was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Case Instance Identity Links"
      summary: "Add an involved user to a case instance"
      description: "Note that the groupId in Response Body will always be null, as\
        \ it’s only possible to involve users with a case instance."
      operationId: "createCaseInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestIdentityLink"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/RestIdentityLink"
        201:
          description: "Indicates the case instance was found and the link is created."
        400:
          description: "Indicates the requested body did not contain a userId or a\
            \ type."
        404:
          description: "Indicates the requested case instance was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/case-instances/{caseInstanceId}/identitylinks/users/{identityId}/{type}:
    get:
      tags:
      - "Case Instance Identity Links"
      summary: "Get a specific involved people from case instance"
      description: ""
      operationId: "getCaseInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      - name: "type"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case instance was found and the specified link\
            \ is retrieved."
          schema:
            $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested case instance was not found or the\
            \ link to delete does not exist. The response status contains additional\
            \ information about the error."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Case Instance Identity Links"
      summary: "Remove an involved user to from case instance"
      description: ""
      operationId: "deleteCaseInstanceIdentityLinks"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      - name: "type"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the case instance was found and the link has been\
            \ deleted. Response body is left empty intentionally."
        404:
          description: "Indicates the requested case instance was not found or the\
            \ link to delete does not exist. The response status contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /cmmn-runtime/case-instances/{caseInstanceId}/stage-overview:
    get:
      tags:
      - "Case Instances"
      operationId: "getStageOverview"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/StageResponse"
      security:
      - basicAuth: []
  /cmmn-runtime/case-instances/{caseInstanceId}/variables:
    get:
      tags:
      - "Case Instance Variables"
      summary: "List variables for a case instance"
      description: "In case the variable is a binary variable or serializable, the\
        \ valueUrl points to an URL to fetch the raw value. If it’s a plain variable,\
        \ the value is present in the response. Note that only local scoped variables\
        \ are returned, as there is no global scope for process-instance variables."
      operationId: "listCaseInstanceVariables"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the case instance was found and variables are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestVariable"
        400:
          description: "Indicates the requested case instance was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Case Instance Variables"
      summary: "Create variables or new binary variable on a case instance"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable\
        \ or an array of RestVariable) or by passing a multipart/form-data Object.\n\
        Nonexistent variables are created on the process-instance and existing ones\
        \ are overridden without any error.\nAny number of variables can be passed\
        \ into the request body array.\nNote that scope is ignored, only local variables\
        \ can be set in a case instance.\nNB: Swagger V2 specification doesn't support\
        \ this use case that is why this endpoint might be buggy/incomplete if used\
        \ with other tools."
      operationId: "createCaseInstanceVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      - "text/plain"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Create a variable on a case instance"
        required: false
        schema:
          $ref: "#/definitions/CaseInstanceVariableCollectionResource"
      - name: "file"
        in: "formData"
        required: false
        type: "file"
      - name: "name"
        in: "formData"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        required: false
        type: "string"
        x-example: "integer"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
        201:
          description: "Indicates the case instance was found and variable is created."
        400:
          description: "Indicates the request body is incomplete or contains illegal\
            \ values. The status description contains additional information about\
            \ the error."
        404:
          description: "Indicates the requested case instance was not found."
        409:
          description: "Indicates the case instance was found but already contains\
            \ a variable with the given name (only thrown when POST method is used).\
            \ Use the update-method instead."
      security:
      - basicAuth: []
    put:
      tags:
      - "Case Instance Variables"
      summary: "Update a multiple/single (non)binary variable on a case instance"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable\
        \ or an array of RestVariable) or by passing a multipart/form-data Object.\n\
        Nonexistent variables are created on the process-instance and existing ones\
        \ are overridden without any error.\nAny number of variables can be passed\
        \ into the request body array.\nNote that scope is ignored, only local variables\
        \ can be set in a case instance.\nNB: Swagger V2 specification doesn't support\
        \ this use case that is why this endpoint might be buggy/incomplete if used\
        \ with other tools."
      operationId: "createOrUpdateCaseVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Create a variable on a process instance"
        required: false
        schema:
          $ref: "#/definitions/CaseInstanceVariableCollectionResource"
      - name: "file"
        in: "formData"
        required: false
        type: "file"
      - name: "name"
        in: "formData"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        required: false
        type: "string"
        x-example: "integer"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
        201:
          description: "Indicates the case instance was found and variable is created."
        400:
          description: "Indicates the request body is incomplete or contains illegal\
            \ values. The status description contains additional information about\
            \ the error."
        404:
          description: "Indicates the requested case instance was not found."
        415:
          description: "Indicates the serializable data contains an object for which\
            \ no class is present in the JVM running the Flowable engine and therefore\
            \ cannot be deserialized."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Case Instance Variables"
      summary: "Delete all variables"
      description: ""
      operationId: "deleteCaseVariable"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates variables were found and have been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested case instance was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/case-instances/{caseInstanceId}/variables/{variableName}:
    get:
      tags:
      - "Case Instance Variables"
      summary: "Get a variable for a case instance"
      description: ""
      operationId: "getCaseInstanceVariable"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates both the case instance and variable were found and\
            \ variable is returned."
          schema:
            $ref: "#/definitions/RestVariable"
        404:
          description: "Indicates the requested case instance was not found or the\
            \ case instance does not have a variable with the given name. Status description\
            \ contains additional information about the error."
      security:
      - basicAuth: []
    put:
      tags:
      - "Case Instance Variables"
      summary: "Update a single variable on a case instance"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable)\
        \ or by passing a multipart/form-data Object.\nNonexistent variables are created\
        \ on the case instance and existing ones are overridden without any error.\n\
        Note that scope is ignored, only local variables can be set in a process instance.\n\
        NB: Swagger V2 specification doesn't support this use case that is why this\
        \ endpoint might be buggy/incomplete if used with other tools."
      operationId: "updateCaseInstanceVariable"
      consumes:
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Create a variable on a case instance"
        required: false
        schema:
          $ref: "#/definitions/CaseInstanceVariableResource"
      - name: "file"
        in: "formData"
        required: false
        type: "file"
      - name: "name"
        in: "formData"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        required: false
        type: "string"
        x-example: "integer"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/RestVariable"
        201:
          description: "Indicates both the case instance and variable were found and\
            \ variable is updated."
        404:
          description: "Indicates the requested case instance was not found or the\
            \ process instance does not have a variable with the given name. Status\
            \ description contains additional information about the error."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Case Instance Variables"
      summary: "Delete a variable"
      description: ""
      operationId: "deleteCaseInstanceVariable"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        204:
          description: "Indicates the variable was found and has been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested variable was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/case-instances/{caseInstanceId}/variables/{variableName}/data:
    get:
      tags:
      - "Case Instance Variables"
      summary: "Get the binary data for a variable"
      description: ""
      operationId: "getCaseInstanceVariableData"
      parameters:
      - name: "caseInstanceId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the case instance was found and the requested variables\
            \ are returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name (in the given scope). Status\
            \ message provides additional information."
      security:
      - basicAuth: []
  /cmmn-runtime/plan-item-instances:
    get:
      tags:
      - "Plan Item Instances"
      summary: "List of plan item instances"
      description: ""
      operationId: "listPlanItemInstances"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "query"
        description: "Only return models with the given version."
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "Only return plan item instances with the given case definition\
          \ id."
        required: false
        type: "string"
      - name: "caseInstanceId"
        in: "query"
        description: "Only return plan item instances which are part of the case instance\
          \ with the given id."
        required: false
        type: "string"
      - name: "stageInstanceId"
        in: "query"
        description: "Only return plan item instances which are part of the given\
          \ stage instance."
        required: false
        type: "string"
      - name: "planItemDefinitionId"
        in: "query"
        description: "Only return plan item instances which have the given plan item\
          \ definition id."
        required: false
        type: "string"
      - name: "planItemDefinitionType"
        in: "query"
        description: "Only return plan item instances which have the given plan item\
          \ definition type."
        required: false
        type: "string"
      - name: "planItemDefinitionTypes"
        in: "query"
        description: "Only return plan item instances which have any of the given\
          \ plan item definition types. Comma-separated string e.g. humantask, stage"
        required: false
        type: "string"
      - name: "state"
        in: "query"
        description: "Only return plan item instances which have the given state."
        required: false
        type: "string"
      - name: "name"
        in: "query"
        description: "Only return plan item instances which have the given name."
        required: false
        type: "string"
      - name: "elementId"
        in: "query"
        description: "Only return plan item instances which have the given element\
          \ id."
        required: false
        type: "string"
      - name: "referenceId"
        in: "query"
        description: "Only return plan item instances which have the given reference\
          \ id."
        required: false
        type: "string"
      - name: "referenceType"
        in: "query"
        description: "Only return plan item instances which have the given reference\
          \ type."
        required: false
        type: "string"
      - name: "createdBefore"
        in: "query"
        description: "Only return plan item instances which are created before the\
          \ given date."
        required: false
        type: "date"
      - name: "createdAfter"
        in: "query"
        description: "Only return plan item instances which are created after the\
          \ given date."
        required: false
        type: "date"
      - name: "startUserId"
        in: "query"
        description: "Only return plan item instances which are started by the given\
          \ user id."
        required: false
        type: "string"
      - name: "tenantId"
        in: "query"
        description: "Only return plan item instances with the given tenantId."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns plan item instances without a tenantId\
          \ set. If false, the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "sort"
        in: "query"
        description: "Property to sort on, to be used together with the order."
        required: false
        type: "string"
        enum:
        - "name"
        - "createTime"
        - "startTime"
      responses:
        200:
          description: "Indicates request was successful and the executions are returned"
          schema:
            $ref: "#/definitions/DataResponsePlanItemInstanceResponse"
        400:
          description: "Indicates a parameter was passed in the wrong format . The\
            \ status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-runtime/plan-item-instances/{planItemInstanceId}:
    get:
      tags:
      - "Plan Item Instances"
      summary: "Get an plan item instance"
      description: ""
      operationId: "getPlanItemInstance"
      produces:
      - "application/json"
      parameters:
      - name: "planItemInstanceId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the plan item instance was found and returned."
          schema:
            $ref: "#/definitions/PlanItemInstanceResponse"
        404:
          description: "Indicates the plan item instance was not found."
      security:
      - basicAuth: []
    put:
      tags:
      - "Plan Item Instances"
      summary: "Execute an action on a plan item instance"
      description: ""
      operationId: "performPlanItemInstanceAction"
      produces:
      - "application/json"
      parameters:
      - name: "planItemInstanceId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestActionRequest"
      responses:
        200:
          description: "Indicates the plan item instance was found and the action\
            \ is performed."
          schema:
            $ref: "#/definitions/PlanItemInstanceResponse"
        204:
          description: "Indicates the plan item instance was found, the action was\
            \ performed and the action caused the plan item instance to end."
        400:
          description: "Indicates an illegal action was requested, required parameters\
            \ are missing in the request body or illegal variables are passed in.\
            \ Status description contains additional information about the error."
        404:
          description: "Indicates the plan item instance was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks:
    get:
      tags:
      - "Tasks"
      summary: "List of tasks"
      description: ""
      operationId: "listTasks"
      produces:
      - "application/json"
      parameters:
      - name: "name"
        in: "query"
        description: "Only return models with the given version."
        required: false
        type: "string"
      - name: "nameLike"
        in: "query"
        description: "Only return tasks with a name like the given name."
        required: false
        type: "string"
      - name: "description"
        in: "query"
        description: "Only return tasks with the given description."
        required: false
        type: "string"
      - name: "priority"
        in: "query"
        description: "Only return tasks with the given priority."
        required: false
        type: "string"
      - name: "minimumPriority"
        in: "query"
        description: "Only return tasks with a priority greater than the given value."
        required: false
        type: "string"
      - name: "maximumPriority"
        in: "query"
        description: "Only return tasks with a priority lower than the given value."
        required: false
        type: "string"
      - name: "assignee"
        in: "query"
        description: "Only return tasks assigned to the given user."
        required: false
        type: "string"
      - name: "assigneeLike"
        in: "query"
        description: "Only return tasks assigned with an assignee like the given value."
        required: false
        type: "string"
      - name: "owner"
        in: "query"
        description: "Only return tasks owned by the given user."
        required: false
        type: "string"
      - name: "ownerLike"
        in: "query"
        description: "Only return tasks assigned with an owner like the given value."
        required: false
        type: "string"
      - name: "unassigned"
        in: "query"
        description: "Only return tasks that are not assigned to anyone. If false\
          \ is passed, the value is ignored."
        required: false
        type: "string"
      - name: "delegationState"
        in: "query"
        description: "Only return tasks that have the given delegation state. Possible\
          \ values are pending and resolved."
        required: false
        type: "string"
      - name: "candidateUser"
        in: "query"
        description: "Only return tasks that can be claimed by the given user. This\
          \ includes both tasks where the user is an explicit candidate for and task\
          \ that are claimable by a group that the user is a member of."
        required: false
        type: "string"
      - name: "candidateGroup"
        in: "query"
        description: "Only return tasks that can be claimed by a user in the given\
          \ group."
        required: false
        type: "string"
      - name: "candidateGroups"
        in: "query"
        description: "Only return tasks that can be claimed by a user in the given\
          \ groups. Values split by comma."
        required: false
        type: "string"
      - name: "involvedUser"
        in: "query"
        description: "Only return tasks in which the given user is involved."
        required: false
        type: "string"
      - name: "taskDefinitionKey"
        in: "query"
        description: "Only return tasks with the given task definition id."
        required: false
        type: "string"
      - name: "taskDefinitionKeyLike"
        in: "query"
        description: "Only return tasks with a given task definition id like the given\
          \ value."
        required: false
        type: "string"
      - name: "caseInstanceId"
        in: "query"
        description: "Only return tasks which are part of the case instance with the\
          \ given id."
        required: false
        type: "string"
      - name: "caseInstanceIdWithChildren"
        in: "query"
        description: "Only return tasks which are part of the case instance and its\
          \ children with the given id."
        required: false
        type: "string"
      - name: "caseDefinitionId"
        in: "query"
        description: "Only return tasks which are part of a case instance which has\
          \ a case definition with the given id."
        required: false
        type: "string"
      - name: "createdOn"
        in: "query"
        description: "Only return tasks which are created on the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "createdBefore"
        in: "query"
        description: "Only return tasks which are created before the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "createdAfter"
        in: "query"
        description: "Only return tasks which are created after the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueOn"
        in: "query"
        description: "Only return tasks which are due on the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueBefore"
        in: "query"
        description: "Only return tasks which are due before the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "dueAfter"
        in: "query"
        description: "Only return tasks which are due after the given date."
        required: false
        type: "string"
        format: "date-time"
      - name: "withoutDueDate"
        in: "query"
        description: "Only return tasks which do not have a due date. The property\
          \ is ignored if the value is false."
        required: false
        type: "boolean"
      - name: "excludeSubTasks"
        in: "query"
        description: "Only return tasks that are not a subtask of another task."
        required: false
        type: "boolean"
      - name: "active"
        in: "query"
        description: "If true, only return tasks that are not suspended (either part\
          \ of a process that is not suspended or not part of a process at all). If\
          \ false, only tasks that are part of suspended process instances are returned."
        required: false
        type: "boolean"
      - name: "includeTaskLocalVariables"
        in: "query"
        description: "Indication to include task local variables in the result."
        required: false
        type: "boolean"
      - name: "tenantId"
        in: "query"
        description: "Only return tasks with the given tenantId."
        required: false
        type: "string"
      - name: "tenantIdLike"
        in: "query"
        description: "Only return tasks with a tenantId like the given value."
        required: false
        type: "string"
      - name: "withoutTenantId"
        in: "query"
        description: "If true, only returns tasks without a tenantId set. If false,\
          \ the withoutTenantId parameter is ignored."
        required: false
        type: "boolean"
      - name: "candidateOrAssigned"
        in: "query"
        description: "Select tasks that has been claimed or assigned to user or waiting\
          \ to claim by user (candidate user or groups)."
        required: false
        type: "string"
      - name: "category"
        in: "query"
        description: "Select tasks with the given category. Note that this is the\
          \ task category, not the category of the process definition (namespace within\
          \ the BPMN Xml).\n"
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the tasks are returned"
          schema:
            $ref: "#/definitions/DataResponseTaskResponse"
        404:
          description: "Indicates a parameter was passed in the wrong format or that\
            \ delegationState has an invalid value (other than pending and resolved).\
            \ The status-message contains additional information."
      security:
      - basicAuth: []
    post:
      tags:
      - "Tasks"
      summary: "Create Task"
      description: ""
      operationId: "createTask"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/TaskRequest"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/TaskResponse"
        201:
          description: "Indicates request was successful and the tasks are returned"
        400:
          description: "Indicates a parameter was passed in the wrong format or that\
            \ delegationState has an invalid value (other than pending and resolved).\
            \ The status-message contains additional information."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks/{taskId}:
    get:
      tags:
      - "Tasks"
      summary: "Get a task"
      description: ""
      operationId: "getTask"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and returned."
          schema:
            $ref: "#/definitions/TaskResponse"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Tasks"
      summary: "Tasks actions"
      description: ""
      operationId: "executeTaskAction"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/TaskActionRequest"
      responses:
        200:
          description: "Indicates the action was executed."
        400:
          description: "When the body contains an invalid value or when the assignee\
            \ is missing when the action requires it."
        404:
          description: "Indicates the requested task was not found."
        409:
          description: "Indicates the action cannot be performed due to a conflict.\
            \ Either the task was updates simultaneously or the task was claimed by\
            \ another user, in case of the claim action."
      security:
      - basicAuth: []
    put:
      tags:
      - "Tasks"
      summary: "Update a task"
      description: "All request values are optional. For example, you can only include\
        \ the assignee attribute in the request body JSON-object, only updating the\
        \ assignee of the task, leaving all other fields unaffected. When an attribute\
        \ is explicitly included and is set to null, the task-value will be updated\
        \ to null. Example: {\"dueDate\" : null} will clear the duedate of the task)."
      operationId: "updateTask"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/TaskRequest"
      responses:
        200:
          description: "Indicates the task was updated."
          schema:
            $ref: "#/definitions/TaskResponse"
        404:
          description: "Indicates the requested task was not found."
        409:
          description: "Indicates the requested task was updated simultaneously."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Tasks"
      summary: "Delete a task"
      description: ""
      operationId: "deleteTask"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "cascadeHistory"
        in: "query"
        description: "Whether or not to delete the HistoricTask instance when deleting\
          \ the task (if applicable). If not provided, this value defaults to false."
        required: false
        type: "string"
      - name: "deleteReason"
        in: "query"
        description: "Reason why the task is deleted. This value is ignored when cascadeHistory\
          \ is true."
        required: false
        type: "string"
      responses:
        204:
          description: "Indicates the task was found and has been deleted. Response-body\
            \ is intentionally empty."
        403:
          description: "Indicates the requested task cannot be deleted because it’\
            s part of a workflow."
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks/{taskId}/form:
    get:
      tags:
      - "Tasks"
      summary: "Get a task form"
      description: ""
      operationId: "getTaskForm"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the task form is returned"
          schema:
            type: "string"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks/{taskId}/identitylinks:
    get:
      tags:
      - "Task Identity Links"
      summary: "List identity links for a task"
      description: ""
      operationId: "listTasksInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested identity links\
            \ are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
    post:
      tags:
      - "Task Identity Links"
      summary: "Create an identity link on a task"
      description: "It is possible to add either a user or a group."
      operationId: "createTaskInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          $ref: "#/definitions/RestIdentityLink"
      responses:
        200:
          description: "successful operation"
          schema:
            $ref: "#/definitions/RestIdentityLink"
        201:
          description: "Indicates the task was found and the identity link was created."
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have the requested identityLink. The status contains additional\
            \ information about this error."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks/{taskId}/identitylinks/{family}:
    get:
      tags:
      - "Task Identity Links"
      summary: "List identity links for a task for either groups or users"
      description: "Returns only identity links targeting either users or groups.\
        \ Response body and status-codes are exactly the same as when getting the\
        \ full list of identity links for a task."
      operationId: "listIdentityLinksForFamily"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested identity links\
            \ are returned."
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks/{taskId}/identitylinks/{family}/{identityId}/{type}:
    get:
      tags:
      - "Task Identity Links"
      summary: "Get a single identity link on a task"
      description: ""
      operationId: "getTaskInstanceIdentityLinks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      - name: "type"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates the task and identity link was found and returned."
          schema:
            $ref: "#/definitions/RestIdentityLink"
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have the requested identityLink. The status contains additional\
            \ information about this error."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Task Identity Links"
      summary: "Delete an identity link on a task"
      description: ""
      operationId: "deleteTaskInstanceIdentityLinks"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "family"
        in: "path"
        required: true
        type: "string"
      - name: "identityId"
        in: "path"
        required: true
        type: "string"
      - name: "type"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates the task and identity link were found and the link\
            \ has been deleted. Response-body is intentionally empty."
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have the requested identityLink. The status contains additional\
            \ information about this error."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks/{taskId}/subtasks:
    get:
      tags:
      - "Tasks"
      summary: "List of sub tasks for a task"
      description: ""
      operationId: "listTaskSubtasks"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        200:
          description: "Indicates request was successful and the  sub tasks are returned"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/TaskResponse"
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks/{taskId}/variables:
    get:
      tags:
      - "Task Variables"
      summary: "List variables for a task"
      description: ""
      operationId: "listTaskVariables"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested variables are\
            \ returned"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/RestVariable"
        404:
          description: "Indicates the requested task was not found.."
      security:
      - basicAuth: []
    post:
      tags:
      - "Tasks"
      - "Task Variables"
      summary: "Create new variables on a task"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable\
        \ or an Array of RestVariable) or by passing a multipart/form-data Object.\n\
        It is possible to create simple (non-binary) variable or list of variables\
        \ or new binary variable \nAny number of variables can be passed into the\
        \ request body array.\nNB: Swagger V2 specification does not support this\
        \ use case that is why this endpoint might be buggy/incomplete if used with\
        \ other tools."
      operationId: "createTaskVariable"
      consumes:
      - "text/plain"
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Create a variable on a task"
        required: false
        schema:
          $ref: "#/definitions/TaskVariableCollectionResource"
      - name: "name"
        in: "formData"
        description: "Required name of the variable"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        description: "Type of variable that is created. If omitted, reverts to raw\
          \ JSON-value type (string, boolean, integer or double)"
        required: false
        type: "string"
        x-example: "integer"
      - name: "scope"
        in: "formData"
        description: "Scope of variable that is created. If omitted, local is assumed."
        required: false
        type: "string"
        x-example: "local"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
        201:
          description: "Indicates the variables were created and the result is returned."
        400:
          description: "Indicates the name of a variable to create was missing or\
            \ that an attempt is done to create a variable on a standalone task (without\
            \ a process associated) with scope global or an empty array of variables\
            \ was included in the request or request did not contain an array of variables.\
            \ Status message provides additional information."
        404:
          description: "Indicates the requested task was not found."
        409:
          description: "Indicates the task already has a variable with the given name.\
            \ Use the PUT method to update the task variable instead."
        415:
          description: "Indicates the serializable data contains an object for which\
            \ no class is present in the JVM running the Flowable engine and therefore\
            \ cannot be deserialized."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Tasks"
      summary: "Delete all local variables on a task"
      description: ""
      operationId: "deleteAllLocalTaskVariables"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      responses:
        204:
          description: "Indicates all local task variables have been deleted. Response-body\
            \ is intentionally empty."
        404:
          description: "Indicates the requested task was not found."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks/{taskId}/variables/{variableName}:
    get:
      tags:
      - "Task Variables"
      summary: "Get a variable from a task"
      description: ""
      operationId: "getTaskInstanceVariable"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested variables are\
            \ returned."
          schema:
            $ref: "#/definitions/RestVariable"
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name (in the given scope). Status\
            \ message provides additional information."
      security:
      - basicAuth: []
    put:
      tags:
      - "Task Variables"
      summary: "Update an existing variable on a task"
      description: "This endpoint can be used in 2 ways: By passing a JSON Body (RestVariable)\
        \ or by passing a multipart/form-data Object.\nIt is possible to update simple\
        \ (non-binary) variable or  binary variable \nAny number of variables can\
        \ be passed into the request body array.\nNB: Swagger V2 specification does\
        \ not support this use case that is why this endpoint might be buggy/incomplete\
        \ if used with other tools."
      operationId: "updateTaskInstanceVariable"
      consumes:
      - "text/plain"
      - "application/json"
      - "multipart/form-data"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Update a task variable"
        required: false
        schema:
          $ref: "#/definitions/TaskVariableResource"
      - name: "name"
        in: "formData"
        description: "Required name of the variable"
        required: false
        type: "string"
        x-example: "Simple content item"
      - name: "type"
        in: "formData"
        description: "Type of variable that is updated. If omitted, reverts to raw\
          \ JSON-value type (string, boolean, integer or double)"
        required: false
        type: "string"
        x-example: "integer"
      - name: "scope"
        in: "formData"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable.."
        required: false
        type: "string"
        x-example: "local"
      responses:
        200:
          description: "Indicates the variables was updated and the result is returned."
          schema:
            $ref: "#/definitions/RestVariable"
        400:
          description: "Indicates the name of a variable to update was missing or\
            \ that an attempt is done to update a variable on a standalone task (without\
            \ a process associated) with scope global. Status message provides additional\
            \ information."
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name in the given scope. Status message\
            \ contains additional information about the error."
        415:
          description: "Indicates the serializable data contains an object for which\
            \ no class is present in the JVM running the Flowable engine and therefore\
            \ cannot be deserialized."
      security:
      - basicAuth: []
    delete:
      tags:
      - "Task Variables"
      summary: "Delete a variable on a task"
      description: ""
      operationId: "deleteTaskInstanceVariable"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable."
        required: false
        type: "string"
      responses:
        204:
          description: "Indicates the task variable was found and has been deleted.\
            \ Response-body is intentionally empty."
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name. Status message contains additional\
            \ information about the error."
      security:
      - basicAuth: []
  /cmmn-runtime/tasks/{taskId}/variables/{variableName}/data:
    get:
      tags:
      - "Task Variables"
      summary: "Get the binary data for a variable"
      description: "The response body contains the binary value of the variable. When\
        \ the variable is of type binary, the content-type of the response is set\
        \ to application/octet-stream, regardless of the content of the variable or\
        \ the request accept-type header. In case of serializable, application/x-java-serialized-object\
        \ is used as content-type."
      operationId: "getTaskVariableData"
      produces:
      - "application/json"
      parameters:
      - name: "taskId"
        in: "path"
        required: true
        type: "string"
      - name: "variableName"
        in: "path"
        required: true
        type: "string"
      - name: "scope"
        in: "query"
        description: "Scope of variable to be returned. When local, only task-local\
          \ variable value is returned. When global, only variable value from the\
          \ task’s parent execution-hierarchy are returned. When the parameter is\
          \ omitted, a local variable will be returned if it exists, otherwise a global\
          \ variable."
        required: false
        type: "string"
      responses:
        200:
          description: "Indicates the task was found and the requested variables are\
            \ returned."
          schema:
            type: "array"
            items:
              type: "string"
              format: "byte"
        404:
          description: "Indicates the requested task was not found or the task does\
            \ not have a variable with the given name (in the given scope). Status\
            \ message provides additional information."
      security:
      - basicAuth: []
securityDefinitions:
  basicAuth:
    type: "basic"
definitions:
  Association:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      sourceRef:
        type: "string"
      sourceElement:
        $ref: "#/definitions/BaseElement"
      targetRef:
        type: "string"
      targetElement:
        $ref: "#/definitions/BaseElement"
      transitionEvent:
        type: "string"
  BaseElement:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
  Case:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      initiatorVariableName:
        type: "string"
      planModel:
        $ref: "#/definitions/Stage"
      startEventType:
        type: "string"
      candidateStarterUsers:
        type: "array"
        items:
          type: "string"
      candidateStarterGroups:
        type: "array"
        items:
          type: "string"
      allCaseElements:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/CaseElement"
      lifecycleListeners:
        type: "array"
        items:
          $ref: "#/definitions/FlowableListener"
  CaseDefinitionActionRequest:
    type: "object"
    properties:
      action:
        type: "string"
      category:
        type: "string"
  CaseDefinitionResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "oneTaskCase:1:4"
      url:
        type: "string"
        example: "http://localhost:8182/cmmn-repository/case-definitions/oneTaskCase%3A1%3A4"
      key:
        type: "string"
        example: "oneTaskCase"
      version:
        type: "integer"
        format: "int32"
        example: 1
      name:
        type: "string"
        example: "The One Task Case"
      description:
        type: "string"
        example: "This is a case for testing purposes"
      tenantId:
        type: "string"
        example: "null"
      deploymentId:
        type: "string"
        example: "2"
      deploymentUrl:
        type: "string"
        example: "http://localhost:8081/cmmn-repository/deployments/2"
      resource:
        type: "string"
        example: "http://localhost:8182/cmmn-repository/deployments/2/resources/testCase.cmmn"
        description: "Contains the actual deployed CMMN 1.1 xml."
      diagramResource:
        type: "string"
        example: "http://localhost:8182/cmmn-repository/deployments/2/resources/testProcess.png"
        description: "Contains a graphical representation of the case, null when no\
          \ diagram is available."
      category:
        type: "string"
        example: "Examples"
      graphicalNotationDefined:
        type: "boolean"
        description: "Indicates the case definition contains graphical information\
          \ (CMMN DI)."
      startFormDefined:
        type: "boolean"
  CaseElement:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      parentStage:
        $ref: "#/definitions/Stage"
  CaseInstanceCreateRequest:
    type: "object"
    properties:
      caseDefinitionId:
        type: "string"
        example: "oneTaskCase:1:158"
      caseDefinitionKey:
        type: "string"
        example: "oneTaskCase"
      businessKey:
        type: "string"
        example: "myBusinessKey"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      transientVariables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      startFormVariables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      outcome:
        type: "string"
      tenantId:
        type: "string"
        example: "tenant1"
      returnVariables:
        type: "boolean"
    description: "Only one of caseDefinitionId or caseDefinitionKey can be used in\
      \ the request body"
  CaseInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      caseInstanceId:
        type: "string"
      caseBusinessKey:
        type: "string"
      caseDefinitionId:
        type: "string"
      caseDefinitionKey:
        type: "string"
      caseInstanceParentId:
        type: "string"
      involvedUser:
        type: "string"
      includeCaseVariables:
        type: "boolean"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      tenantId:
        type: "string"
      tenantIdLike:
        type: "string"
      withoutTenantId:
        type: "boolean"
  CaseInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "187"
      name:
        type: "string"
        example: "processName"
      url:
        type: "string"
        example: "http://localhost:8182/cmmn-repository/case-definitions/caseOne%3A1%3A4"
      businessKey:
        type: "string"
        example: "myBusinessKey"
      startTime:
        type: "string"
        format: "date-time"
        example: "2019-04-17T10:17:43.902+0000"
      startUserId:
        type: "string"
        example: "aUserId"
      state:
        type: "string"
        example: "active"
      ended:
        type: "boolean"
      caseDefinitionId:
        type: "string"
        example: "oneTaskCase:1:158"
      caseDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-repository/case-definitions/caseOne%3A1%3A4"
      caseDefinitionName:
        type: "string"
        example: "aCaseDefinitionName"
      caseDefinitionDescription:
        type: "string"
        example: "A case definition description"
      parentId:
        type: "string"
        example: "123"
      callbackId:
        type: "string"
        example: "123"
      callbackType:
        type: "string"
        example: "cmmn-1.1-to-cmmn-1.1-child-case"
      referenceId:
        type: "string"
        example: "123"
      referenceType:
        type: "string"
        example: "event-to-cmmn-1.1-case"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      tenantId:
        type: "string"
        example: "null"
      completed:
        type: "boolean"
  CaseInstanceVariableCollectionResource:
    type: "object"
  CaseInstanceVariableResource:
    type: "object"
  CmmnDeploymentResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "10"
      name:
        type: "string"
        example: "flowable-examples.bar"
      deploymentTime:
        type: "string"
        format: "date-time"
        example: "2010-10-13T14:54:26.750+02:00"
      category:
        type: "string"
        example: "examples"
      parentDeploymentId:
        type: "string"
        example: "12"
      url:
        type: "string"
        example: "http://localhost:8081/flowable-rest/service/cmmn-repository/deployments/10"
      tenantId:
        type: "string"
  CmmnEngineInfoResponse:
    type: "object"
    properties:
      name:
        type: "string"
      version:
        type: "string"
  CmmnModel:
    type: "object"
    properties:
      id:
        type: "string"
      name:
        type: "string"
      targetNamespace:
        type: "string"
      expressionLanguage:
        type: "string"
      exporter:
        type: "string"
      exporterVersion:
        type: "string"
      author:
        type: "string"
      creationDate:
        type: "string"
        format: "date-time"
      cases:
        type: "array"
        items:
          $ref: "#/definitions/Case"
      processes:
        type: "array"
        items:
          $ref: "#/definitions/Process"
      decisions:
        type: "array"
        items:
          $ref: "#/definitions/Decision"
      associations:
        type: "array"
        items:
          $ref: "#/definitions/Association"
      locationMap:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/GraphicInfo"
      labelLocationMap:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/GraphicInfo"
      flowLocationMap:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/GraphicInfo"
      primaryCase:
        $ref: "#/definitions/Case"
      namespaces:
        type: "object"
        additionalProperties:
          type: "string"
  CompletionNeutralRule:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      condition:
        type: "string"
      parentStage:
        $ref: "#/definitions/Stage"
  Criterion:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      technicalId:
        type: "string"
      sentryRef:
        type: "string"
      sentry:
        $ref: "#/definitions/Sentry"
      attachedToRefId:
        type: "string"
      exitType:
        type: "string"
      exitEventType:
        type: "string"
      incomingAssociations:
        type: "array"
        items:
          $ref: "#/definitions/Association"
      outgoingAssociations:
        type: "array"
        items:
          $ref: "#/definitions/Association"
      entryCriterion:
        type: "boolean"
      exitCriterion:
        type: "boolean"
      parentStage:
        $ref: "#/definitions/Stage"
  DataResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          type: "object"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseCaseDefinitionResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/CaseDefinitionResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseCaseInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/CaseInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseCmmnDeploymentResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/CmmnDeploymentResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricCaseInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricCaseInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricMilestoneInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricMilestoneInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricPlanItemInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricPlanItemInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricTaskInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricTaskInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseHistoricVariableInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoricVariableInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseJobResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/JobResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponsePlanItemInstanceResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/PlanItemInstanceResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  DataResponseTaskResponse:
    type: "object"
    properties:
      data:
        type: "array"
        items:
          $ref: "#/definitions/TaskResponse"
      total:
        type: "integer"
        format: "int64"
      start:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      size:
        type: "integer"
        format: "int32"
  Decision:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      externalRef:
        type: "string"
      implementationType:
        type: "string"
  DecisionTableResponse:
    type: "object"
    properties:
      id:
        type: "string"
      category:
        type: "string"
      name:
        type: "string"
      key:
        type: "string"
      description:
        type: "string"
      version:
        type: "integer"
        format: "int32"
      resourceName:
        type: "string"
      deploymentId:
        type: "string"
      tenantId:
        type: "string"
      url:
        type: "string"
  DeploymentResourceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "diagrams/my-process.bpmn20.xml"
      url:
        type: "string"
        example: "http://localhost:8081/flowable-rest/service/cmmn-repository/deployments/10/resources/diagrams%2Fmy-process.bpmn20.xml"
        description: "For a single resource contains the actual URL to use for retrieving\
          \ the binary resource"
      contentUrl:
        type: "string"
        example: "http://localhost:8081/flowable-rest/service/cmmn-repository/deployments/10/resourcedata/diagrams%2Fmy-process.bpmn20.xml"
      mediaType:
        type: "string"
        example: "text/xml"
        description: "Contains the media-type the resource has. This is resolved using\
          \ a (pluggable) MediaTypeResolver and contains, by default, a limited number\
          \ of mime-type mappings."
      type:
        type: "string"
        example: "processDefinition"
        description: "Type of resource"
        enum:
        - "resource"
        - "processDefinition"
        - "processImage"
  DmnDecisionTable:
    type: "object"
    properties:
      name:
        type: "string"
      key:
        type: "string"
      id:
        type: "string"
      version:
        type: "integer"
        format: "int32"
      category:
        type: "string"
      deploymentId:
        type: "string"
      tenantId:
        type: "string"
      resourceName:
        type: "string"
      description:
        type: "string"
  ExtensionAttribute:
    type: "object"
    properties:
      name:
        type: "string"
      value:
        type: "string"
      namespacePrefix:
        type: "string"
      namespace:
        type: "string"
  ExtensionElement:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      name:
        type: "string"
      namespacePrefix:
        type: "string"
      namespace:
        type: "string"
      elementText:
        type: "string"
      childElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
  FieldExtension:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      fieldName:
        type: "string"
      stringValue:
        type: "string"
      expression:
        type: "string"
  FlowableListener:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      event:
        type: "string"
      sourceState:
        type: "string"
      targetState:
        type: "string"
      implementationType:
        type: "string"
      implementation:
        type: "string"
      fieldExtensions:
        type: "array"
        items:
          $ref: "#/definitions/FieldExtension"
      onTransaction:
        type: "string"
  FormDefinition:
    type: "object"
    properties:
      name:
        type: "string"
      key:
        type: "string"
      id:
        type: "string"
      version:
        type: "integer"
        format: "int32"
      category:
        type: "string"
      deploymentId:
        type: "string"
      tenantId:
        type: "string"
      resourceName:
        type: "string"
      description:
        type: "string"
  FormDefinitionResponse:
    type: "object"
    properties:
      id:
        type: "string"
      url:
        type: "string"
      category:
        type: "string"
      name:
        type: "string"
      key:
        type: "string"
      description:
        type: "string"
      version:
        type: "integer"
        format: "int32"
      resourceName:
        type: "string"
      deploymentId:
        type: "string"
      tenantId:
        type: "string"
  GraphicInfo:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      x:
        type: "number"
        format: "double"
      y:
        type: "number"
        format: "double"
      height:
        type: "number"
        format: "double"
      width:
        type: "number"
        format: "double"
      element:
        $ref: "#/definitions/BaseElement"
      expanded:
        type: "boolean"
  HistoricCaseInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      caseInstanceId:
        type: "string"
      caseInstanceIds:
        type: "array"
        items:
          type: "string"
      caseBusinessKey:
        type: "string"
      caseDefinitionId:
        type: "string"
      caseDefinitionKey:
        type: "string"
      excludeSubprocesses:
        type: "boolean"
      finished:
        type: "boolean"
      involvedUser:
        type: "string"
      finishedAfter:
        type: "string"
        format: "date-time"
      finishedBefore:
        type: "string"
        format: "date-time"
      startedAfter:
        type: "string"
        format: "date-time"
      startedBefore:
        type: "string"
        format: "date-time"
      startedBy:
        type: "string"
      includeCaseVariables:
        type: "boolean"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      tenantId:
        type: "string"
      withoutTenantId:
        type: "boolean"
  HistoricCaseInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "5"
      url:
        type: "string"
        example: "http://localhost:8182/cmmn-history/historic-case-instances/5"
      name:
        type: "string"
        example: "myName"
      businessKey:
        type: "string"
        example: "myKey"
      caseDefinitionId:
        type: "string"
        example: "oneTaskCase%3A1%3A4"
      caseDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-repository/case-definitions/oneTaskCaseProcess%3A1%3A4"
      caseDefinitionName:
        type: "string"
        example: "aCaseDefinitionName"
      caseDefinitionDescription:
        type: "string"
        example: "A case definition description"
      startTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      endTime:
        type: "string"
        format: "date-time"
        example: "2013-04-18T14:06:32.715+0000"
      startUserId:
        type: "string"
        example: "kermit"
      superProcessInstanceId:
        type: "string"
        example: "3"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      tenantId:
        type: "string"
        example: "null"
      state:
        type: "string"
        example: "active"
      callbackId:
        type: "string"
        example: "123"
      callbackType:
        type: "string"
        example: "cmmn-1.1-to-cmmn-1.1-child-case"
      referenceId:
        type: "string"
        example: "123"
      referenceType:
        type: "string"
        example: "event-to-cmmn-1.1-case"
  HistoricIdentityLinkResponse:
    type: "object"
    properties:
      type:
        type: "string"
        example: "participant"
      userId:
        type: "string"
        example: "kermit"
      groupId:
        type: "string"
        example: "sales"
      taskId:
        type: "string"
        example: "null"
      taskUrl:
        type: "string"
        example: "null"
      caseInstanceId:
        type: "string"
        example: "5"
      caseInstanceUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-history/historic-case-instances/5"
  HistoricMilestoneInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      id:
        type: "string"
      name:
        type: "string"
      caseInstanceId:
        type: "string"
      caseDefinitionId:
        type: "string"
      reachedBefore:
        type: "string"
        format: "date-time"
      reachedAfter:
        type: "string"
        format: "date-time"
  HistoricMilestoneInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "5"
      name:
        type: "string"
        example: "milestoneName"
      elementId:
        type: "string"
        example: "milestonePlanItemId"
      timestamp:
        type: "string"
        format: "date-time"
        example: "2013-04-18T14:06:32.715+0000"
      caseInstanceId:
        type: "string"
        example: "12345"
      caseDefinitionId:
        type: "string"
        example: "oneMilestoneCase%3A1%3A4"
      url:
        type: "string"
        example: "http://localhost:8182/cmmn-history/historic-milestone-instances/5"
      historicCaseInstanceUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-history/historic-case-instances/12345"
      caseDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-repository/case-definitions/oneMilestoneCase%3A1%3A4"
  HistoricPlanItemInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      planItemInstanceId:
        type: "string"
      planItemInstanceName:
        type: "string"
      planItemInstanceState:
        type: "string"
      caseDefinitionId:
        type: "string"
      caseInstanceId:
        type: "string"
      stageInstanceId:
        type: "string"
      elementId:
        type: "string"
      planItemDefinitionId:
        type: "string"
      planItemDefinitionType:
        type: "string"
      createdBefore:
        type: "string"
        format: "date-time"
      createdAfter:
        type: "string"
        format: "date-time"
      lastAvailableBefore:
        type: "string"
        format: "date-time"
      lastAvailableAfter:
        type: "string"
        format: "date-time"
      lastEnabledBefore:
        type: "string"
        format: "date-time"
      lastEnabledAfter:
        type: "string"
        format: "date-time"
      lastDisabledBefore:
        type: "string"
        format: "date-time"
      lastDisabledAfter:
        type: "string"
        format: "date-time"
      lastStartedBefore:
        type: "string"
        format: "date-time"
      lastStartedAfter:
        type: "string"
        format: "date-time"
      lastSuspendedBefore:
        type: "string"
        format: "date-time"
      lastSuspendedAfter:
        type: "string"
        format: "date-time"
      completedBefore:
        type: "string"
        format: "date-time"
      completedAfter:
        type: "string"
        format: "date-time"
      terminatedBefore:
        type: "string"
        format: "date-time"
      terminatedAfter:
        type: "string"
        format: "date-time"
      occurredBefore:
        type: "string"
        format: "date-time"
      occurredAfter:
        type: "string"
        format: "date-time"
      exitBefore:
        type: "string"
        format: "date-time"
      exitAfter:
        type: "string"
        format: "date-time"
      endedBefore:
        type: "string"
        format: "date-time"
      endedAfter:
        type: "string"
        format: "date-time"
      startUserId:
        type: "string"
      referenceId:
        type: "string"
      referenceType:
        type: "string"
      tenantId:
        type: "string"
      withoutTenantId:
        type: "boolean"
  HistoricPlanItemInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "5"
      name:
        type: "string"
        example: "myPlanItemName"
      state:
        type: "string"
        example: "completed"
      caseDefinitionId:
        type: "string"
        example: "myCaseId%3A1%3A4"
      derivedCaseDefinitionId:
        type: "string"
      caseInstanceId:
        type: "string"
        example: "12345"
      stageInstanceId:
        type: "string"
        example: "stageId"
      stage:
        type: "boolean"
        example: true
      elementId:
        type: "string"
        example: "someElementId"
      planItemDefinitionId:
        type: "string"
        example: "someId"
      planItemDefinitionType:
        type: "string"
        example: "timerEventListener"
      createTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      lastAvailableTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      lastEnabledTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      lastDisabledTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      lastStartedTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      lastSuspendedTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      completedTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      occurredTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      terminatedTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      exitTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      endedTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      lastUpdatedTime:
        type: "string"
        format: "date-time"
      startUserId:
        type: "string"
        example: "kermit"
      referenceId:
        type: "string"
        example: "referenceId"
      referenceType:
        type: "string"
        example: "referenceType"
      entryCriterionId:
        type: "string"
      exitCriterionId:
        type: "string"
      formKey:
        type: "string"
      extraValue:
        type: "string"
      showInOverview:
        type: "boolean"
      tenantId:
        type: "string"
        example: "null"
      url:
        type: "string"
        example: "http://localhost:8182/cmmn-history/historic-planitem-instances/5"
      caseInstanceUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-history/historic-case-instances/12345"
      caseDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-repository/case-definitions/myCaseId%3A1%3A4"
      derivedCaseDefinitionUrl:
        type: "string"
      stageInstanceUrl:
        type: "string"
  HistoricTaskInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      taskId:
        type: "string"
      caseInstanceId:
        type: "string"
      caseInstanceIdWithChildren:
        type: "string"
      caseDefinitionId:
        type: "string"
      taskName:
        type: "string"
      taskNameLike:
        type: "string"
      taskDescription:
        type: "string"
      taskDescriptionLike:
        type: "string"
      taskDefinitionKey:
        type: "string"
      taskDefinitionKeyLike:
        type: "string"
      taskCategory:
        type: "string"
      taskDeleteReason:
        type: "string"
      taskDeleteReasonLike:
        type: "string"
      taskAssignee:
        type: "string"
      taskAssigneeLike:
        type: "string"
      taskOwner:
        type: "string"
      taskOwnerLike:
        type: "string"
      taskInvolvedUser:
        type: "string"
      taskPriority:
        type: "integer"
        format: "int32"
      taskMinPriority:
        type: "integer"
        format: "int32"
      taskMaxPriority:
        type: "integer"
        format: "int32"
      finished:
        type: "boolean"
      processFinished:
        type: "boolean"
      parentTaskId:
        type: "string"
      dueDate:
        type: "string"
        format: "date-time"
      dueDateAfter:
        type: "string"
        format: "date-time"
      dueDateBefore:
        type: "string"
        format: "date-time"
      withoutDueDate:
        type: "boolean"
      taskCreatedOn:
        type: "string"
        format: "date-time"
      taskCreatedBefore:
        type: "string"
        format: "date-time"
      taskCreatedAfter:
        type: "string"
        format: "date-time"
      taskCompletedOn:
        type: "string"
        format: "date-time"
      taskCompletedBefore:
        type: "string"
        format: "date-time"
      taskCompletedAfter:
        type: "string"
        format: "date-time"
      includeTaskLocalVariables:
        type: "boolean"
      taskVariables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      tenantId:
        type: "string"
      tenantIdLike:
        type: "string"
      withoutTenantId:
        type: "boolean"
      taskCandidateGroup:
        type: "string"
  HistoricTaskInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
      name:
        type: "string"
      description:
        type: "string"
      deleteReason:
        type: "string"
      owner:
        type: "string"
      assignee:
        type: "string"
      startTime:
        type: "string"
        format: "date-time"
      endTime:
        type: "string"
        format: "date-time"
      durationInMillis:
        type: "integer"
        format: "int64"
      workTimeInMillis:
        type: "integer"
        format: "int64"
      claimTime:
        type: "string"
        format: "date-time"
      taskDefinitionKey:
        type: "string"
      formKey:
        type: "string"
      priority:
        type: "integer"
        format: "int32"
      dueDate:
        type: "string"
        format: "date-time"
      parentTaskId:
        type: "string"
      url:
        type: "string"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
      tenantId:
        type: "string"
      category:
        type: "string"
      caseInstanceId:
        type: "string"
      caseInstanceUrl:
        type: "string"
      caseDefinitionId:
        type: "string"
      caseDefinitionUrl:
        type: "string"
      scopeDefinitionId:
        type: "string"
      scopeId:
        type: "string"
      subScopeId:
        type: "string"
      scopeType:
        type: "string"
      propagatedStageInstanceId:
        type: "string"
      executionId:
        type: "string"
      processInstanceId:
        type: "string"
      processDefinitionId:
        type: "string"
  HistoricVariableInstanceQueryRequest:
    type: "object"
    properties:
      excludeTaskVariables:
        type: "boolean"
      taskId:
        type: "string"
      planItemInstanceId:
        type: "string"
      caseInstanceId:
        type: "string"
      variableName:
        type: "string"
      variableNameLike:
        type: "string"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
  HistoricVariableInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "14"
      caseInstanceId:
        type: "string"
        example: "5"
      caseInstanceUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-history/historic-case-instances/5"
      taskId:
        type: "string"
        example: "6"
      variable:
        $ref: "#/definitions/RestVariable"
  JobResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "8"
      url:
        type: "string"
        example: "http://localhost:8182/management/jobs/8"
      caseInstanceId:
        type: "string"
        example: "5"
      caseInstanceUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-runtime/case-instances/5"
      caseDefinitionId:
        type: "string"
        example: "timerCase:1:4"
      caseDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-repository/case-definitions/timerCase%3A1%3A4"
      planItemInstanceId:
        type: "string"
        example: "7"
      retries:
        type: "integer"
        format: "int32"
        example: 3
      exceptionMessage:
        type: "string"
        example: "null"
      dueDate:
        type: "string"
        format: "date-time"
        example: "2013-06-04T22:05:05.474+0000"
      createTime:
        type: "string"
        format: "date-time"
        example: "2013-06-03T22:05:05.474+0000"
      tenantId:
        type: "string"
        example: "null"
  ManualActivationRule:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      condition:
        type: "string"
      parentStage:
        $ref: "#/definitions/Stage"
  ParentCompletionRule:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      condition:
        type: "string"
      type:
        type: "string"
      parentStage:
        $ref: "#/definitions/Stage"
  PlanFragment:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      planItemRef:
        type: "string"
      defaultControl:
        $ref: "#/definitions/PlanItemControl"
      lifecycleListeners:
        type: "array"
        items:
          $ref: "#/definitions/FlowableListener"
      planItem:
        $ref: "#/definitions/PlanItem"
      planItemMap:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/PlanItem"
      sentries:
        type: "array"
        items:
          $ref: "#/definitions/Sentry"
      case:
        $ref: "#/definitions/Case"
      planItems:
        type: "array"
        items:
          $ref: "#/definitions/PlanItem"
      parentStage:
        $ref: "#/definitions/Stage"
  PlanItem:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      definitionRef:
        type: "string"
      planItemDefinition:
        $ref: "#/definitions/PlanItemDefinition"
      itemControl:
        $ref: "#/definitions/PlanItemControl"
      criteriaRefs:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
      entryCriteria:
        type: "array"
        items:
          $ref: "#/definitions/Criterion"
      exitCriteria:
        type: "array"
        items:
          $ref: "#/definitions/Criterion"
      incomingAssociations:
        type: "array"
        items:
          $ref: "#/definitions/Association"
      outgoingAssociations:
        type: "array"
        items:
          $ref: "#/definitions/Association"
      entryDependencies:
        type: "array"
        items:
          $ref: "#/definitions/PlanItem"
      exitDependencies:
        type: "array"
        items:
          $ref: "#/definitions/PlanItem"
      entryDependentPlanItems:
        type: "array"
        items:
          $ref: "#/definitions/PlanItem"
      exitDependentPlanItems:
        type: "array"
        items:
          $ref: "#/definitions/PlanItem"
      behavior:
        type: "object"
      allDependentPlanItems:
        type: "array"
        items:
          $ref: "#/definitions/PlanItem"
      parentStage:
        $ref: "#/definitions/Stage"
  PlanItemControl:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      requiredRule:
        $ref: "#/definitions/RequiredRule"
      repetitionRule:
        $ref: "#/definitions/RepetitionRule"
      manualActivationRule:
        $ref: "#/definitions/ManualActivationRule"
      completionNeutralRule:
        $ref: "#/definitions/CompletionNeutralRule"
      parentCompletionRule:
        $ref: "#/definitions/ParentCompletionRule"
      parentStage:
        $ref: "#/definitions/Stage"
  PlanItemDefinition:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      planItemRef:
        type: "string"
      defaultControl:
        $ref: "#/definitions/PlanItemControl"
      lifecycleListeners:
        type: "array"
        items:
          $ref: "#/definitions/FlowableListener"
      parentStage:
        $ref: "#/definitions/Stage"
  PlanItemInstanceQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      id:
        type: "string"
      elementId:
        type: "string"
      name:
        type: "string"
      caseInstanceId:
        type: "string"
      caseDefinitionId:
        type: "string"
      stageInstanceId:
        type: "string"
      planItemDefinitionId:
        type: "string"
      planItemDefinitionType:
        type: "string"
      planItemDefinitionTypes:
        type: "array"
        items:
          type: "string"
      state:
        type: "string"
      createdBefore:
        type: "string"
        format: "date-time"
      createdAfter:
        type: "string"
        format: "date-time"
      startUserId:
        type: "string"
      referenceId:
        type: "string"
      referenceType:
        type: "string"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      caseInstanceVariables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
      tenantId:
        type: "string"
      withoutTenantId:
        type: "boolean"
  PlanItemInstanceResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "5"
      url:
        type: "string"
        example: "http://localhost:8182/runtime/executions/5"
      name:
        type: "string"
      caseInstanceId:
        type: "string"
      caseInstanceUrl:
        type: "string"
      caseDefinitionId:
        type: "string"
      caseDefinitionUrl:
        type: "string"
      derivedCaseDefinitionId:
        type: "string"
      derivedCaseDefinitionUrl:
        type: "string"
      stageInstanceId:
        type: "string"
      stageInstanceUrl:
        type: "string"
      planItemDefinitionId:
        type: "string"
      planItemDefinitionType:
        type: "string"
      state:
        type: "string"
      stage:
        type: "boolean"
      elementId:
        type: "string"
      createTime:
        type: "string"
        format: "date-time"
      lastAvailableTime:
        type: "string"
        format: "date-time"
      lastEnabledTime:
        type: "string"
        format: "date-time"
      lastDisabledTime:
        type: "string"
        format: "date-time"
      lastStartedTime:
        type: "string"
        format: "date-time"
      lastSuspendedTime:
        type: "string"
        format: "date-time"
      completedTime:
        type: "string"
        format: "date-time"
      occurredTime:
        type: "string"
        format: "date-time"
      terminatedTime:
        type: "string"
        format: "date-time"
      exitTime:
        type: "string"
        format: "date-time"
      endedTime:
        type: "string"
        format: "date-time"
      startUserId:
        type: "string"
      referenceId:
        type: "string"
      referenceType:
        type: "string"
      completable:
        type: "boolean"
      entryCriterionId:
        type: "string"
      exitCriterionId:
        type: "string"
      formKey:
        type: "string"
      extraValue:
        type: "string"
      tenantId:
        type: "string"
        example: "null"
  Process:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      externalRef:
        type: "string"
      implementationType:
        type: "string"
  QueryVariable:
    type: "object"
    properties:
      name:
        type: "string"
      operation:
        type: "string"
        enum:
        - "equals"
        - "notEquals"
        - "equalsIgnoreCase"
        - "notEqualsIgnoreCase"
        - "like"
        - "likeIgnoreCase"
        - "greaterThan"
        - "greaterThanOrEquals"
        - "lessThan"
        - "lessThanOrEquals"
      value:
        type: "object"
      type:
        type: "string"
  RepetitionRule:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      condition:
        type: "string"
      repetitionCounterVariableName:
        type: "string"
      collectionVariableName:
        type: "string"
      elementVariableName:
        type: "string"
      elementIndexVariableName:
        type: "string"
      maxInstanceCount:
        type: "integer"
        format: "int32"
      parentStage:
        $ref: "#/definitions/Stage"
  RequiredRule:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      condition:
        type: "string"
      parentStage:
        $ref: "#/definitions/Stage"
  RestActionRequest:
    type: "object"
    properties:
      action:
        type: "string"
  RestIdentityLink:
    type: "object"
    properties:
      url:
        type: "string"
      user:
        type: "string"
        example: "kermit"
      group:
        type: "string"
        example: "sales"
      type:
        type: "string"
        example: "candidate"
  RestVariable:
    type: "object"
    properties:
      name:
        type: "string"
        example: "myVariable"
        description: "Name of the variable"
      type:
        type: "string"
        example: "string"
        description: "Type of the variable."
      value:
        type: "object"
        example: "test"
        description: "Value of the variable."
      valueUrl:
        type: "string"
        example: "http://...."
      scope:
        type: "string"
  Sentry:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      triggerMode:
        type: "string"
      onParts:
        type: "array"
        items:
          $ref: "#/definitions/SentryOnPart"
      sentryIfPart:
        $ref: "#/definitions/SentryIfPart"
      onEventTriggerMode:
        type: "boolean"
      defaultTriggerMode:
        type: "boolean"
      parentStage:
        $ref: "#/definitions/Stage"
  SentryIfPart:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      condition:
        type: "string"
  SentryOnPart:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      sourceRef:
        type: "string"
      source:
        $ref: "#/definitions/PlanItem"
      standardEvent:
        type: "string"
  Stage:
    type: "object"
    properties:
      id:
        type: "string"
      xmlRowNumber:
        type: "integer"
        format: "int32"
      xmlColumnNumber:
        type: "integer"
        format: "int32"
      extensionElements:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionElement"
      attributes:
        type: "object"
        additionalProperties:
          type: "array"
          items:
            $ref: "#/definitions/ExtensionAttribute"
      documentationTextFormat:
        type: "string"
      documentation:
        type: "string"
      name:
        type: "string"
      parent:
        $ref: "#/definitions/PlanFragment"
      planItemRef:
        type: "string"
      defaultControl:
        $ref: "#/definitions/PlanItemControl"
      lifecycleListeners:
        type: "array"
        items:
          $ref: "#/definitions/FlowableListener"
      planItem:
        $ref: "#/definitions/PlanItem"
      planItemMap:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/PlanItem"
      sentries:
        type: "array"
        items:
          $ref: "#/definitions/Sentry"
      exitCriteria:
        type: "array"
        items:
          $ref: "#/definitions/Criterion"
      autoComplete:
        type: "boolean"
      autoCompleteCondition:
        type: "string"
      formKey:
        type: "string"
      sameDeployment:
        type: "boolean"
      validateFormFields:
        type: "string"
      displayOrder:
        type: "integer"
        format: "int32"
      includeInStageOverview:
        type: "string"
      planItemDefinitionMap:
        type: "object"
        additionalProperties:
          $ref: "#/definitions/PlanItemDefinition"
      planItemDefinitions:
        type: "array"
        items:
          $ref: "#/definitions/PlanItemDefinition"
      planModel:
        type: "boolean"
      case:
        $ref: "#/definitions/Case"
      planItems:
        type: "array"
        items:
          $ref: "#/definitions/PlanItem"
      parentStage:
        $ref: "#/definitions/Stage"
  StageResponse:
    type: "object"
    properties:
      id:
        type: "string"
      name:
        type: "string"
      ended:
        type: "boolean"
      endTime:
        type: "string"
        format: "date-time"
      current:
        type: "boolean"
  TaskActionRequest:
    type: "object"
    required:
    - "action"
    properties:
      action:
        type: "string"
        example: "complete"
        description: "Action to perform: Either complete, claim, delegate or resolve"
      assignee:
        type: "string"
        example: "userWhoClaims/userToDelegateTo"
        description: "If action is claim or delegate, you can use this parameter to\
          \ set the assignee associated "
      formDefinitionId:
        type: "string"
        example: "12345"
        description: "Required when completing a task with a form"
      outcome:
        type: "string"
        example: "accepted/rejected"
        description: "Optional outcome value when completing a task with a form"
      variables:
        type: "array"
        description: "If action is complete, you can use this parameter to set variables "
        items:
          $ref: "#/definitions/RestVariable"
      transientVariables:
        type: "array"
        description: "If action is complete, you can use this parameter to set transient\
          \ variables "
        items:
          $ref: "#/definitions/RestVariable"
  TaskQueryRequest:
    type: "object"
    properties:
      start:
        type: "integer"
        format: "int32"
      size:
        type: "integer"
        format: "int32"
      sort:
        type: "string"
      order:
        type: "string"
      name:
        type: "string"
      nameLike:
        type: "string"
      description:
        type: "string"
      descriptionLike:
        type: "string"
      priority:
        type: "integer"
        format: "int32"
      minimumPriority:
        type: "integer"
        format: "int32"
      maximumPriority:
        type: "integer"
        format: "int32"
      assignee:
        type: "string"
      assigneeLike:
        type: "string"
      owner:
        type: "string"
      ownerLike:
        type: "string"
      unassigned:
        type: "boolean"
      delegationState:
        type: "string"
      candidateUser:
        type: "string"
      candidateGroup:
        type: "string"
      candidateGroupIn:
        type: "array"
        items:
          type: "string"
      involvedUser:
        type: "string"
      caseInstanceId:
        type: "string"
      caseInstanceIdWithChildren:
        type: "string"
      caseDefinitionId:
        type: "string"
      createdOn:
        type: "string"
        format: "date-time"
      createdBefore:
        type: "string"
        format: "date-time"
      createdAfter:
        type: "string"
        format: "date-time"
      excludeSubTasks:
        type: "boolean"
      taskDefinitionKey:
        type: "string"
      taskDefinitionKeyLike:
        type: "string"
      dueDate:
        type: "string"
        format: "date-time"
      dueBefore:
        type: "string"
        format: "date-time"
      dueAfter:
        type: "string"
        format: "date-time"
      withoutDueDate:
        type: "boolean"
      active:
        type: "boolean"
      includeTaskLocalVariables:
        type: "boolean"
      tenantId:
        type: "string"
      tenantIdLike:
        type: "string"
      withoutTenantId:
        type: "boolean"
      candidateOrAssigned:
        type: "string"
      category:
        type: "string"
      taskVariables:
        type: "array"
        items:
          $ref: "#/definitions/QueryVariable"
  TaskRequest:
    type: "object"
    properties:
      owner:
        type: "string"
      assignee:
        type: "string"
      delegationState:
        type: "string"
      name:
        type: "string"
      description:
        type: "string"
      dueDate:
        type: "string"
        format: "date-time"
      priority:
        type: "integer"
        format: "int32"
      parentTaskId:
        type: "string"
      category:
        type: "string"
      tenantId:
        type: "string"
      formKey:
        type: "string"
      ownerSet:
        type: "boolean"
      assigneeSet:
        type: "boolean"
      delegationStateSet:
        type: "boolean"
      nameSet:
        type: "boolean"
      descriptionSet:
        type: "boolean"
      duedateSet:
        type: "boolean"
      prioritySet:
        type: "boolean"
      parentTaskIdSet:
        type: "boolean"
      categorySet:
        type: "boolean"
      tenantIdSet:
        type: "boolean"
      formKeySet:
        type: "boolean"
  TaskResponse:
    type: "object"
    properties:
      id:
        type: "string"
        example: "8"
      url:
        type: "string"
        example: "http://localhost:8182/cmmn-runtime/tasks/8"
      owner:
        type: "string"
        example: "owner"
      assignee:
        type: "string"
        example: "kermit"
      delegationState:
        type: "string"
        example: "pending"
        description: "Delegation-state of the task, can be null, \"pending\" or \"\
          resolved\"."
      name:
        type: "string"
        example: "My task"
      description:
        type: "string"
        example: "Task description"
      createTime:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      dueDate:
        type: "string"
        format: "date-time"
        example: "2013-04-17T10:17:43.902+0000"
      priority:
        type: "integer"
        format: "int32"
        example: 50
      suspended:
        type: "boolean"
      claimTime:
        type: "string"
        example: "2018-04-17T10:17:43.902+0000"
      taskDefinitionKey:
        type: "string"
        example: "theTask"
      scopeDefinitionId:
        type: "string"
        example: "123"
      scopeId:
        type: "string"
        example: "123"
      subScopeId:
        type: "string"
        example: "123"
      scopeType:
        type: "string"
        example: "cmmn"
      propagatedStageInstanceId:
        type: "string"
        example: "123"
      tenantId:
        type: "string"
        example: "null"
      category:
        type: "string"
        example: "ExampleCategory"
      formKey:
        type: "string"
      caseInstanceId:
        type: "string"
        example: "5"
      caseInstanceUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-runtime/case-instances/5"
      caseDefinitionId:
        type: "string"
        example: "oneTaskCase%3A1%3A4"
      caseDefinitionUrl:
        type: "string"
        example: "http://localhost:8182/cmmn-runtime/case-definitions/oneTaskCase%3A1%3A4"
      parentTaskId:
        type: "string"
        example: "null"
      parentTaskUrl:
        type: "string"
        example: "null"
      executionId:
        type: "string"
        example: "123"
      processInstanceId:
        type: "string"
        example: "123"
      processDefinitionId:
        type: "string"
        example: "123"
      variables:
        type: "array"
        items:
          $ref: "#/definitions/RestVariable"
  TaskVariableCollectionResource:
    type: "object"
  TaskVariableResource:
    type: "object"
