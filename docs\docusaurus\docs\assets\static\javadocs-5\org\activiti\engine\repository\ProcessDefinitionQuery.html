<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProcessDefinitionQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProcessDefinitionQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":38,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessDefinitionQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/ProcessDefinitionQuery.html" target="_top">Frames</a></li>
<li><a href="ProcessDefinitionQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.repository</div>
<h2 title="Interface ProcessDefinitionQuery" class="title">Interface ProcessDefinitionQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>,<a href="../../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository">ProcessDefinition</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ProcessDefinitionQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>,<a href="../../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository">ProcessDefinition</a>&gt;</pre>
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><code>ProcessDefinition</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez, Daniel Meyer, Saeid Mirzaei</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#active--">active</a></span>()</code>
<div class="block">Only selects process definitions which are active</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#deploymentId-java.lang.String-">deploymentId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block">Only select process definitions that are deployed in a deployment with the
 given deployment id</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#deploymentIds-java.util.Set-">deploymentIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;deploymentIds)</code>
<div class="block">Select process definitions that are deployed in deployments with the given set of ids</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#latestVersion--">latestVersion</a></span>()</code>
<div class="block">Only select the process definitions which are the latest deployed
 (ie.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#messageEventSubscription-java.lang.String-">messageEventSubscription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#messageEventSubscriptionName-java.lang.String-">messageEventSubscriptionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</code>
<div class="block">Selects the single process definition which has a start message event 
 with the messageName.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByDeploymentId--">orderByDeploymentId</a></span>()</code>
<div class="block">Order by deployment id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionCategory--">orderByProcessDefinitionCategory</a></span>()</code>
<div class="block">Order by the category of the process definitions (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionId--">orderByProcessDefinitionId</a></span>()</code>
<div class="block">Order by the id of the process definitions (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionKey--">orderByProcessDefinitionKey</a></span>()</code>
<div class="block">Order by process definition key (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionName--">orderByProcessDefinitionName</a></span>()</code>
<div class="block">Order by the name of the process definitions (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionVersion--">orderByProcessDefinitionVersion</a></span>()</code>
<div class="block">Order by the version of the process definitions (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionCategory-java.lang.String-">processDefinitionCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategory)</code>
<div class="block">Only select process definitions with the given category.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionCategoryLike-java.lang.String-">processDefinitionCategoryLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategoryLike)</code>
<div class="block">Only select process definitions where the category matches the given parameter.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionCategoryNotEquals-java.lang.String-">processDefinitionCategoryNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;categoryNotEquals)</code>
<div class="block">Only select deployments that have a different category then the given one.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Only select process definiton with the given id.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionIds-java.util.Set-">processDefinitionIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionIds)</code>
<div class="block">Only select process definitions with the given ids.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Only select process definition with the given key.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionKeyLike-java.lang.String-">processDefinitionKeyLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKeyLike)</code>
<div class="block">Only select process definitions where the key matches the given parameter.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionName-java.lang.String-">processDefinitionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</code>
<div class="block">Only select process definitions with the given name.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionNameLike-java.lang.String-">processDefinitionNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionNameLike)</code>
<div class="block">Only select process definitions where the name matches the given parameter.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionResourceName-java.lang.String-">processDefinitionResourceName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName)</code>
<div class="block">Only select process definition with the given resource name.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionResourceNameLike-java.lang.String-">processDefinitionResourceNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceNameLike)</code>
<div class="block">Only select process definition with a resource name like the given .</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionTenantId-java.lang.String-">processDefinitionTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select process definitions that have the given tenant id.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionTenantIdLike-java.lang.String-">processDefinitionTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select process definitions with a tenant id like the given one.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersion-java.lang.Integer-">processDefinitionVersion</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definition with a certain version.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersionGreaterThan-java.lang.Integer-">processDefinitionVersionGreaterThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definitions which version are greater than a certain version.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersionGreaterThanOrEquals-java.lang.Integer-">processDefinitionVersionGreaterThanOrEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definitions which version are greater than or equals a certain version.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersionLowerThan-java.lang.Integer-">processDefinitionVersionLowerThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definitions which version are lower than a certain version.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersionLowerThanOrEquals-java.lang.Integer-">processDefinitionVersionLowerThanOrEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definitions which version are lower than or equals a certain version.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionWithoutTenantId--">processDefinitionWithoutTenantId</a></span>()</code>
<div class="block">Only select process definitions that do not have a tenant id.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#startableByUser-java.lang.String-">startableByUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Only selects process definitions which given userId is authoriezed to start</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#suspended--">suspended</a></span>()</code>
<div class="block">Only selects process definitions which are suspended</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="processDefinitionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</pre>
<div class="block">Only select process definiton with the given id.</div>
</li>
</ul>
<a name="processDefinitionIds-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionIds</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionIds(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionIds)</pre>
<div class="block">Only select process definitions with the given ids.</div>
</li>
</ul>
<a name="processDefinitionCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionCategory</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategory)</pre>
<div class="block">Only select process definitions with the given category.</div>
</li>
</ul>
<a name="processDefinitionCategoryLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionCategoryLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionCategoryLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategoryLike)</pre>
<div class="block">Only select process definitions where the category matches the given parameter.
 The syntax that should be used is the same as in SQL, eg. %activiti%</div>
</li>
</ul>
<a name="processDefinitionCategoryNotEquals-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionCategoryNotEquals</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionCategoryNotEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;categoryNotEquals)</pre>
<div class="block">Only select deployments that have a different category then the given one.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../org/activiti/engine/repository/DeploymentBuilder.html#category-java.lang.String-"><code>DeploymentBuilder.category(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="processDefinitionName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionName</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</pre>
<div class="block">Only select process definitions with the given name.</div>
</li>
</ul>
<a name="processDefinitionNameLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionNameLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionNameLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionNameLike)</pre>
<div class="block">Only select process definitions where the name matches the given parameter.
 The syntax that should be used is the same as in SQL, eg. %activiti%</div>
</li>
</ul>
<a name="deploymentId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;deploymentId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</pre>
<div class="block">Only select process definitions that are deployed in a deployment with the
 given deployment id</div>
</li>
</ul>
<a name="deploymentIds-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deploymentIds</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;deploymentIds(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;deploymentIds)</pre>
<div class="block">Select process definitions that are deployed in deployments with the given set of ids</div>
</li>
</ul>
<a name="processDefinitionKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</pre>
<div class="block">Only select process definition with the given key.</div>
</li>
</ul>
<a name="processDefinitionKeyLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionKeyLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionKeyLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKeyLike)</pre>
<div class="block">Only select process definitions where the key matches the given parameter.
 The syntax that should be used is the same as in SQL, eg. %activiti%</div>
</li>
</ul>
<a name="processDefinitionVersion-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionVersion</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionVersion(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</pre>
<div class="block">Only select process definition with a certain version.
 Particulary useful when used in combination with <a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionKey-java.lang.String-"><code>processDefinitionKey(String)</code></a></div>
</li>
</ul>
<a name="processDefinitionVersionGreaterThan-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionVersionGreaterThan</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionVersionGreaterThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</pre>
<div class="block">Only select process definitions which version are greater than a certain version.</div>
</li>
</ul>
<a name="processDefinitionVersionGreaterThanOrEquals-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionVersionGreaterThanOrEquals</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionVersionGreaterThanOrEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</pre>
<div class="block">Only select process definitions which version are greater than or equals a certain version.</div>
</li>
</ul>
<a name="processDefinitionVersionLowerThan-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionVersionLowerThan</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionVersionLowerThan(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</pre>
<div class="block">Only select process definitions which version are lower than a certain version.</div>
</li>
</ul>
<a name="processDefinitionVersionLowerThanOrEquals-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionVersionLowerThanOrEquals</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionVersionLowerThanOrEquals(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</pre>
<div class="block">Only select process definitions which version are lower than or equals a certain version.</div>
</li>
</ul>
<a name="latestVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>latestVersion</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;latestVersion()</pre>
<div class="block">Only select the process definitions which are the latest deployed
 (ie. which have the highest version number for the given key).
 
 Can also be used without any other criteria (ie. query.latest().list()), which
 will then give all the latest versions of all the deployed process definitions.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></code> - if used in combination with  <code>#groupId(string)</code>, <code>#processDefinitionVersion(int)</code>
                           or <a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#deploymentId-java.lang.String-"><code>deploymentId(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="processDefinitionResourceName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionResourceName</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionResourceName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName)</pre>
<div class="block">Only select process definition with the given resource name.</div>
</li>
</ul>
<a name="processDefinitionResourceNameLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionResourceNameLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionResourceNameLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceNameLike)</pre>
<div class="block">Only select process definition with a resource name like the given .</div>
</li>
</ul>
<a name="startableByUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startableByUser</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;startableByUser(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">Only selects process definitions which given userId is authoriezed to start</div>
</li>
</ul>
<a name="suspended--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suspended</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;suspended()</pre>
<div class="block">Only selects process definitions which are suspended</div>
</li>
</ul>
<a name="active--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>active</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;active()</pre>
<div class="block">Only selects process definitions which are active</div>
</li>
</ul>
<a name="processDefinitionTenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionTenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Only select process definitions that have the given tenant id.</div>
</li>
</ul>
<a name="processDefinitionTenantIdLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionTenantIdLike</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionTenantIdLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</pre>
<div class="block">Only select process definitions with a tenant id like the given one.</div>
</li>
</ul>
<a name="processDefinitionWithoutTenantId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefinitionWithoutTenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;processDefinitionWithoutTenantId()</pre>
<div class="block">Only select process definitions that do not have a tenant id.</div>
</li>
</ul>
<a name="messageEventSubscription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>messageEventSubscription</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
<a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;messageEventSubscription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#messageEventSubscriptionName-java.lang.String-"><code>messageEventSubscriptionName(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="messageEventSubscriptionName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>messageEventSubscriptionName</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;messageEventSubscriptionName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</pre>
<div class="block">Selects the single process definition which has a start message event 
 with the messageName.</div>
</li>
</ul>
<a name="orderByProcessDefinitionCategory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionCategory</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;orderByProcessDefinitionCategory()</pre>
<div class="block">Order by the category of the process definitions (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessDefinitionKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionKey</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;orderByProcessDefinitionKey()</pre>
<div class="block">Order by process definition key (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessDefinitionId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;orderByProcessDefinitionId()</pre>
<div class="block">Order by the id of the process definitions (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessDefinitionVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionVersion</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;orderByProcessDefinitionVersion()</pre>
<div class="block">Order by the version of the process definitions (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByProcessDefinitionName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessDefinitionName</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;orderByProcessDefinitionName()</pre>
<div class="block">Order by the name of the process definitions (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByDeploymentId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByDeploymentId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;orderByDeploymentId()</pre>
<div class="block">Order by deployment id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByTenantId--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByTenantId</h4>
<pre><a href="../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a>&nbsp;orderByTenantId()</pre>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProcessDefinitionQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/repository/ProcessDefinition.html" title="interface in org.activiti.engine.repository"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/repository/ProcessDefinitionQuery.html" target="_top">Frames</a></li>
<li><a href="ProcessDefinitionQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
