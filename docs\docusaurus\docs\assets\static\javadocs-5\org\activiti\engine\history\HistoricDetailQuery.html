<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HistoricDetailQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HistoricDetailQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricDetailQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricFormProperty.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricDetailQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricDetailQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.history</div>
<h2 title="Interface HistoricDetailQuery" class="title">Interface HistoricDetailQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history">HistoricDetail</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">HistoricDetailQuery</span>
extends <a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history">HistoricDetail</a>&gt;</pre>
<div class="block">Programmatic querying for <a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><code>HistoricDetail</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#activityInstanceId-java.lang.String-">activityInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityInstanceId)</code>
<div class="block">Only select historic variable updates associated to the given <a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>activity instance</code></a>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#excludeTaskDetails--">excludeTaskDetails</a></span>()</code>
<div class="block">Exclude all task-related <a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><code>HistoricDetail</code></a>s, so only items which have no 
 task-id set will be selected.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#executionId-java.lang.String-">executionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Only select historic variable updates with the given execution.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#formProperties--">formProperties</a></span>()</code>
<div class="block">Only select <a href="../../../../org/activiti/engine/history/HistoricFormProperty.html" title="interface in org.activiti.engine.history"><code>HistoricFormProperty</code></a>s.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#id-java.lang.String-">id</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</code>
<div class="block">Only select historic info with the given id.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByFormPropertyId--">orderByFormPropertyId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByTime--">orderByTime</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByVariableName--">orderByVariableName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByVariableRevision--">orderByVariableRevision</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#orderByVariableType--">orderByVariableType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Only select historic variable updates with the given process instance.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#taskId-java.lang.String-">taskId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</code>
<div class="block">Only select historic variable updates associated to the given <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>historic task instance</code></a>.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#variableUpdates--">variableUpdates</a></span>()</code>
<div class="block">Only select <a href="../../../../org/activiti/engine/history/HistoricVariableUpdate.html" title="interface in org.activiti.engine.history"><code>HistoricVariableUpdate</code></a>s.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="id-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>id</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;id(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</pre>
<div class="block">Only select historic info with the given id.</div>
</li>
</ul>
<a name="processInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;processInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</pre>
<div class="block">Only select historic variable updates with the given process instance.
 ProcessInstance) ids and {@link HistoricProcessInstance} ids match.</div>
</li>
</ul>
<a name="executionId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executionId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;executionId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</pre>
<div class="block">Only select historic variable updates with the given execution.
 Note that <a href="../../../../org/activiti/engine/runtime/Execution.html" title="interface in org.activiti.engine.runtime"><code>Execution</code></a> ids are not stored in the history as first class citizen, 
 only process instances are.</div>
</li>
</ul>
<a name="activityInstanceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;activityInstanceId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityInstanceId)</pre>
<div class="block">Only select historic variable updates associated to the given <a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>activity instance</code></a>.</div>
</li>
</ul>
<a name="taskId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;taskId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskId)</pre>
<div class="block">Only select historic variable updates associated to the given <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>historic task instance</code></a>.</div>
</li>
</ul>
<a name="formProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formProperties</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;formProperties()</pre>
<div class="block">Only select <a href="../../../../org/activiti/engine/history/HistoricFormProperty.html" title="interface in org.activiti.engine.history"><code>HistoricFormProperty</code></a>s.</div>
</li>
</ul>
<a name="variableUpdates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>variableUpdates</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;variableUpdates()</pre>
<div class="block">Only select <a href="../../../../org/activiti/engine/history/HistoricVariableUpdate.html" title="interface in org.activiti.engine.history"><code>HistoricVariableUpdate</code></a>s.</div>
</li>
</ul>
<a name="excludeTaskDetails--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeTaskDetails</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;excludeTaskDetails()</pre>
<div class="block">Exclude all task-related <a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><code>HistoricDetail</code></a>s, so only items which have no 
 task-id set will be selected. When used togheter with <a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html#taskId-java.lang.String-"><code>taskId(String)</code></a>, this
 call is ignored task details are NOT excluded.</div>
</li>
</ul>
<a name="orderByProcessInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByProcessInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;orderByProcessInstanceId()</pre>
</li>
</ul>
<a name="orderByVariableName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByVariableName</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;orderByVariableName()</pre>
</li>
</ul>
<a name="orderByFormPropertyId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByFormPropertyId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;orderByFormPropertyId()</pre>
</li>
</ul>
<a name="orderByVariableType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByVariableType</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;orderByVariableType()</pre>
</li>
</ul>
<a name="orderByVariableRevision--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByVariableRevision</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;orderByVariableRevision()</pre>
</li>
</ul>
<a name="orderByTime--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByTime</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a>&nbsp;orderByTime()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricDetailQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricFormProperty.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricDetailQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricDetailQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
