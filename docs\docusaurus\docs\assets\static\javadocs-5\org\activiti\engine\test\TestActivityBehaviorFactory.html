<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TestActivityBehaviorFactory (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TestActivityBehaviorFactory (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TestActivityBehaviorFactory.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/test/Deployment.html" title="annotation in org.activiti.engine.test"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/test/TestActivityBehaviorFactory.html" target="_top">Frames</a></li>
<li><a href="TestActivityBehaviorFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.test</div>
<h2 title="Class TestActivityBehaviorFactory" class="title">Class TestActivityBehaviorFactory</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.impl.bpmn.parser.factory.AbstractBehaviorFactory</li>
<li>
<ul class="inheritance">
<li>org.activiti.engine.test.TestActivityBehaviorFactory</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">TestActivityBehaviorFactory</span>
extends org.activiti.engine.impl.bpmn.parser.factory.AbstractBehaviorFactory
implements org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</pre>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#allServiceTasksNoOp">allServiceTasksNoOp</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#mockedClassDelegatesMapping">mockedClassDelegatesMapping</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#noOpServiceTaskClassNames">noOpServiceTaskClassNames</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#noOpServiceTaskIds">noOpServiceTaskIds</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#wrappedActivityBehaviorFactory">wrappedActivityBehaviorFactory</a></span></code>
<div class="block">The ActivityBehaviorFactory that is constructed when the process engine was created
 This class delegates to this instance, unless some mocking has been defined.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.activiti.engine.impl.bpmn.parser.factory.AbstractBehaviorFactory">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.activiti.engine.impl.bpmn.parser.factory.AbstractBehaviorFactory</h3>
<code>expressionManager</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#TestActivityBehaviorFactory--">TestActivityBehaviorFactory</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#TestActivityBehaviorFactory-org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory-">TestActivityBehaviorFactory</a></span>(org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory&nbsp;wrappedActivityBehaviorFactory)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#addClassDelegateMock-java.lang.String-java.lang.Class-">addClassDelegateMock</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;originalClassFqn,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;&nbsp;mockClass)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#addClassDelegateMock-java.lang.String-java.lang.String-">addClassDelegateMock</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;originalClassFqn,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mockedClassFqn)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#addNoOpServiceTaskByClassName-java.lang.String-">addNoOpServiceTaskByClassName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;className)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#addNoOpServiceTaskById-java.lang.String-">addNoOpServiceTaskById</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.BoundaryEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createBoundaryEventActivityBehavior-org.activiti.bpmn.model.BoundaryEvent-boolean-org.activiti.engine.impl.pvm.process.ActivityImpl-">createBoundaryEventActivityBehavior</a></span>(org.activiti.bpmn.model.BoundaryEvent&nbsp;boundaryEvent,
                                   boolean&nbsp;interrupting,
                                   org.activiti.engine.impl.pvm.process.ActivityImpl&nbsp;activity)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.pvm.delegate.ActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createBusinessRuleTaskActivityBehavior-org.activiti.bpmn.model.BusinessRuleTask-">createBusinessRuleTaskActivityBehavior</a></span>(org.activiti.bpmn.model.BusinessRuleTask&nbsp;businessRuleTask)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.CallActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createCallActivityBehavior-org.activiti.bpmn.model.CallActivity-">createCallActivityBehavior</a></span>(org.activiti.bpmn.model.CallActivity&nbsp;callActivity)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.pvm.delegate.ActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createCamelActivityBehavior-org.activiti.bpmn.model.SendTask-org.activiti.bpmn.model.BpmnModel-">createCamelActivityBehavior</a></span>(org.activiti.bpmn.model.SendTask&nbsp;sendTask,
                           org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.pvm.delegate.ActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createCamelActivityBehavior-org.activiti.bpmn.model.ServiceTask-org.activiti.bpmn.model.BpmnModel-">createCamelActivityBehavior</a></span>(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask,
                           org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.CancelBoundaryEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createCancelBoundaryEventActivityBehavior-org.activiti.bpmn.model.CancelEventDefinition-">createCancelBoundaryEventActivityBehavior</a></span>(org.activiti.bpmn.model.CancelEventDefinition&nbsp;cancelEventDefinition)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.CancelEndEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createCancelEndEventActivityBehavior-org.activiti.bpmn.model.EndEvent-">createCancelEndEventActivityBehavior</a></span>(org.activiti.bpmn.model.EndEvent&nbsp;endEvent)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.helper.ClassDelegate</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createClassDelegateServiceTask-org.activiti.bpmn.model.ServiceTask-">createClassDelegateServiceTask</a></span>(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ErrorEndEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createErrorEndEventActivityBehavior-org.activiti.bpmn.model.EndEvent-org.activiti.bpmn.model.ErrorEventDefinition-">createErrorEndEventActivityBehavior</a></span>(org.activiti.bpmn.model.EndEvent&nbsp;endEvent,
                                   org.activiti.bpmn.model.ErrorEventDefinition&nbsp;errorEventDefinition)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.EventBasedGatewayActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createEventBasedGatewayActivityBehavior-org.activiti.bpmn.model.EventGateway-">createEventBasedGatewayActivityBehavior</a></span>(org.activiti.bpmn.model.EventGateway&nbsp;eventGateway)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.EventSubProcessStartEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createEventSubProcessStartEventActivityBehavior-org.activiti.bpmn.model.StartEvent-java.lang.String-">createEventSubProcessStartEventActivityBehavior</a></span>(org.activiti.bpmn.model.StartEvent&nbsp;startEvent,
                                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ExclusiveGatewayActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createExclusiveGatewayActivityBehavior-org.activiti.bpmn.model.ExclusiveGateway-">createExclusiveGatewayActivityBehavior</a></span>(org.activiti.bpmn.model.ExclusiveGateway&nbsp;exclusiveGateway)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.InclusiveGatewayActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createInclusiveGatewayActivityBehavior-org.activiti.bpmn.model.InclusiveGateway-">createInclusiveGatewayActivityBehavior</a></span>(org.activiti.bpmn.model.InclusiveGateway&nbsp;inclusiveGateway)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.IntermediateCatchEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createIntermediateCatchEventActivityBehavior-org.activiti.bpmn.model.IntermediateCatchEvent-">createIntermediateCatchEventActivityBehavior</a></span>(org.activiti.bpmn.model.IntermediateCatchEvent&nbsp;intermediateCatchEvent)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.IntermediateThrowCompensationEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createIntermediateThrowCompensationEventActivityBehavior-org.activiti.bpmn.model.ThrowEvent-org.activiti.engine.impl.bpmn.parser.CompensateEventDefinition-">createIntermediateThrowCompensationEventActivityBehavior</a></span>(org.activiti.bpmn.model.ThrowEvent&nbsp;throwEvent,
                                                        org.activiti.engine.impl.bpmn.parser.CompensateEventDefinition&nbsp;compensateEventDefinition)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.IntermediateThrowNoneEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createIntermediateThrowNoneEventActivityBehavior-org.activiti.bpmn.model.ThrowEvent-">createIntermediateThrowNoneEventActivityBehavior</a></span>(org.activiti.bpmn.model.ThrowEvent&nbsp;throwEvent)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.IntermediateThrowSignalEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createIntermediateThrowSignalEventActivityBehavior-org.activiti.bpmn.model.ThrowEvent-org.activiti.bpmn.model.Signal-org.activiti.engine.impl.bpmn.parser.EventSubscriptionDeclaration-">createIntermediateThrowSignalEventActivityBehavior</a></span>(org.activiti.bpmn.model.ThrowEvent&nbsp;throwEvent,
                                                  org.activiti.bpmn.model.Signal&nbsp;signal,
                                                  org.activiti.engine.impl.bpmn.parser.EventSubscriptionDeclaration&nbsp;eventSubscriptionDeclaration)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.MailActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createMailActivityBehavior-org.activiti.bpmn.model.SendTask-">createMailActivityBehavior</a></span>(org.activiti.bpmn.model.SendTask&nbsp;sendTask)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.MailActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createMailActivityBehavior-org.activiti.bpmn.model.ServiceTask-">createMailActivityBehavior</a></span>(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ManualTaskActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createManualTaskActivityBehavior-org.activiti.bpmn.model.ManualTask-">createManualTaskActivityBehavior</a></span>(org.activiti.bpmn.model.ManualTask&nbsp;manualTask)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.pvm.delegate.ActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createMuleActivityBehavior-org.activiti.bpmn.model.SendTask-org.activiti.bpmn.model.BpmnModel-">createMuleActivityBehavior</a></span>(org.activiti.bpmn.model.SendTask&nbsp;sendTask,
                          org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.pvm.delegate.ActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createMuleActivityBehavior-org.activiti.bpmn.model.ServiceTask-org.activiti.bpmn.model.BpmnModel-">createMuleActivityBehavior</a></span>(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask,
                          org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.NoneEndEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createNoneEndEventActivityBehavior-org.activiti.bpmn.model.EndEvent-">createNoneEndEventActivityBehavior</a></span>(org.activiti.bpmn.model.EndEvent&nbsp;endEvent)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.NoneStartEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createNoneStartEventActivityBehavior-org.activiti.bpmn.model.StartEvent-">createNoneStartEventActivityBehavior</a></span>(org.activiti.bpmn.model.StartEvent&nbsp;startEvent)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ParallelGatewayActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createParallelGatewayActivityBehavior-org.activiti.bpmn.model.ParallelGateway-">createParallelGatewayActivityBehavior</a></span>(org.activiti.bpmn.model.ParallelGateway&nbsp;parallelGateway)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ParallelMultiInstanceBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createParallelMultiInstanceBehavior-org.activiti.engine.impl.pvm.process.ActivityImpl-org.activiti.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior-">createParallelMultiInstanceBehavior</a></span>(org.activiti.engine.impl.pvm.process.ActivityImpl&nbsp;activity,
                                   org.activiti.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior&nbsp;innerActivityBehavior)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ReceiveTaskActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createReceiveTaskActivityBehavior-org.activiti.bpmn.model.ReceiveTask-">createReceiveTaskActivityBehavior</a></span>(org.activiti.bpmn.model.ReceiveTask&nbsp;receiveTask)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ScriptTaskActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createScriptTaskActivityBehavior-org.activiti.bpmn.model.ScriptTask-">createScriptTaskActivityBehavior</a></span>(org.activiti.bpmn.model.ScriptTask&nbsp;scriptTask)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.SequentialMultiInstanceBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createSequentialMultiInstanceBehavior-org.activiti.engine.impl.pvm.process.ActivityImpl-org.activiti.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior-">createSequentialMultiInstanceBehavior</a></span>(org.activiti.engine.impl.pvm.process.ActivityImpl&nbsp;activity,
                                     org.activiti.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior&nbsp;innerActivityBehavior)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ServiceTaskDelegateExpressionActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createServiceTaskDelegateExpressionActivityBehavior-org.activiti.bpmn.model.ServiceTask-">createServiceTaskDelegateExpressionActivityBehavior</a></span>(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ServiceTaskExpressionActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createServiceTaskExpressionActivityBehavior-org.activiti.bpmn.model.ServiceTask-">createServiceTaskExpressionActivityBehavior</a></span>(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.ShellActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createShellActivityBehavior-org.activiti.bpmn.model.ServiceTask-">createShellActivityBehavior</a></span>(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.SubProcessActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createSubprocActivityBehavior-org.activiti.bpmn.model.SubProcess-">createSubprocActivityBehavior</a></span>(org.activiti.bpmn.model.SubProcess&nbsp;subProcess)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.TaskActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createTaskActivityBehavior-org.activiti.bpmn.model.Task-">createTaskActivityBehavior</a></span>(org.activiti.bpmn.model.Task&nbsp;task)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.TerminateEndEventActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createTerminateEndEventActivityBehavior-org.activiti.bpmn.model.EndEvent-">createTerminateEndEventActivityBehavior</a></span>(org.activiti.bpmn.model.EndEvent&nbsp;endEvent)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.TransactionActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createTransactionActivityBehavior-org.activiti.bpmn.model.Transaction-">createTransactionActivityBehavior</a></span>(org.activiti.bpmn.model.Transaction&nbsp;transaction)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.UserTaskActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createUserTaskActivityBehavior-org.activiti.bpmn.model.UserTask-org.activiti.engine.impl.task.TaskDefinition-">createUserTaskActivityBehavior</a></span>(org.activiti.bpmn.model.UserTask&nbsp;userTask,
                              org.activiti.engine.impl.task.TaskDefinition&nbsp;taskDefinition)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.WebServiceActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createWebServiceActivityBehavior-org.activiti.bpmn.model.SendTask-">createWebServiceActivityBehavior</a></span>(org.activiti.bpmn.model.SendTask&nbsp;sendTask)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.behavior.WebServiceActivityBehavior</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#createWebServiceActivityBehavior-org.activiti.bpmn.model.ServiceTask-">createWebServiceActivityBehavior</a></span>(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#getWrappedActivityBehaviorFactory--">getWrappedActivityBehaviorFactory</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#reset--">reset</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#setAllServiceTasksNoOp--">setAllServiceTasksNoOp</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/test/TestActivityBehaviorFactory.html#setWrappedActivityBehaviorFactory-org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory-">setWrappedActivityBehaviorFactory</a></span>(org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory&nbsp;wrappedActivityBehaviorFactory)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.impl.bpmn.parser.factory.AbstractBehaviorFactory">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.activiti.engine.impl.bpmn.parser.factory.AbstractBehaviorFactory</h3>
<code>createFieldDeclarations, getExpressionManager, setExpressionManager</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="wrappedActivityBehaviorFactory">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wrappedActivityBehaviorFactory</h4>
<pre>protected&nbsp;org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory wrappedActivityBehaviorFactory</pre>
<div class="block">The ActivityBehaviorFactory that is constructed when the process engine was created
 This class delegates to this instance, unless some mocking has been defined.</div>
</li>
</ul>
<a name="allServiceTasksNoOp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allServiceTasksNoOp</h4>
<pre>protected&nbsp;boolean allServiceTasksNoOp</pre>
</li>
</ul>
<a name="mockedClassDelegatesMapping">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mockedClassDelegatesMapping</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt; mockedClassDelegatesMapping</pre>
</li>
</ul>
<a name="noOpServiceTaskIds">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>noOpServiceTaskIds</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt; noOpServiceTaskIds</pre>
</li>
</ul>
<a name="noOpServiceTaskClassNames">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>noOpServiceTaskClassNames</h4>
<pre>protected&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt; noOpServiceTaskClassNames</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TestActivityBehaviorFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TestActivityBehaviorFactory</h4>
<pre>public&nbsp;TestActivityBehaviorFactory()</pre>
</li>
</ul>
<a name="TestActivityBehaviorFactory-org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TestActivityBehaviorFactory</h4>
<pre>public&nbsp;TestActivityBehaviorFactory(org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory&nbsp;wrappedActivityBehaviorFactory)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getWrappedActivityBehaviorFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWrappedActivityBehaviorFactory</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory&nbsp;getWrappedActivityBehaviorFactory()</pre>
</li>
</ul>
<a name="setWrappedActivityBehaviorFactory-org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWrappedActivityBehaviorFactory</h4>
<pre>public&nbsp;void&nbsp;setWrappedActivityBehaviorFactory(org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory&nbsp;wrappedActivityBehaviorFactory)</pre>
</li>
</ul>
<a name="createNoneStartEventActivityBehavior-org.activiti.bpmn.model.StartEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNoneStartEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.NoneStartEventActivityBehavior&nbsp;createNoneStartEventActivityBehavior(org.activiti.bpmn.model.StartEvent&nbsp;startEvent)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createNoneStartEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createEventSubProcessStartEventActivityBehavior-org.activiti.bpmn.model.StartEvent-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEventSubProcessStartEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.EventSubProcessStartEventActivityBehavior&nbsp;createEventSubProcessStartEventActivityBehavior(org.activiti.bpmn.model.StartEvent&nbsp;startEvent,
                                                                                                                                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;activityId)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createEventSubProcessStartEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createTaskActivityBehavior-org.activiti.bpmn.model.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTaskActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.TaskActivityBehavior&nbsp;createTaskActivityBehavior(org.activiti.bpmn.model.Task&nbsp;task)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createTaskActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createManualTaskActivityBehavior-org.activiti.bpmn.model.ManualTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createManualTaskActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ManualTaskActivityBehavior&nbsp;createManualTaskActivityBehavior(org.activiti.bpmn.model.ManualTask&nbsp;manualTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createManualTaskActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createReceiveTaskActivityBehavior-org.activiti.bpmn.model.ReceiveTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createReceiveTaskActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ReceiveTaskActivityBehavior&nbsp;createReceiveTaskActivityBehavior(org.activiti.bpmn.model.ReceiveTask&nbsp;receiveTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createReceiveTaskActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createUserTaskActivityBehavior-org.activiti.bpmn.model.UserTask-org.activiti.engine.impl.task.TaskDefinition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserTaskActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.UserTaskActivityBehavior&nbsp;createUserTaskActivityBehavior(org.activiti.bpmn.model.UserTask&nbsp;userTask,
                                                                                                      org.activiti.engine.impl.task.TaskDefinition&nbsp;taskDefinition)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createUserTaskActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createClassDelegateServiceTask-org.activiti.bpmn.model.ServiceTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createClassDelegateServiceTask</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.helper.ClassDelegate&nbsp;createClassDelegateServiceTask(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createClassDelegateServiceTask</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createServiceTaskDelegateExpressionActivityBehavior-org.activiti.bpmn.model.ServiceTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createServiceTaskDelegateExpressionActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ServiceTaskDelegateExpressionActivityBehavior&nbsp;createServiceTaskDelegateExpressionActivityBehavior(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createServiceTaskDelegateExpressionActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createServiceTaskExpressionActivityBehavior-org.activiti.bpmn.model.ServiceTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createServiceTaskExpressionActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ServiceTaskExpressionActivityBehavior&nbsp;createServiceTaskExpressionActivityBehavior(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createServiceTaskExpressionActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createWebServiceActivityBehavior-org.activiti.bpmn.model.ServiceTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWebServiceActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.WebServiceActivityBehavior&nbsp;createWebServiceActivityBehavior(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createWebServiceActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createWebServiceActivityBehavior-org.activiti.bpmn.model.SendTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWebServiceActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.WebServiceActivityBehavior&nbsp;createWebServiceActivityBehavior(org.activiti.bpmn.model.SendTask&nbsp;sendTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createWebServiceActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createMailActivityBehavior-org.activiti.bpmn.model.ServiceTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMailActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.MailActivityBehavior&nbsp;createMailActivityBehavior(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createMailActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createMailActivityBehavior-org.activiti.bpmn.model.SendTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMailActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.MailActivityBehavior&nbsp;createMailActivityBehavior(org.activiti.bpmn.model.SendTask&nbsp;sendTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createMailActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createMuleActivityBehavior-org.activiti.bpmn.model.ServiceTask-org.activiti.bpmn.model.BpmnModel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMuleActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.pvm.delegate.ActivityBehavior&nbsp;createMuleActivityBehavior(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask,
                                                                                         org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createMuleActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createMuleActivityBehavior-org.activiti.bpmn.model.SendTask-org.activiti.bpmn.model.BpmnModel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMuleActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.pvm.delegate.ActivityBehavior&nbsp;createMuleActivityBehavior(org.activiti.bpmn.model.SendTask&nbsp;sendTask,
                                                                                         org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createMuleActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createCamelActivityBehavior-org.activiti.bpmn.model.ServiceTask-org.activiti.bpmn.model.BpmnModel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCamelActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.pvm.delegate.ActivityBehavior&nbsp;createCamelActivityBehavior(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask,
                                                                                          org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createCamelActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createCamelActivityBehavior-org.activiti.bpmn.model.SendTask-org.activiti.bpmn.model.BpmnModel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCamelActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.pvm.delegate.ActivityBehavior&nbsp;createCamelActivityBehavior(org.activiti.bpmn.model.SendTask&nbsp;sendTask,
                                                                                          org.activiti.bpmn.model.BpmnModel&nbsp;bpmnModel)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createCamelActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createShellActivityBehavior-org.activiti.bpmn.model.ServiceTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createShellActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ShellActivityBehavior&nbsp;createShellActivityBehavior(org.activiti.bpmn.model.ServiceTask&nbsp;serviceTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createShellActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createBusinessRuleTaskActivityBehavior-org.activiti.bpmn.model.BusinessRuleTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBusinessRuleTaskActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.pvm.delegate.ActivityBehavior&nbsp;createBusinessRuleTaskActivityBehavior(org.activiti.bpmn.model.BusinessRuleTask&nbsp;businessRuleTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createBusinessRuleTaskActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createScriptTaskActivityBehavior-org.activiti.bpmn.model.ScriptTask-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createScriptTaskActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ScriptTaskActivityBehavior&nbsp;createScriptTaskActivityBehavior(org.activiti.bpmn.model.ScriptTask&nbsp;scriptTask)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createScriptTaskActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createExclusiveGatewayActivityBehavior-org.activiti.bpmn.model.ExclusiveGateway-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createExclusiveGatewayActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ExclusiveGatewayActivityBehavior&nbsp;createExclusiveGatewayActivityBehavior(org.activiti.bpmn.model.ExclusiveGateway&nbsp;exclusiveGateway)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createExclusiveGatewayActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createParallelGatewayActivityBehavior-org.activiti.bpmn.model.ParallelGateway-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createParallelGatewayActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ParallelGatewayActivityBehavior&nbsp;createParallelGatewayActivityBehavior(org.activiti.bpmn.model.ParallelGateway&nbsp;parallelGateway)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createParallelGatewayActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createInclusiveGatewayActivityBehavior-org.activiti.bpmn.model.InclusiveGateway-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createInclusiveGatewayActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.InclusiveGatewayActivityBehavior&nbsp;createInclusiveGatewayActivityBehavior(org.activiti.bpmn.model.InclusiveGateway&nbsp;inclusiveGateway)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createInclusiveGatewayActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createEventBasedGatewayActivityBehavior-org.activiti.bpmn.model.EventGateway-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEventBasedGatewayActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.EventBasedGatewayActivityBehavior&nbsp;createEventBasedGatewayActivityBehavior(org.activiti.bpmn.model.EventGateway&nbsp;eventGateway)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createEventBasedGatewayActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createSequentialMultiInstanceBehavior-org.activiti.engine.impl.pvm.process.ActivityImpl-org.activiti.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createSequentialMultiInstanceBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.SequentialMultiInstanceBehavior&nbsp;createSequentialMultiInstanceBehavior(org.activiti.engine.impl.pvm.process.ActivityImpl&nbsp;activity,
                                                                                                                    org.activiti.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior&nbsp;innerActivityBehavior)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createSequentialMultiInstanceBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createParallelMultiInstanceBehavior-org.activiti.engine.impl.pvm.process.ActivityImpl-org.activiti.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createParallelMultiInstanceBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ParallelMultiInstanceBehavior&nbsp;createParallelMultiInstanceBehavior(org.activiti.engine.impl.pvm.process.ActivityImpl&nbsp;activity,
                                                                                                                org.activiti.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior&nbsp;innerActivityBehavior)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createParallelMultiInstanceBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createSubprocActivityBehavior-org.activiti.bpmn.model.SubProcess-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createSubprocActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.SubProcessActivityBehavior&nbsp;createSubprocActivityBehavior(org.activiti.bpmn.model.SubProcess&nbsp;subProcess)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createSubprocActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createCallActivityBehavior-org.activiti.bpmn.model.CallActivity-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCallActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.CallActivityBehavior&nbsp;createCallActivityBehavior(org.activiti.bpmn.model.CallActivity&nbsp;callActivity)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createCallActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createTransactionActivityBehavior-org.activiti.bpmn.model.Transaction-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTransactionActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.TransactionActivityBehavior&nbsp;createTransactionActivityBehavior(org.activiti.bpmn.model.Transaction&nbsp;transaction)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createTransactionActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createIntermediateCatchEventActivityBehavior-org.activiti.bpmn.model.IntermediateCatchEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createIntermediateCatchEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.IntermediateCatchEventActivityBehavior&nbsp;createIntermediateCatchEventActivityBehavior(org.activiti.bpmn.model.IntermediateCatchEvent&nbsp;intermediateCatchEvent)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createIntermediateCatchEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createIntermediateThrowNoneEventActivityBehavior-org.activiti.bpmn.model.ThrowEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createIntermediateThrowNoneEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.IntermediateThrowNoneEventActivityBehavior&nbsp;createIntermediateThrowNoneEventActivityBehavior(org.activiti.bpmn.model.ThrowEvent&nbsp;throwEvent)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createIntermediateThrowNoneEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createIntermediateThrowSignalEventActivityBehavior-org.activiti.bpmn.model.ThrowEvent-org.activiti.bpmn.model.Signal-org.activiti.engine.impl.bpmn.parser.EventSubscriptionDeclaration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createIntermediateThrowSignalEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.IntermediateThrowSignalEventActivityBehavior&nbsp;createIntermediateThrowSignalEventActivityBehavior(org.activiti.bpmn.model.ThrowEvent&nbsp;throwEvent,
                                                                                                                                              org.activiti.bpmn.model.Signal&nbsp;signal,
                                                                                                                                              org.activiti.engine.impl.bpmn.parser.EventSubscriptionDeclaration&nbsp;eventSubscriptionDeclaration)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createIntermediateThrowSignalEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createIntermediateThrowCompensationEventActivityBehavior-org.activiti.bpmn.model.ThrowEvent-org.activiti.engine.impl.bpmn.parser.CompensateEventDefinition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createIntermediateThrowCompensationEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.IntermediateThrowCompensationEventActivityBehavior&nbsp;createIntermediateThrowCompensationEventActivityBehavior(org.activiti.bpmn.model.ThrowEvent&nbsp;throwEvent,
                                                                                                                                                          org.activiti.engine.impl.bpmn.parser.CompensateEventDefinition&nbsp;compensateEventDefinition)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createIntermediateThrowCompensationEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createNoneEndEventActivityBehavior-org.activiti.bpmn.model.EndEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNoneEndEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.NoneEndEventActivityBehavior&nbsp;createNoneEndEventActivityBehavior(org.activiti.bpmn.model.EndEvent&nbsp;endEvent)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createNoneEndEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createErrorEndEventActivityBehavior-org.activiti.bpmn.model.EndEvent-org.activiti.bpmn.model.ErrorEventDefinition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createErrorEndEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.ErrorEndEventActivityBehavior&nbsp;createErrorEndEventActivityBehavior(org.activiti.bpmn.model.EndEvent&nbsp;endEvent,
                                                                                                                org.activiti.bpmn.model.ErrorEventDefinition&nbsp;errorEventDefinition)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createErrorEndEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createCancelEndEventActivityBehavior-org.activiti.bpmn.model.EndEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCancelEndEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.CancelEndEventActivityBehavior&nbsp;createCancelEndEventActivityBehavior(org.activiti.bpmn.model.EndEvent&nbsp;endEvent)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createCancelEndEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createTerminateEndEventActivityBehavior-org.activiti.bpmn.model.EndEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTerminateEndEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.TerminateEndEventActivityBehavior&nbsp;createTerminateEndEventActivityBehavior(org.activiti.bpmn.model.EndEvent&nbsp;endEvent)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createTerminateEndEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createBoundaryEventActivityBehavior-org.activiti.bpmn.model.BoundaryEvent-boolean-org.activiti.engine.impl.pvm.process.ActivityImpl-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBoundaryEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.BoundaryEventActivityBehavior&nbsp;createBoundaryEventActivityBehavior(org.activiti.bpmn.model.BoundaryEvent&nbsp;boundaryEvent,
                                                                                                                boolean&nbsp;interrupting,
                                                                                                                org.activiti.engine.impl.pvm.process.ActivityImpl&nbsp;activity)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createBoundaryEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="createCancelBoundaryEventActivityBehavior-org.activiti.bpmn.model.CancelEventDefinition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCancelBoundaryEventActivityBehavior</h4>
<pre>public&nbsp;org.activiti.engine.impl.bpmn.behavior.CancelBoundaryEventActivityBehavior&nbsp;createCancelBoundaryEventActivityBehavior(org.activiti.bpmn.model.CancelEventDefinition&nbsp;cancelEventDefinition)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createCancelBoundaryEventActivityBehavior</code>&nbsp;in interface&nbsp;<code>org.activiti.engine.impl.bpmn.parser.factory.ActivityBehaviorFactory</code></dd>
</dl>
</li>
</ul>
<a name="addClassDelegateMock-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClassDelegateMock</h4>
<pre>public&nbsp;void&nbsp;addClassDelegateMock(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;originalClassFqn,
                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;?&gt;&nbsp;mockClass)</pre>
</li>
</ul>
<a name="addClassDelegateMock-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClassDelegateMock</h4>
<pre>public&nbsp;void&nbsp;addClassDelegateMock(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;originalClassFqn,
                                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mockedClassFqn)</pre>
</li>
</ul>
<a name="addNoOpServiceTaskById-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addNoOpServiceTaskById</h4>
<pre>public&nbsp;void&nbsp;addNoOpServiceTaskById(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</pre>
</li>
</ul>
<a name="addNoOpServiceTaskByClassName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addNoOpServiceTaskByClassName</h4>
<pre>public&nbsp;void&nbsp;addNoOpServiceTaskByClassName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;className)</pre>
</li>
</ul>
<a name="setAllServiceTasksNoOp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllServiceTasksNoOp</h4>
<pre>public&nbsp;void&nbsp;setAllServiceTasksNoOp()</pre>
</li>
</ul>
<a name="reset--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>reset</h4>
<pre>public&nbsp;void&nbsp;reset()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TestActivityBehaviorFactory.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/test/Deployment.html" title="annotation in org.activiti.engine.test"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/test/TestActivityBehaviorFactory.html" target="_top">Frames</a></li>
<li><a href="TestActivityBehaviorFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
