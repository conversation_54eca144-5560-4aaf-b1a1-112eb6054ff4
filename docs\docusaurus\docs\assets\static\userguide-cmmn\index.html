<html>
<head>
<title>Flowable DOCS</title>
<script type="text/javascript">
// Closure-wrapped for security.
(function () {
    var anchorMap = {
"_architecture": "../cmmn/ch07-architecture#architecture",
"_automatic_history_cleaning_configuration": "../cmmn/ch03-API#automatic-history-cleaning-configuration",
"_automatic_removal_of_event_listeners": "../cmmn/ch06-cmmn#automatic-removal-of-event-listeners",
"_automatic_resource_deployment": "../cmmn/ch04-Spring#automatic-resource-deployment",
"_available_condition": "../cmmn/ch06-cmmn#available-condition",
"_basic_concepts_and_terminology": "../cmmn/ch06-cmmn#basic-concepts-and-terminology",
"_case_task": "../cmmn/ch06-cmmn#case-task",
"_cmmn_11_constructs": "../cmmn/ch06-cmmn#cmmn-11-constructs",
"_cmmn_11": "../cmmn/ch06-cmmn#cmmn-11",
"_cmmnenginefactorybean": "../cmmn/ch04-Spring#cmmnenginefactorybean",
"_concepts": "../cmmn/ch06-cmmn#concepts",
"_configuration": "../cmmn/ch02-Configuration#configuration",
"_criteria": "../cmmn/ch06-cmmn#criteria",
"_custom_properties": "../cmmn/ch02-Configuration#custom-properties",
"_decision_task": "../cmmn/ch06-cmmn#decision-task",
"_default_behavior": "../cmmn/ch06-cmmn#default-behavior",
"_default_spring_configuration": "../cmmn/ch04-Spring#default-spring-configuration",
"_download": "../oss-introduction#download",
"_entry_criterion_entry_sentry": "../cmmn/ch06-cmmn#entry-criterion-entry-sentry",
"_event_listeners": "../cmmn/ch06-cmmn#event-listeners",
"_exception_strategy": "../cmmn/ch03-API#exception-strategy",
"_exit_criterion_exit_sentry": "../cmmn/ch06-cmmn#exit-criterion-exit-sentry",
"_exiteventtype": "../cmmn/ch06-cmmn#exiteventtype",
"_exittype": "../cmmn/ch06-cmmn#exittype",
"_experimental_features": "../oss-introduction#experimental-features",
"_external_resources": "../cmmn/ch05-Deployment#external-resources",
"_generic_event_listener": "../cmmn/ch06-cmmn#generic-event-listener",
"_history_cleaning": "../cmmn/ch03-API#history-cleaning",
"_http_task": "../cmmn/ch06-cmmn#http-task",
"_human_task": "../cmmn/ch06-cmmn#human-task",
"_ide": "../oss-introduction#ide",
"_internal_implementation_classes": "../oss-introduction#internal-implementation-classes",
"_introduction": "../oss-introduction",
"_item_control_completion_neutral_rule": "../cmmn/ch06-cmmn#item-control-completion-neutral-rule",
"_item_control_manual_activation_rule": "../cmmn/ch06-cmmn#item-control-manual-activation-rule",
"_item_control_parent_completion_rule": "../cmmn/ch06-cmmn#item-control-parent-completion-rule",
"_item_control_repetition_rule": "../cmmn/ch06-cmmn#item-control-repetition-rule",
"_item_control_required_rule": "../cmmn/ch06-cmmn#item-control-required-rule",
"_java_classes": "../cmmn/ch05-Deployment#java-classes",
"_java_service_task": "../cmmn/ch06-cmmn#java-service-task",
"_jdk_8": "../oss-introduction#jdk-8",
"_jndi_properties": "../cmmn/ch02-Configuration#jndi-properties",
"_license": "../oss-introduction#license",
"_manually_deleting_history": "../cmmn/ch03-API#manually-deleting-history",
"_milestone": "../cmmn/ch06-cmmn#milestone",
"_process_task": "../cmmn/ch06-cmmn#process-task",
"_programmatic_example": "../cmmn/ch06-cmmn#programmatic-example",
"_repetition_rule_max_instance_count_attribute": "../cmmn/ch06-cmmn#repetition-rule-max-instance-count-attribute",
"_repetition_rule_repetition_collection_variable": "../cmmn/ch06-cmmn#repetition-rule-repetition-collection-variable",
"_reporting_problems": "../oss-introduction#reporting-problems",
"_required_software": "../oss-introduction#required-software",
"_script_task": "../cmmn/ch06-cmmn#script-task",
"_sources": "../oss-introduction#sources",
"_stage": "../cmmn/ch06-cmmn#stage",
"_task": "../cmmn/ch06-cmmn#task",
"_timer_event_listener": "../cmmn/ch06-cmmn#timer-event-listener",
"_trigger_mode_onevent": "../cmmn/ch06-cmmn#trigger-mode-onevent",
"_user_event_listener": "../cmmn/ch06-cmmn#user-event-listener",
"_using_spring_beans_from_a_case_instance": "../cmmn/ch05-Deployment#using-spring-beans-from-a-case-instance",
"_versioning_strategy": "../oss-introduction#versioning-strategy",
"_what_is_cmmn": "../cmmn/ch06-cmmn#what-is-cmmn",
"_when_are_sentries_evaluated": "../cmmn/ch06-cmmn#when-are-sentries-evaluated",
"apiEngine": "../cmmn/ch03-API#the-process-cmmn-engine-api-and-services",
"apiExpressions": "../cmmn/ch03-API#expressions",
"apiTransientVariables": "../cmmn/ch03-API#transient-variables",
"apiUnitTesting": "../cmmn/ch03-API#unit-testing",
"apiVariables": "../cmmn/ch03-API#variables",
"architecture": "../dmn/ch07-architecture",
"caseDefinitionCacheConfiguration": "../cmmn/ch02-Configuration#deployment-cache-configuration",
"chapterApi": "../cmmn/ch03-API#the-flowable-cmmn-api",
"chDeployment": "../cmmn/ch05-Deployment#deployment",
"cmmn_sentry_evaluation": "../cmmn/ch06-cmmn#sentry-evaluation",
"cmmnExpressionsFunctions": "../cmmn/ch03-API#expression-functions",
"configuration": "../cmmn/ch02-Configuration#creating-a-cmmnengine",
"configurationRoot": "../cmmn/ch02-Configuration#cmmnengineconfiguration-bean",
"creatingDatabaseTable": "../cmmn/ch02-Configuration#creating-the-database-tables",
"database.tables.explained": "../cmmn/ch02-Configuration#database-table-names-explained",
"databaseConfiguration": "../cmmn/ch02-Configuration#database-configuration",
"databaseTypes": "../cmmn/ch02-Configuration#supported-databases",
"databaseUpgrade": "../cmmn/ch02-Configuration#database-upgrade",
"deploymentCategory": "../cmmn/ch05-Deployment#category",
"download": "../oss-introduction#download",
"experimental": "../oss-introduction#experimental-features",
"exposingConfigurationBeans": "../cmmn/ch02-Configuration#exposing-configuration-beans-in-expressions-and-scripts",
"historyConfiguration": "../cmmn/ch02-Configuration#history-configuration",
"internal": "../oss-introduction#internal-implementation-classes",
"jndi_configuration": "../cmmn/ch02-Configuration#configuration",
"jndiDatasourceConfig": "../cmmn/ch02-Configuration#jndi-datasource-configuration",
"license": "../oss-introduction#license",
"loggingConfiguration": "../cmmn/ch02-Configuration#logging",
"queryAPI": "../cmmn/ch03-API#query-api",
"reporting.problems": "../oss-introduction#reporting-problems",
"required.software": "../oss-introduction#required-software",
"sources": "../oss-introduction#sources",
"springExpressions": "../cmmn/ch04-Spring#expressions",
"springintegration": "../cmmn/ch04-Spring#spring-integration",
"springUnitTest": "../cmmn/ch04-Spring#unit-testing",
"supporteddatabases": "../cmmn/ch02-Configuration#supported-databases",
"versioningOfCaseDefinitions": "../cmmn/ch05-Deployment#versioning-of-case-definitions"
    }
    /*
    * Best practice for extracting hashes:
    * https://stackoverflow.com/a/10076097/151365
    */
    var hash = window.location.hash.substring(1);
    if (hash) {
        /*
        * Best practice for javascript redirects: 
        * https://stackoverflow.com/a/506004/151365
        */
        window.location.replace(anchorMap[hash]);
    }
    else {
        window.location.replace("../cmmn/ch02-Configuration/");
    }
})();
</script>
</head>
<body>
<h2>Redirecting...</h2>
</body>
</html>
