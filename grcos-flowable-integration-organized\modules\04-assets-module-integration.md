# Assets Module Flowable Integration

## Overview

The Assets Module integration with Flowable automates asset lifecycle management, configuration compliance validation, and security assessment workflows. This integration connects DATAGERRY CMDB with intelligent workflow automation to ensure continuous asset compliance and automated remediation processes.

## Integration Architecture

### Asset Workflow Patterns

#### 1. Asset Onboarding Workflow
Automated workflow for new asset registration and initial compliance assessment.

#### 2. Configuration Compliance Workflow
Continuous monitoring and validation of asset configurations against OSCAL controls.

#### 3. Asset Lifecycle Management Workflow
Automated processes for asset updates, decommissioning, and compliance maintenance.

#### 4. Vulnerability Assessment Workflow
Scheduled and event-driven vulnerability assessments with automated remediation.

## DATAGERRY CMDB Integration

### Service Task Implementation

#### CMDBAssetManagerTask.java
```java
@Component("cmdbAssetManagerTask")
public class CMDBAssetManagerTask extends AbstractGRCOSServiceTask {
    
    @Autowired
    private DatagerryService datagerryService;
    
    @Autowired
    private OSCALService oscalService;
    
    @Autowired
    private ComplianceAgent complianceAgent;
    
    @Override
    protected void validateExecution(DelegateExecution execution) throws ValidationException {
        String operation = (String) execution.getVariable("cmdbOperation");
        String assetId = (String) execution.getVariable("assetId");
        
        if (operation == null || operation.trim().isEmpty()) {
            throw new ValidationException("CMDB operation is required");
        }
        
        if (assetId == null || assetId.trim().isEmpty()) {
            throw new ValidationException("Asset ID is required");
        }
        
        // Validate operation type
        if (!isValidOperation(operation)) {
            throw new ValidationException("Invalid CMDB operation: " + operation);
        }
    }
    
    @Override
    protected TaskExecutionResult executeTask(DelegateExecution execution) 
            throws TaskExecutionException {
        
        String operation = (String) execution.getVariable("cmdbOperation");
        String assetId = (String) execution.getVariable("assetId");
        
        try {
            switch (operation) {
                case "register_asset":
                    return registerNewAsset(execution);
                case "validate_configuration":
                    return validateAssetConfiguration(execution, assetId);
                case "update_compliance_status":
                    return updateComplianceStatus(execution, assetId);
                case "assess_vulnerabilities":
                    return assessVulnerabilities(execution, assetId);
                case "generate_compliance_evidence":
                    return generateComplianceEvidence(execution, assetId);
                case "decommission_asset":
                    return decommissionAsset(execution, assetId);
                default:
                    throw new TaskExecutionException("Unsupported operation: " + operation);
            }
        } catch (Exception e) {
            throw new TaskExecutionException("CMDB operation failed", e);
        }
    }
    
    private TaskExecutionResult registerNewAsset(DelegateExecution execution) {
        Map<String, Object> assetData = (Map<String, Object>) execution.getVariable("assetData");
        
        // Create asset in DATAGERRY
        CMDBObject asset = datagerryService.createObject("asset", assetData);
        
        // Generate initial OSCAL component definition
        OSCALComponent component = oscalService.createComponentFromAsset(asset);
        
        // Perform initial compliance assessment
        ComplianceAssessmentResult assessment = complianceAgent.performInitialAssessment(
            asset, component);
        
        // Store results
        Map<String, Object> results = new HashMap<>();
        results.put("assetId", asset.getId());
        results.put("componentUuid", component.getUuid());
        results.put("initialAssessment", assessment.toMap());
        results.put("complianceStatus", assessment.getOverallStatus());
        
        return TaskExecutionResult.success(results);
    }
    
    private TaskExecutionResult validateAssetConfiguration(DelegateExecution execution, 
                                                          String assetId) {
        // Get asset from DATAGERRY
        CMDBObject asset = datagerryService.getObject(assetId);
        
        // Get associated OSCAL component
        OSCALComponent component = oscalService.getComponentByAssetId(assetId);
        
        // Get applicable controls
        List<OSCALControl> applicableControls = oscalService.getApplicableControls(component);
        
        // Validate configuration against each control
        List<ConfigurationValidationResult> validationResults = new ArrayList<>();
        
        for (OSCALControl control : applicableControls) {
            ConfigurationValidationResult result = validateAgainstControl(
                asset, component, control);
            validationResults.add(result);
        }
        
        // Aggregate results
        boolean overallValid = validationResults.stream()
            .allMatch(ConfigurationValidationResult::isValid);
        
        List<String> violations = validationResults.stream()
            .filter(r -> !r.isValid())
            .map(ConfigurationValidationResult::getViolationDescription)
            .collect(Collectors.toList());
        
        // Update asset compliance status
        datagerryService.updateAssetField(assetId, "compliance_status", 
            overallValid ? "compliant" : "non-compliant");
        datagerryService.updateAssetField(assetId, "last_validation", 
            System.currentTimeMillis());
        
        Map<String, Object> results = new HashMap<>();
        results.put("assetId", assetId);
        results.put("configurationValid", overallValid);
        results.put("validationResults", validationResults);
        results.put("violations", violations);
        results.put("validatedControls", applicableControls.size());
        
        return TaskExecutionResult.success(results);
    }
    
    private TaskExecutionResult assessVulnerabilities(DelegateExecution execution, 
                                                     String assetId) {
        // Get asset details
        CMDBObject asset = datagerryService.getObject(assetId);
        
        // Determine assessment tools based on asset type
        List<String> assessmentTools = determineAssessmentTools(asset);
        
        // Execute vulnerability assessment
        VulnerabilityAssessmentResult assessment = executeVulnerabilityAssessment(
            asset, assessmentTools);
        
        // Process findings
        List<VulnerabilityFinding> findings = assessment.getFindings();
        
        // Update asset with vulnerability status
        datagerryService.updateAssetField(assetId, "vulnerability_status", 
            assessment.getRiskLevel());
        datagerryService.updateAssetField(assetId, "last_vulnerability_scan", 
            System.currentTimeMillis());
        datagerryService.updateAssetField(assetId, "vulnerability_count", 
            findings.size());
        
        // Create OSCAL findings
        List<OSCALFinding> oscalFindings = findings.stream()
            .map(this::convertToOSCALFinding)
            .collect(Collectors.toList());
        
        // Store findings in OSCAL assessment results
        oscalService.addFindingsToAssessmentResults(assetId, oscalFindings);
        
        Map<String, Object> results = new HashMap<>();
        results.put("assetId", assetId);
        results.put("riskLevel", assessment.getRiskLevel());
        results.put("findingsCount", findings.size());
        results.put("criticalFindings", assessment.getCriticalCount());
        results.put("highFindings", assessment.getHighCount());
        results.put("mediumFindings", assessment.getMediumCount());
        results.put("lowFindings", assessment.getLowCount());
        
        return TaskExecutionResult.success(results);
    }
    
    private ConfigurationValidationResult validateAgainstControl(CMDBObject asset, 
                                                                OSCALComponent component, 
                                                                OSCALControl control) {
        // Get control implementation details
        ControlImplementation implementation = oscalService.getControlImplementation(
            component.getUuid(), control.getId());
        
        if (implementation == null) {
            return ConfigurationValidationResult.notImplemented(
                control.getId(), "Control not implemented for this component");
        }
        
        // Validate implementation parameters against asset configuration
        Map<String, Object> assetConfig = asset.getFields();
        List<ImplementationParameter> parameters = implementation.getParameters();
        
        List<String> violations = new ArrayList<>();
        
        for (ImplementationParameter param : parameters) {
            String paramName = param.getName();
            Object expectedValue = param.getValue();
            Object actualValue = assetConfig.get(paramName);
            
            if (!validateParameterValue(expectedValue, actualValue)) {
                violations.add(String.format(
                    "Parameter %s: expected %s, actual %s", 
                    paramName, expectedValue, actualValue));
            }
        }
        
        if (violations.isEmpty()) {
            return ConfigurationValidationResult.valid(control.getId());
        } else {
            return ConfigurationValidationResult.invalid(control.getId(), violations);
        }
    }
}
```

## Asset Lifecycle Workflows

### Asset Onboarding Workflow

#### asset-onboarding.bpmn20.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://grcos.com/workflows">
  
  <process id="asset-onboarding" name="Asset Onboarding Workflow" isExecutable="true">
    
    <!-- Start Event -->
    <startEvent id="start" name="Asset Registration Request">
      <extensionElements>
        <flowable:formProperty id="assetType" name="Asset Type" type="enum" required="true">
          <flowable:value id="server" name="Server"/>
          <flowable:value id="workstation" name="Workstation"/>
          <flowable:value id="network-device" name="Network Device"/>
          <flowable:value id="iot-device" name="IoT Device"/>
          <flowable:value id="application" name="Application"/>
        </flowable:formProperty>
        <flowable:formProperty id="assetName" name="Asset Name" type="string" required="true"/>
        <flowable:formProperty id="assetOwner" name="Asset Owner" type="string" required="true"/>
        <flowable:formProperty id="businessCriticality" name="Business Criticality" type="enum" required="true">
          <flowable:value id="critical" name="Critical"/>
          <flowable:value id="high" name="High"/>
          <flowable:value id="medium" name="Medium"/>
          <flowable:value id="low" name="Low"/>
        </flowable:formProperty>
      </extensionElements>
    </startEvent>
    
    <!-- Register Asset in CMDB -->
    <serviceTask id="register-asset" name="Register Asset in CMDB"
                 flowable:class="com.grcos.workflow.CMDBAssetManagerTask">
      <extensionElements>
        <flowable:field name="cmdbOperation">
          <flowable:string>register_asset</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Generate OSCAL Component -->
    <serviceTask id="generate-oscal-component" name="Generate OSCAL Component"
                 flowable:class="com.grcos.workflow.OSCALComponentGeneratorTask">
      <extensionElements>
        <flowable:field name="generateFromAsset">
          <flowable:string>true</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Initial Security Assessment -->
    <serviceTask id="initial-assessment" name="Perform Initial Security Assessment"
                 flowable:class="com.grcos.workflow.SecurityAssessmentTask">
      <extensionElements>
        <flowable:field name="assessmentType">
          <flowable:string>initial</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Risk Assessment Gateway -->
    <exclusiveGateway id="risk-assessment-gateway" name="Risk Level Assessment"/>
    
    <!-- High Risk Path -->
    <userTask id="security-review" name="Security Team Review"
              flowable:candidateGroups="security-team">
      <documentation>Review high-risk asset registration and approve security controls</documentation>
      <extensionElements>
        <flowable:formProperty id="reviewDecision" name="Review Decision" type="enum" required="true">
          <flowable:value id="approved" name="Approved"/>
          <flowable:value id="rejected" name="Rejected"/>
          <flowable:value id="requires-additional-controls" name="Requires Additional Controls"/>
        </flowable:formProperty>
        <flowable:formProperty id="additionalControls" name="Additional Controls Required" type="string"/>
        <flowable:formProperty id="reviewComments" name="Review Comments" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Configure Security Controls -->
    <serviceTask id="configure-controls" name="Configure Security Controls"
                 flowable:class="com.grcos.workflow.SecurityControlConfigurationTask"/>
    
    <!-- Compliance Validation -->
    <serviceTask id="validate-compliance" name="Validate Compliance"
                 flowable:class="com.grcos.workflow.CMDBAssetManagerTask">
      <extensionElements>
        <flowable:field name="cmdbOperation">
          <flowable:string>validate_configuration</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Generate Asset Documentation -->
    <serviceTask id="generate-documentation" name="Generate Asset Documentation"
                 flowable:class="com.grcos.workflow.DocumentationGeneratorTask"/>
    
    <!-- Notify Stakeholders -->
    <serviceTask id="notify-stakeholders" name="Notify Stakeholders"
                 flowable:class="com.grcos.workflow.NotificationTask">
      <extensionElements>
        <flowable:field name="notificationType">
          <flowable:string>asset-onboarding-complete</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- End Event -->
    <endEvent id="end" name="Asset Onboarding Complete"/>
    
    <!-- Rejection End Event -->
    <endEvent id="rejected-end" name="Asset Registration Rejected"/>
    
    <!-- Sequence Flows -->
    <sequenceFlow id="flow1" sourceRef="start" targetRef="register-asset"/>
    <sequenceFlow id="flow2" sourceRef="register-asset" targetRef="generate-oscal-component"/>
    <sequenceFlow id="flow3" sourceRef="generate-oscal-component" targetRef="initial-assessment"/>
    <sequenceFlow id="flow4" sourceRef="initial-assessment" targetRef="risk-assessment-gateway"/>
    
    <!-- High Risk Flow -->
    <sequenceFlow id="flow5" sourceRef="risk-assessment-gateway" targetRef="security-review">
      <conditionExpression>${riskLevel == 'high' || riskLevel == 'critical'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow6" sourceRef="security-review" targetRef="configure-controls">
      <conditionExpression>${reviewDecision == 'approved' || reviewDecision == 'requires-additional-controls'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow7" sourceRef="security-review" targetRef="rejected-end">
      <conditionExpression>${reviewDecision == 'rejected'}</conditionExpression>
    </sequenceFlow>
    
    <!-- Low/Medium Risk Flow -->
    <sequenceFlow id="flow8" sourceRef="risk-assessment-gateway" targetRef="configure-controls">
      <conditionExpression>${riskLevel == 'low' || riskLevel == 'medium'}</conditionExpression>
    </sequenceFlow>
    
    <!-- Common Flow -->
    <sequenceFlow id="flow9" sourceRef="configure-controls" targetRef="validate-compliance"/>
    <sequenceFlow id="flow10" sourceRef="validate-compliance" targetRef="generate-documentation"/>
    <sequenceFlow id="flow11" sourceRef="generate-documentation" targetRef="notify-stakeholders"/>
    <sequenceFlow id="flow12" sourceRef="notify-stakeholders" targetRef="end"/>
    
  </process>
</definitions>
```

### Configuration Compliance Monitoring Workflow

#### configuration-compliance-monitoring.bpmn20.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://grcos.com/workflows">
  
  <process id="configuration-compliance-monitoring" 
           name="Configuration Compliance Monitoring" 
           isExecutable="true">
    
    <!-- Timer Start Event - Runs every 4 hours -->
    <startEvent id="timer-start" name="Scheduled Compliance Check">
      <timerEventDefinition>
        <timeCycle>R/PT4H</timeCycle>
      </timerEventDefinition>
    </startEvent>
    
    <!-- Get Assets for Monitoring -->
    <serviceTask id="get-assets" name="Get Assets for Monitoring"
                 flowable:class="com.grcos.workflow.AssetDiscoveryTask">
      <extensionElements>
        <flowable:field name="assetFilter">
          <flowable:string>active=true AND compliance_monitoring=true</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Multi-Instance Subprocess for Each Asset -->
    <subProcess id="asset-compliance-check" name="Asset Compliance Check">
      <multiInstanceLoopCharacteristics isSequential="false" 
                                       flowable:collection="assetList" 
                                       flowable:elementVariable="currentAsset"/>
      
      <!-- Validate Asset Configuration -->
      <serviceTask id="validate-asset-config" name="Validate Asset Configuration"
                   flowable:class="com.grcos.workflow.CMDBAssetManagerTask">
        <extensionElements>
          <flowable:field name="cmdbOperation">
            <flowable:string>validate_configuration</flowable:string>
          </flowable:field>
          <flowable:field name="assetId">
            <flowable:expression>${currentAsset.id}</flowable:expression>
          </flowable:field>
        </extensionElements>
      </serviceTask>
      
      <!-- Check for Violations -->
      <exclusiveGateway id="violations-gateway" name="Violations Found?"/>
      
      <!-- Create Remediation Task -->
      <serviceTask id="create-remediation" name="Create Remediation Task"
                   flowable:class="com.grcos.workflow.RemediationTaskCreatorTask">
        <extensionElements>
          <flowable:field name="violationType">
            <flowable:string>configuration-compliance</flowable:string>
          </flowable:field>
        </extensionElements>
      </serviceTask>
      
      <!-- Update Compliance Status -->
      <serviceTask id="update-status" name="Update Compliance Status"
                   flowable:class="com.grcos.workflow.CMDBAssetManagerTask">
        <extensionElements>
          <flowable:field name="cmdbOperation">
            <flowable:string>update_compliance_status</flowable:string>
          </flowable:field>
        </extensionElements>
      </serviceTask>
      
      <!-- Subprocess Flows -->
      <sequenceFlow id="sub-flow1" sourceRef="validate-asset-config" targetRef="violations-gateway"/>
      <sequenceFlow id="sub-flow2" sourceRef="violations-gateway" targetRef="create-remediation">
        <conditionExpression>${!configurationValid}</conditionExpression>
      </sequenceFlow>
      <sequenceFlow id="sub-flow3" sourceRef="violations-gateway" targetRef="update-status">
        <conditionExpression>${configurationValid}</conditionExpression>
      </sequenceFlow>
      <sequenceFlow id="sub-flow4" sourceRef="create-remediation" targetRef="update-status"/>
      
    </subProcess>
    
    <!-- Generate Compliance Report -->
    <serviceTask id="generate-report" name="Generate Compliance Report"
                 flowable:class="com.grcos.workflow.ComplianceReportGeneratorTask"/>
    
    <!-- End Event -->
    <endEvent id="end" name="Compliance Check Complete"/>
    
    <!-- Main Process Flows -->
    <sequenceFlow id="flow1" sourceRef="timer-start" targetRef="get-assets"/>
    <sequenceFlow id="flow2" sourceRef="get-assets" targetRef="asset-compliance-check"/>
    <sequenceFlow id="flow3" sourceRef="asset-compliance-check" targetRef="generate-report"/>
    <sequenceFlow id="flow4" sourceRef="generate-report" targetRef="end"/>
    
  </process>
</definitions>
```

## Event-Driven Asset Workflows

### Asset Change Detection

#### AssetChangeEventListener.java
```java
@Component
public class AssetChangeEventListener {
    
    @Autowired
    private FlowableRuntimeService runtimeService;
    
    @Autowired
    private WorkflowAgent workflowAgent;
    
    @EventListener
    public void handleAssetChangeEvent(AssetChangeEvent event) {
        String assetId = event.getAssetId();
        String changeType = event.getChangeType();
        
        // Determine if workflow trigger is needed
        if (requiresWorkflowTrigger(changeType)) {
            
            // Get AI recommendation for workflow
            WorkflowRecommendation recommendation = workflowAgent.recommendWorkflow(
                "asset-change", event.toMap());
            
            if (recommendation.shouldTrigger()) {
                Map<String, Object> variables = new HashMap<>();
                variables.put("assetId", assetId);
                variables.put("changeType", changeType);
                variables.put("changeData", event.getChangeData());
                variables.put("triggeredBy", "asset-change-event");
                
                // Start appropriate workflow
                runtimeService.startProcessInstanceByKey(
                    recommendation.getWorkflowKey(),
                    assetId,
                    variables
                );
            }
        }
    }
    
    private boolean requiresWorkflowTrigger(String changeType) {
        return Arrays.asList(
            "configuration-change",
            "security-control-modification",
            "compliance-status-change",
            "vulnerability-detected"
        ).contains(changeType);
    }
}
```

## Performance Optimization

### Asset Workflow Caching Strategy

#### AssetWorkflowCacheConfiguration.java
```java
@Configuration
@EnableCaching
public class AssetWorkflowCacheConfiguration {
    
    @Bean
    public CacheManager assetWorkflowCacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());
        
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
    
    @Cacheable(value = "asset-configurations", key = "#assetId")
    public AssetConfiguration getAssetConfiguration(String assetId) {
        return datagerryService.getAssetConfiguration(assetId);
    }
    
    @CacheEvict(value = "asset-configurations", key = "#assetId")
    public void evictAssetConfiguration(String assetId) {
        // Cache eviction handled by annotation
    }
}
```

## Monitoring and Metrics

### Asset Workflow Metrics

#### Key Performance Indicators
- **Asset Onboarding Time**: Average time from registration to compliance validation
- **Configuration Compliance Rate**: Percentage of assets in compliant state
- **Remediation Response Time**: Time from violation detection to resolution
- **Workflow Success Rate**: Percentage of workflows completing successfully

#### Metrics Collection
```java
@Component
public class AssetWorkflowMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter assetOnboardingCounter;
    private final Timer assetOnboardingTimer;
    private final Gauge complianceRateGauge;
    
    public AssetWorkflowMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.assetOnboardingCounter = Counter.builder("grcos.asset.onboarding.total")
            .description("Total number of asset onboarding workflows")
            .register(meterRegistry);
        this.assetOnboardingTimer = Timer.builder("grcos.asset.onboarding.duration")
            .description("Asset onboarding workflow duration")
            .register(meterRegistry);
        this.complianceRateGauge = Gauge.builder("grcos.asset.compliance.rate")
            .description("Asset compliance rate")
            .register(meterRegistry, this, AssetWorkflowMetrics::getComplianceRate);
    }
    
    public void recordAssetOnboarding(Duration duration) {
        assetOnboardingCounter.increment();
        assetOnboardingTimer.record(duration);
    }
    
    private double getComplianceRate() {
        // Calculate compliance rate from CMDB data
        return datagerryService.getComplianceRate();
    }
}
```

## Next Steps

1. **Implement Custom Service Tasks** - Create GRCOS-specific asset management tasks
2. **Configure Event Listeners** - Set up asset change event handling
3. **Deploy Workflow Templates** - Install standard asset workflow patterns
4. **Set up Monitoring** - Configure metrics and alerting for asset workflows
5. **Test Integration** - Validate workflows with sample asset data

This integration provides comprehensive automation for asset lifecycle management while maintaining compliance and security standards.
