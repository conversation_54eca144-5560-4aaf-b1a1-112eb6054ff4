# GRCOS OPA Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying Open Policy Agent (OPA) within the GRCOS platform, including installation, configuration, and integration with existing GRCOS components. The deployment supports both cloud-native Kubernetes environments and on-premises installations.

## Prerequisites

### System Requirements
```yaml
Minimum Requirements:
  CPU: 4 cores
  Memory: 8GB RAM
  Storage: 100GB SSD
  Network: 1Gbps

Recommended Requirements:
  CPU: 8 cores
  Memory: 16GB RAM
  Storage: 500GB SSD
  Network: 10Gbps

Kubernetes Requirements:
  Version: 1.28+
  Nodes: 3+ (for HA)
  Storage Class: Fast SSD
  Network Policy: Enabled
```

### Software Dependencies
```yaml
Required Components:
  - Kubernetes 1.28+
  - Helm 3.12+
  - OPA 0.60.0+
  - OPA Gatekeeper 3.14+
  - Redis 7.0+ (for caching)
  - MongoDB 7.0+ (for policy storage)
  - Prometheus (for monitoring)
  - Grafana (for dashboards)

Optional Components:
  - Istio 1.19+ (for service mesh integration)
  - Envoy Proxy (for API gateway integration)
  - <PERSON><PERSON><PERSON> (for distributed tracing)
```

## Installation Process

### Step 1: Prepare Kubernetes Environment
```bash
# Create GRCOS namespace
kubectl create namespace grcos-system

# Create OPA namespace
kubectl create namespace opa-system

# Label namespaces for policy enforcement
kubectl label namespace grcos-system grcos.io/policy-enforcement=enabled
kubectl label namespace opa-system grcos.io/policy-enforcement=enabled

# Create service accounts
kubectl create serviceaccount grcos-opa -n opa-system
kubectl create serviceaccount grcos-policy-manager -n grcos-system

# Apply RBAC configurations
kubectl apply -f manifests/rbac/
```

### Step 2: Install OPA Gatekeeper
```bash
# Add OPA Gatekeeper Helm repository
helm repo add gatekeeper https://open-policy-agent.github.io/gatekeeper/charts
helm repo update

# Install OPA Gatekeeper with GRCOS configuration
helm install gatekeeper gatekeeper/gatekeeper \
  --namespace opa-system \
  --create-namespace \
  --values values/gatekeeper-values.yaml \
  --wait
```

#### Gatekeeper Configuration (values/gatekeeper-values.yaml)
```yaml
# OPA Gatekeeper Configuration for GRCOS
replicas: 3

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 256Mi

nodeSelector:
  grcos.io/node-type: policy-enforcement

tolerations:
  - key: grcos.io/policy-enforcement
    operator: Equal
    value: "true"
    effect: NoSchedule

audit:
  replicas: 2
  resources:
    limits:
      cpu: 1000m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 256Mi

controllerManager:
  exemptNamespaces:
    - kube-system
    - kube-public
    - opa-system
    - gatekeeper-system

validatingAdmissionWebhook:
  exemptNamespaces:
    - kube-system
    - kube-public
    - opa-system
    - gatekeeper-system

mutatingAdmissionWebhook:
  exemptNamespaces:
    - kube-system
    - kube-public
    - opa-system
    - gatekeeper-system

config:
  validation:
    traces:
      - user:
          kind:
            group: "*"
            version: "*"
            kind: "*"
      - kind:
          group: "*"
          version: "*"
          kind: "*"
  
  sync:
    syncOnly:
      - group: ""
        version: "v1"
        kind: "Namespace"
      - group: ""
        version: "v1"
        kind: "Pod"
      - group: "apps"
        version: "v1"
        kind: "Deployment"
      - group: "grcos.io"
        version: "v1"
        kind: "PolicyBundle"
```

### Step 3: Deploy GRCOS OPA Components
```bash
# Deploy GRCOS OPA Policy Engine
kubectl apply -f manifests/opa-engine/

# Deploy Policy Decision Points
kubectl apply -f manifests/policy-decision-points/

# Deploy Policy Bundle Manager
kubectl apply -f manifests/policy-bundle-manager/

# Deploy Policy Monitoring Components
kubectl apply -f manifests/policy-monitoring/
```

#### GRCOS OPA Engine Deployment
```yaml
# manifests/opa-engine/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grcos-opa-engine
  namespace: opa-system
  labels:
    app: grcos-opa-engine
    component: policy-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: grcos-opa-engine
  template:
    metadata:
      labels:
        app: grcos-opa-engine
        component: policy-engine
    spec:
      serviceAccountName: grcos-opa
      containers:
      - name: opa
        image: openpolicyagent/opa:0.60.0-envoy
        ports:
        - containerPort: 8181
          name: http
        - containerPort: 8282
          name: metrics
        args:
          - "run"
          - "--server"
          - "--config-file=/config/opa-config.yaml"
          - "--addr=0.0.0.0:8181"
          - "--diagnostic-addr=0.0.0.0:8282"
          - "--set=plugins.envoy_ext_authz_grpc.addr=:9191"
          - "--set=plugins.envoy_ext_authz_grpc.enable_reflection=true"
          - "--set=decision_logs.console=true"
          - "/policies"
        volumeMounts:
        - name: opa-config
          mountPath: /config
        - name: policy-bundles
          mountPath: /policies
        - name: data-sources
          mountPath: /data
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 8181
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health?bundle=true
            port: 8181
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: opa-config
        configMap:
          name: grcos-opa-config
      - name: policy-bundles
        persistentVolumeClaim:
          claimName: grcos-policy-bundles
      - name: data-sources
        configMap:
          name: grcos-opa-data
---
apiVersion: v1
kind: Service
metadata:
  name: grcos-opa-engine
  namespace: opa-system
  labels:
    app: grcos-opa-engine
spec:
  selector:
    app: grcos-opa-engine
  ports:
  - name: http
    port: 8181
    targetPort: 8181
  - name: grpc
    port: 9191
    targetPort: 9191
  - name: metrics
    port: 8282
    targetPort: 8282
  type: ClusterIP
```

### Step 4: Configure Policy Bundle Management
```yaml
# manifests/policy-bundle-manager/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grcos-policy-bundle-manager
  namespace: grcos-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: grcos-policy-bundle-manager
  template:
    metadata:
      labels:
        app: grcos-policy-bundle-manager
    spec:
      serviceAccountName: grcos-policy-manager
      containers:
      - name: bundle-manager
        image: grcos/policy-bundle-manager:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: OPA_ENDPOINT
          value: "http://grcos-opa-engine.opa-system:8181"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: grcos-mongodb-secret
              key: uri
        - name: REDIS_URI
          valueFrom:
            secretKeyRef:
              name: grcos-redis-secret
              key: uri
        - name: BLOCKCHAIN_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: grcos-config
              key: blockchain-endpoint
        volumeMounts:
        - name: policy-templates
          mountPath: /templates
        - name: bundle-storage
          mountPath: /bundles
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
      volumes:
      - name: policy-templates
        configMap:
          name: grcos-policy-templates
      - name: bundle-storage
        persistentVolumeClaim:
          claimName: grcos-bundle-storage
```

## Configuration Management

### OPA Engine Configuration
```yaml
# OPA Configuration (opa-config.yaml)
services:
  authz:
    url: http://grcos-opa-engine.opa-system:8181

bundles:
  grcos-policies:
    service: authz
    resource: "/v1/bundles/grcos-policies"
    polling:
      min_delay_seconds: 10
      max_delay_seconds: 20

decision_logs:
  service: authz
  resource: "/v1/logs"
  reporting:
    min_delay_seconds: 5
    max_delay_seconds: 10

status:
  service: authz
  resource: "/v1/status"

plugins:
  envoy_ext_authz_grpc:
    addr: :9191
    enable_reflection: true

caching:
  inter_query_builtin_cache:
    max_size_bytes: 100000000  # 100MB

server:
  encoding:
    gzip:
      min_length: 1024
```

### Policy Data Sources Configuration
```yaml
# Data Sources Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: grcos-opa-data
  namespace: opa-system
data:
  users.json: |
    {
      "users": {
        "user1": {
          "roles": ["user", "developer"],
          "attributes": {
            "department": "engineering",
            "clearance": "secret"
          }
        }
      }
    }
  
  roles.json: |
    {
      "roles": {
        "user": {
          "permissions": ["read", "write"]
        },
        "admin": {
          "permissions": ["read", "write", "admin", "delete"]
        }
      }
    }
  
  resources.json: |
    {
      "resources": {
        "app1": {
          "required_role": "user",
          "classification": "internal"
        },
        "admin_panel": {
          "required_role": "admin",
          "classification": "restricted"
        }
      }
    }
```

## Integration Configuration

### API Gateway Integration (Envoy)
```yaml
# Envoy Configuration for OPA Integration
static_resources:
  listeners:
  - name: listener_0
    address:
      socket_address:
        address: 0.0.0.0
        port_value: 8080
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: ingress_http
          http_filters:
          - name: envoy.filters.http.ext_authz
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.ext_authz.v3.ExtAuthz
              grpc_service:
                envoy_grpc:
                  cluster_name: opa_cluster
                timeout: 0.5s
              include_peer_certificate: true
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
          route_config:
            name: local_route
            virtual_hosts:
            - name: local_service
              domains: ["*"]
              routes:
              - match:
                  prefix: "/"
                route:
                  cluster: backend_service

  clusters:
  - name: opa_cluster
    type: STRICT_DNS
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: opa_cluster
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: grcos-opa-engine.opa-system
                port_value: 9191
  
  - name: backend_service
    type: STRICT_DNS
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: backend_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: grcos-backend.grcos-system
                port_value: 8080
```

### Service Mesh Integration (Istio)
```yaml
# Istio AuthorizationPolicy for OPA Integration
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: grcos-opa-authz
  namespace: grcos-system
spec:
  selector:
    matchLabels:
      app: grcos-backend
  action: CUSTOM
  provider:
    name: "grcos-opa-provider"
  rules:
  - to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE"]
---
apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: grcos-istio-config
spec:
  meshConfig:
    extensionProviders:
    - name: "grcos-opa-provider"
      envoyExtAuthzGrpc:
        service: "grcos-opa-engine.opa-system.svc.cluster.local"
        port: "9191"
```

## Monitoring and Observability

### Prometheus Configuration
```yaml
# Prometheus ServiceMonitor for OPA
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: grcos-opa-metrics
  namespace: opa-system
spec:
  selector:
    matchLabels:
      app: grcos-opa-engine
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
```

### Grafana Dashboard Configuration
```json
{
  "dashboard": {
    "title": "GRCOS OPA Policy Engine",
    "panels": [
      {
        "title": "Policy Decision Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(opa_http_request_duration_seconds_count[5m])",
            "legendFormat": "Decisions/sec"
          }
        ]
      },
      {
        "title": "Policy Decision Latency",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(opa_http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Operations Team
