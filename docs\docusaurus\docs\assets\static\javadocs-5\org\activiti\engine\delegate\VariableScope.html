<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:24 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>VariableScope (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VariableScope (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6,"i44":6,"i45":6,"i46":6,"i47":6,"i48":6,"i49":6,"i50":6,"i51":6,"i52":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/VariableScope.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/TaskListener.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/delegate/VariableScope.html" target="_top">Frames</a></li>
<li><a href="VariableScope.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.delegate</div>
<h2 title="Interface VariableScope" class="title">Interface VariableScope</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>, <a href="../../../../org/activiti/engine/delegate/DelegateTask.html" title="interface in org.activiti.engine.delegate">DelegateTask</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">VariableScope</span></pre>
<div class="block">Interface for class that acts as a scope for variables: i.e. the implementation
 can be used to set and get variables.
 
 Typically, executions (and thus process instances) and tasks are the primary use case
 to get and set variables. The <a href="../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a> for example is often used
 in <a href="../../../../org/activiti/engine/delegate/JavaDelegate.html" title="interface in org.activiti.engine.delegate"><code>JavaDelegate</code></a> implementation to get and set variables.
 
 Variables are typically stored on the 'highest parent'. For executions, this
 means that when called on an execution the variable will be stored on the process instance
 execution. Variables can be stored on the actual scope itself though, by calling the xxLocal methods.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#createVariableLocal-java.lang.String-java.lang.Object-">createVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                   <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariable-java.lang.String-">getTransientVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-"><code>getVariable(String)</code></a>, including the searching via the parent scopes, but
 for transient variables only.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariableLocal-java.lang.String-">getTransientVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a>, but for a transient variable.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariables--">getTransientVariables</a></span>()</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables--"><code>getVariables()</code></a>, but for transient variables only.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getTransientVariablesLocal--">getTransientVariablesLocal</a></span>()</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a>, but for transient variables only.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-">getVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Returns the variable value for one specific variable.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-boolean-">getVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
           boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-"><code>getVariable(String)</code></a>, but has an extra flag that indicates whether or not 
 all variables need to be fetched when getting one variable.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-java.lang.Class-">getVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</code>
<div class="block">Typed version of the <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-"><code>getVariable(String)</code></a> method.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.persistence.entity.VariableInstance</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstance-java.lang.String-">getVariableInstance</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-"><code>getVariable(String)</code></a>, but returns a <code>VariableInstance</code> instance,
 which contains more information than just the value.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.persistence.entity.VariableInstance</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstance-java.lang.String-boolean-">getVariableInstance</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                   boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-boolean-"><code>getVariable(String, boolean)</code></a>, but returns an instance of <code>VariableInstance</code>, 
 which has some additional information beyond the value.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>org.activiti.engine.impl.persistence.entity.VariableInstance</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstanceLocal-java.lang.String-">getVariableInstanceLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a>, but returns an instance of <code>VariableInstance</code>, 
 which has some additional information beyond the value.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>org.activiti.engine.impl.persistence.entity.VariableInstance</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstanceLocal-java.lang.String-boolean-">getVariableInstanceLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                        boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-boolean-"><code>getVariableLocal(String, boolean)</code></a>, but returns an instance of <code>VariableInstance</code>, 
 which has some additional information beyond the value.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances--">getVariableInstances</a></span>()</code>
<div class="block">Returns all variables, as instances of the <code>VariableInstance</code> interface,
 which gives more information than only the the value (type, execution id, etc.)</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-">getVariableInstances</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances--"><code>getVariableInstances()</code></a>, but limited to only the variables with the provided names.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-boolean-">getVariableInstances</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                    boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-boolean-"><code>getVariables(Collection, boolean)</code></a> but returns the variables 
 as instances of the <code>VariableInstance</code> interface,
 which gives more information than only the the value (type, execution id, etc.)</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstancesLocal--">getVariableInstancesLocal</a></span>()</code>
<div class="block">Returns the variables local to this scope as instances of the <code>VariableInstance</code> interface,
 which provided additional information about the variable.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstancesLocal-java.util.Collection-">getVariableInstancesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-"><code>getVariableInstances(Collection)</code></a>, but only for variables local to this scope.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstancesLocal-java.util.Collection-boolean-">getVariableInstancesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                         boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-boolean-"><code>getVariableInstances(Collection, boolean)</code></a>, but only for variables local to this scope.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-">getVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Returns the value for the specific variable and only checks this scope and not any parent scope.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-boolean-">getVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a>, but has an extra flag that indicates whether or not 
 all variables need to be fetched when getting one variable.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-java.lang.Class-">getVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</code>
<div class="block">Typed version of the <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a> method.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableNames--">getVariableNames</a></span>()</code>
<div class="block">Returns all the names of the variables for this scope and all parent scopes.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableNamesLocal--">getVariableNamesLocal</a></span>()</code>
<div class="block">Returns all the names of the variables for this scope (no parent scopes).</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables--">getVariables</a></span>()</code>
<div class="block">Returns all variables.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-">getVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables--"><code>getVariables()</code></a>, but limited to only the variables with the provided names.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-boolean-">getVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
            boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <code>#getVariables(Collection))</code>, but with a flag that indicates that all 
 variables should be fetched when fetching the specific variables.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariablesLocal--">getVariablesLocal</a></span>()</code>
<div class="block">Returns the variable local to this scope only.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariablesLocal-java.util.Collection-">getVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-"><code>getVariables(Collection)</code></a>, but only for variables local to this scope.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariablesLocal-java.util.Collection-boolean-">getVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                 boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-boolean-"><code>getVariables(Collection, boolean)</code></a>, but only for variables local to this scope.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariable-java.lang.String-">hasVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Returns whether this scope or any parent scope has a specific variable.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariableLocal-java.lang.String-">hasVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Returns whether this scope has a specific variable.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariables--">hasVariables</a></span>()</code>
<div class="block">Returns whether this scope or any parent scope has variables.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#hasVariablesLocal--">hasVariablesLocal</a></span>()</code>
<div class="block">Returns whether this scope has variables.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariable-java.lang.String-">removeTransientVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Removes a specific transient variable.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariableLocal-java.lang.String-">removeTransientVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Removes a specific transient variable (also searching parent scopes).</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariables--">removeTransientVariables</a></span>()</code>
<div class="block">Remove all transient variable of this scope and its parent scopes.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeTransientVariablesLocal--">removeTransientVariablesLocal</a></span>()</code>
<div class="block">Removes all local transient variables.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariable-java.lang.String-">removeVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Removes the variable and creates a new;@link HistoricVariableUpdateEntity}</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariableLocal-java.lang.String-">removeVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</code>
<div class="block">Removes the local variable and creates a new <code>HistoricVariableUpdate</code>.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariables--">removeVariables</a></span>()</code>
<div class="block">Removes the (local) variables and creates a new
 <code>HistoricVariableUpdateEntity</code> for each of them.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariables-java.util.Collection-">removeVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Removes the variables and creates a new
 <code>HistoricVariableUpdateEntity</code> for each of them.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariablesLocal--">removeVariablesLocal</a></span>()</code>
<div class="block">Removes the (local) variables and creates a new
 <code>HistoricVariableUpdateEntity</code> for each of them.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#removeVariablesLocal-java.util.Collection-">removeVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</code>
<div class="block">Removes the local variables and creates a new
 <code>HistoricVariableUpdateEntity</code> for each of them.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-">setTransientVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                    <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>setVariable(String, Object)</code></a>, but the variable is transient:
 
 - no history is kept for the variable
 - the variable is only available until a waitstate is reached in the process
 - transient variables 'shadow' persistent variable (when getVariable('abc') 
   where 'abc' is both persistent and transient, the transient value is returned.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariableLocal-java.lang.String-java.lang.Object-">setTransientVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-"><code>setVariableLocal(String, Object)</code></a>, but for a transient variable.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariables-java.util.Map-">setTransientVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-"><code>setVariables(Map)</code></a>, but for transient variables.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariablesLocal-java.util.Map-">setTransientVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariablesLocal-java.util.Map-"><code>setVariablesLocal(Map)</code></a>, but for transient variables.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-">setVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Sets the variable with the provided name to the provided value.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-boolean-">setVariable</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
           <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value,
           boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>setVariable(String, Object)</code></a>, but with an extra flag to indicate whether 
 all variables should be fetched while doing this or not.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-">setVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>setVariable(String, Object)</code></a>, but the variable is set to this scope specifically.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-boolean-">setVariableLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value,
                boolean&nbsp;fetchAllVariables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-boolean-"><code>setVariableLocal(String, Object, boolean)</code></a>, but the variable is set to this scope specifically.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-">setVariables</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Sets the provided variables to the variable scope.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariablesLocal-java.util.Map-">setVariablesLocal</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</code>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-"><code>setVariables(Map)</code></a>, but the variable are set on this scope specifically.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariables</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariables()</pre>
<div class="block">Returns all variables. 
 This will include all variables of parent scopes too.</div>
</li>
</ul>
<a name="getVariableInstances--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstances</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstances()</pre>
<div class="block">Returns all variables, as instances of the <code>VariableInstance</code> interface,
 which gives more information than only the the value (type, execution id, etc.)</div>
</li>
</ul>
<a name="getVariables-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariables</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables--"><code>getVariables()</code></a>, but limited to only the variables with the provided names.</div>
</li>
</ul>
<a name="getVariableInstances-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstances</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstances(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances--"><code>getVariableInstances()</code></a>, but limited to only the variables with the provided names.</div>
</li>
</ul>
<a name="getVariables-java.util.Collection-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariables</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                                boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <code>#getVariables(Collection))</code>, but with a flag that indicates that all 
 variables should be fetched when fetching the specific variables.
  
 If set to false, only the specific variables will be fetched.
 Dependening on the use case, this can be better for performance, as it avoids fetching and processing 
 the other variables. However, if the other variables are needed further on, getting them in
 one go is probably better (and the variables are cached during one <code>Command</code> execution).</div>
</li>
</ul>
<a name="getVariableInstances-java.util.Collection-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstances</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstances(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                                                                                              boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-boolean-"><code>getVariables(Collection, boolean)</code></a> but returns the variables 
 as instances of the <code>VariableInstance</code> interface,
 which gives more information than only the the value (type, execution id, etc.)</div>
</li>
</ul>
<a name="getVariablesLocal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariablesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariablesLocal()</pre>
<div class="block">Returns the variable local to this scope only.
 So, in contrary to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables--"><code>getVariables()</code></a>, the variables from the parent scope won't be returned.</div>
</li>
</ul>
<a name="getVariableInstancesLocal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstancesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstancesLocal()</pre>
<div class="block">Returns the variables local to this scope as instances of the <code>VariableInstance</code> interface,
 which provided additional information about the variable.</div>
</li>
</ul>
<a name="getVariablesLocal-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariablesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-"><code>getVariables(Collection)</code></a>, but only for variables local to this scope.</div>
</li>
</ul>
<a name="getVariableInstancesLocal-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstancesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstancesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-"><code>getVariableInstances(Collection)</code></a>, but only for variables local to this scope.</div>
</li>
</ul>
<a name="getVariablesLocal-java.util.Collection-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariablesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                                     boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables-java.util.Collection-boolean-"><code>getVariables(Collection, boolean)</code></a>, but only for variables local to this scope.</div>
</li>
</ul>
<a name="getVariableInstancesLocal-java.util.Collection-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstancesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,org.activiti.engine.impl.persistence.entity.VariableInstance&gt;&nbsp;getVariableInstancesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames,
                                                                                                   boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableInstances-java.util.Collection-boolean-"><code>getVariableInstances(Collection, boolean)</code></a>, but only for variables local to this scope.</div>
</li>
</ul>
<a name="getVariable-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariable</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Returns the variable value for one specific variable.
 Will look in parent scopes when the variable does not exist on this particular scope.</div>
</li>
</ul>
<a name="getVariableInstance-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstance</h4>
<pre>org.activiti.engine.impl.persistence.entity.VariableInstance&nbsp;getVariableInstance(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-"><code>getVariable(String)</code></a>, but returns a <code>VariableInstance</code> instance,
 which contains more information than just the value.</div>
</li>
</ul>
<a name="getVariable-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariable</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                   boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-"><code>getVariable(String)</code></a>, but has an extra flag that indicates whether or not 
 all variables need to be fetched when getting one variable.
 
 By default true (for backwards compatibility reasons), which means that calling <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-"><code>getVariable(String)</code></a>
 will fetch all variables, of the current scope and all parent scopes.
 Setting this flag to false can thus be better for performance. However, variables are cached, and 
 if other variables are used later on, setting this true might actually be better for performance.</div>
</li>
</ul>
<a name="getVariableInstance-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstance</h4>
<pre>org.activiti.engine.impl.persistence.entity.VariableInstance&nbsp;getVariableInstance(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                                                                 boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-boolean-"><code>getVariable(String, boolean)</code></a>, but returns an instance of <code>VariableInstance</code>, 
 which has some additional information beyond the value.</div>
</li>
</ul>
<a name="getVariableLocal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Returns the value for the specific variable and only checks this scope and not any parent scope.</div>
</li>
</ul>
<a name="getVariableInstanceLocal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstanceLocal</h4>
<pre>org.activiti.engine.impl.persistence.entity.VariableInstance&nbsp;getVariableInstanceLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a>, but returns an instance of <code>VariableInstance</code>, 
 which has some additional information beyond the value.</div>
</li>
</ul>
<a name="getVariableLocal-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                        boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a>, but has an extra flag that indicates whether or not 
 all variables need to be fetched when getting one variable.
 
 By default true (for backwards compatibility reasons), which means that calling <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a>
 will fetch all variables, of the current scope.
 Setting this flag to false can thus be better for performance. However, variables are cached, and 
 if other variables are used later on, setting this true might actually be better for performance.</div>
</li>
</ul>
<a name="getVariableInstanceLocal-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableInstanceLocal</h4>
<pre>org.activiti.engine.impl.persistence.entity.VariableInstance&nbsp;getVariableInstanceLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                                                                                      boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-boolean-"><code>getVariableLocal(String, boolean)</code></a>, but returns an instance of <code>VariableInstance</code>, 
 which has some additional information beyond the value.</div>
</li>
</ul>
<a name="getVariable-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariable</h4>
<pre>&lt;T&gt;&nbsp;T&nbsp;getVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</pre>
<div class="block">Typed version of the <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-"><code>getVariable(String)</code></a> method.</div>
</li>
</ul>
<a name="getVariableLocal-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableLocal</h4>
<pre>&lt;T&gt;&nbsp;T&nbsp;getVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                       <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;T&gt;&nbsp;variableClass)</pre>
<div class="block">Typed version of the <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a> method.</div>
</li>
</ul>
<a name="getVariableNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableNames</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getVariableNames()</pre>
<div class="block">Returns all the names of the variables for this scope and all parent scopes.</div>
</li>
</ul>
<a name="getVariableNamesLocal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableNamesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getVariableNamesLocal()</pre>
<div class="block">Returns all the names of the variables for this scope (no parent scopes).</div>
</li>
</ul>
<a name="setVariable-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariable</h4>
<pre>void&nbsp;setVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Sets the variable with the provided name to the provided value.
 
 <p>
 A variable is set according to the following algorithm:
 
 <p>
 <li>If this scope already contains a variable by the provided name as a
 <strong>local</strong> variable, its value is overwritten to the provided
 value.</li>
 <li>If this scope does <strong>not</strong> contain a variable by the
 provided name as a local variable, the variable is set to this scope's
 parent scope, if there is one. If there is no parent scope (meaning this
 scope is the root scope of the hierarchy it belongs to), this scope is
 used. This applies recursively up the parent scope chain until, if no scope
 contains a local variable by the provided name, ultimately the root scope
 is reached and the variable value is set on that scope.</li>
 <p>
 In practice for most cases, this algorithm will set variables to the scope
 of the execution at the process instance’s root level, if there is no
 execution-local variable by the provided name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>variableName</code> - the name of the variable to be set</dd>
<dd><code>value</code> - the value of the variable to be set</dd>
</dl>
</li>
</ul>
<a name="setVariable-java.lang.String-java.lang.Object-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariable</h4>
<pre>void&nbsp;setVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                 <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value,
                 boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>setVariable(String, Object)</code></a>, but with an extra flag to indicate whether 
 all variables should be fetched while doing this or not.
 
 The variable will be put on the highest possible scope. For an execution this is the process instance execution.
 If this is not wanted, use the <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-"><code>setVariableLocal(String, Object)</code></a> method instead. 
 
 The default (e.g. when calling <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>setVariable(String, Object)</code></a>), is <i>true</i>, for backwards
 compatibility reasons. However, in some use cases, it might make sense not to fetch any other variables
 when setting one variable (for example when doing nothing more than just setting one variable).</div>
</li>
</ul>
<a name="setVariableLocal-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariableLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;setVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>setVariable(String, Object)</code></a>, but the variable is set to this scope specifically.</div>
</li>
</ul>
<a name="setVariableLocal-java.lang.String-java.lang.Object-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariableLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;setVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value,
                        boolean&nbsp;fetchAllVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-boolean-"><code>setVariableLocal(String, Object, boolean)</code></a>, but the variable is set to this scope specifically.</div>
</li>
</ul>
<a name="setVariables-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariables</h4>
<pre>void&nbsp;setVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Sets the provided variables to the variable scope.
 
 <p>
 Variables are set according algorithm for
 <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>setVariable(String, Object)</code></a>, applied separately to each variable.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>variables</code> - a map of keys and values for the variables to be set</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>{@link VariableScope#setVariable(String, Object)}</code></a></dd>
</dl>
</li>
</ul>
<a name="setVariablesLocal-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariablesLocal</h4>
<pre>void&nbsp;setVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,? extends <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;variables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-"><code>setVariables(Map)</code></a>, but the variable are set on this scope specifically.</div>
</li>
</ul>
<a name="hasVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasVariables</h4>
<pre>boolean&nbsp;hasVariables()</pre>
<div class="block">Returns whether this scope or any parent scope has variables.</div>
</li>
</ul>
<a name="hasVariablesLocal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasVariablesLocal</h4>
<pre>boolean&nbsp;hasVariablesLocal()</pre>
<div class="block">Returns whether this scope has variables.</div>
</li>
</ul>
<a name="hasVariable-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasVariable</h4>
<pre>boolean&nbsp;hasVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Returns whether this scope or any parent scope has a specific variable.</div>
</li>
</ul>
<a name="hasVariableLocal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasVariableLocal</h4>
<pre>boolean&nbsp;hasVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Returns whether this scope has a specific variable.</div>
</li>
</ul>
<a name="removeVariable-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariable</h4>
<pre>void&nbsp;removeVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Removes the variable and creates a new;@link HistoricVariableUpdateEntity}</div>
</li>
</ul>
<a name="removeVariableLocal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariableLocal</h4>
<pre>void&nbsp;removeVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Removes the local variable and creates a new <code>HistoricVariableUpdate</code>.</div>
</li>
</ul>
<a name="createVariableLocal-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createVariableLocal</h4>
<pre>void&nbsp;createVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                         <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
</li>
</ul>
<a name="removeVariables-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariables</h4>
<pre>void&nbsp;removeVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Removes the variables and creates a new
 <code>HistoricVariableUpdateEntity</code> for each of them.</div>
</li>
</ul>
<a name="removeVariablesLocal-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariablesLocal</h4>
<pre>void&nbsp;removeVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;variableNames)</pre>
<div class="block">Removes the local variables and creates a new
 <code>HistoricVariableUpdateEntity</code> for each of them.</div>
</li>
</ul>
<a name="removeVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariables</h4>
<pre>void&nbsp;removeVariables()</pre>
<div class="block">Removes the (local) variables and creates a new
 <code>HistoricVariableUpdateEntity</code> for each of them.</div>
</li>
</ul>
<a name="removeVariablesLocal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeVariablesLocal</h4>
<pre>void&nbsp;removeVariablesLocal()</pre>
<div class="block">Removes the (local) variables and creates a new
 <code>HistoricVariableUpdateEntity</code> for each of them.</div>
</li>
</ul>
<a name="setTransientVariable-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransientVariable</h4>
<pre>void&nbsp;setTransientVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                          <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariable-java.lang.String-java.lang.Object-"><code>setVariable(String, Object)</code></a>, but the variable is transient:
 
 - no history is kept for the variable
 - the variable is only available until a waitstate is reached in the process
 - transient variables 'shadow' persistent variable (when getVariable('abc') 
   where 'abc' is both persistent and transient, the transient value is returned.</div>
</li>
</ul>
<a name="setTransientVariableLocal-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransientVariableLocal</h4>
<pre>void&nbsp;setTransientVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName,
                               <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;variableValue)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariableLocal-java.lang.String-java.lang.Object-"><code>setVariableLocal(String, Object)</code></a>, but for a transient variable.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="setTransientVariables-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransientVariables</h4>
<pre>void&nbsp;setTransientVariables(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariables-java.util.Map-"><code>setVariables(Map)</code></a>, but for transient variables.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="getTransientVariable-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTransientVariable</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getTransientVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariable-java.lang.String-"><code>getVariable(String)</code></a>, including the searching via the parent scopes, but
 for transient variables only.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="getTransientVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTransientVariables</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getTransientVariables()</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariables--"><code>getVariables()</code></a>, but for transient variables only.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="setTransientVariablesLocal-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransientVariablesLocal</h4>
<pre>void&nbsp;setTransientVariablesLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;transientVariables)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setVariablesLocal-java.util.Map-"><code>setVariablesLocal(Map)</code></a>, but for transient variables.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="getTransientVariableLocal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTransientVariableLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getTransientVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a>, but for a transient variable.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="getTransientVariablesLocal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTransientVariablesLocal</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getTransientVariablesLocal()</pre>
<div class="block">Similar to <a href="../../../../org/activiti/engine/delegate/VariableScope.html#getVariableLocal-java.lang.String-"><code>getVariableLocal(String)</code></a>, but for transient variables only.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="removeTransientVariableLocal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeTransientVariableLocal</h4>
<pre>void&nbsp;removeTransientVariableLocal(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Removes a specific transient variable (also searching parent scopes).
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="removeTransientVariable-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeTransientVariable</h4>
<pre>void&nbsp;removeTransientVariable(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;variableName)</pre>
<div class="block">Removes a specific transient variable.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="removeTransientVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeTransientVariables</h4>
<pre>void&nbsp;removeTransientVariables()</pre>
<div class="block">Remove all transient variable of this scope and its parent scopes.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
<a name="removeTransientVariablesLocal--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>removeTransientVariablesLocal</h4>
<pre>void&nbsp;removeTransientVariablesLocal()</pre>
<div class="block">Removes all local transient variables.
 See <a href="../../../../org/activiti/engine/delegate/VariableScope.html#setTransientVariable-java.lang.String-java.lang.Object-"><code>setTransientVariable(String, Object)</code></a> for the rules on 'transient' variables.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/VariableScope.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/delegate/TaskListener.html" title="interface in org.activiti.engine.delegate"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/delegate/VariableScope.html" target="_top">Frames</a></li>
<li><a href="VariableScope.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
