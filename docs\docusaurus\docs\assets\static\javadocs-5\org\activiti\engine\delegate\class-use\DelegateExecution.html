<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.activiti.engine.delegate.DelegateExecution (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.activiti.engine.delegate.DelegateExecution (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/class-use/DelegateExecution.html" target="_top">Frames</a></li>
<li><a href="DelegateExecution.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.activiti.engine.delegate.DelegateExecution" class="title">Uses of Interface<br>org.activiti.engine.delegate.DelegateExecution</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine.delegate">org.activiti.engine.delegate</a></td>
<td class="colLast">
<div class="block">Interfaces used to include Java code in a process as the behavior of an activity 
    or as a listener to process events with <a href="../../../../../org/activiti/engine/delegate/JavaDelegate.html" title="interface in org.activiti.engine.delegate"><code>JavaDelegate</code></a>s.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine.delegate">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a> in <a href="../../../../../org/activiti/engine/delegate/package-summary.html">org.activiti.engine.delegate</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/delegate/package-summary.html">org.activiti.engine.delegate</a> that return <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a></code></td>
<td class="colLast"><span class="typeNameLabel">DelegateTask.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/DelegateTask.html#getExecution--">getExecution</a></span>()</code>
<div class="block">Returns the execution currently at the task.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/delegate/package-summary.html">org.activiti.engine.delegate</a> with parameters of type <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">JavaDelegate.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/JavaDelegate.html#execute-org.activiti.engine.delegate.DelegateExecution-">execute</a></span>(<a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static org.activiti.bpmn.model.BpmnModel</code></td>
<td class="colLast"><span class="typeNameLabel">DelegateHelper.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/DelegateHelper.html#getBpmnModel-org.activiti.engine.delegate.DelegateExecution-">getBpmnModel</a></span>(<a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>
<div class="block">Returns the <code>BpmnModel</code> matching the process definition bpmn model
 for the process definition of the passed <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.bpmn.model.ExtensionElement&gt;&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DelegateHelper.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/DelegateHelper.html#getExtensionElements-org.activiti.engine.delegate.DelegateExecution-">getExtensionElements</a></span>(<a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>
<div class="block">Returns for the activityId of the passed <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a> the
 <a href="http://docs.oracle.com/javase/6/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util"><code>Map</code></a> of <code>ExtensionElement</code> instances.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static org.activiti.bpmn.model.FieldExtension</code></td>
<td class="colLast"><span class="typeNameLabel">DelegateHelper.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/DelegateHelper.html#getField-org.activiti.engine.delegate.DelegateExecution-java.lang.String-">getField</a></span>(<a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution,
        <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldName)</code>
<div class="block">Returns the <code>FieldExtension</code> matching the provided 'fieldName' which is
 defined for the current activity of the provided <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate">Expression</a></code></td>
<td class="colLast"><span class="typeNameLabel">DelegateHelper.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/DelegateHelper.html#getFieldExpression-org.activiti.engine.delegate.DelegateExecution-java.lang.String-">getFieldExpression</a></span>(<a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution,
                  <a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldName)</code>
<div class="block">Returns the <a href="../../../../../org/activiti/engine/delegate/Expression.html" title="interface in org.activiti.engine.delegate"><code>Expression</code></a> for the field defined for the current activity
 of the provided <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/6/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.activiti.bpmn.model.FieldExtension&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DelegateHelper.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/DelegateHelper.html#getFields-org.activiti.engine.delegate.DelegateExecution-">getFields</a></span>(<a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>
<div class="block">Returns the list of field extensions, represented as instances of <code>FieldExtension</code>,
 for the current activity of the passed <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static org.activiti.bpmn.model.FlowElement</code></td>
<td class="colLast"><span class="typeNameLabel">DelegateHelper.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/DelegateHelper.html#getFlowElement-org.activiti.engine.delegate.DelegateExecution-">getFlowElement</a></span>(<a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>
<div class="block">Returns the current <code>FlowElement</code> where the <a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate"><code>DelegateExecution</code></a> is currently at.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ExecutionListener.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/delegate/ExecutionListener.html#notify-org.activiti.engine.delegate.DelegateExecution-">notify</a></span>(<a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">DelegateExecution</a>&nbsp;execution)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/delegate/DelegateExecution.html" title="interface in org.activiti.engine.delegate">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/delegate/class-use/DelegateExecution.html" target="_top">Frames</a></li>
<li><a href="DelegateExecution.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
