# GRCOS Flowable Integration Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the GRCOS Flowable integration in production environments. It covers infrastructure setup, configuration management, security hardening, monitoring setup, and operational procedures.

## Deployment Architecture

### Production Architecture Overview

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        GRCOS Production Deployment Architecture                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        Load Balancer & CDN Layer                           │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │   AWS ALB   │  │ CloudFlare  │  │    WAF      │  │   SSL/TLS   │       │ │
│  │  │             │  │     CDN     │  │             │  │ Termination │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        Application Layer                                    │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │   GRCOS     │  │  Flowable   │  │   CrewAI    │  │   OSCAL     │       │ │
│  │  │ Application │  │   Engine    │  │ Agents      │  │ Processor   │       │ │
│  │  │ (3 nodes)   │  │ (3 nodes)   │  │ (2 nodes)   │  │ (2 nodes)   │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        Data Layer                                           │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │ PostgreSQL  │  │   MongoDB   │  │    Redis    │  │ Blockchain  │       │ │
│  │  │  Cluster    │  │   Cluster   │  │   Cluster   │  │   Network   │       │ │
│  │  │ (3 nodes)   │  │ (3 nodes)   │  │ (3 nodes)   │  │ (4 nodes)   │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        Monitoring & Logging                                │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │ Prometheus  │  │   Grafana   │  │    ELK      │  │   Jaeger    │       │ │
│  │  │   Cluster   │  │ Dashboard   │  │    Stack    │  │   Tracing   │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Infrastructure Setup

### Kubernetes Cluster Configuration

#### cluster-setup.yaml
```yaml
# Kubernetes cluster configuration for GRCOS
apiVersion: v1
kind: Namespace
metadata:
  name: grcos-production
  labels:
    name: grcos-production
    environment: production
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: grcos-resource-quota
  namespace: grcos-production
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "20"
    services: "10"
    secrets: "20"
    configmaps: "20"
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: grcos-network-policy
  namespace: grcos-production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: grcos-production
    - namespaceSelector:
        matchLabels:
          name: monitoring
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: grcos-production
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
```

### Application Deployment

#### grcos-flowable-deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grcos-flowable-engine
  namespace: grcos-production
  labels:
    app: grcos-flowable-engine
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: grcos-flowable-engine
  template:
    metadata:
      labels:
        app: grcos-flowable-engine
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8081"
        prometheus.io/path: "/actuator/prometheus"
    spec:
      serviceAccountName: grcos-flowable-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: grcos-flowable-engine
        image: grcos/flowable-engine:1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        - containerPort: 8081
          name: management
          protocol: TCP
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production,kubernetes"
        - name: JAVA_OPTS
          value: "-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: grcos-db-secret
              key: host
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grcos-db-secret
              key: password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grcos-redis-secret
              key: password
        - name: OAUTH2_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: grcos-oauth2-secret
              key: client-secret
        - name: CREW_AI_API_KEY
          valueFrom:
            secretKeyRef:
              name: grcos-crewai-secret
              key: api-key
        - name: BLOCKCHAIN_WALLET_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grcos-blockchain-secret
              key: wallet-password
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8081
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /actuator/health/startup
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: config-volume
          mountPath: /config
          readOnly: true
        - name: logs-volume
          mountPath: /logs
        - name: blockchain-certs
          mountPath: /blockchain/certs
          readOnly: true
        - name: temp-volume
          mountPath: /tmp
      volumes:
      - name: config-volume
        configMap:
          name: grcos-flowable-config
      - name: logs-volume
        emptyDir:
          sizeLimit: 1Gi
      - name: blockchain-certs
        secret:
          secretName: grcos-blockchain-certs
      - name: temp-volume
        emptyDir:
          sizeLimit: 500Mi
      nodeSelector:
        node-type: application
      tolerations:
      - key: "application"
        operator: "Equal"
        value: "grcos"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - grcos-flowable-engine
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: grcos-flowable-service
  namespace: grcos-production
  labels:
    app: grcos-flowable-engine
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    protocol: TCP
  - name: management
    port: 8081
    targetPort: 8081
    protocol: TCP
  selector:
    app: grcos-flowable-engine
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: grcos-flowable-ingress
  namespace: grcos-production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - grcos.yourdomain.com
    secretName: grcos-tls-secret
  rules:
  - host: grcos.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grcos-flowable-service
            port:
              number: 8080
```

### Database Setup

#### postgresql-cluster.yaml
```yaml
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: grcos-postgresql-cluster
  namespace: grcos-production
spec:
  instances: 3
  
  postgresql:
    parameters:
      max_connections: "200"
      shared_buffers: "256MB"
      effective_cache_size: "1GB"
      maintenance_work_mem: "64MB"
      checkpoint_completion_target: "0.9"
      wal_buffers: "16MB"
      default_statistics_target: "100"
      random_page_cost: "1.1"
      effective_io_concurrency: "200"
      work_mem: "4MB"
      min_wal_size: "1GB"
      max_wal_size: "4GB"
      
  bootstrap:
    initdb:
      database: grcos_flowable
      owner: grcos_user
      secret:
        name: grcos-postgresql-credentials
        
  storage:
    size: 100Gi
    storageClass: fast-ssd
    
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"
      
  monitoring:
    enabled: true
    
  backup:
    retentionPolicy: "30d"
    barmanObjectStore:
      destinationPath: "s3://grcos-backups/postgresql"
      s3Credentials:
        accessKeyId:
          name: grcos-s3-credentials
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: grcos-s3-credentials
          key: SECRET_ACCESS_KEY
      wal:
        retention: "7d"
      data:
        retention: "30d"
```

## Configuration Management

### Production Configuration

#### application-production.yml
```yaml
spring:
  application:
    name: grcos-flowable-engine
    
  profiles:
    active: production
    
  datasource:
    primary:
      url: jdbc:postgresql://${DB_HOST}:5432/grcos_flowable
      username: grcos_user
      password: ${DB_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        leak-detection-threshold: 60000
        
  redis:
    host: ${REDIS_HOST}
    port: 6379
    password: ${REDIS_PASSWORD}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${OAUTH2_ISSUER_URI}
          
# Flowable Configuration
flowable:
  database-schema-update: false
  async:
    executor:
      async-executor-activate: true
      async-executor-core-pool-size: 8
      async-executor-max-pool-size: 16
      async-executor-queue-capacity: 1000
      
# GRCOS Configuration
grcos:
  flowable:
    agents:
      enabled: true
      crew-ai:
        api-key: ${CREW_AI_API_KEY}
        
    blockchain:
      enabled: true
      network-config: /blockchain/network-config.yaml
      wallet-path: /blockchain/wallet
      wallet-password: ${BLOCKCHAIN_WALLET_PASSWORD}
      
    monitoring:
      metrics:
        enabled: true
        export:
          prometheus:
            enabled: true
            
# Logging Configuration
logging:
  level:
    org.flowable: INFO
    com.grcos: INFO
    org.springframework.security: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /logs/grcos-flowable.log
    max-size: 100MB
    max-history: 30
    
# Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
      probes:
        enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 30s
```

## Security Configuration

### Security Hardening

#### security-policies.yaml
```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: grcos-flowable-sa
  namespace: grcos-production
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: grcos-production
  name: grcos-flowable-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: grcos-flowable-rolebinding
  namespace: grcos-production
subjects:
- kind: ServiceAccount
  name: grcos-flowable-sa
  namespace: grcos-production
roleRef:
  kind: Role
  name: grcos-flowable-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: grcos-flowable-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

## Monitoring Setup

### Prometheus Configuration

#### prometheus-config.yaml
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 30s
      evaluation_interval: 30s
      
    rule_files:
      - "/etc/prometheus/rules/*.yml"
      
    scrape_configs:
    - job_name: 'grcos-flowable'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - grcos-production
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
        
    - job_name: 'grcos-database'
      static_configs:
      - targets: ['grcos-postgresql-cluster:5432']
      metrics_path: /metrics
      
    alerting:
      alertmanagers:
      - static_configs:
        - targets:
          - alertmanager:9093
```

### Grafana Dashboards

#### grcos-dashboard.json
```json
{
  "dashboard": {
    "id": null,
    "title": "GRCOS Flowable Monitoring",
    "tags": ["grcos", "flowable", "compliance"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Active Processes",
        "type": "stat",
        "targets": [
          {
            "expr": "grcos_workflow_processes_active",
            "legendFormat": "Active Processes"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 50},
                {"color": "red", "value": 100}
              ]
            }
          }
        }
      },
      {
        "id": 2,
        "title": "Process Completion Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(grcos_workflow_processes_completed_total[5m]) / rate(grcos_workflow_processes_started_total[5m]) * 100",
            "legendFormat": "Completion Rate %"
          }
        ]
      },
      {
        "id": 3,
        "title": "Average Process Duration",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(grcos_workflow_process_execution_time_sum[5m]) / rate(grcos_workflow_process_execution_time_count[5m])",
            "legendFormat": "Avg Duration (seconds)"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

## Deployment Scripts

### Automated Deployment Script

#### deploy.sh
```bash
#!/bin/bash

set -e

# Configuration
NAMESPACE="grcos-production"
RELEASE_NAME="grcos-flowable"
IMAGE_TAG=${1:-"latest"}
ENVIRONMENT=${2:-"production"}

echo "Deploying GRCOS Flowable Integration v${IMAGE_TAG} to ${ENVIRONMENT}"

# Pre-deployment checks
echo "Running pre-deployment checks..."
kubectl cluster-info
kubectl get nodes
kubectl get namespace ${NAMESPACE} || kubectl create namespace ${NAMESPACE}

# Database migration
echo "Running database migrations..."
kubectl apply -f k8s/database/postgresql-cluster.yaml
kubectl wait --for=condition=Ready cluster/grcos-postgresql-cluster -n ${NAMESPACE} --timeout=300s

# Apply secrets
echo "Applying secrets..."
kubectl apply -f k8s/secrets/ -n ${NAMESPACE}

# Apply configuration
echo "Applying configuration..."
kubectl apply -f k8s/config/ -n ${NAMESPACE}

# Deploy application
echo "Deploying application..."
sed "s/{{IMAGE_TAG}}/${IMAGE_TAG}/g" k8s/app/grcos-flowable-deployment.yaml | kubectl apply -f - -n ${NAMESPACE}

# Wait for deployment
echo "Waiting for deployment to be ready..."
kubectl rollout status deployment/grcos-flowable-engine -n ${NAMESPACE} --timeout=600s

# Health check
echo "Performing health check..."
kubectl wait --for=condition=Ready pod -l app=grcos-flowable-engine -n ${NAMESPACE} --timeout=300s

# Verify deployment
echo "Verifying deployment..."
kubectl get pods -n ${NAMESPACE}
kubectl get services -n ${NAMESPACE}

# Run smoke tests
echo "Running smoke tests..."
./scripts/smoke-tests.sh ${NAMESPACE}

echo "Deployment completed successfully!"
```

This deployment guide provides a comprehensive approach to deploying the GRCOS Flowable integration in production with proper security, monitoring, and operational procedures.
