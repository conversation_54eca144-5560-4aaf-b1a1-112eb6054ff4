/**
 * Copyright (c) 2017-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// See https://docusaurus.io/docs/site-config for all the possible
// site configuration options.

// List of projects/orgs using your project for the users page.
const users = [];

const siteConfig = {
  title: 'Flowable Open Source Documentation', // Title for your website.
  tagline: 'Reference and User Guides',
  url: 'https://flowable.com', // Your website URL
  baseUrl: '/open-source/docs/', // Base URL for your project */
  // For github.io type URLs, you would set the url and baseUrl like:
  //   url: 'https://facebook.github.io',
  //   baseUrl: '/test-site/',

  // Used for publishing and more
  projectName: 'flowable-userguide',
  organizationName: 'flowable',
  // For top-level user or org sites, the organization is still the same.
  // e.g., for the https://JoelMarcey.github.io site, it would be set like...
  //   organizationName: '<PERSON><PERSON><PERSON><PERSON>'

  // For no header links in the top nav bar -> headerLinks: [],
  headerLinks: [
    {doc: 'oss-introduction', label: 'Guides'},
    {page: 'all-javadocs', label: 'Javadocs'},
    {href: 'https://flowable.com/open-source/', label: 'Open source home'},
  ],

  // If you have users set above, you add it here:
  users,

  /* path to images for header/footer */
  headerIcon: 'img/<EMAIL>',
  footerIcon: 'img/<EMAIL>',
  favicon: 'img/favicon.png',

  /* Colors for website */
  colors: {
    primaryColor: '#1f3245',
    secondaryColor: '#a0cc47',
  },

  algolia: {
    apiKey: 'f0c2bdc4f694cbc6509b436055f1dc8d',
    indexName: 'flowable_open-source',
    algoliaOptions: {} // Optional, if provided by Algolia
  },

  /* Custom fonts for website */
  /*
  fonts: {
    myFont: [
      "Times New Roman",
      "Serif"
    ],
    myOtherFont: [
      "-apple-system",
      "system-ui"
    ]
  },
  */

  // This copyright info is used in /core/Footer.js and blog RSS/Atom feeds.
  copyright: `Copyright © ${new Date().getFullYear()} Flowable AG`,

  highlight: {
    // Highlight.js theme to use for syntax highlighting in code blocks.
    theme: 'default',
  },

  // Add custom scripts here that would be placed in <script> tags.
  scripts: ['https://buttons.github.io/buttons.js'],

  // On page navigation for the current documentation page.
  onPageNav: 'separate',
  // No .html extensions for paths.
  cleanUrl: true,
  docsUrl: '',
  scrollToTop: true,

  // Open Graph and Twitter card images.
  ogImage: 'img/undraw_online.svg',
  twitterImage: 'img/undraw_tweetstorm.svg',

  // For sites with a sizable amount of content, set collapsible to true.
  // Expand/collapse the links and subcategories under categories.
  docsSideNavCollapsible: true,

  // Show documentation's last contributor's name.
  // enableUpdateBy: true,

  // Show documentation's last update time.
  // enableUpdateTime: true,

  // You may provide arbitrary config keys to be used as needed by your
  // template. For example, if you need your repo's URL...
  repoUrl: 'https://github.com/flowable/flowable-engine',
};

module.exports = siteConfig;
