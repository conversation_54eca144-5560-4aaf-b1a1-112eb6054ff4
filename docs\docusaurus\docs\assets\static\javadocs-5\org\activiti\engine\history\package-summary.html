<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine.history (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.activiti.engine.history (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/form/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../org/activiti/engine/identity/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.activiti.engine.history</h1>
<div class="docSummary">
<div class="block">Classes related to the <a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history">HistoricActivityInstance</a></td>
<td class="colLast">
<div class="block">Represents one execution of an activity and it's stored permanent for statistics, audit and other business intelligence purposes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricActivityInstanceQuery</a></td>
<td class="colLast">
<div class="block">Programmatic querying for <a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a>s.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricData.html" title="interface in org.activiti.engine.history">HistoricData</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history">HistoricDetail</a></td>
<td class="colLast">
<div class="block">Base class for all kinds of information that is related to 
 either a <a href="../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a> or a <a href="../../../../org/activiti/engine/history/HistoricActivityInstance.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstance</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricDetailQuery.html" title="interface in org.activiti.engine.history">HistoricDetailQuery</a></td>
<td class="colLast">
<div class="block">Programmatic querying for <a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><code>HistoricDetail</code></a>s.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricFormProperty.html" title="interface in org.activiti.engine.history">HistoricFormProperty</a></td>
<td class="colLast">
<div class="block">A single field that was submitted in either a start form or a task form.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricIdentityLink.html" title="interface in org.activiti.engine.history">HistoricIdentityLink</a></td>
<td class="colLast">
<div class="block">Historic counterpart of <a href="../../../../org/activiti/engine/task/IdentityLink.html" title="interface in org.activiti.engine.task"><code>IdentityLink</code></a> that represents the current state
 if any runtime link.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history">HistoricProcessInstance</a></td>
<td class="colLast">
<div class="block">A single execution of a whole process definition that is stored permanently.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricProcessInstanceQuery</a></td>
<td class="colLast">
<div class="block">Allows programmatic querying of <a href="../../../../org/activiti/engine/history/HistoricProcessInstance.html" title="interface in org.activiti.engine.history"><code>HistoricProcessInstance</code></a>s.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history">HistoricTaskInstance</a></td>
<td class="colLast">
<div class="block">Represents a historic task instance (waiting, finished or deleted) that is stored permanent for 
 statistics, audit and other business intelligence purposes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></td>
<td class="colLast">
<div class="block">Allows programmatic querying for <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstance</code></a>s.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history">HistoricVariableInstance</a></td>
<td class="colLast">
<div class="block">A single process variable containing the last value when its process instance has finished.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricVariableInstanceQuery</a></td>
<td class="colLast">
<div class="block">Programmatic querying for <a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><code>HistoricVariableInstance</code></a>s.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/HistoricVariableUpdate.html" title="interface in org.activiti.engine.history">HistoricVariableUpdate</a></td>
<td class="colLast">
<div class="block">Update of a process variable.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/NativeHistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricActivityInstanceQuery</a></td>
<td class="colLast">
<div class="block">Allows querying of <a href="../../../../org/activiti/engine/history/HistoricActivityInstanceQuery.html" title="interface in org.activiti.engine.history"><code>HistoricActivityInstanceQuery</code></a>s via native (SQL) queries</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/NativeHistoricDetailQuery.html" title="interface in org.activiti.engine.history">NativeHistoricDetailQuery</a></td>
<td class="colLast">
<div class="block">Allows querying of <a href="../../../../org/activiti/engine/history/HistoricDetail.html" title="interface in org.activiti.engine.history"><code>HistoricDetail</code></a>s via native (SQL) queries</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/NativeHistoricProcessInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricProcessInstanceQuery</a></td>
<td class="colLast">
<div class="block">Allows querying of <a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstanceQuery</code></a>s via native (SQL) queries</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/NativeHistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricTaskInstanceQuery</a></td>
<td class="colLast">
<div class="block">Allows querying of <a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstanceQuery</code></a>s via native (SQL) queries</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/NativeHistoricVariableInstanceQuery.html" title="interface in org.activiti.engine.history">NativeHistoricVariableInstanceQuery</a></td>
<td class="colLast">
<div class="block">Allows querying of <a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><code>HistoricVariableInstance</code></a>s via native (SQL) queries</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLog</a></td>
<td class="colLast">
<div class="block">A trail of data for a given process instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLogQuery.html" title="interface in org.activiti.engine.history">ProcessInstanceHistoryLogQuery</a></td>
<td class="colLast">
<div class="block">Allows to fetch the <a href="../../../../org/activiti/engine/history/ProcessInstanceHistoryLog.html" title="interface in org.activiti.engine.history"><code>ProcessInstanceHistoryLog</code></a> for a process instance.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package org.activiti.engine.history Description">Package org.activiti.engine.history Description</h2>
<div class="block">Classes related to the <a href="../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/form/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../org/activiti/engine/identity/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
