name: Flowable 5 Build

on:
  push:
    branches:
      - main
      - 'flowable-release-*'

jobs:
  test_jdk:
    name: Flowable 5 Tests
    runs-on: ubuntu-latest
    env:
      # '>-' is a special YAML syntax and means that new lines would be replaced with spaces
      # and new lines from the end would be removed
      MAVEN_CONFIG: >-
        -V
        -B
        --no-transfer-progress
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: 17
    - name: Build and Test
      run: cd scripts && ./run-flowable5-tests.sh
