name: Flowable DB2 Build

on:
  push:
    branches:
      - main
      - 'flowable-release-*'
env:
  MAVEN_ARGS: >-
    -Dmaven.javadoc.skip=true
    -B -V --no-transfer-progress
    -Dhttp.keepAlive=false -Dmaven.wagon.http.pool=false -Dmaven.wagon.httpconnectionManager.ttlSeconds=120

jobs:
  test_db2:
    name: DB2 ${{ matrix.db2 }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        db2: ["********"]
    services:
      db2:
        image: icr.io/db2_community/db2:${{ matrix.db2 }}
        env:
          DB2INST1_PASSWORD: flowable
          DBNAME: flowable
          LICENSE: accept
          ARCHIVE_LOGS: false
          AUTOCONFIG: false
        ports:
          - 50000:50000
        # needed because the db2 container does not provide a health check
        options: >-
          --privileged=true
          --health-cmd="su - db2inst1 -c \"~/sqllib/bin/db2gcf -s\""
          --health-interval 30s
          --health-timeout 40s
          --health-retries 10
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 17
      - name: Test
        # use db2 for the host here because we have specified a container for the job.
        # If we were running the job on the VM this would be localhost
        # '>-' is a special YAML syntax and means that new lines would be replaced with spaces
        # and new lines from the end would be removed
        run: >-
          ./mvnw clean install
          ${MAVEN_ARGS}
          -PcleanDb,db2,distro
          -P'!include-spring-boot-samples'
          -Djdbc.url=********************:${{ job.services.db2.ports[50000] }}/flowable
          -Djdbc.username=db2inst1
          -Djdbc.password=flowable
          -Djdbc.driver=com.ibm.db2.jcc.DB2Driver
          -Dspring.datasource.url=********************:${{ job.services.db2.ports[50000] }}/flowable
          -Dspring.datasource.username=db2inst1
          -Dspring.datasource.password=flowable
          -Dmaven.test.redirectTestOutputToFile=false
