<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Task (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Task (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Task.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/task/NativeTaskQuery.html" title="interface in org.activiti.engine.task"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/task/Task.html" target="_top">Frames</a></li>
<li><a href="Task.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.task</div>
<h2 title="Interface Task" class="title">Interface Task</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">Task</span>
extends <a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a></pre>
<div class="block">Represents one task for a human user.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joram Barrez, Tijs Rademakers</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#DEFAULT_PRIORITY">DEFAULT_PRIORITY</a></span></code>
<div class="block">Default value used for priority when a new <a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a> is created.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#delegate-java.lang.String-">delegate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">delegates this task to the given user and sets the <a href="../../../../org/activiti/engine/task/Task.html#getDelegationState--"><code>delegationState</code></a> to <a href="../../../../org/activiti/engine/task/DelegationState.html#PENDING"><code>DelegationState.PENDING</code></a>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#getDelegationState--">getDelegationState</a></span>()</code>
<div class="block">The current <a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a> for this task.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#isSuspended--">isSuspended</a></span>()</code>
<div class="block">Indicates whether this task is suspended or not.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setAssignee-java.lang.String-">setAssignee</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee)</code>
<div class="block">The <code>userId</code> of the person to which this task is delegated.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setCategory-java.lang.String-">setCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</code>
<div class="block">Change the category of the task.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setDelegationState-org.activiti.engine.task.DelegationState-">setDelegationState</a></span>(<a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a>&nbsp;delegationState)</code>
<div class="block">The current <a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a> for this task.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setDescription-java.lang.String-">setDescription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</code>
<div class="block">Change the description of the task</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setDueDate-java.util.Date-">setDueDate</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>
<div class="block">Change due date of the task.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setFormKey-java.lang.String-">setFormKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;formKey)</code>
<div class="block">Change the form key of the task</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setLocalizedDescription-java.lang.String-">setLocalizedDescription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</code>
<div class="block">Sets an optional localized description for the task.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setLocalizedName-java.lang.String-">setLocalizedName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Sets an optional localized name for the task.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setName-java.lang.String-">setName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Name or title of the task.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setOwner-java.lang.String-">setOwner</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner)</code>
<div class="block">The <code>userId</code> of the person that is responsible for this task.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setParentTaskId-java.lang.String-">setParentTaskId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentTaskId)</code>
<div class="block">the parent task for which this task is a subtask</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setPriority-int-">setPriority</a></span>(int&nbsp;priority)</code>
<div class="block">Sets the indication of how important/urgent this task is</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/task/Task.html#setTenantId-java.lang.String-">setTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Change the tenantId of the task</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.task.TaskInfo">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.task.<a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task">TaskInfo</a></h3>
<code><a href="../../../../org/activiti/engine/task/TaskInfo.html#getAssignee--">getAssignee</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getCategory--">getCategory</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getCreateTime--">getCreateTime</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getDescription--">getDescription</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getDueDate--">getDueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getExecutionId--">getExecutionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getFormKey--">getFormKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getId--">getId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getName--">getName</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getOwner--">getOwner</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getParentTaskId--">getParentTaskId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getPriority--">getPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getProcessDefinitionId--">getProcessDefinitionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getProcessInstanceId--">getProcessInstanceId</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getProcessVariables--">getProcessVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getTaskDefinitionKey--">getTaskDefinitionKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getTaskLocalVariables--">getTaskLocalVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfo.html#getTenantId--">getTenantId</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DEFAULT_PRIORITY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEFAULT_PRIORITY</h4>
<pre>static final&nbsp;int DEFAULT_PRIORITY</pre>
<div class="block">Default value used for priority when a new <a href="../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a> is created.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.activiti.engine.task.Task.DEFAULT_PRIORITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>void&nbsp;setName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Name or title of the task.</div>
</li>
</ul>
<a name="setLocalizedName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocalizedName</h4>
<pre>void&nbsp;setLocalizedName(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Sets an optional localized name for the task.</div>
</li>
</ul>
<a name="setDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescription</h4>
<pre>void&nbsp;setDescription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</pre>
<div class="block">Change the description of the task</div>
</li>
</ul>
<a name="setLocalizedDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocalizedDescription</h4>
<pre>void&nbsp;setLocalizedDescription(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description)</pre>
<div class="block">Sets an optional localized description for the task.</div>
</li>
</ul>
<a name="setPriority-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriority</h4>
<pre>void&nbsp;setPriority(int&nbsp;priority)</pre>
<div class="block">Sets the indication of how important/urgent this task is</div>
</li>
</ul>
<a name="setOwner-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOwner</h4>
<pre>void&nbsp;setOwner(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;owner)</pre>
<div class="block">The <code>userId</code> of the person that is responsible for this task.</div>
</li>
</ul>
<a name="setAssignee-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAssignee</h4>
<pre>void&nbsp;setAssignee(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;assignee)</pre>
<div class="block">The <code>userId</code> of the person to which this task is delegated.</div>
</li>
</ul>
<a name="getDelegationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDelegationState</h4>
<pre><a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a>&nbsp;getDelegationState()</pre>
<div class="block">The current <a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a> for this task.</div>
</li>
</ul>
<a name="setDelegationState-org.activiti.engine.task.DelegationState-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDelegationState</h4>
<pre>void&nbsp;setDelegationState(<a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task">DelegationState</a>&nbsp;delegationState)</pre>
<div class="block">The current <a href="../../../../org/activiti/engine/task/DelegationState.html" title="enum in org.activiti.engine.task"><code>DelegationState</code></a> for this task.</div>
</li>
</ul>
<a name="setDueDate-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDueDate</h4>
<pre>void&nbsp;setDueDate(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</pre>
<div class="block">Change due date of the task.</div>
</li>
</ul>
<a name="setCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCategory</h4>
<pre>void&nbsp;setCategory(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</pre>
<div class="block">Change the category of the task. This is an optional field and allows to 'tag' tasks as belonging to a certain category.</div>
</li>
</ul>
<a name="delegate-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>delegate</h4>
<pre>void&nbsp;delegate(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</pre>
<div class="block">delegates this task to the given user and sets the <a href="../../../../org/activiti/engine/task/Task.html#getDelegationState--"><code>delegationState</code></a> to <a href="../../../../org/activiti/engine/task/DelegationState.html#PENDING"><code>DelegationState.PENDING</code></a>.
 If no owner is set on the task, the owner is set to the current assignee of the task.</div>
</li>
</ul>
<a name="setParentTaskId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParentTaskId</h4>
<pre>void&nbsp;setParentTaskId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentTaskId)</pre>
<div class="block">the parent task for which this task is a subtask</div>
</li>
</ul>
<a name="setTenantId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTenantId</h4>
<pre>void&nbsp;setTenantId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</pre>
<div class="block">Change the tenantId of the task</div>
</li>
</ul>
<a name="setFormKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFormKey</h4>
<pre>void&nbsp;setFormKey(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;formKey)</pre>
<div class="block">Change the form key of the task</div>
</li>
</ul>
<a name="isSuspended--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isSuspended</h4>
<pre>boolean&nbsp;isSuspended()</pre>
<div class="block">Indicates whether this task is suspended or not.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Task.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/task/NativeTaskQuery.html" title="interface in org.activiti.engine.task"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/task/TaskInfo.html" title="interface in org.activiti.engine.task"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/task/Task.html" target="_top">Frames</a></li>
<li><a href="Task.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
