/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.flowable.batch.api;

import java.util.Date;

public interface Batch {

    String PROCESS_MIGRATION_TYPE = "processMigration";

    String CASE_MIGRATION_TYPE = "caseMigration";

    String HISTORIC_PROCESS_DELETE_TYPE = "historicProcessDelete";

    String HISTORIC_CASE_DELETE_TYPE = "historicCaseDelete";

    String getId();

    String getBatchType();

    Date getCreateTime();
    
    Date getCompleteTime();

    String getBatchSearchKey();

    String getBatchSearchKey2();
    
    String getStatus();

    String getBatchDocumentJson(String engineType);
    
    String getTenantId();

}