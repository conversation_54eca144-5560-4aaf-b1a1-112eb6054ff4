# Remediation Module Flowable Integration

## Overview

The Remediation Module integration with Flowable automates incident response, vulnerability remediation, and Plan of Action & Milestones (POA&M) management workflows. This integration provides comprehensive automation for identifying, tracking, and resolving security findings and compliance gaps.

## Integration Architecture

### Remediation Workflow Patterns

#### 1. Incident Response Workflow
Automated incident detection, classification, and response coordination with stakeholder notification.

#### 2. Vulnerability Remediation Workflow
Systematic vulnerability management from discovery through resolution with risk-based prioritization.

#### 3. POA&M Management Workflow
Comprehensive Plan of Action & Milestones tracking with automated milestone monitoring and reporting.

#### 4. Finding Resolution Workflow
Structured resolution process for assessment findings with validation and closure procedures.

## Remediation Service Integration

### Service Task Implementation

#### RemediationManagerTask.java
```java
@Component("remediationManagerTask")
public class RemediationManagerTask extends AbstractGRCOSServiceTask {
    
    @Autowired
    private RemediationService remediationService;
    
    @Autowired
    private OSCALService oscalService;
    
    @Autowired
    private RemediationAgent remediationAgent;
    
    @Override
    protected void validateExecution(DelegateExecution execution) throws ValidationException {
        String operation = (String) execution.getVariable("remediationOperation");
        
        if (operation == null || operation.trim().isEmpty()) {
            throw new ValidationException("Remediation operation is required");
        }
        
        if (!isValidRemediationOperation(operation)) {
            throw new ValidationException("Invalid remediation operation: " + operation);
        }
    }
    
    @Override
    protected TaskExecutionResult executeTask(DelegateExecution execution) throws TaskExecutionException {
        String operation = (String) execution.getVariable("remediationOperation");
        
        try {
            switch (operation) {
                case "create_poam":
                    return createPOAM(execution);
                case "prioritize_findings":
                    return prioritizeFindings(execution);
                case "assign_remediation":
                    return assignRemediation(execution);
                case "execute_remediation":
                    return executeRemediation(execution);
                case "validate_remediation":
                    return validateRemediation(execution);
                case "close_finding":
                    return closeFinding(execution);
                case "escalate_issue":
                    return escalateIssue(execution);
                default:
                    throw new TaskExecutionException("Unsupported remediation operation: " + operation);
            }
        } catch (Exception e) {
            throw new TaskExecutionException("Remediation operation failed", e);
        }
    }
    
    private TaskExecutionResult createPOAM(DelegateExecution execution) {
        String findingId = (String) execution.getVariable("findingId");
        String systemId = (String) execution.getVariable("systemId");
        
        // Get finding details
        OSCALFinding finding = oscalService.getFinding(findingId);
        
        // Analyze finding and determine remediation approach
        RemediationAnalysis analysis = remediationAgent.analyzeFinding(finding);
        
        // Create POA&M entry
        OSCALPOAM poam = oscalService.createPOAM(finding, analysis);
        
        // Generate remediation plan
        RemediationPlan plan = remediationAgent.generateRemediationPlan(finding, analysis);
        
        // Estimate effort and timeline
        RemediationEstimate estimate = remediationAgent.estimateRemediationEffort(plan);
        
        // Store POA&M
        String poamId = remediationService.storePOAM(poam, plan, estimate);
        
        Map<String, Object> results = new HashMap<>();
        results.put("poamId", poamId);
        results.put("poamUuid", poam.getUuid());
        results.put("findingId", findingId);
        results.put("riskLevel", analysis.getRiskLevel());
        results.put("estimatedEffort", estimate.getEffortHours());
        results.put("targetDate", estimate.getTargetCompletionDate());
        results.put("remediationSteps", plan.getSteps().size());
        
        return TaskExecutionResult.success(results);
    }
    
    private TaskExecutionResult prioritizeFindings(DelegateExecution execution) {
        String systemId = (String) execution.getVariable("systemId");
        List<String> findingIds = (List<String>) execution.getVariable("findingIds");
        
        // Get all findings
        List<OSCALFinding> findings = findingIds.stream()
            .map(oscalService::getFinding)
            .collect(Collectors.toList());
        
        // Perform risk-based prioritization
        FindingsPrioritization prioritization = remediationAgent.prioritizeFindings(findings);
        
        // Update finding priorities
        for (FindingPriority priority : prioritization.getPriorities()) {
            remediationService.updateFindingPriority(priority.getFindingId(), 
                                                   priority.getPriorityLevel());
        }
        
        // Generate prioritization report
        PrioritizationReport report = generatePrioritizationReport(prioritization);
        
        Map<String, Object> results = new HashMap<>();
        results.put("systemId", systemId);
        results.put("findingsProcessed", findings.size());
        results.put("criticalFindings", prioritization.getCriticalCount());
        results.put("highFindings", prioritization.getHighCount());
        results.put("mediumFindings", prioritization.getMediumCount());
        results.put("lowFindings", prioritization.getLowCount());
        results.put("reportId", report.getId());
        
        return TaskExecutionResult.success(results);
    }
    
    private TaskExecutionResult assignRemediation(DelegateExecution execution) {
        String poamId = (String) execution.getVariable("poamId");
        
        // Get POA&M details
        OSCALPOAM poam = remediationService.getPOAM(poamId);
        RemediationPlan plan = remediationService.getRemediationPlan(poamId);
        
        // Determine optimal assignment
        RemediationAssignment assignment = remediationAgent.determineOptimalAssignment(
            poam, plan);
        
        // Assign remediation tasks
        List<RemediationTask> tasks = createRemediationTasks(plan, assignment);
        
        // Store assignments
        remediationService.storeAssignments(poamId, assignment, tasks);
        
        // Notify assigned personnel
        notifyAssignedPersonnel(assignment, tasks);
        
        Map<String, Object> results = new HashMap<>();
        results.put("poamId", poamId);
        results.put("assignedTo", assignment.getPrimaryAssignee());
        results.put("tasksCreated", tasks.size());
        results.put("estimatedStartDate", assignment.getEstimatedStartDate());
        results.put("targetCompletionDate", assignment.getTargetCompletionDate());
        
        return TaskExecutionResult.success(results);
    }
    
    private TaskExecutionResult executeRemediation(DelegateExecution execution) {
        String remediationTaskId = (String) execution.getVariable("remediationTaskId");
        
        // Get remediation task
        RemediationTask task = remediationService.getRemediationTask(remediationTaskId);
        
        // Determine execution method
        String executionMethod = remediationAgent.determineExecutionMethod(task);
        
        RemediationExecutionResult result;
        switch (executionMethod) {
            case "automated":
                result = executeAutomatedRemediation(task);
                break;
            case "manual":
                result = executeManualRemediation(task);
                break;
            case "hybrid":
                result = executeHybridRemediation(task);
                break;
            default:
                throw new TaskExecutionException("Unknown execution method: " + executionMethod);
        }
        
        // Update task status
        remediationService.updateTaskStatus(remediationTaskId, result);
        
        // Update POA&M progress
        updatePOAMProgress(task.getPoamId());
        
        Map<String, Object> taskResults = new HashMap<>();
        taskResults.put("remediationTaskId", remediationTaskId);
        taskResults.put("executionMethod", executionMethod);
        taskResults.put("executionStatus", result.getStatus());
        taskResults.put("successRate", result.getSuccessRate());
        taskResults.put("executionTime", result.getExecutionTime());
        
        return TaskExecutionResult.success(taskResults);
    }
    
    private TaskExecutionResult validateRemediation(DelegateExecution execution) {
        String poamId = (String) execution.getVariable("poamId");
        
        // Get POA&M and associated finding
        OSCALPOAM poam = remediationService.getPOAM(poamId);
        OSCALFinding finding = oscalService.getFinding(poam.getFindingId());
        
        // Perform remediation validation
        RemediationValidationResult validation = remediationAgent.validateRemediation(
            finding, poam);
        
        // Update POA&M status based on validation
        if (validation.isValid()) {
            remediationService.updatePOAMStatus(poamId, "remediated");
        } else {
            remediationService.updatePOAMStatus(poamId, "validation-failed");
        }
        
        // Generate validation report
        ValidationReport report = generateValidationReport(validation);
        
        Map<String, Object> results = new HashMap<>();
        results.put("poamId", poamId);
        results.put("validationResult", validation.isValid());
        results.put("validationScore", validation.getValidationScore());
        results.put("remainingIssues", validation.getRemainingIssues().size());
        results.put("reportId", report.getId());
        
        return TaskExecutionResult.success(results);
    }
    
    private TaskExecutionResult closeFinding(DelegateExecution execution) {
        String findingId = (String) execution.getVariable("findingId");
        String closureReason = (String) execution.getVariable("closureReason");
        
        // Get finding and validation evidence
        OSCALFinding finding = oscalService.getFinding(findingId);
        
        // Perform final validation
        FindingClosureValidation validation = remediationAgent.validateFindingClosure(
            finding, closureReason);
        
        if (validation.isValid()) {
            // Close finding
            oscalService.closeFinding(findingId, closureReason, validation.getEvidence());
            
            // Update related POA&M
            String poamId = remediationService.getPOAMByFindingId(findingId);
            if (poamId != null) {
                remediationService.updatePOAMStatus(poamId, "closed");
            }
            
            // Generate closure documentation
            ClosureDocumentation documentation = generateClosureDocumentation(
                finding, validation);
            
            Map<String, Object> results = new HashMap<>();
            results.put("findingId", findingId);
            results.put("closureStatus", "closed");
            results.put("closureReason", closureReason);
            results.put("documentationId", documentation.getId());
            
            return TaskExecutionResult.success(results);
        } else {
            Map<String, Object> results = new HashMap<>();
            results.put("findingId", findingId);
            results.put("closureStatus", "validation-failed");
            results.put("validationErrors", validation.getErrors());
            
            return TaskExecutionResult.success(results);
        }
    }
    
    private RemediationExecutionResult executeAutomatedRemediation(RemediationTask task) {
        // Get automated remediation tools
        List<RemediationTool> tools = remediationAgent.selectRemediationTools(task);
        
        List<String> executionResults = new ArrayList<>();
        int successCount = 0;
        
        for (RemediationTool tool : tools) {
            try {
                ToolExecutionResult result = tool.execute(task);
                executionResults.add(result.getMessage());
                
                if (result.isSuccess()) {
                    successCount++;
                }
            } catch (Exception e) {
                executionResults.add("Tool execution failed: " + e.getMessage());
            }
        }
        
        double successRate = tools.isEmpty() ? 0.0 : (double) successCount / tools.size();
        
        return RemediationExecutionResult.builder()
            .status(successRate > 0.8 ? "completed" : "partially-completed")
            .successRate(successRate)
            .executionResults(executionResults)
            .executionTime(System.currentTimeMillis())
            .build();
    }
    
    private RemediationExecutionResult executeManualRemediation(RemediationTask task) {
        // Create manual remediation work item
        ManualRemediationWorkItem workItem = remediationAgent.createManualWorkItem(task);
        
        return RemediationExecutionResult.builder()
            .status("pending_manual_completion")
            .workItemId(workItem.getId())
            .executionTime(System.currentTimeMillis())
            .build();
    }
}
```

### Incident Response Workflow

#### incident-response.bpmn20.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://grcos.com/workflows">
  
  <process id="incident-response" name="Incident Response Workflow" isExecutable="true">
    
    <!-- Start Event -->
    <startEvent id="incident-detected" name="Incident Detected">
      <messageEventDefinition messageRef="IncidentMessage"/>
    </startEvent>
    
    <!-- Initial Triage -->
    <userTask id="initial-triage" name="Initial Incident Triage"
              flowable:candidateGroups="incident-responders">
      <documentation>Perform initial triage and classification of the incident</documentation>
      <extensionElements>
        <flowable:formProperty id="incidentType" name="Incident Type" type="enum" required="true">
          <flowable:value id="security-breach" name="Security Breach"/>
          <flowable:value id="data-loss" name="Data Loss"/>
          <flowable:value id="system-compromise" name="System Compromise"/>
          <flowable:value id="malware" name="Malware"/>
          <flowable:value id="unauthorized-access" name="Unauthorized Access"/>
        </flowable:formProperty>
        <flowable:formProperty id="severity" name="Severity" type="enum" required="true">
          <flowable:value id="low" name="Low"/>
          <flowable:value id="medium" name="Medium"/>
          <flowable:value id="high" name="High"/>
          <flowable:value id="critical" name="Critical"/>
        </flowable:formProperty>
        <flowable:formProperty id="affectedSystems" name="Affected Systems" type="string" required="true"/>
        <flowable:formProperty id="initialAssessment" name="Initial Assessment" type="string" required="true"/>
      </extensionElements>
    </userTask>
    
    <!-- Create POA&M -->
    <serviceTask id="create-poam" name="Create POA&M"
                 flowable:class="com.grcos.workflow.RemediationManagerTask">
      <extensionElements>
        <flowable:field name="remediationOperation">
          <flowable:string>create_poam</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Severity Gateway -->
    <exclusiveGateway id="severity-gateway" name="Incident Severity"/>
    
    <!-- Critical Incident Path -->
    <userTask id="escalate-critical" name="Escalate Critical Incident"
              flowable:assignee="ciso">
      <documentation>Escalate critical incident to CISO</documentation>
      <extensionElements>
        <flowable:formProperty id="escalationDecision" name="Escalation Decision" type="enum" required="true">
          <flowable:value id="activate-crisis-team" name="Activate Crisis Team"/>
          <flowable:value id="external-support" name="Engage External Support"/>
          <flowable:value id="standard-response" name="Standard Response"/>
        </flowable:formProperty>
        <flowable:formProperty id="escalationNotes" name="Escalation Notes" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Assign Response Team -->
    <serviceTask id="assign-response-team" name="Assign Response Team"
                 flowable:class="com.grcos.workflow.RemediationManagerTask">
      <extensionElements>
        <flowable:field name="remediationOperation">
          <flowable:string>assign_remediation</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Parallel Response Activities -->
    <parallelGateway id="parallel-start" name="Start Parallel Response"/>
    
    <!-- Containment Branch -->
    <userTask id="containment" name="Incident Containment"
              flowable:candidateGroups="incident-responders">
      <documentation>Contain the incident to prevent further damage</documentation>
      <extensionElements>
        <flowable:formProperty id="containmentActions" name="Containment Actions" type="string" required="true"/>
        <flowable:formProperty id="containmentEffective" name="Containment Effective" type="boolean" required="true"/>
        <flowable:formProperty id="additionalMeasures" name="Additional Measures Needed" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Investigation Branch -->
    <serviceTask id="automated-investigation" name="Automated Investigation"
                 flowable:class="com.grcos.workflow.InvestigationTask"/>
    
    <userTask id="manual-investigation" name="Manual Investigation"
              flowable:candidateGroups="forensic-analysts">
      <documentation>Perform detailed manual investigation</documentation>
      <extensionElements>
        <flowable:formProperty id="investigationFindings" name="Investigation Findings" type="string" required="true"/>
        <flowable:formProperty id="rootCause" name="Root Cause" type="string"/>
        <flowable:formProperty id="evidenceCollected" name="Evidence Collected" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Communication Branch -->
    <serviceTask id="stakeholder-notification" name="Notify Stakeholders"
                 flowable:class="com.grcos.workflow.NotificationTask">
      <extensionElements>
        <flowable:field name="notificationType">
          <flowable:string>incident-response-active</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Parallel End -->
    <parallelGateway id="parallel-end" name="End Parallel Response"/>
    
    <!-- Execute Remediation -->
    <serviceTask id="execute-remediation" name="Execute Remediation"
                 flowable:class="com.grcos.workflow.RemediationManagerTask">
      <extensionElements>
        <flowable:field name="remediationOperation">
          <flowable:string>execute_remediation</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Validate Remediation -->
    <serviceTask id="validate-remediation" name="Validate Remediation"
                 flowable:class="com.grcos.workflow.RemediationManagerTask">
      <extensionElements>
        <flowable:field name="remediationOperation">
          <flowable:string>validate_remediation</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Validation Gateway -->
    <exclusiveGateway id="validation-gateway" name="Remediation Valid?"/>
    
    <!-- Close Incident -->
    <serviceTask id="close-incident" name="Close Incident"
                 flowable:class="com.grcos.workflow.RemediationManagerTask">
      <extensionElements>
        <flowable:field name="remediationOperation">
          <flowable:string>close_finding</flowable:string>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Post-Incident Review -->
    <userTask id="post-incident-review" name="Post-Incident Review"
              flowable:candidateGroups="incident-review-board">
      <documentation>Conduct post-incident review and lessons learned</documentation>
      <extensionElements>
        <flowable:formProperty id="lessonsLearned" name="Lessons Learned" type="string" required="true"/>
        <flowable:formProperty id="processImprovements" name="Process Improvements" type="string"/>
        <flowable:formProperty id="preventiveMeasures" name="Preventive Measures" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- End Event -->
    <endEvent id="incident-resolved" name="Incident Resolved"/>
    
    <!-- Sequence Flows -->
    <sequenceFlow id="flow1" sourceRef="incident-detected" targetRef="initial-triage"/>
    <sequenceFlow id="flow2" sourceRef="initial-triage" targetRef="create-poam"/>
    <sequenceFlow id="flow3" sourceRef="create-poam" targetRef="severity-gateway"/>
    
    <!-- Severity-based flows -->
    <sequenceFlow id="flow4" sourceRef="severity-gateway" targetRef="escalate-critical">
      <conditionExpression>${severity == 'critical'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow5" sourceRef="severity-gateway" targetRef="assign-response-team">
      <conditionExpression>${severity != 'critical'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow6" sourceRef="escalate-critical" targetRef="assign-response-team"/>
    
    <!-- Parallel response flows -->
    <sequenceFlow id="flow7" sourceRef="assign-response-team" targetRef="parallel-start"/>
    <sequenceFlow id="flow8" sourceRef="parallel-start" targetRef="containment"/>
    <sequenceFlow id="flow9" sourceRef="parallel-start" targetRef="automated-investigation"/>
    <sequenceFlow id="flow10" sourceRef="parallel-start" targetRef="stakeholder-notification"/>
    <sequenceFlow id="flow11" sourceRef="containment" targetRef="parallel-end"/>
    <sequenceFlow id="flow12" sourceRef="automated-investigation" targetRef="manual-investigation"/>
    <sequenceFlow id="flow13" sourceRef="manual-investigation" targetRef="parallel-end"/>
    <sequenceFlow id="flow14" sourceRef="stakeholder-notification" targetRef="parallel-end"/>
    
    <!-- Remediation flows -->
    <sequenceFlow id="flow15" sourceRef="parallel-end" targetRef="execute-remediation"/>
    <sequenceFlow id="flow16" sourceRef="execute-remediation" targetRef="validate-remediation"/>
    <sequenceFlow id="flow17" sourceRef="validate-remediation" targetRef="validation-gateway"/>
    <sequenceFlow id="flow18" sourceRef="validation-gateway" targetRef="close-incident">
      <conditionExpression>${validationResult == true}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow19" sourceRef="validation-gateway" targetRef="execute-remediation">
      <conditionExpression>${validationResult == false}</conditionExpression>
    </sequenceFlow>
    
    <!-- Closure flows -->
    <sequenceFlow id="flow20" sourceRef="close-incident" targetRef="post-incident-review"/>
    <sequenceFlow id="flow21" sourceRef="post-incident-review" targetRef="incident-resolved"/>
    
  </process>
  
  <!-- Message Definition -->
  <message id="IncidentMessage" name="IncidentAlert"/>
  
</definitions>
```

### POA&M Management Dashboard

#### POAMDashboardService.java
```java
@Service
public class POAMDashboardService {
    
    @Autowired
    private RemediationService remediationService;
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    public POAMDashboard generateDashboard() {
        // Get all active POA&Ms
        List<OSCALPOAM> activePoams = remediationService.getActivePOAMs();
        
        // Calculate metrics
        POAMMetrics metrics = calculatePOAMMetrics(activePoams);
        
        // Get overdue items
        List<OSCALPOAM> overduePoams = remediationService.getOverduePOAMs();
        
        // Get upcoming milestones
        List<POAMMilestone> upcomingMilestones = remediationService.getUpcomingMilestones();
        
        return POAMDashboard.builder()
            .totalPoams(activePoams.size())
            .overduePoams(overduePoams.size())
            .upcomingMilestones(upcomingMilestones.size())
            .metrics(metrics)
            .riskDistribution(calculateRiskDistribution(activePoams))
            .completionTrends(calculateCompletionTrends())
            .build();
    }
    
    private POAMMetrics calculatePOAMMetrics(List<OSCALPOAM> poams) {
        return POAMMetrics.builder()
            .criticalCount(countByRisk(poams, "critical"))
            .highCount(countByRisk(poams, "high"))
            .mediumCount(countByRisk(poams, "medium"))
            .lowCount(countByRisk(poams, "low"))
            .averageAge(calculateAverageAge(poams))
            .completionRate(calculateCompletionRate())
            .build();
    }
}
```

This Remediation Module integration provides comprehensive automation for incident response and vulnerability remediation with intelligent prioritization and tracking capabilities.
