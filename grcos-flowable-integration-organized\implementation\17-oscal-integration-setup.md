# OSCAL Integration Setup for GRCOS Flowable

## Overview

This guide provides detailed instructions for integrating OSCAL (Open Security Controls Assessment Language) with the GRCOS Flowable workflow engine. The integration enables automatic workflow generation from OSCAL documents, real-time compliance tracking, and seamless data exchange between OSCAL artifacts and workflow processes.

## OSCAL Architecture Integration

### Component Overview

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        GRCOS OSCAL Integration Architecture                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        OSCAL Document Layer                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │   Catalog   │  │   Profile   │  │    SSP      │  │ Assessment  │       │ │
│  │  │             │  │             │  │             │  │    Plan     │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                     OSCAL Processing Layer                                  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │   Parser    │  │ Validator   │  │ Transformer │  │  Generator  │       │ │
│  │  │   Service   │  │   Service   │  │   Service   │  │   Service   │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                    Workflow Integration Layer                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       │ │
│  │  │  Workflow   │  │ Assessment  │  │ Compliance  │  │   Report    │       │ │
│  │  │ Generator   │  │  Executor   │  │  Monitor    │  │ Generator   │       │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│         │                   │                   │                   │           │
│         ▼                   ▼                   ▼                   ▼           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                      Flowable Engine                                        │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## OSCAL Service Implementation

### Core OSCAL Service

#### OSCALService.java
```java
@Service
@Transactional
public class OSCALService {
    
    @Autowired
    private OSCALRepository oscalRepository;
    
    @Autowired
    private OSCALValidator oscalValidator;
    
    @Autowired
    private OSCALTransformer oscalTransformer;
    
    @Autowired
    private WorkflowGeneratorService workflowGenerator;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    public OSCALDocument parseOSCALDocument(String documentContent, OSCALDocumentType type) {
        try {
            // Validate document structure
            ValidationResult validation = oscalValidator.validateDocument(documentContent, type);
            if (!validation.isValid()) {
                throw new OSCALValidationException("Invalid OSCAL document", validation.getErrors());
            }
            
            // Parse based on document type
            OSCALDocument document = switch (type) {
                case CATALOG -> parseOSCALCatalog(documentContent);
                case PROFILE -> parseOSCALProfile(documentContent);
                case SYSTEM_SECURITY_PLAN -> parseOSCALSSP(documentContent);
                case ASSESSMENT_PLAN -> parseOSCALAssessmentPlan(documentContent);
                case ASSESSMENT_RESULTS -> parseOSCALAssessmentResults(documentContent);
                case PLAN_OF_ACTION_AND_MILESTONES -> parseOSCALPOAM(documentContent);
            };
            
            // Store document
            document = oscalRepository.save(document);
            
            // Generate workflows if applicable
            if (shouldGenerateWorkflows(document)) {
                generateWorkflowsFromDocument(document);
            }
            
            return document;
            
        } catch (Exception e) {
            throw new OSCALProcessingException("Failed to parse OSCAL document", e);
        }
    }
    
    public OSCALCatalog parseOSCALCatalog(String catalogContent) {
        try {
            JsonNode catalogNode = objectMapper.readTree(catalogContent);
            JsonNode catalog = catalogNode.get("catalog");
            
            OSCALCatalog oscalCatalog = OSCALCatalog.builder()
                .uuid(catalog.get("uuid").asText())
                .metadata(parseMetadata(catalog.get("metadata")))
                .groups(parseGroups(catalog.get("groups")))
                .controls(parseControls(catalog.get("controls")))
                .backMatter(parseBackMatter(catalog.get("back-matter")))
                .build();
            
            return oscalCatalog;
            
        } catch (Exception e) {
            throw new OSCALParsingException("Failed to parse OSCAL catalog", e);
        }
    }
    
    public OSCALAssessmentPlan parseOSCALAssessmentPlan(String planContent) {
        try {
            JsonNode planNode = objectMapper.readTree(planContent);
            JsonNode assessmentPlan = planNode.get("assessment-plan");
            
            OSCALAssessmentPlan oscalPlan = OSCALAssessmentPlan.builder()
                .uuid(assessmentPlan.get("uuid").asText())
                .metadata(parseMetadata(assessmentPlan.get("metadata")))
                .importSsp(parseImportSSP(assessmentPlan.get("import-ssp")))
                .localDefinitions(parseLocalDefinitions(assessmentPlan.get("local-definitions")))
                .reviewedControls(parseReviewedControls(assessmentPlan.get("reviewed-controls")))
                .assessmentSubjects(parseAssessmentSubjects(assessmentPlan.get("assessment-subjects")))
                .assessmentAssets(parseAssessmentAssets(assessmentPlan.get("assessment-assets")))
                .tasks(parseTasks(assessmentPlan.get("tasks")))
                .build();
            
            return oscalPlan;
            
        } catch (Exception e) {
            throw new OSCALParsingException("Failed to parse OSCAL assessment plan", e);
        }
    }
    
    public WorkflowDefinition generateWorkflowFromAssessmentPlan(OSCALAssessmentPlan assessmentPlan) {
        try {
            // Extract workflow requirements from assessment plan
            WorkflowRequirements requirements = extractWorkflowRequirements(assessmentPlan);
            
            // Generate workflow definition
            WorkflowDefinition workflow = workflowGenerator.generateAssessmentWorkflow(requirements);
            
            // Add OSCAL-specific variables
            workflow.addVariable("oscalAssessmentPlanUuid", assessmentPlan.getUuid());
            workflow.addVariable("assessmentScope", determineAssessmentScope(assessmentPlan));
            workflow.addVariable("reviewedControls", assessmentPlan.getReviewedControls());
            
            // Deploy workflow
            String deploymentId = workflowGenerator.deployWorkflow(workflow);
            
            // Link workflow to OSCAL document
            linkWorkflowToOSCAL(assessmentPlan.getUuid(), deploymentId);
            
            return workflow;
            
        } catch (Exception e) {
            throw new WorkflowGenerationException("Failed to generate workflow from assessment plan", e);
        }
    }
    
    public OSCALAssessmentResults createAssessmentResults(String assessmentPlanUuid, 
                                                         List<AssessmentResult> results) {
        try {
            // Get original assessment plan
            OSCALAssessmentPlan plan = oscalRepository.findAssessmentPlanByUuid(assessmentPlanUuid);
            
            // Create assessment results document
            OSCALAssessmentResults assessmentResults = OSCALAssessmentResults.builder()
                .uuid(UUID.randomUUID().toString())
                .metadata(createResultsMetadata(plan))
                .importAp(createImportAP(plan))
                .localDefinitions(plan.getLocalDefinitions())
                .results(transformToOSCALResults(results))
                .build();
            
            // Store results
            assessmentResults = oscalRepository.save(assessmentResults);
            
            // Update related workflows
            updateWorkflowsWithResults(assessmentPlanUuid, assessmentResults);
            
            return assessmentResults;
            
        } catch (Exception e) {
            throw new OSCALProcessingException("Failed to create assessment results", e);
        }
    }
    
    private WorkflowRequirements extractWorkflowRequirements(OSCALAssessmentPlan plan) {
        WorkflowRequirements requirements = new WorkflowRequirements();
        
        // Extract assessment activities from tasks
        List<AssessmentActivity> activities = new ArrayList<>();
        for (OSCALTask task : plan.getTasks()) {
            AssessmentActivity activity = AssessmentActivity.builder()
                .id(task.getUuid())
                .title(task.getTitle())
                .description(task.getDescription())
                .type(determineActivityType(task))
                .subjects(extractSubjects(task, plan.getAssessmentSubjects()))
                .assets(extractAssets(task, plan.getAssessmentAssets()))
                .build();
            activities.add(activity);
        }
        requirements.setActivities(activities);
        
        // Extract control requirements
        List<ControlRequirement> controlRequirements = new ArrayList<>();
        for (OSCALReviewedControl control : plan.getReviewedControls().getControlSelections()) {
            ControlRequirement requirement = ControlRequirement.builder()
                .controlId(control.getControlId())
                .includeAll(control.isIncludeAll())
                .includedControls(control.getIncludedControls())
                .excludedControls(control.getExcludedControls())
                .build();
            controlRequirements.add(requirement);
        }
        requirements.setControlRequirements(controlRequirements);
        
        // Extract timing requirements
        if (plan.getMetadata().getProps() != null) {
            for (OSCALProperty prop : plan.getMetadata().getProps()) {
                if ("assessment-duration".equals(prop.getName())) {
                    requirements.setEstimatedDuration(Duration.parse(prop.getValue()));
                } else if ("assessment-deadline".equals(prop.getName())) {
                    requirements.setDeadline(Instant.parse(prop.getValue()));
                }
            }
        }
        
        return requirements;
    }
    
    private List<OSCALControl> parseControls(JsonNode controlsNode) {
        List<OSCALControl> controls = new ArrayList<>();
        
        if (controlsNode != null && controlsNode.isArray()) {
            for (JsonNode controlNode : controlsNode) {
                OSCALControl control = OSCALControl.builder()
                    .id(controlNode.get("id").asText())
                    .clazz(controlNode.has("class") ? controlNode.get("class").asText() : null)
                    .title(controlNode.get("title").asText())
                    .params(parseParameters(controlNode.get("params")))
                    .props(parseProperties(controlNode.get("props")))
                    .links(parseLinks(controlNode.get("links")))
                    .parts(parseParts(controlNode.get("parts")))
                    .controls(parseControls(controlNode.get("controls"))) // Recursive for sub-controls
                    .build();
                controls.add(control);
            }
        }
        
        return controls;
    }
    
    private OSCALMetadata parseMetadata(JsonNode metadataNode) {
        if (metadataNode == null) return null;
        
        return OSCALMetadata.builder()
            .title(metadataNode.get("title").asText())
            .published(metadataNode.has("published") ? 
                Instant.parse(metadataNode.get("published").asText()) : null)
            .lastModified(metadataNode.has("last-modified") ? 
                Instant.parse(metadataNode.get("last-modified").asText()) : null)
            .version(metadataNode.has("version") ? metadataNode.get("version").asText() : null)
            .oscalVersion(metadataNode.has("oscal-version") ? 
                metadataNode.get("oscal-version").asText() : null)
            .props(parseProperties(metadataNode.get("props")))
            .links(parseLinks(metadataNode.get("links")))
            .roles(parseRoles(metadataNode.get("roles")))
            .parties(parseParties(metadataNode.get("parties")))
            .responsibleParties(parseResponsibleParties(metadataNode.get("responsible-parties")))
            .build();
    }
}
```

### OSCAL Workflow Generator

#### OSCALWorkflowGenerator.java
```java
@Component
public class OSCALWorkflowGenerator {
    
    @Autowired
    private FlowableRepositoryService repositoryService;
    
    @Autowired
    private BpmnModelBuilder bpmnModelBuilder;
    
    public String generateAssessmentWorkflow(OSCALAssessmentPlan assessmentPlan) {
        try {
            // Create BPMN model
            BpmnModel bpmnModel = new BpmnModel();
            
            // Create main process
            Process process = createMainProcess(assessmentPlan);
            bpmnModel.addProcess(process);
            
            // Add start event
            StartEvent startEvent = createStartEvent(assessmentPlan);
            process.addFlowElement(startEvent);
            
            // Generate activities from OSCAL tasks
            List<FlowElement> activities = generateActivitiesFromTasks(assessmentPlan.getTasks());
            activities.forEach(process::addFlowElement);
            
            // Add gateways for parallel execution
            List<Gateway> gateways = createParallelGateways(assessmentPlan);
            gateways.forEach(process::addFlowElement);
            
            // Add end event
            EndEvent endEvent = createEndEvent(assessmentPlan);
            process.addFlowElement(endEvent);
            
            // Create sequence flows
            List<SequenceFlow> sequenceFlows = createSequenceFlows(process.getFlowElements());
            sequenceFlows.forEach(process::addFlowElement);
            
            // Deploy workflow
            Deployment deployment = repositoryService.createDeployment()
                .name("OSCAL Assessment Workflow - " + assessmentPlan.getMetadata().getTitle())
                .addBpmnModel("assessment-workflow.bpmn20.xml", bpmnModel)
                .deploy();
            
            return deployment.getId();
            
        } catch (Exception e) {
            throw new WorkflowGenerationException("Failed to generate assessment workflow", e);
        }
    }
    
    private List<FlowElement> generateActivitiesFromTasks(List<OSCALTask> tasks) {
        List<FlowElement> activities = new ArrayList<>();
        
        for (OSCALTask task : tasks) {
            // Determine activity type based on task properties
            ActivityType activityType = determineActivityType(task);
            
            FlowElement activity = switch (activityType) {
                case SERVICE_TASK -> createServiceTask(task);
                case USER_TASK -> createUserTask(task);
                case SCRIPT_TASK -> createScriptTask(task);
                case CALL_ACTIVITY -> createCallActivity(task);
            };
            
            activities.add(activity);
        }
        
        return activities;
    }
    
    private ServiceTask createServiceTask(OSCALTask task) {
        ServiceTask serviceTask = new ServiceTask();
        serviceTask.setId("task_" + task.getUuid().replace("-", "_"));
        serviceTask.setName(task.getTitle());
        
        // Set implementation based on task type
        if (isAutomatedAssessment(task)) {
            serviceTask.setImplementation("${assessmentManagerTask}");
            serviceTask.setImplementationType(ImplementationType.IMPLEMENTATION_TYPE_DELEGATEEXPRESSION);
            
            // Add task parameters
            addTaskParameters(serviceTask, task);
        }
        
        return serviceTask;
    }
    
    private UserTask createUserTask(OSCALTask task) {
        UserTask userTask = new UserTask();
        userTask.setId("task_" + task.getUuid().replace("-", "_"));
        userTask.setName(task.getTitle());
        userTask.setDocumentation(task.getDescription());
        
        // Set assignee based on task properties
        String assignee = extractAssignee(task);
        if (assignee != null) {
            userTask.setAssignee(assignee);
        }
        
        // Set candidate groups
        List<String> candidateGroups = extractCandidateGroups(task);
        if (!candidateGroups.isEmpty()) {
            userTask.setCandidateGroups(candidateGroups);
        }
        
        // Add form properties
        List<FormProperty> formProperties = createFormProperties(task);
        userTask.setFormProperties(formProperties);
        
        return userTask;
    }
    
    private void addTaskParameters(ServiceTask serviceTask, OSCALTask task) {
        List<FieldExtension> fieldExtensions = new ArrayList<>();
        
        // Add operation type
        FieldExtension operationField = new FieldExtension();
        operationField.setFieldName("operation");
        operationField.setStringValue(determineOperation(task));
        fieldExtensions.add(operationField);
        
        // Add task UUID
        FieldExtension uuidField = new FieldExtension();
        uuidField.setFieldName("taskUuid");
        uuidField.setStringValue(task.getUuid());
        fieldExtensions.add(uuidField);
        
        // Add assessment method
        if (task.getAssessmentMethod() != null) {
            FieldExtension methodField = new FieldExtension();
            methodField.setFieldName("assessmentMethod");
            methodField.setStringValue(task.getAssessmentMethod());
            fieldExtensions.add(methodField);
        }
        
        serviceTask.setFieldExtensions(fieldExtensions);
    }
}
```

### OSCAL Data Models

#### OSCALDocument.java
```java
@Entity
@Table(name = "oscal_documents")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "document_type")
public abstract class OSCALDocument {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String uuid;
    
    @Embedded
    private OSCALMetadata metadata;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "document_type", insertable = false, updatable = false)
    private OSCALDocumentType documentType;
    
    @Column(columnDefinition = "TEXT")
    private String rawContent;
    
    @CreationTimestamp
    private Instant createdAt;
    
    @UpdateTimestamp
    private Instant updatedAt;
    
    @Version
    private Long version;
    
    // Getters and setters
}

@Entity
@DiscriminatorValue("CATALOG")
public class OSCALCatalog extends OSCALDocument {
    
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "catalog_id")
    private List<OSCALGroup> groups = new ArrayList<>();
    
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "catalog_id")
    private List<OSCALControl> controls = new ArrayList<>();
    
    @Embedded
    private OSCALBackMatter backMatter;
    
    // Getters and setters
}

@Entity
@DiscriminatorValue("ASSESSMENT_PLAN")
public class OSCALAssessmentPlan extends OSCALDocument {
    
    @Embedded
    private OSCALImportSSP importSsp;
    
    @Embedded
    private OSCALLocalDefinitions localDefinitions;
    
    @Embedded
    private OSCALReviewedControls reviewedControls;
    
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "assessment_plan_id")
    private List<OSCALAssessmentSubject> assessmentSubjects = new ArrayList<>();
    
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "assessment_plan_id")
    private List<OSCALAssessmentAsset> assessmentAssets = new ArrayList<>();
    
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "assessment_plan_id")
    private List<OSCALTask> tasks = new ArrayList<>();
    
    // Getters and setters
}
```

### OSCAL Validation Service

#### OSCALValidator.java
```java
@Service
public class OSCALValidator {
    
    private final Map<OSCALDocumentType, JsonSchema> schemas = new HashMap<>();
    
    @PostConstruct
    public void initializeSchemas() {
        try {
            // Load OSCAL JSON schemas
            schemas.put(OSCALDocumentType.CATALOG, loadSchema("oscal-catalog-schema.json"));
            schemas.put(OSCALDocumentType.PROFILE, loadSchema("oscal-profile-schema.json"));
            schemas.put(OSCALDocumentType.SYSTEM_SECURITY_PLAN, loadSchema("oscal-ssp-schema.json"));
            schemas.put(OSCALDocumentType.ASSESSMENT_PLAN, loadSchema("oscal-ap-schema.json"));
            schemas.put(OSCALDocumentType.ASSESSMENT_RESULTS, loadSchema("oscal-ar-schema.json"));
            schemas.put(OSCALDocumentType.PLAN_OF_ACTION_AND_MILESTONES, loadSchema("oscal-poam-schema.json"));
        } catch (Exception e) {
            throw new OSCALInitializationException("Failed to load OSCAL schemas", e);
        }
    }
    
    public ValidationResult validateDocument(String documentContent, OSCALDocumentType type) {
        try {
            JsonSchema schema = schemas.get(type);
            if (schema == null) {
                throw new OSCALValidationException("No schema found for document type: " + type);
            }
            
            JsonNode documentNode = objectMapper.readTree(documentContent);
            Set<ValidationMessage> validationMessages = schema.validate(documentNode);
            
            List<ValidationError> errors = validationMessages.stream()
                .map(this::convertToValidationError)
                .collect(Collectors.toList());
            
            return ValidationResult.builder()
                .valid(errors.isEmpty())
                .errors(errors)
                .documentType(type)
                .build();
            
        } catch (Exception e) {
            return ValidationResult.builder()
                .valid(false)
                .errors(List.of(new ValidationError("PARSING_ERROR", e.getMessage())))
                .documentType(type)
                .build();
        }
    }
    
    public BusinessValidationResult validateBusinessRules(OSCALDocument document) {
        List<BusinessValidationError> errors = new ArrayList<>();
        
        // Validate document-specific business rules
        switch (document.getDocumentType()) {
            case ASSESSMENT_PLAN -> validateAssessmentPlanBusinessRules((OSCALAssessmentPlan) document, errors);
            case SYSTEM_SECURITY_PLAN -> validateSSPBusinessRules((OSCALSystemSecurityPlan) document, errors);
            // Add other document types as needed
        }
        
        return BusinessValidationResult.builder()
            .valid(errors.isEmpty())
            .errors(errors)
            .build();
    }
    
    private void validateAssessmentPlanBusinessRules(OSCALAssessmentPlan plan, 
                                                   List<BusinessValidationError> errors) {
        // Validate that all referenced controls exist
        if (plan.getReviewedControls() != null) {
            for (OSCALReviewedControl control : plan.getReviewedControls().getControlSelections()) {
                if (!controlExists(control.getControlId())) {
                    errors.add(new BusinessValidationError(
                        "CONTROL_NOT_FOUND",
                        "Referenced control not found: " + control.getControlId()
                    ));
                }
            }
        }
        
        // Validate that assessment subjects are properly defined
        if (plan.getAssessmentSubjects().isEmpty()) {
            errors.add(new BusinessValidationError(
                "NO_ASSESSMENT_SUBJECTS",
                "Assessment plan must define at least one assessment subject"
            ));
        }
        
        // Validate task dependencies
        validateTaskDependencies(plan.getTasks(), errors);
    }
}
```

This OSCAL integration setup provides comprehensive support for parsing, validating, and transforming OSCAL documents into executable Flowable workflows while maintaining full compliance with OSCAL standards.
