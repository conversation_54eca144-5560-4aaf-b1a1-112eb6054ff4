<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Constant Field Values (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Constant Field Values (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Constant Field Values" class="title">Constant Field Values</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#org.activiti">org.activiti.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="org.activiti">
<!--   -->
</a>
<h2 title="org.activiti">org.activiti.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.<a href="org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine">DynamicBpmnConstants</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.BPMN_NODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#BPMN_NODE">BPMN_NODE</a></code></td>
<td class="colLast"><code>"bpmn"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.LOCALIZATION_DESCRIPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_DESCRIPTION">LOCALIZATION_DESCRIPTION</a></code></td>
<td class="colLast"><code>"description"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.LOCALIZATION_LANGUAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_LANGUAGE">LOCALIZATION_LANGUAGE</a></code></td>
<td class="colLast"><code>"language"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.LOCALIZATION_NAME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_NAME">LOCALIZATION_NAME</a></code></td>
<td class="colLast"><code>"name"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.LOCALIZATION_NODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#LOCALIZATION_NODE">LOCALIZATION_NODE</a></code></td>
<td class="colLast"><code>"localization"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.SCRIPT_TASK_SCRIPT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#SCRIPT_TASK_SCRIPT">SCRIPT_TASK_SCRIPT</a></code></td>
<td class="colLast"><code>"scriptTaskScript"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.SEQUENCE_FLOW_CONDITION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#SEQUENCE_FLOW_CONDITION">SEQUENCE_FLOW_CONDITION</a></code></td>
<td class="colLast"><code>"sequenceFlowCondition"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.SERVICE_TASK_CLASS_NAME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_CLASS_NAME">SERVICE_TASK_CLASS_NAME</a></code></td>
<td class="colLast"><code>"serviceTaskClassName"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.SERVICE_TASK_DELEGATE_EXPRESSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_DELEGATE_EXPRESSION">SERVICE_TASK_DELEGATE_EXPRESSION</a></code></td>
<td class="colLast"><code>"serviceTaskDelegateExpression"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.SERVICE_TASK_EXPRESSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#SERVICE_TASK_EXPRESSION">SERVICE_TASK_EXPRESSION</a></code></td>
<td class="colLast"><code>"serviceTaskExpression"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.TASK_SKIP_EXPRESSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#TASK_SKIP_EXPRESSION">TASK_SKIP_EXPRESSION</a></code></td>
<td class="colLast"><code>"taskSkipExpression"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_ASSIGNEE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_ASSIGNEE">USER_TASK_ASSIGNEE</a></code></td>
<td class="colLast"><code>"userTaskAssignee"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_CANDIDATE_GROUPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_GROUPS">USER_TASK_CANDIDATE_GROUPS</a></code></td>
<td class="colLast"><code>"userTaskCandidateGroups"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_CANDIDATE_USERS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CANDIDATE_USERS">USER_TASK_CANDIDATE_USERS</a></code></td>
<td class="colLast"><code>"userTaskCandidateUsers"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_CATEGORY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_CATEGORY">USER_TASK_CATEGORY</a></code></td>
<td class="colLast"><code>"userTaskCategory"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_DESCRIPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_DESCRIPTION">USER_TASK_DESCRIPTION</a></code></td>
<td class="colLast"><code>"userTaskDescription"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_DUEDATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_DUEDATE">USER_TASK_DUEDATE</a></code></td>
<td class="colLast"><code>"userTaskDueDate"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_FORM_KEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_FORM_KEY">USER_TASK_FORM_KEY</a></code></td>
<td class="colLast"><code>"userTaskFormKey"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_NAME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_NAME">USER_TASK_NAME</a></code></td>
<td class="colLast"><code>"userTaskName"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_OWNER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_OWNER">USER_TASK_OWNER</a></code></td>
<td class="colLast"><code>"userTaskOwner"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.DynamicBpmnConstants.USER_TASK_PRIORITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/DynamicBpmnConstants.html#USER_TASK_PRIORITY">USER_TASK_PRIORITY</a></code></td>
<td class="colLast"><code>"userTaskPriority"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.<a href="org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.ProcessEngine.VERSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/ProcessEngine.html#VERSION">VERSION</a></code></td>
<td class="colLast"><code>"5.23.0.0"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.<a href="org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.ProcessEngineConfiguration.DB_SCHEMA_UPDATE_CREATE_DROP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/ProcessEngineConfiguration.html#DB_SCHEMA_UPDATE_CREATE_DROP">DB_SCHEMA_UPDATE_CREATE_DROP</a></code></td>
<td class="colLast"><code>"create-drop"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.ProcessEngineConfiguration.DB_SCHEMA_UPDATE_FALSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/ProcessEngineConfiguration.html#DB_SCHEMA_UPDATE_FALSE">DB_SCHEMA_UPDATE_FALSE</a></code></td>
<td class="colLast"><code>"false"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/ProcessEngineConfiguration.html#DB_SCHEMA_UPDATE_TRUE">DB_SCHEMA_UPDATE_TRUE</a></code></td>
<td class="colLast"><code>"true"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.ProcessEngineConfiguration.NO_TENANT_ID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/ProcessEngineConfiguration.html#NO_TENANT_ID">NO_TENANT_ID</a></code></td>
<td class="colLast"><code>""</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.<a href="org/activiti/engine/ProcessEngines.html" title="class in org.activiti.engine">ProcessEngines</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.ProcessEngines.NAME_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/ProcessEngines.html#NAME_DEFAULT">NAME_DEFAULT</a></code></td>
<td class="colLast"><code>"default"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.delegate.<a href="org/activiti/engine/delegate/ExecutionListener.html" title="interface in org.activiti.engine.delegate">ExecutionListener</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.delegate.ExecutionListener.EVENTNAME_END">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/delegate/ExecutionListener.html#EVENTNAME_END">EVENTNAME_END</a></code></td>
<td class="colLast"><code>"end"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.delegate.ExecutionListener.EVENTNAME_START">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/delegate/ExecutionListener.html#EVENTNAME_START">EVENTNAME_START</a></code></td>
<td class="colLast"><code>"start"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.delegate.ExecutionListener.EVENTNAME_TAKE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/delegate/ExecutionListener.html#EVENTNAME_TAKE">EVENTNAME_TAKE</a></code></td>
<td class="colLast"><code>"take"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.delegate.<a href="org/activiti/engine/delegate/TaskListener.html" title="interface in org.activiti.engine.delegate">TaskListener</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.delegate.TaskListener.EVENTNAME_ALL_EVENTS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/delegate/TaskListener.html#EVENTNAME_ALL_EVENTS">EVENTNAME_ALL_EVENTS</a></code></td>
<td class="colLast"><code>"all"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.delegate.TaskListener.EVENTNAME_ASSIGNMENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/delegate/TaskListener.html#EVENTNAME_ASSIGNMENT">EVENTNAME_ASSIGNMENT</a></code></td>
<td class="colLast"><code>"assignment"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.delegate.TaskListener.EVENTNAME_COMPLETE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/delegate/TaskListener.html#EVENTNAME_COMPLETE">EVENTNAME_COMPLETE</a></code></td>
<td class="colLast"><code>"complete"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.delegate.TaskListener.EVENTNAME_CREATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/delegate/TaskListener.html#EVENTNAME_CREATE">EVENTNAME_CREATE</a></code></td>
<td class="colLast"><code>"create"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.delegate.TaskListener.EVENTNAME_DELETE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/delegate/TaskListener.html#EVENTNAME_DELETE">EVENTNAME_DELETE</a></code></td>
<td class="colLast"><code>"delete"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.dynamic.<a href="org/activiti/engine/dynamic/PropertiesParserConstants.html" title="interface in org.activiti.engine.dynamic">PropertiesParserConstants</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.dynamic.PropertiesParserConstants.BPMN_MODEL_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/dynamic/PropertiesParserConstants.html#BPMN_MODEL_VALUE">BPMN_MODEL_VALUE</a></code></td>
<td class="colLast"><code>"bpmnmodel"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.dynamic.PropertiesParserConstants.DYNAMIC_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/dynamic/PropertiesParserConstants.html#DYNAMIC_VALUE">DYNAMIC_VALUE</a></code></td>
<td class="colLast"><code>"dynamic"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.dynamic.PropertiesParserConstants.ELEMENT_ID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/dynamic/PropertiesParserConstants.html#ELEMENT_ID">ELEMENT_ID</a></code></td>
<td class="colLast"><code>"elementId"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.dynamic.PropertiesParserConstants.ELEMENT_PROPERTIES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/dynamic/PropertiesParserConstants.html#ELEMENT_PROPERTIES">ELEMENT_PROPERTIES</a></code></td>
<td class="colLast"><code>"elementProperties"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.dynamic.PropertiesParserConstants.ELEMENT_TYPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/dynamic/PropertiesParserConstants.html#ELEMENT_TYPE">ELEMENT_TYPE</a></code></td>
<td class="colLast"><code>"elementType"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.logging.<a href="org/activiti/engine/logging/LogMDC.html" title="class in org.activiti.engine.logging">LogMDC</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.logging.LogMDC.LOG_MDC_BUSINESS_KEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/logging/LogMDC.html#LOG_MDC_BUSINESS_KEY">LOG_MDC_BUSINESS_KEY</a></code></td>
<td class="colLast"><code>"mdcBusinessKey"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.logging.LogMDC.LOG_MDC_EXECUTION_ID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/logging/LogMDC.html#LOG_MDC_EXECUTION_ID">LOG_MDC_EXECUTION_ID</a></code></td>
<td class="colLast"><code>"mdcExecutionId"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.logging.LogMDC.LOG_MDC_PROCESSDEFINITION_ID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/logging/LogMDC.html#LOG_MDC_PROCESSDEFINITION_ID">LOG_MDC_PROCESSDEFINITION_ID</a></code></td>
<td class="colLast"><code>"mdcProcessDefinitionID"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.logging.LogMDC.LOG_MDC_PROCESSINSTANCE_ID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/logging/LogMDC.html#LOG_MDC_PROCESSINSTANCE_ID">LOG_MDC_PROCESSINSTANCE_ID</a></code></td>
<td class="colLast"><code>"mdcProcessInstanceID"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.logging.LogMDC.LOG_MDC_TASK_ID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/logging/LogMDC.html#LOG_MDC_TASK_ID">LOG_MDC_TASK_ID</a></code></td>
<td class="colLast"><code>"mdcTaskId"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.task.<a href="org/activiti/engine/task/Event.html" title="interface in org.activiti.engine.task">Event</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.task.Event.ACTION_ADD_ATTACHMENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/Event.html#ACTION_ADD_ATTACHMENT">ACTION_ADD_ATTACHMENT</a></code></td>
<td class="colLast"><code>"AddAttachment"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.task.Event.ACTION_ADD_COMMENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/Event.html#ACTION_ADD_COMMENT">ACTION_ADD_COMMENT</a></code></td>
<td class="colLast"><code>"AddComment"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.task.Event.ACTION_ADD_GROUP_LINK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/Event.html#ACTION_ADD_GROUP_LINK">ACTION_ADD_GROUP_LINK</a></code></td>
<td class="colLast"><code>"AddGroupLink"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.task.Event.ACTION_ADD_USER_LINK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/Event.html#ACTION_ADD_USER_LINK">ACTION_ADD_USER_LINK</a></code></td>
<td class="colLast"><code>"AddUserLink"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.task.Event.ACTION_DELETE_ATTACHMENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/Event.html#ACTION_DELETE_ATTACHMENT">ACTION_DELETE_ATTACHMENT</a></code></td>
<td class="colLast"><code>"DeleteAttachment"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.task.Event.ACTION_DELETE_GROUP_LINK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/Event.html#ACTION_DELETE_GROUP_LINK">ACTION_DELETE_GROUP_LINK</a></code></td>
<td class="colLast"><code>"DeleteGroupLink"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.task.Event.ACTION_DELETE_USER_LINK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/Event.html#ACTION_DELETE_USER_LINK">ACTION_DELETE_USER_LINK</a></code></td>
<td class="colLast"><code>"DeleteUserLink"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.task.<a href="org/activiti/engine/task/IdentityLinkType.html" title="class in org.activiti.engine.task">IdentityLinkType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.task.IdentityLinkType.ASSIGNEE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/IdentityLinkType.html#ASSIGNEE">ASSIGNEE</a></code></td>
<td class="colLast"><code>"assignee"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.task.IdentityLinkType.CANDIDATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/IdentityLinkType.html#CANDIDATE">CANDIDATE</a></code></td>
<td class="colLast"><code>"candidate"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.task.IdentityLinkType.OWNER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/IdentityLinkType.html#OWNER">OWNER</a></code></td>
<td class="colLast"><code>"owner"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.activiti.engine.task.IdentityLinkType.PARTICIPANT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/IdentityLinkType.html#PARTICIPANT">PARTICIPANT</a></code></td>
<td class="colLast"><code>"participant"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.task.IdentityLinkType.STARTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td><code><a href="org/activiti/engine/task/IdentityLinkType.html#STARTER">STARTER</a></code></td>
<td class="colLast"><code>"starter"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.activiti.engine.task.<a href="org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task">Task</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.activiti.engine.task.Task.DEFAULT_PRIORITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/activiti/engine/task/Task.html#DEFAULT_PRIORITY">DEFAULT_PRIORITY</a></code></td>
<td class="colLast"><code>50</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
