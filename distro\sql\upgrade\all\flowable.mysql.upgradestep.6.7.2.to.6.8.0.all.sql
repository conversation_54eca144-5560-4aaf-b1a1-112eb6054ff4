update ACT_GE_PROPERTY set VALUE_ = '*******' where NAME_ = 'common.schema.version';

update ACT_GE_PROPERTY set VALUE_ = '*******' where NAME_ = 'entitylink.schema.version';

update ACT_GE_PROPERTY set VALUE_ = '*******' where NAME_ = 'identitylink.schema.version';

update ACT_GE_PROPERTY set VALUE_ = '*******' where NAME_ = 'job.schema.version';

update ACT_GE_PROPERTY set VALUE_ = '*******' where NAME_ = 'batch.schema.version';

update ACT_GE_PROPERTY set VALUE_ = '*******' where NAME_ = 'task.schema.version';

update ACT_GE_PROPERTY set VALUE_ = '*******' where NAME_ = 'variable.schema.version';

alter table ACT_RU_EVENT_SUBSCR add column LOCK_TIME_ timestamp(3) NULL;
alter table ACT_RU_EVENT_SUBSCR add column LOCK_OWNER_ varchar(255);

update ACT_GE_PROPERTY set VALUE_ = '*******' where NAME_ = 'eventsubscription.schema.version';
create index ACT_IDX_HI_PRO_SUPER_PROCINST on ACT_HI_PROCINST(SUPER_PROCESS_INSTANCE_ID_);

update ACT_GE_PROPERTY set VALUE_ = '*******' where NAME_ = 'schema.version';

update ACT_ID_PROPERTY set VALUE_ = '*******' where NAME_ = 'schema.version';

UPDATE ACT_APP_DATABASECHANGELOGLOCK SET `LOCKED` = 1, LOCKEDBY = '************ (************)', LOCKGRANTED = '2022-12-21 18:20:04.836' WHERE ID = 1 AND `LOCKED` = 0;

UPDATE ACT_APP_DATABASECHANGELOGLOCK SET `LOCKED` = 0, LOCKEDBY = NULL, LOCKGRANTED = NULL WHERE ID = 1;


UPDATE ACT_CMMN_DATABASECHANGELOGLOCK SET `LOCKED` = 1, LOCKEDBY = '************ (************)', LOCKGRANTED = '2022-12-21 18:20:05.357' WHERE ID = 1 AND `LOCKED` = 0;

CREATE INDEX ACT_IDX_HI_CASE_INST_END ON ACT_CMMN_HI_CASE_INST(END_TIME_);

INSERT INTO ACT_CMMN_DATABASECHANGELOG (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, `DESCRIPTION`, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('17', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', NOW(), 16, '8:d3706c5813a9b97fd2a59d12a9523946', 'createIndex indexName=ACT_IDX_HI_CASE_INST_END, tableName=ACT_CMMN_HI_CASE_INST', '', 'EXECUTED', NULL, NULL, '4.3.5', '1643205484');

UPDATE ACT_CMMN_DATABASECHANGELOGLOCK SET `LOCKED` = 0, LOCKEDBY = NULL, LOCKGRANTED = NULL WHERE ID = 1;


UPDATE FLW_EV_DATABASECHANGELOGLOCK SET `LOCKED` = 1, LOCKEDBY = '************ (************)', LOCKGRANTED = '2022-12-21 18:20:05.637' WHERE ID = 1 AND `LOCKED` = 0;

UPDATE FLW_EV_DATABASECHANGELOGLOCK SET `LOCKED` = 0, LOCKEDBY = NULL, LOCKGRANTED = NULL WHERE ID = 1;


UPDATE ACT_DMN_DATABASECHANGELOGLOCK SET `LOCKED` = 1, LOCKEDBY = '************ (************)', LOCKGRANTED = '2022-12-21 18:20:05.824' WHERE ID = 1 AND `LOCKED` = 0;

CREATE INDEX ACT_IDX_DMN_INSTANCE_ID ON ACT_DMN_HI_DECISION_EXECUTION(INSTANCE_ID_);

INSERT INTO ACT_DMN_DATABASECHANGELOG (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, `DESCRIPTION`, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('9', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', NOW(), 9, '8:0fe82086431b1953d293f0199f805876', 'createIndex indexName=ACT_IDX_DMN_INSTANCE_ID, tableName=ACT_DMN_HI_DECISION_EXECUTION', '', 'EXECUTED', NULL, NULL, '4.3.5', '1643205932');

UPDATE ACT_DMN_DATABASECHANGELOGLOCK SET `LOCKED` = 0, LOCKEDBY = NULL, LOCKGRANTED = NULL WHERE ID = 1;


UPDATE ACT_FO_DATABASECHANGELOGLOCK SET `LOCKED` = 1, LOCKEDBY = '************ (************)', LOCKGRANTED = '2022-12-21 18:20:06.028' WHERE ID = 1 AND `LOCKED` = 0;

UPDATE ACT_FO_DATABASECHANGELOGLOCK SET `LOCKED` = 0, LOCKEDBY = NULL, LOCKGRANTED = NULL WHERE ID = 1;


UPDATE ACT_CO_DATABASECHANGELOGLOCK SET `LOCKED` = 1, LOCKEDBY = '************ (************)', LOCKGRANTED = '2022-12-21 18:20:06.224' WHERE ID = 1 AND `LOCKED` = 0;

UPDATE ACT_CO_DATABASECHANGELOGLOCK SET `LOCKED` = 0, LOCKEDBY = NULL, LOCKGRANTED = NULL WHERE ID = 1;

