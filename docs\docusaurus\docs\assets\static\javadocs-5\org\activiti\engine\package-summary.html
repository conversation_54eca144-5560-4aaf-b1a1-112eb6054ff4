<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.activiti.engine (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.activiti.engine (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li><a href="../../../org/activiti/engine/cfg/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.activiti.engine</h1>
<div class="docSummary">
<div class="block">Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine"><code>ProcessEngineConfiguration</code></a>
    (typically based on a configuration file), from which a <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> can be obtained.<br/><br/>
    Through the services obtained from such a <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>: </b> Manages <a href="../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s <br/>

    <b><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>: </b> For starting and searching <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s <br/>
    
    <b><a href="../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>: </b> Exposes operations to manage human (standalone) <a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s, 
    such as claiming, completing and assigning tasks<br/>

    <b><a href="../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>: </b> Used for managing <a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s, 
    <a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s and the relations between them<br/>
        
    <b><a href="../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b><a href="../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/></div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/DynamicBpmnConstants.html" title="interface in org.activiti.engine">DynamicBpmnConstants</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/DynamicBpmnService.html" title="interface in org.activiti.engine">DynamicBpmnService</a></td>
<td class="colLast">
<div class="block">Service providing access to the repository of process definitions and deployments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/EngineServices.html" title="interface in org.activiti.engine">EngineServices</a></td>
<td class="colLast">
<div class="block">Interface implemented by all classes that expose the Activiti services.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine">FormService</a></td>
<td class="colLast">
<div class="block">Access to form data and rendered forms for starting new process instances and completing tasks.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine">HistoryService</a></td>
<td class="colLast">
<div class="block">Service exposing information about ongoing and past process instances.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine">IdentityService</a></td>
<td class="colLast">
<div class="block">Service to manage <a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s and <a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine">ManagementService</a></td>
<td class="colLast">
<div class="block">Service for admin and maintenance operations on the process engine.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine">ProcessEngine</a></td>
<td class="colLast">
<div class="block">Provides access to all the services that expose the BPM and workflow operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ProcessEngineInfo.html" title="interface in org.activiti.engine">ProcessEngineInfo</a></td>
<td class="colLast">
<div class="block">Represents information about the initialization of the process engine.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ProcessEngineLifecycleListener.html" title="interface in org.activiti.engine">ProcessEngineLifecycleListener</a></td>
<td class="colLast">
<div class="block">Interface describing a listener that get's notified when certain event occurs,
 related to the process-engine lifecycle it is attached to.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine">RepositoryService</a></td>
<td class="colLast">
<div class="block">Service providing access to the repository of process definitions and deployments.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine">RuntimeService</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine">TaskService</a></td>
<td class="colLast">
<div class="block">Service which provides access to <a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a> and form related operations.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine">ProcessEngineConfiguration</a></td>
<td class="colLast">
<div class="block">Configuration information from which a process engine can be build.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ProcessEngines.html" title="class in org.activiti.engine">ProcessEngines</a></td>
<td class="colLast">
<div class="block">Helper for initializing and closing process engines in server environments.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ActivitiClassLoadingException.html" title="class in org.activiti.engine">ActivitiClassLoadingException</a></td>
<td class="colLast">
<div class="block">Runtime exception indicating the requested class was not found or an error occurred
 while loading the class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ActivitiException.html" title="class in org.activiti.engine">ActivitiException</a></td>
<td class="colLast">
<div class="block">Runtime exception that is the superclass of all Activiti exceptions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ActivitiIllegalArgumentException.html" title="class in org.activiti.engine">ActivitiIllegalArgumentException</a></td>
<td class="colLast">
<div class="block">An exception indicating that an illegal argument has been supplied in an Activiti API-call, 
 an illegal value was configured in the engine's configuration or an illegal value has been supplied
 or an illegal value is used in a process-definition.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ActivitiObjectNotFoundException.html" title="class in org.activiti.engine">ActivitiObjectNotFoundException</a></td>
<td class="colLast">
<div class="block">An exception indicating that the object that is required or actioned on
 does not exist.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ActivitiOptimisticLockingException.html" title="class in org.activiti.engine">ActivitiOptimisticLockingException</a></td>
<td class="colLast">
<div class="block">Exception that is thrown when an optimistic locking occurs in the datastore 
 caused by concurrent access of the same data entry.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ActivitiTaskAlreadyClaimedException.html" title="class in org.activiti.engine">ActivitiTaskAlreadyClaimedException</a></td>
<td class="colLast">
<div class="block">This exception is thrown when you try to claim a task that is already claimed
 by someone else.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/activiti/engine/ActivitiWrongDbException.html" title="class in org.activiti.engine">ActivitiWrongDbException</a></td>
<td class="colLast">
<div class="block">Exception that is thrown when the Activiti engine discovers a mismatch between the 
 database schema version and the engine version.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/activiti/engine/JobNotFoundException.html" title="class in org.activiti.engine">JobNotFoundException</a></td>
<td class="colLast">
<div class="block">This exception is thrown when you try to execute a job that is not found (may
 be due to cancelActiviti="true" for instance)..</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package org.activiti.engine Description">Package org.activiti.engine Description</h2>
<div class="block">Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a <a href="../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine"><code>ProcessEngineConfiguration</code></a>
    (typically based on a configuration file), from which a <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> can be obtained.<br/><br/>
    Through the services obtained from such a <a href="../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b><a href="../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>: </b> Manages <a href="../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s <br/>

    <b><a href="../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>: </b> For starting and searching <a href="../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s <br/>
    
    <b><a href="../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>: </b> Exposes operations to manage human (standalone) <a href="../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s, 
    such as claiming, completing and assigning tasks<br/>

    <b><a href="../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>: </b> Used for managing <a href="../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s, 
    <a href="../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s and the relations between them<br/>
        
    <b><a href="../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b><a href="../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b><a href="../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/></div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li><a href="../../../org/activiti/engine/cfg/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/activiti/engine/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
