<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:25 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HistoricTaskInstanceQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HistoricTaskInstanceQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":38,"i4":6,"i5":6,"i6":38,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricTaskInstanceQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricTaskInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricTaskInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.activiti.engine.history</div>
<h2 title="Interface HistoricTaskInstanceQuery" class="title">Interface HistoricTaskInstanceQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history">HistoricTaskInstance</a>&gt;, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task">TaskInfoQuery</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history">HistoricTaskInstance</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">HistoricTaskInstanceQuery</span>
extends <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task">TaskInfoQuery</a>&lt;<a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>,<a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history">HistoricTaskInstance</a>&gt;</pre>
<div class="block">Allows programmatic querying for <a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><code>HistoricTaskInstance</code></a>s.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Tom Baeyens, Joram Barrez</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#finished--">finished</a></span>()</code>
<div class="block">Only select historic task instances which are finished.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByDeleteReason--">orderByDeleteReason</a></span>()</code>
<div class="block">Order by task delete reason (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricActivityInstanceId--">orderByHistoricActivityInstanceId</a></span>()</code>
<div class="block">Order by the historic activity instance id this task was used in
 (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricActivityInstanceStartTime--">orderByHistoricActivityInstanceStartTime</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">use <a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricTaskInstanceStartTime--"><code>orderByHistoricTaskInstanceStartTime()</code></a></span></div>
</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricTaskInstanceDuration--">orderByHistoricTaskInstanceDuration</a></span>()</code>
<div class="block">Order by duration (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricTaskInstanceEndTime--">orderByHistoricTaskInstanceEndTime</a></span>()</code>
<div class="block">Order by end time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricTaskInstanceStartTime--">orderByHistoricTaskInstanceStartTime</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskCreateTime--"><code>TaskInfoQuery.orderByTaskCreateTime()</code></a></span></div>
</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#processFinished--">processFinished</a></span>()</code>
<div class="block">Only select historic task instances which are part of a process
 instance which is already finished.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#processUnfinished--">processUnfinished</a></span>()</code>
<div class="block">Only select historic task instances which are part of a process
 instance which is not finished yet.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#taskCompletedAfter-java.util.Date-">taskCompletedAfter</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate)</code>
<div class="block">Only select select historic task instances which are completed after the given date</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#taskCompletedBefore-java.util.Date-">taskCompletedBefore</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate)</code>
<div class="block">Only select select historic task instances which are completed before the given date</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#taskCompletedOn-java.util.Date-">taskCompletedOn</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate)</code>
<div class="block">Only select select historic task instances which are completed on the given date</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#taskDeleteReason-java.lang.String-">taskDeleteReason</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskDeleteReason)</code>
<div class="block">Only select historic task instances with the given task delete reason.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#taskDeleteReasonLike-java.lang.String-">taskDeleteReasonLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskDeleteReasonLike)</code>
<div class="block">Only select historic task instances with a task description like the given value.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#taskParentTaskId-java.lang.String-">taskParentTaskId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentTaskId)</code>
<div class="block">Only select subtasks of the given parent task</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#unfinished--">unfinished</a></span>()</code>
<div class="block">Only select historic task instances which aren't finished yet.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.task.TaskInfoQuery">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.task.<a href="../../../../org/activiti/engine/task/TaskInfoQuery.html" title="interface in org.activiti.engine.task">TaskInfoQuery</a></h3>
<code><a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#deploymentId-java.lang.String-">deploymentId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#deploymentIdIn-java.util.List-">deploymentIdIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#dueAfter-java.util.Date-">dueAfter</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#dueBefore-java.util.Date-">dueBefore</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#dueDate-java.util.Date-">dueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#endOr--">endOr</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#executionId-java.lang.String-">executionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#includeProcessVariables--">includeProcessVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#includeTaskLocalVariables--">includeTaskLocalVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#limitTaskVariables-java.lang.Integer-">limitTaskVariables</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#locale-java.lang.String-">locale</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#or--">or</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByDueDateNullsFirst--">orderByDueDateNullsFirst</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByDueDateNullsLast--">orderByDueDateNullsLast</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByExecutionId--">orderByExecutionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByProcessDefinitionId--">orderByProcessDefinitionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskAssignee--">orderByTaskAssignee</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskCreateTime--">orderByTaskCreateTime</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskDefinitionKey--">orderByTaskDefinitionKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskDescription--">orderByTaskDescription</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskDueDate--">orderByTaskDueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskId--">orderByTaskId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskName--">orderByTaskName</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskOwner--">orderByTaskOwner</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskPriority--">orderByTaskPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTenantId--">orderByTenantId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processCategoryIn-java.util.List-">processCategoryIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processCategoryNotIn-java.util.List-">processCategoryNotIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyIn-java.util.List-">processDefinitionKeyIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyLike-java.lang.String-">processDefinitionKeyLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionKeyLikeIgnoreCase-java.lang.String-">processDefinitionKeyLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionName-java.lang.String-">processDefinitionName</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processDefinitionNameLike-java.lang.String-">processDefinitionNameLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKey-java.lang.String-">processInstanceBusinessKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKeyLike-java.lang.String-">processInstanceBusinessKeyLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceBusinessKeyLikeIgnoreCase-java.lang.String-">processInstanceBusinessKeyLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceId-java.lang.String-">processInstanceId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processInstanceIdIn-java.util.List-">processInstanceIdIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueEquals-java.lang.Object-">processVariableValueEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueEquals-java.lang.String-java.lang.Object-">processVariableValueEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">processVariableValueEqualsIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueGreaterThan-java.lang.String-java.lang.Object-">processVariableValueGreaterThan</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">processVariableValueGreaterThanOrEqual</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLessThan-java.lang.String-java.lang.Object-">processVariableValueLessThan</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLessThanOrEqual-java.lang.String-java.lang.Object-">processVariableValueLessThanOrEqual</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLike-java.lang.String-java.lang.String-">processVariableValueLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">processVariableValueLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueNotEquals-java.lang.String-java.lang.Object-">processVariableValueNotEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#processVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">processVariableValueNotEqualsIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssignee-java.lang.String-">taskAssignee</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeIds-java.util.List-">taskAssigneeIds</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeLike-java.lang.String-">taskAssigneeLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskAssigneeLikeIgnoreCase-java.lang.String-">taskAssigneeLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateGroup-java.lang.String-">taskCandidateGroup</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateGroupIn-java.util.List-">taskCandidateGroupIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCandidateUser-java.lang.String-">taskCandidateUser</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCategory-java.lang.String-">taskCategory</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCreatedAfter-java.util.Date-">taskCreatedAfter</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCreatedBefore-java.util.Date-">taskCreatedBefore</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskCreatedOn-java.util.Date-">taskCreatedOn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDefinitionKey-java.lang.String-">taskDefinitionKey</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDefinitionKeyLike-java.lang.String-">taskDefinitionKeyLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescription-java.lang.String-">taskDescription</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescriptionLike-java.lang.String-">taskDescriptionLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDescriptionLikeIgnoreCase-java.lang.String-">taskDescriptionLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDueAfter-java.util.Date-">taskDueAfter</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDueBefore-java.util.Date-">taskDueBefore</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskDueDate-java.util.Date-">taskDueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskId-java.lang.String-">taskId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskInvolvedUser-java.lang.String-">taskInvolvedUser</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskMaxPriority-java.lang.Integer-">taskMaxPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskMinPriority-java.lang.Integer-">taskMinPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskName-java.lang.String-">taskName</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameIn-java.util.List-">taskNameIn</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameInIgnoreCase-java.util.List-">taskNameInIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameLike-java.lang.String-">taskNameLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskNameLikeIgnoreCase-java.lang.String-">taskNameLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwner-java.lang.String-">taskOwner</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwnerLike-java.lang.String-">taskOwnerLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskOwnerLikeIgnoreCase-java.lang.String-">taskOwnerLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskPriority-java.lang.Integer-">taskPriority</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskTenantId-java.lang.String-">taskTenantId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskTenantIdLike-java.lang.String-">taskTenantIdLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueEquals-java.lang.Object-">taskVariableValueEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueEquals-java.lang.String-java.lang.Object-">taskVariableValueEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueEqualsIgnoreCase-java.lang.String-java.lang.String-">taskVariableValueEqualsIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueGreaterThan-java.lang.String-java.lang.Object-">taskVariableValueGreaterThan</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueGreaterThanOrEqual-java.lang.String-java.lang.Object-">taskVariableValueGreaterThanOrEqual</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLessThan-java.lang.String-java.lang.Object-">taskVariableValueLessThan</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLessThanOrEqual-java.lang.String-java.lang.Object-">taskVariableValueLessThanOrEqual</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLike-java.lang.String-java.lang.String-">taskVariableValueLike</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueLikeIgnoreCase-java.lang.String-java.lang.String-">taskVariableValueLikeIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueNotEquals-java.lang.String-java.lang.Object-">taskVariableValueNotEquals</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskVariableValueNotEqualsIgnoreCase-java.lang.String-java.lang.String-">taskVariableValueNotEqualsIgnoreCase</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#taskWithoutTenantId--">taskWithoutTenantId</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#withLocalizationFallback--">withLocalizationFallback</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#withoutDueDate--">withoutDueDate</a>, <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#withoutTaskDueDate--">withoutTaskDueDate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.activiti.engine.query.Query">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.activiti.engine.query.<a href="../../../../org/activiti/engine/query/Query.html" title="interface in org.activiti.engine.query">Query</a></h3>
<code><a href="../../../../org/activiti/engine/query/Query.html#asc--">asc</a>, <a href="../../../../org/activiti/engine/query/Query.html#count--">count</a>, <a href="../../../../org/activiti/engine/query/Query.html#desc--">desc</a>, <a href="../../../../org/activiti/engine/query/Query.html#list--">list</a>, <a href="../../../../org/activiti/engine/query/Query.html#listPage-int-int-">listPage</a>, <a href="../../../../org/activiti/engine/query/Query.html#singleResult--">singleResult</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="taskDeleteReason-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDeleteReason</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;taskDeleteReason(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskDeleteReason)</pre>
<div class="block">Only select historic task instances with the given task delete reason.</div>
</li>
</ul>
<a name="taskDeleteReasonLike-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskDeleteReasonLike</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;taskDeleteReasonLike(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskDeleteReasonLike)</pre>
<div class="block">Only select historic task instances with a task description like the given value.
 The syntax that should be used is the same as in SQL, eg. %activiti%.</div>
</li>
</ul>
<a name="finished--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finished</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;finished()</pre>
<div class="block">Only select historic task instances which are finished.</div>
</li>
</ul>
<a name="unfinished--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unfinished</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;unfinished()</pre>
<div class="block">Only select historic task instances which aren't finished yet.</div>
</li>
</ul>
<a name="processFinished--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processFinished</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;processFinished()</pre>
<div class="block">Only select historic task instances which are part of a process
 instance which is already finished.</div>
</li>
</ul>
<a name="processUnfinished--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processUnfinished</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;processUnfinished()</pre>
<div class="block">Only select historic task instances which are part of a process
 instance which is not finished yet.</div>
</li>
</ul>
<a name="taskParentTaskId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskParentTaskId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;taskParentTaskId(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentTaskId)</pre>
<div class="block">Only select subtasks of the given parent task</div>
</li>
</ul>
<a name="taskCompletedOn-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCompletedOn</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;taskCompletedOn(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate)</pre>
<div class="block">Only select select historic task instances which are completed on the given date</div>
</li>
</ul>
<a name="taskCompletedBefore-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCompletedBefore</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;taskCompletedBefore(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate)</pre>
<div class="block">Only select select historic task instances which are completed before the given date</div>
</li>
</ul>
<a name="taskCompletedAfter-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskCompletedAfter</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;taskCompletedAfter(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate)</pre>
<div class="block">Only select select historic task instances which are completed after the given date</div>
</li>
</ul>
<a name="orderByHistoricActivityInstanceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByHistoricActivityInstanceId</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;orderByHistoricActivityInstanceId()</pre>
<div class="block">Order by the historic activity instance id this task was used in
 (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByHistoricTaskInstanceDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByHistoricTaskInstanceDuration</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;orderByHistoricTaskInstanceDuration()</pre>
<div class="block">Order by duration (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByHistoricTaskInstanceEndTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByHistoricTaskInstanceEndTime</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;orderByHistoricTaskInstanceEndTime()</pre>
<div class="block">Order by end time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByHistoricActivityInstanceStartTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByHistoricActivityInstanceStartTime</h4>
<pre><a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
<a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;orderByHistoricActivityInstanceStartTime()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">use <a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricTaskInstanceStartTime--"><code>orderByHistoricTaskInstanceStartTime()</code></a></span></div>
<div class="block">Order by start time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByHistoricTaskInstanceStartTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orderByHistoricTaskInstanceStartTime</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;orderByHistoricTaskInstanceStartTime()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use <a href="../../../../org/activiti/engine/task/TaskInfoQuery.html#orderByTaskCreateTime--"><code>TaskInfoQuery.orderByTaskCreateTime()</code></a></span></div>
<div class="block">Order by start time (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
<a name="orderByDeleteReason--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>orderByDeleteReason</h4>
<pre><a href="../../../../org/activiti/engine/history/HistoricTaskInstanceQuery.html" title="interface in org.activiti.engine.history">HistoricTaskInstanceQuery</a>&nbsp;orderByDeleteReason()</pre>
<div class="block">Order by task delete reason (needs to be followed by <a href="../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoricTaskInstanceQuery.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/activiti/engine/history/HistoricTaskInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/activiti/engine/history/HistoricVariableInstance.html" title="interface in org.activiti.engine.history"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/activiti/engine/history/HistoricTaskInstanceQuery.html" target="_top">Frames</a></li>
<li><a href="HistoricTaskInstanceQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
