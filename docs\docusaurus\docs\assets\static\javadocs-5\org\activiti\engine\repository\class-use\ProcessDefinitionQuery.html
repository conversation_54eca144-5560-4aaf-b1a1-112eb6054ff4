<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:26 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.activiti.engine.repository.ProcessDefinitionQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.activiti.engine.repository.ProcessDefinitionQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/repository/class-use/ProcessDefinitionQuery.html" target="_top">Frames</a></li>
<li><a href="ProcessDefinitionQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.activiti.engine.repository.ProcessDefinitionQuery" class="title">Uses of Interface<br>org.activiti.engine.repository.ProcessDefinitionQuery</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine">org.activiti.engine</a></td>
<td class="colLast">
<div class="block">Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a <a href="../../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine"><code>ProcessEngineConfiguration</code></a>
    (typically based on a configuration file), from which a <a href="../../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> can be obtained.<br/><br/>
    Through the services obtained from such a <a href="../../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b><a href="../../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>: </b> Manages <a href="../../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s <br/>

    <b><a href="../../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>: </b> For starting and searching <a href="../../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s <br/>
    
    <b><a href="../../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>: </b> Exposes operations to manage human (standalone) <a href="../../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s, 
    such as claiming, completing and assigning tasks<br/>

    <b><a href="../../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>: </b> Used for managing <a href="../../../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s, 
    <a href="../../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s and the relations between them<br/>
        
    <b><a href="../../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b><a href="../../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b><a href="../../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.activiti.engine.repository">org.activiti.engine.repository</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="../../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a> in <a href="../../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> that return <a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">RepositoryService.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/RepositoryService.html#createProcessDefinitionQuery--">createProcessDefinitionQuery</a></span>()</code>
<div class="block">Query process definitions.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.repository">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a> in <a href="../../../../../org/activiti/engine/repository/package-summary.html">org.activiti.engine.repository</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/repository/package-summary.html">org.activiti.engine.repository</a> that return <a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#active--">active</a></span>()</code>
<div class="block">Only selects process definitions which are active</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#deploymentId-java.lang.String-">deploymentId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;deploymentId)</code>
<div class="block">Only select process definitions that are deployed in a deployment with the
 given deployment id</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#deploymentIds-java.util.Set-">deploymentIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;deploymentIds)</code>
<div class="block">Select process definitions that are deployed in deployments with the given set of ids</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#latestVersion--">latestVersion</a></span>()</code>
<div class="block">Only select the process definitions which are the latest deployed
 (ie.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#messageEventSubscription-java.lang.String-">messageEventSubscription</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#messageEventSubscriptionName-java.lang.String-">messageEventSubscriptionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageName)</code>
<div class="block">Selects the single process definition which has a start message event 
 with the messageName.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByDeploymentId--">orderByDeploymentId</a></span>()</code>
<div class="block">Order by deployment id (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionCategory--">orderByProcessDefinitionCategory</a></span>()</code>
<div class="block">Order by the category of the process definitions (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionId--">orderByProcessDefinitionId</a></span>()</code>
<div class="block">Order by the id of the process definitions (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionKey--">orderByProcessDefinitionKey</a></span>()</code>
<div class="block">Order by process definition key (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionName--">orderByProcessDefinitionName</a></span>()</code>
<div class="block">Order by the name of the process definitions (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByProcessDefinitionVersion--">orderByProcessDefinitionVersion</a></span>()</code>
<div class="block">Order by the version of the process definitions (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or
 <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionCategory-java.lang.String-">processDefinitionCategory</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategory)</code>
<div class="block">Only select process definitions with the given category.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionCategoryLike-java.lang.String-">processDefinitionCategoryLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionCategoryLike)</code>
<div class="block">Only select process definitions where the category matches the given parameter.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionCategoryNotEquals-java.lang.String-">processDefinitionCategoryNotEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;categoryNotEquals)</code>
<div class="block">Only select deployments that have a different category then the given one.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionId)</code>
<div class="block">Only select process definiton with the given id.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionIds-java.util.Set-">processDefinitionIds</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;processDefinitionIds)</code>
<div class="block">Only select process definitions with the given ids.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionKey-java.lang.String-">processDefinitionKey</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKey)</code>
<div class="block">Only select process definition with the given key.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionKeyLike-java.lang.String-">processDefinitionKeyLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionKeyLike)</code>
<div class="block">Only select process definitions where the key matches the given parameter.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionName-java.lang.String-">processDefinitionName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionName)</code>
<div class="block">Only select process definitions with the given name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionNameLike-java.lang.String-">processDefinitionNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionNameLike)</code>
<div class="block">Only select process definitions where the name matches the given parameter.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionResourceName-java.lang.String-">processDefinitionResourceName</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceName)</code>
<div class="block">Only select process definition with the given resource name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionResourceNameLike-java.lang.String-">processDefinitionResourceNameLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;resourceNameLike)</code>
<div class="block">Only select process definition with a resource name like the given .</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionTenantId-java.lang.String-">processDefinitionTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select process definitions that have the given tenant id.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionTenantIdLike-java.lang.String-">processDefinitionTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select process definitions with a tenant id like the given one.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersion-java.lang.Integer-">processDefinitionVersion</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definition with a certain version.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersionGreaterThan-java.lang.Integer-">processDefinitionVersionGreaterThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definitions which version are greater than a certain version.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersionGreaterThanOrEquals-java.lang.Integer-">processDefinitionVersionGreaterThanOrEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definitions which version are greater than or equals a certain version.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersionLowerThan-java.lang.Integer-">processDefinitionVersionLowerThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definitions which version are lower than a certain version.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionVersionLowerThanOrEquals-java.lang.Integer-">processDefinitionVersionLowerThanOrEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;processDefinitionVersion)</code>
<div class="block">Only select process definitions which version are lower than or equals a certain version.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#processDefinitionWithoutTenantId--">processDefinitionWithoutTenantId</a></span>()</code>
<div class="block">Only select process definitions that do not have a tenant id.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#startableByUser-java.lang.String-">startableByUser</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userId)</code>
<div class="block">Only selects process definitions which given userId is authoriezed to start</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">ProcessDefinitionQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProcessDefinitionQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html#suspended--">suspended</a></span>()</code>
<div class="block">Only selects process definitions which are suspended</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/repository/ProcessDefinitionQuery.html" title="interface in org.activiti.engine.repository">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/repository/class-use/ProcessDefinitionQuery.html" target="_top">Frames</a></li>
<li><a href="ProcessDefinitionQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
