<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<name>Flowable - Batch Service API</name>
	<artifactId>flowable-batch-service-api</artifactId>

	<parent>
		<groupId>org.flowable</groupId>
		<artifactId>flowable-root</artifactId>
		<relativePath>../..</relativePath>
		<version>7.1.1-SNAPSHOT</version>
	</parent>

	<dependencies>
		<dependency>
			<groupId>org.flowable</groupId>
			<artifactId>flowable-engine-common-api</artifactId>
		</dependency>
	</dependencies>

	<properties>
		<flowable.artifact>
			org.flowable.batch.api
		</flowable.artifact>
	</properties>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<goals>
							<goal>cleanVersions</goal>
						</goals>
					</execution>
					<execution>
						<id>bundle-manifest</id>
						<phase>process-classes</phase>
						<goals>
							<goal>manifest</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<!--This plugin's configuration is used to store Eclipse m2e settings 
					only. It has no influence on the Maven build itself. -->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.apache.felix</groupId>
										<artifactId>
											maven-bundle-plugin
										</artifactId>
										<versionRange>
											[2.1.0,)
										</versionRange>
										<goals>
											<goal>cleanVersions</goal>
											<goal>manifest</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore></ignore>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
    
</project>
