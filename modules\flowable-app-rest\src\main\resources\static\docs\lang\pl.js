'use strict';

/* jshint quotmark: double */
window.SwaggerTranslator.learn({
    "Warning: Deprecated":"Uwaga: Wycofane",
    "Implementation Notes":"Uwagi Implementacji",
    "Response Class":"<PERSON>las<PERSON>d<PERSON>",
    "Status":"Status",
    "Parameters":"Parametry",
    "Parameter":"Parametr",
    "Value":"Wartość",
    "Description":"Opis",
    "Parameter Type":"Typ Parametru",
    "Data Type":"Typ Danych",
    "Response Messages":"Wiadomości Odpowiedzi",
    "HTTP Status Code":"Kod Statusu HTTP",
    "Reason":"Przyczyna",
    "Response Model":"Model Odpowiedzi",
    "Request URL":"URL Wywołania",
    "Response Body":"Treść Odpowiedzi",
    "Response Code":"Kod Odpowiedzi",
    "Response Headers":"Nagłówki Odpowiedzi",
    "Hide Response":"Ukryj <PERSON>d<PERSON>ź",
    "Headers":"Nagłówki",
    "Try it out!":"Wypróbuj!",
    "Show/Hide":"Pokaż/Ukryj",
    "List Operations":"Lista Operacji",
    "Expand Operations":"Rozwiń Operacje",
    "Raw":"Nieprzetworzone",
    "can't parse JSON.  Raw result":"nie można przetworzyć pliku JSON.  Nieprzetworzone dane",
    "Model Schema":"Schemat Modelu",
    "Model":"Model",
    "apply":"użyj",
    "Username":"Nazwa użytkownika",
    "Password":"Hasło",
    "Terms of service":"Warunki używania",
    "Created by":"Utworzone przez",
    "See more at":"Zobacz więcej na",
    "Contact the developer":"Kontakt z deweloperem",
    "api version":"wersja api",
    "Response Content Type":"Typ Zasobu Odpowiedzi",
    "fetching resource":"ładowanie zasobu",
    "fetching resource list":"ładowanie listy zasobów",
    "Explore":"Eksploruj",
    "Show Swagger Petstore Example Apis":"Pokaż Przykładowe Api Swagger Petstore",
    "Can't read from server.  It may not have the appropriate access-control-origin settings.":"Brak połączenia z serwerem. Może on nie mieć odpowiednich ustawień access-control-origin.",
    "Please specify the protocol for":"Proszę podać protokół dla",
    "Can't read swagger JSON from":"Nie można odczytać swagger JSON z",
    "Finished Loading Resource Information. Rendering Swagger UI":"Ukończono Ładowanie Informacji o Zasobie. Renderowanie Swagger UI",
    "Unable to read api":"Nie można odczytać api",
    "from path":"ze ścieżki",
    "server returned":"serwer zwrócił"
});
