<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:26 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Deprecated List (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Deprecated List (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Deprecated API" class="title">Deprecated API</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#method">Deprecated Methods</a></li>
</ul>
</div>
<div class="contentContainer"><a name="method">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="Deprecated Methods table, listing deprecated methods, and an explanation">
<caption><span>Deprecated Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="org/activiti/engine/RepositoryService.html#deleteDeploymentCascade-java.lang.String-">org.activiti.engine.RepositoryService.deleteDeploymentCascade(String)</a>
<div class="block"><span class="deprecationComment">use <a href="org/activiti/engine/RepositoryService.html#deleteDeployment-java.lang.String-boolean-"><code>RepositoryService.deleteDeployment(String, boolean)</code></a>.  This methods may be deleted from 5.3.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/activiti/engine/task/TaskInfoQuery.html#dueAfter-java.util.Date-">org.activiti.engine.task.TaskInfoQuery.dueAfter(Date)</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/activiti/engine/task/TaskInfoQuery.html#dueBefore-java.util.Date-">org.activiti.engine.task.TaskInfoQuery.dueBefore(Date)</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/activiti/engine/task/TaskInfoQuery.html#dueDate-java.util.Date-">org.activiti.engine.task.TaskInfoQuery.dueDate(Date)</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/activiti/engine/runtime/JobQuery.html#duedateHigherThen-java.util.Date-">org.activiti.engine.runtime.JobQuery.duedateHigherThen(Date)</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/activiti/engine/runtime/JobQuery.html#duedateHigherThenOrEquals-java.util.Date-">org.activiti.engine.runtime.JobQuery.duedateHigherThenOrEquals(Date)</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/activiti/engine/runtime/JobQuery.html#duedateLowerThen-java.util.Date-">org.activiti.engine.runtime.JobQuery.duedateLowerThen(Date)</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/activiti/engine/runtime/JobQuery.html#duedateLowerThenOrEquals-java.util.Date-">org.activiti.engine.runtime.JobQuery.duedateLowerThenOrEquals(Date)</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/activiti/engine/delegate/DelegateExecution.html#getBusinessKey--">org.activiti.engine.delegate.DelegateExecution.getBusinessKey()</a>
<div class="block"><span class="deprecationComment">use <a href="org/activiti/engine/delegate/DelegateExecution.html#getProcessBusinessKey--"><code>DelegateExecution.getProcessBusinessKey()</code></a> to get the business key for the process
             associated with this execution, regardless whether or not this execution is a 
             process-instance.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/activiti/engine/history/HistoricProcessInstance.html#getEndActivityId--">org.activiti.engine.history.HistoricProcessInstance.getEndActivityId()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/activiti/engine/repository/ProcessDefinitionQuery.html#messageEventSubscription-java.lang.String-">org.activiti.engine.repository.ProcessDefinitionQuery.messageEventSubscription(String)</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/activiti/engine/task/TaskQuery.html#orderByDueDate--">org.activiti.engine.task.TaskQuery.orderByDueDate()</a>
<div class="block"><span class="deprecationComment">Use orderByTaskDueDate() instead</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricActivityInstanceStartTime--">org.activiti.engine.history.HistoricTaskInstanceQuery.orderByHistoricActivityInstanceStartTime()</a>
<div class="block"><span class="deprecationComment">use <a href="org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricTaskInstanceStartTime--"><code>HistoricTaskInstanceQuery.orderByHistoricTaskInstanceStartTime()</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/activiti/engine/history/HistoricTaskInstanceQuery.html#orderByHistoricTaskInstanceStartTime--">org.activiti.engine.history.HistoricTaskInstanceQuery.orderByHistoricTaskInstanceStartTime()</a>
<div class="block"><span class="deprecationComment">Use <a href="org/activiti/engine/task/TaskInfoQuery.html#orderByTaskCreateTime--"><code>TaskInfoQuery.orderByTaskCreateTime()</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/activiti/engine/runtime/ExecutionQuery.html#signalEventSubscription-java.lang.String-">org.activiti.engine.runtime.ExecutionQuery.signalEventSubscription(String)</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/activiti/engine/task/TaskQuery.html#taskUnnassigned--">org.activiti.engine.task.TaskQuery.taskUnnassigned()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/activiti/engine/task/TaskInfoQuery.html#withoutDueDate--">org.activiti.engine.task.TaskInfoQuery.withoutDueDate()</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
