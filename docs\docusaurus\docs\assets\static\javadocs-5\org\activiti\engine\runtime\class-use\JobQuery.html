<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_121) on Mon Mar 27 10:01:26 CEST 2017 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.activiti.engine.runtime.JobQuery (Flowable - Engine 5.23.0 API)</title>
<meta name="date" content="2017-03-27">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.activiti.engine.runtime.JobQuery (Flowable - Engine 5.23.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/runtime/class-use/JobQuery.html" target="_top">Frames</a></li>
<li><a href="JobQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.activiti.engine.runtime.JobQuery" class="title">Uses of Interface<br>org.activiti.engine.runtime.JobQuery</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.activiti.engine">org.activiti.engine</a></td>
<td class="colLast">
<div class="block">Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a <a href="../../../../../org/activiti/engine/ProcessEngineConfiguration.html" title="class in org.activiti.engine"><code>ProcessEngineConfiguration</code></a>
    (typically based on a configuration file), from which a <a href="../../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a> can be obtained.<br/><br/>
    Through the services obtained from such a <a href="../../../../../org/activiti/engine/ProcessEngine.html" title="interface in org.activiti.engine"><code>ProcessEngine</code></a>, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b><a href="../../../../../org/activiti/engine/RepositoryService.html" title="interface in org.activiti.engine"><code>RepositoryService</code></a>: </b> Manages <a href="../../../../../org/activiti/engine/repository/Deployment.html" title="interface in org.activiti.engine.repository"><code>Deployment</code></a>s <br/>

    <b><a href="../../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>: </b> For starting and searching <a href="../../../../../org/activiti/engine/runtime/ProcessInstance.html" title="interface in org.activiti.engine.runtime"><code>ProcessInstance</code></a>s <br/>
    
    <b><a href="../../../../../org/activiti/engine/TaskService.html" title="interface in org.activiti.engine"><code>TaskService</code></a>: </b> Exposes operations to manage human (standalone) <a href="../../../../../org/activiti/engine/task/Task.html" title="interface in org.activiti.engine.task"><code>Task</code></a>s, 
    such as claiming, completing and assigning tasks<br/>

    <b><a href="../../../../../org/activiti/engine/IdentityService.html" title="interface in org.activiti.engine"><code>IdentityService</code></a>: </b> Used for managing <a href="../../../../../org/activiti/engine/identity/User.html" title="interface in org.activiti.engine.identity"><code>User</code></a>s, 
    <a href="../../../../../org/activiti/engine/identity/Group.html" title="interface in org.activiti.engine.identity"><code>Group</code></a>s and the relations between them<br/>
        
    <b><a href="../../../../../org/activiti/engine/ManagementService.html" title="interface in org.activiti.engine"><code>ManagementService</code></a>: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b><a href="../../../../../org/activiti/engine/HistoryService.html" title="interface in org.activiti.engine"><code>HistoryService</code></a>: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b><a href="../../../../../org/activiti/engine/FormService.html" title="interface in org.activiti.engine"><code>FormService</code></a>: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.activiti.engine.runtime">org.activiti.engine.runtime</a></td>
<td class="colLast">
<div class="block">Classes related to the <a href="../../../../../org/activiti/engine/RuntimeService.html" title="interface in org.activiti.engine"><code>RuntimeService</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.activiti.engine">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a> in <a href="../../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/package-summary.html">org.activiti.engine</a> that return <a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">ManagementService.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/ManagementService.html#createJobQuery--">createJobQuery</a></span>()</code>
<div class="block">Returns a new JobQuery implementation, that can be used
 to dynamically query the jobs.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.activiti.engine.runtime">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a> in <a href="../../../../../org/activiti/engine/runtime/package-summary.html">org.activiti.engine.runtime</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../org/activiti/engine/runtime/package-summary.html">org.activiti.engine.runtime</a> that return <a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#duedateHigherThan-java.util.Date-">duedateHigherThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block">Only select jobs where the duedate is higher then the given date.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#duedateHigherThen-java.util.Date-">duedateHigherThen</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#duedateHigherThenOrEquals-java.util.Date-">duedateHigherThenOrEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#duedateLowerThan-java.util.Date-">duedateLowerThan</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block">Only select jobs where the duedate is lower than the given date.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#duedateLowerThen-java.util.Date-">duedateLowerThen</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#duedateLowerThenOrEquals-java.util.Date-">duedateLowerThenOrEquals</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;date)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#exceptionMessage-java.lang.String-">exceptionMessage</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;exceptionMessage)</code>
<div class="block">Only select jobs that failed due to an exception with the given message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#executable--">executable</a></span>()</code>
<div class="block">Only select jobs which are executable, 
 ie.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#executionId-java.lang.String-">executionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;executionId)</code>
<div class="block">Only select jobs which exist for the given execution</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#jobId-java.lang.String-">jobId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;jobId)</code>
<div class="block">Only select jobs with the given id</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#jobTenantId-java.lang.String-">jobTenantId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">Only select jobs that have the given tenant id.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#jobTenantIdLike-java.lang.String-">jobTenantIdLike</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantIdLike)</code>
<div class="block">Only select jobs with a tenant id like the given one.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#jobWithoutTenantId--">jobWithoutTenantId</a></span>()</code>
<div class="block">Only select jobs that do not have a tenant id.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#messages--">messages</a></span>()</code>
<div class="block">Only select jobs that are messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#noRetriesLeft--">noRetriesLeft</a></span>()</code>
<div class="block">Only select jobs which have no retries left</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#orderByExecutionId--">orderByExecutionId</a></span>()</code>
<div class="block">Order by execution id (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#orderByJobDuedate--">orderByJobDuedate</a></span>()</code>
<div class="block">Order by duedate (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#orderByJobId--">orderByJobId</a></span>()</code>
<div class="block">Order by job id (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#orderByJobRetries--">orderByJobRetries</a></span>()</code>
<div class="block">Order by retries (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#orderByProcessInstanceId--">orderByProcessInstanceId</a></span>()</code>
<div class="block">Order by process instance id (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#orderByTenantId--">orderByTenantId</a></span>()</code>
<div class="block">Order by tenant id (needs to be followed by <a href="../../../../../org/activiti/engine/query/Query.html#asc--"><code>Query.asc()</code></a> or <a href="../../../../../org/activiti/engine/query/Query.html#desc--"><code>Query.desc()</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#processDefinitionId-java.lang.String-">processDefinitionId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processDefinitionid)</code>
<div class="block">Only select jobs which exist for the given process definition id</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#processInstanceId-java.lang.String-">processInstanceId</a></span>(<a href="http://docs.oracle.com/javase/6/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;processInstanceId)</code>
<div class="block">Only select jobs which exist for the given process instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#timers--">timers</a></span>()</code>
<div class="block">Only select jobs that are timers.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#withException--">withException</a></span>()</code>
<div class="block">Only select jobs that failed due to an exception.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">JobQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">JobQuery.</span><code><span class="memberNameLink"><a href="../../../../../org/activiti/engine/runtime/JobQuery.html#withRetriesLeft--">withRetriesLeft</a></span>()</code>
<div class="block">Only select jobs which have retries left</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../org/activiti/engine/runtime/JobQuery.html" title="interface in org.activiti.engine.runtime">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?org/activiti/engine/runtime/class-use/JobQuery.html" target="_top">Frames</a></li>
<li><a href="JobQuery.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2017 <a href="http://flowable.org">Flowable</a>. All rights reserved.</small></p>
</body>
</html>
