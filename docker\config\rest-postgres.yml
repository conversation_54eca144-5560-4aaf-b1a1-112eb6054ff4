version: '3.6'
services:
    flowable-rest-app:
        image: flowable/flowable-rest
        depends_on:
            - flowable-rest-db
        environment:
            - SERVER_PORT=8080
            - SPRING_DATASOURCE_DRIVER-CLASS-NAME=org.postgresql.Driver
            - SPRING_DATASOURCE_URL=************************************************
            - SPRING_DATASOURCE_USERNAME=flowable
            - SPRING_DATASOURCE_PASSWORD=flowable
            - FLOWABLE_COMMON_APP_IDM-ADMIN_USER=admin
            - FLOWABLE_COMMON_APP_IDM-ADMIN_PASSWORD=test
        ports:
            - 8080:8080
        entrypoint: ["./wait-for-something.sh", "flowable-rest-db", "5432", "PostgreSQL", "/flowable-entrypoint.sh"]
    flowable-rest-db:
        image: postgres:9.6-alpine
        container_name: flowable-rest-postgres
        environment:
            - POSTGRES_PASSWORD=flowable
            - POSTGRES_USER=flowable
            - POSTGRES_DB=flowable
        ports:
            - 5433:5432
        command: postgres